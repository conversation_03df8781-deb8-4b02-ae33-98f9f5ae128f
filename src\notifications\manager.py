"""Notification Manager for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum
import json

logger = logging.getLogger(__name__)


class NotificationChannel(Enum):
    """Notification channels."""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push_notification"
    WEBHOOK = "webhook"
    IN_APP = "in_app"


class NotificationPriority(Enum):
    """Notification priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class NotificationManager:
    """Notification management system."""
    
    def __init__(self):
        self.notification_history: List[Dict[str, Any]] = []
        self.subscribers: Dict[str, List[str]] = {}
        self.channel_configs = {
            NotificationChannel.EMAIL: {
                'enabled': True,
                'smtp_server': 'smtp.example.com',
                'port': 587,
                'username': '<EMAIL>'
            },
            NotificationChannel.SMS: {
                'enabled': True,
                'provider': 'twilio',
                'api_key': 'simulated_key'
            },
            NotificationChannel.PUSH: {
                'enabled': True,
                'service': 'firebase',
                'api_key': 'simulated_key'
            },
            NotificationChannel.WEBHOOK: {
                'enabled': True,
                'timeout': 30
            },
            NotificationChannel.IN_APP: {
                'enabled': True,
                'retention_days': 30
            }
        }
    
    async def send_notification(self, notification_data: Dict[str, Any]):
        """Send notification through specified channels."""
        try:
            notification_id = f"notif_{len(self.notification_history) + 1}"
            
            notification = {
                'id': notification_id,
                'type': notification_data.get('type', 'info'),
                'title': notification_data.get('title', 'System Notification'),
                'message': notification_data['message'],
                'priority': notification_data.get('priority', 'medium'),
                'channels': notification_data.get('channels', ['email', 'in_app']),
                'recipients': notification_data.get('recipients', []),
                'data': notification_data.get('data', {}),
                'timestamp': datetime.now().isoformat(),
                'status': 'sent',
                'delivery_status': {}
            }
            
            # Send through each channel
            for channel in notification['channels']:
                try:
                    channel_enum = NotificationChannel(channel)
                    delivery_result = await self._send_through_channel(
                        channel_enum, notification
                    )
                    notification['delivery_status'][channel] = delivery_result
                except ValueError:
                    logger.warning(f"Unknown notification channel: {channel}")
                    notification['delivery_status'][channel] = {
                        'status': 'failed',
                        'error': 'Unknown channel'
                    }
            
            # Store notification
            self.notification_history.append(notification)
            
            logger.info(f"Notification sent: {notification_id}")
            
            return {
                'status': 'success',
                'notification_id': notification_id,
                'delivery_status': notification['delivery_status']
            }
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _send_through_channel(self, channel: NotificationChannel, 
                                  notification: Dict[str, Any]):
        """Send notification through specific channel."""
        if not self.channel_configs[channel]['enabled']:
            return {
                'status': 'skipped',
                'reason': 'Channel disabled'
            }
        
        try:
            if channel == NotificationChannel.EMAIL:
                return await self._send_email(notification)
            elif channel == NotificationChannel.SMS:
                return await self._send_sms(notification)
            elif channel == NotificationChannel.PUSH:
                return await self._send_push_notification(notification)
            elif channel == NotificationChannel.WEBHOOK:
                return await self._send_webhook(notification)
            elif channel == NotificationChannel.IN_APP:
                return await self._send_in_app(notification)
            else:
                return {
                    'status': 'failed',
                    'error': 'Unsupported channel'
                }
        except Exception as e:
            logger.error(f"Channel {channel.value} delivery failed: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    async def _send_email(self, notification: Dict[str, Any]):
        """Send email notification."""
        # Simulate email sending
        await asyncio.sleep(0.1)  # Simulate network delay
        
        logger.info(f"Email sent: {notification['title']}")
        return {
            'status': 'delivered',
            'delivery_time': datetime.now().isoformat(),
            'recipients_count': len(notification['recipients'])
        }
    
    async def _send_sms(self, notification: Dict[str, Any]):
        """Send SMS notification."""
        # Simulate SMS sending
        await asyncio.sleep(0.1)
        
        logger.info(f"SMS sent: {notification['message'][:50]}...")
        return {
            'status': 'delivered',
            'delivery_time': datetime.now().isoformat(),
            'recipients_count': len(notification['recipients'])
        }
    
    async def _send_push_notification(self, notification: Dict[str, Any]):
        """Send push notification."""
        # Simulate push notification
        await asyncio.sleep(0.1)
        
        logger.info(f"Push notification sent: {notification['title']}")
        return {
            'status': 'delivered',
            'delivery_time': datetime.now().isoformat(),
            'recipients_count': len(notification['recipients'])
        }
    
    async def _send_webhook(self, notification: Dict[str, Any]):
        """Send webhook notification."""
        # Simulate webhook call
        await asyncio.sleep(0.1)
        
        logger.info(f"Webhook sent: {notification['type']}")
        return {
            'status': 'delivered',
            'delivery_time': datetime.now().isoformat(),
            'response_code': 200
        }
    
    async def _send_in_app(self, notification: Dict[str, Any]):
        """Send in-app notification."""
        # Store for in-app display
        logger.info(f"In-app notification stored: {notification['title']}")
        return {
            'status': 'stored',
            'delivery_time': datetime.now().isoformat(),
            'storage_location': 'in_app_queue'
        }
    
    async def subscribe_user(self, user_id: str, notification_types: List[str]):
        """Subscribe user to notification types."""
        if user_id not in self.subscribers:
            self.subscribers[user_id] = []
        
        for notif_type in notification_types:
            if notif_type not in self.subscribers[user_id]:
                self.subscribers[user_id].append(notif_type)
        
        logger.info(f"User {user_id} subscribed to {notification_types}")
        return True
    
    async def unsubscribe_user(self, user_id: str, notification_types: List[str]):
        """Unsubscribe user from notification types."""
        if user_id in self.subscribers:
            for notif_type in notification_types:
                if notif_type in self.subscribers[user_id]:
                    self.subscribers[user_id].remove(notif_type)
        
        logger.info(f"User {user_id} unsubscribed from {notification_types}")
        return True
    
    async def get_notification_history(self, limit: int = 100):
        """Get notification history."""
        return {
            'notifications': self.notification_history[-limit:],
            'total_count': len(self.notification_history),
            'channels_status': {
                channel.value: config['enabled'] 
                for channel, config in self.channel_configs.items()
            }
        }
    
    async def send_water_quality_alert(self, alert_data: Dict[str, Any]):
        """Send water quality alert."""
        notification_data = {
            'type': 'water_quality_alert',
            'title': 'Water Quality Alert',
            'message': f"Water quality parameter out of range: {alert_data.get('parameter', 'unknown')}",
            'priority': 'high',
            'channels': ['email', 'sms', 'push', 'in_app'],
            'data': alert_data
        }
        
        return await self.send_notification(notification_data)
    
    async def send_maintenance_reminder(self, maintenance_data: Dict[str, Any]):
        """Send maintenance reminder."""
        notification_data = {
            'type': 'maintenance_reminder',
            'title': 'Maintenance Reminder',
            'message': f"Scheduled maintenance for {maintenance_data.get('equipment', 'system')}",
            'priority': 'medium',
            'channels': ['email', 'in_app'],
            'data': maintenance_data
        }
        
        return await self.send_notification(notification_data)
    
    async def send_system_status_update(self, status_data: Dict[str, Any]):
        """Send system status update."""
        notification_data = {
            'type': 'system_status',
            'title': 'System Status Update',
            'message': f"System status changed to: {status_data.get('status', 'unknown')}",
            'priority': 'low',
            'channels': ['in_app'],
            'data': status_data
        }
        
        return await self.send_notification(notification_data)
