/**
 * WebSocket Handler
 * Manages real-time communication between frontend and backend
 */

const axios = require('axios');

class WebSocketManager {
  constructor(io) {
    this.io = io;
    this.clients = new Map();
    this.dataUpdateInterval = null;
    this.BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8001';
    
    console.log('🔌 WebSocket Manager initialized');
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`👤 Client connected: ${socket.id}`);
      
      // Store client info
      this.clients.set(socket.id, {
        socket,
        connectedAt: new Date(),
        subscriptions: new Set()
      });

      // Handle client events
      this.handleClientEvents(socket);

      // Send initial data
      this.sendInitialData(socket);

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`👤 Client disconnected: ${socket.id}`);
        this.clients.delete(socket.id);
      });
    });

    // Start real-time data updates
    this.startRealTimeUpdates();
  }

  handleClientEvents(socket) {
    // Subscribe to specific data streams
    socket.on('subscribe', (dataType) => {
      const client = this.clients.get(socket.id);
      if (client) {
        client.subscriptions.add(dataType);
        console.log(`📡 Client ${socket.id} subscribed to ${dataType}`);
        
        // Send current data for this subscription
        this.sendDataUpdate(socket, dataType);
      }
    });

    // Unsubscribe from data streams
    socket.on('unsubscribe', (dataType) => {
      const client = this.clients.get(socket.id);
      if (client) {
        client.subscriptions.delete(dataType);
        console.log(`📡 Client ${socket.id} unsubscribed from ${dataType}`);
      }
    });

    // Handle AI chat requests
    socket.on('ai-chat', async (data) => {
      try {
        const response = await this.sendAIChatRequest(data.message);
        socket.emit('ai-chat-response', {
          id: data.id,
          response: response,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        socket.emit('ai-chat-error', {
          id: data.id,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Handle system commands
    socket.on('system-command', async (command) => {
      try {
        const result = await this.executeSystemCommand(command);
        socket.emit('system-command-result', result);
      } catch (error) {
        socket.emit('system-command-error', {
          command,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Handle page navigation
    socket.on('page-change', (pageName) => {
      console.log(`📄 Client ${socket.id} navigated to ${pageName}`);
      // Could be used for analytics or page-specific data loading
    });
  }

  async sendInitialData(socket) {
    try {
      // Fetch initial dashboard data
      const dashboardData = await this.fetchDashboardData();
      
      socket.emit('initial-data', {
        ...dashboardData,
        timestamp: new Date().toISOString(),
        clientId: socket.id
      });
    } catch (error) {
      console.error('Error sending initial data:', error);
      socket.emit('error', {
        message: 'Failed to load initial data',
        timestamp: new Date().toISOString()
      });
    }
  }

  startRealTimeUpdates() {
    // Update data every 30 seconds
    this.dataUpdateInterval = setInterval(async () => {
      try {
        await this.broadcastDataUpdates();
      } catch (error) {
        console.error('Error in real-time updates:', error);
      }
    }, 30000);

    console.log('⏰ Real-time data updates started (30s interval)');
  }

  async broadcastDataUpdates() {
    if (this.clients.size === 0) return;

    try {
      const dashboardData = await this.fetchDashboardData();
      
      // Broadcast to all connected clients
      this.io.emit('data-update', {
        ...dashboardData,
        timestamp: new Date().toISOString()
      });

      console.log(`📡 Broadcasted data update to ${this.clients.size} clients`);
    } catch (error) {
      console.error('Error broadcasting data updates:', error);
    }
  }

  async sendDataUpdate(socket, dataType) {
    try {
      let data;
      
      switch (dataType) {
        case 'climate':
          data = await this.fetchClimateData();
          break;
        case 'water-quality':
          data = await this.fetchWaterQualityData();
          break;
        case 'energy':
          data = await this.fetchEnergyData();
          break;
        case 'ai-agents':
          data = await this.fetchAIAgentsData();
          break;
        case 'sensors':
          data = await this.fetchSensorData();
          break;
        default:
          data = await this.fetchDashboardData();
      }

      socket.emit(`${dataType}-update`, {
        data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Error sending ${dataType} update:`, error);
    }
  }

  async fetchDashboardData() {
    try {
      const [climate, waterQuality, energy, aiAgents, sensors] = await Promise.all([
        this.fetchClimateData().catch(() => null),
        this.fetchWaterQualityData().catch(() => null),
        this.fetchEnergyData().catch(() => null),
        this.fetchAIAgentsData().catch(() => null),
        this.fetchSensorData().catch(() => null)
      ]);

      return {
        climate,
        waterQuality,
        energy,
        aiAgents,
        sensors
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  async fetchClimateData() {
    const response = await axios.get(`${this.BACKEND_API_URL}/api/climate/current`);
    return response.data;
  }

  async fetchWaterQualityData() {
    const response = await axios.get(`${this.BACKEND_API_URL}/api/water-quality`);
    return response.data;
  }

  async fetchEnergyData() {
    const response = await axios.get(`${this.BACKEND_API_URL}/api/energy/grid`);
    return response.data;
  }

  async fetchAIAgentsData() {
    const response = await axios.get(`${this.BACKEND_API_URL}/api/ai/agents`);
    return response.data;
  }

  async fetchSensorData() {
    const response = await axios.get(`${this.BACKEND_API_URL}/api/sensors/network`);
    return response.data;
  }

  async sendAIChatRequest(message) {
    const response = await axios.post(`${this.BACKEND_API_URL}/api/ai/chat`, { message });
    return response.data;
  }

  async executeSystemCommand(command) {
    // Handle system commands like refresh, optimize, etc.
    switch (command.type) {
      case 'refresh':
        return await this.fetchDashboardData();
      case 'optimize':
        // Trigger optimization process
        return { status: 'optimization-started', timestamp: new Date().toISOString() };
      default:
        throw new Error(`Unknown command: ${command.type}`);
    }
  }

  getConnectedClients() {
    return Array.from(this.clients.values()).map(client => ({
      id: client.socket.id,
      connectedAt: client.connectedAt,
      subscriptions: Array.from(client.subscriptions)
    }));
  }

  stop() {
    if (this.dataUpdateInterval) {
      clearInterval(this.dataUpdateInterval);
      this.dataUpdateInterval = null;
    }
    console.log('🛑 WebSocket Manager stopped');
  }
}

let wsManager;

function setupWebSocket(io) {
  wsManager = new WebSocketManager(io);
  wsManager.setupEventHandlers();
  return wsManager;
}

function getWebSocketManager() {
  return wsManager;
}

module.exports = {
  setupWebSocket,
  getWebSocketManager,
  WebSocketManager
};
