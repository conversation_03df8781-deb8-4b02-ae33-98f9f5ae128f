# Water Management Frontend Environment Configuration

# Server Configuration
PORT=3000
NODE_ENV=development

# Backend API Configuration
BACKEND_API_URL=http://localhost:8001

# Security Configuration
SESSION_SECRET=your-session-secret-here
JWT_SECRET=your-jwt-secret-here

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:8001

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/frontend.log

# WebSocket Configuration
WEBSOCKET_ENABLED=true
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=300

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Compression
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# Static Files
STATIC_CACHE_MAX_AGE=86400

# Development Configuration
HOT_RELOAD=true
WEBPACK_DEV_SERVER=false

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=false

# External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
WEATHER_API_KEY=your-weather-api-key

# Feature Flags
FEATURE_AI_CHAT=true
FEATURE_REAL_TIME_UPDATES=true
FEATURE_ADVANCED_CHARTS=true
FEATURE_3D_VISUALIZATION=true
FEATURE_BLOCKCHAIN_DASHBOARD=true
