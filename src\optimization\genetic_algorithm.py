"""Genetic Algorithm Optimization for Water Management Systems."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
import numpy as np
import random
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import copy

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class SelectionMethod(Enum):
    """Selection methods for genetic algorithm."""
    TOURNAMENT = "tournament"
    ROULETTE_WHEEL = "roulette_wheel"
    RANK_BASED = "rank_based"
    ELITIST = "elitist"


class CrossoverMethod(Enum):
    """Crossover methods for genetic algorithm."""
    SINGLE_POINT = "single_point"
    TWO_POINT = "two_point"
    UNIFORM = "uniform"
    ARITHMETIC = "arithmetic"
    BLEND = "blend"


class MutationMethod(Enum):
    """Mutation methods for genetic algorithm."""
    GAUSSIAN = "gaussian"
    UNIFORM = "uniform"
    POLYNOMIAL = "polynomial"
    ADAPTIVE = "adaptive"


@dataclass
class Individual:
    """Individual in genetic algorithm population."""
    genes: List[float]
    fitness: float = 0.0
    age: int = 0
    generation: int = 0
    parent_ids: List[str] = field(default_factory=list)
    individual_id: str = field(default_factory=lambda: f"ind_{random.randint(1000, 9999)}")


@dataclass
class GeneticAlgorithmConfig:
    """Configuration for genetic algorithm."""
    population_size: int = 100
    num_generations: int = 500
    crossover_rate: float = 0.8
    mutation_rate: float = 0.1
    elitism_rate: float = 0.1
    selection_method: SelectionMethod = SelectionMethod.TOURNAMENT
    crossover_method: CrossoverMethod = CrossoverMethod.UNIFORM
    mutation_method: MutationMethod = MutationMethod.GAUSSIAN
    tournament_size: int = 3
    convergence_threshold: float = 1e-6
    max_stagnation: int = 50


@dataclass
class OptimizationProblem:
    """Water management optimization problem definition."""
    problem_id: str
    variables: List[Dict[str, Any]]  # Variable definitions with bounds
    objectives: List[str]  # Objective function names
    constraints: List[Dict[str, Any]]  # Constraint definitions
    problem_type: str  # 'minimization' or 'maximization'


class WaterManagementGA:
    """Genetic Algorithm for Water Management Optimization."""
    
    def __init__(self, config: GeneticAlgorithmConfig):
        self.config = config
        self.population: List[Individual] = []
        self.best_individual: Optional[Individual] = None
        self.generation_history: List[Dict[str, Any]] = []
        self.current_generation = 0
        self.stagnation_counter = 0
        
        # Fitness function registry
        self.fitness_functions = {
            'water_quality_optimization': self._water_quality_fitness,
            'energy_efficiency': self._energy_efficiency_fitness,
            'cost_minimization': self._cost_minimization_fitness,
            'treatment_optimization': self._treatment_optimization_fitness,
            'multi_objective': self._multi_objective_fitness
        }
    
    @log_async_function_call
    async def optimize(self, problem: OptimizationProblem, 
                      fitness_function: str = 'multi_objective') -> Dict[str, Any]:
        """Run genetic algorithm optimization."""
        try:
            optimization_start = datetime.now()
            
            # Initialize population
            await self._initialize_population(problem)
            
            # Evaluate initial population
            await self._evaluate_population(problem, fitness_function)
            
            # Evolution loop
            for generation in range(self.config.num_generations):
                self.current_generation = generation
                
                # Create new generation
                new_population = await self._create_new_generation(problem)
                
                # Evaluate new population
                self.population = new_population
                await self._evaluate_population(problem, fitness_function)
                
                # Update best individual
                await self._update_best_individual()
                
                # Record generation statistics
                generation_stats = await self._record_generation_stats()
                self.generation_history.append(generation_stats)
                
                # Check convergence
                if await self._check_convergence():
                    logger.info(f"Convergence achieved at generation {generation}")
                    break
                
                # Log progress
                if generation % 50 == 0:
                    logger.info(f"Generation {generation}: Best fitness = {self.best_individual.fitness:.6f}")
            
            optimization_end = datetime.now()
            optimization_time = (optimization_end - optimization_start).total_seconds()
            
            # Generate optimization results
            results = await self._generate_optimization_results(
                problem, fitness_function, optimization_time
            )
            
            return {
                'status': 'success',
                'optimization_results': results,
                'best_solution': {
                    'genes': self.best_individual.genes,
                    'fitness': self.best_individual.fitness,
                    'generation_found': self.best_individual.generation
                },
                'convergence_info': {
                    'total_generations': self.current_generation + 1,
                    'converged': self.stagnation_counter >= self.config.max_stagnation,
                    'optimization_time': optimization_time
                }
            }
            
        except Exception as e:
            logger.error(f"Genetic algorithm optimization failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _initialize_population(self, problem: OptimizationProblem):
        """Initialize random population."""
        self.population = []
        
        for i in range(self.config.population_size):
            genes = []
            
            for variable in problem.variables:
                min_val = variable['min']
                max_val = variable['max']
                var_type = variable.get('type', 'continuous')
                
                if var_type == 'continuous':
                    gene = random.uniform(min_val, max_val)
                elif var_type == 'integer':
                    gene = random.randint(int(min_val), int(max_val))
                else:
                    gene = random.uniform(min_val, max_val)
                
                genes.append(gene)
            
            individual = Individual(
                genes=genes,
                generation=0,
                individual_id=f"gen0_ind{i}"
            )
            
            self.population.append(individual)
    
    async def _evaluate_population(self, problem: OptimizationProblem, fitness_function: str):
        """Evaluate fitness for entire population."""
        fitness_func = self.fitness_functions.get(fitness_function, self._multi_objective_fitness)
        
        for individual in self.population:
            individual.fitness = await fitness_func(individual.genes, problem)
    
    async def _water_quality_fitness(self, genes: List[float], problem: OptimizationProblem) -> float:
        """Fitness function for water quality optimization."""
        # Simulate water quality parameters
        ph = genes[0] if len(genes) > 0 else 7.0
        turbidity = genes[1] if len(genes) > 1 else 2.0
        chlorine = genes[2] if len(genes) > 2 else 1.0
        
        # Quality score based on target ranges
        ph_score = 1.0 - abs(ph - 7.2) / 2.0  # Target pH 7.2
        turbidity_score = max(0, 1.0 - turbidity / 10.0)  # Lower turbidity is better
        chlorine_score = 1.0 - abs(chlorine - 1.5) / 2.0  # Target chlorine 1.5 mg/L
        
        # Combined quality score
        quality_score = (ph_score + turbidity_score + chlorine_score) / 3.0
        
        return max(0.0, quality_score)
    
    async def _energy_efficiency_fitness(self, genes: List[float], problem: OptimizationProblem) -> float:
        """Fitness function for energy efficiency optimization."""
        # Simulate energy parameters
        pump_speed = genes[0] if len(genes) > 0 else 0.8
        motor_efficiency = genes[1] if len(genes) > 1 else 0.9
        system_load = genes[2] if len(genes) > 2 else 0.7
        
        # Energy efficiency calculation
        base_efficiency = motor_efficiency * (1.0 - abs(pump_speed - 0.8) * 0.2)
        load_penalty = abs(system_load - 0.75) * 0.1  # Optimal load around 75%
        
        efficiency = base_efficiency - load_penalty
        
        return max(0.0, min(1.0, efficiency))
    
    async def _cost_minimization_fitness(self, genes: List[float], problem: OptimizationProblem) -> float:
        """Fitness function for cost minimization."""
        # Simulate cost components
        energy_cost = genes[0] * 100 if len(genes) > 0 else 80  # Energy cost
        chemical_cost = genes[1] * 50 if len(genes) > 1 else 30  # Chemical cost
        maintenance_cost = genes[2] * 20 if len(genes) > 2 else 15  # Maintenance cost
        
        total_cost = energy_cost + chemical_cost + maintenance_cost
        
        # Convert to fitness (lower cost = higher fitness)
        max_cost = 200  # Maximum expected cost
        fitness = (max_cost - total_cost) / max_cost
        
        return max(0.0, fitness)
    
    async def _treatment_optimization_fitness(self, genes: List[float], problem: OptimizationProblem) -> float:
        """Fitness function for treatment process optimization."""
        # Simulate treatment parameters
        coagulant_dose = genes[0] if len(genes) > 0 else 25.0
        ph_adjustment = genes[1] if len(genes) > 1 else 0.0
        filtration_rate = genes[2] if len(genes) > 2 else 10.0
        
        # Treatment efficiency calculation
        coagulant_efficiency = 1.0 - abs(coagulant_dose - 30.0) / 30.0  # Optimal dose 30 mg/L
        ph_efficiency = 1.0 - abs(ph_adjustment) / 2.0  # Minimal pH adjustment preferred
        filtration_efficiency = min(1.0, filtration_rate / 15.0)  # Higher rate up to limit
        
        # Combined treatment efficiency
        treatment_efficiency = (coagulant_efficiency + ph_efficiency + filtration_efficiency) / 3.0
        
        return max(0.0, treatment_efficiency)
    
    async def _multi_objective_fitness(self, genes: List[float], problem: OptimizationProblem) -> float:
        """Multi-objective fitness function."""
        # Combine multiple objectives with weights
        quality_fitness = await self._water_quality_fitness(genes, problem)
        energy_fitness = await self._energy_efficiency_fitness(genes, problem)
        cost_fitness = await self._cost_minimization_fitness(genes, problem)
        treatment_fitness = await self._treatment_optimization_fitness(genes, problem)
        
        # Weighted combination
        weights = [0.3, 0.25, 0.25, 0.2]  # Quality, Energy, Cost, Treatment
        combined_fitness = (quality_fitness * weights[0] + 
                          energy_fitness * weights[1] + 
                          cost_fitness * weights[2] + 
                          treatment_fitness * weights[3])
        
        return combined_fitness
    
    async def _create_new_generation(self, problem: OptimizationProblem) -> List[Individual]:
        """Create new generation through selection, crossover, and mutation."""
        new_population = []
        
        # Elitism - keep best individuals
        elite_count = int(self.config.population_size * self.config.elitism_rate)
        sorted_population = sorted(self.population, key=lambda x: x.fitness, reverse=True)
        elites = sorted_population[:elite_count]
        
        for elite in elites:
            new_individual = copy.deepcopy(elite)
            new_individual.generation = self.current_generation + 1
            new_individual.age += 1
            new_population.append(new_individual)
        
        # Generate offspring
        while len(new_population) < self.config.population_size:
            # Selection
            parent1 = await self._select_individual()
            parent2 = await self._select_individual()
            
            # Crossover
            if random.random() < self.config.crossover_rate:
                child1, child2 = await self._crossover(parent1, parent2)
            else:
                child1, child2 = copy.deepcopy(parent1), copy.deepcopy(parent2)
            
            # Mutation
            if random.random() < self.config.mutation_rate:
                await self._mutate(child1, problem)
            if random.random() < self.config.mutation_rate:
                await self._mutate(child2, problem)
            
            # Update generation info
            child1.generation = self.current_generation + 1
            child2.generation = self.current_generation + 1
            child1.parent_ids = [parent1.individual_id, parent2.individual_id]
            child2.parent_ids = [parent1.individual_id, parent2.individual_id]
            
            new_population.extend([child1, child2])
        
        # Trim to exact population size
        return new_population[:self.config.population_size]
    
    async def _select_individual(self) -> Individual:
        """Select individual based on selection method."""
        if self.config.selection_method == SelectionMethod.TOURNAMENT:
            return await self._tournament_selection()
        elif self.config.selection_method == SelectionMethod.ROULETTE_WHEEL:
            return await self._roulette_wheel_selection()
        elif self.config.selection_method == SelectionMethod.RANK_BASED:
            return await self._rank_based_selection()
        else:
            return await self._tournament_selection()  # Default
    
    async def _tournament_selection(self) -> Individual:
        """Tournament selection."""
        tournament = random.sample(self.population, self.config.tournament_size)
        return max(tournament, key=lambda x: x.fitness)
    
    async def _roulette_wheel_selection(self) -> Individual:
        """Roulette wheel selection."""
        total_fitness = sum(ind.fitness for ind in self.population)
        if total_fitness == 0:
            return random.choice(self.population)
        
        pick = random.uniform(0, total_fitness)
        current = 0
        
        for individual in self.population:
            current += individual.fitness
            if current >= pick:
                return individual
        
        return self.population[-1]  # Fallback
    
    async def _rank_based_selection(self) -> Individual:
        """Rank-based selection."""
        sorted_population = sorted(self.population, key=lambda x: x.fitness)
        ranks = list(range(1, len(sorted_population) + 1))
        total_rank = sum(ranks)
        
        pick = random.uniform(0, total_rank)
        current = 0
        
        for i, individual in enumerate(sorted_population):
            current += ranks[i]
            if current >= pick:
                return individual
        
        return sorted_population[-1]  # Fallback
    
    async def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Perform crossover between two parents."""
        if self.config.crossover_method == CrossoverMethod.SINGLE_POINT:
            return await self._single_point_crossover(parent1, parent2)
        elif self.config.crossover_method == CrossoverMethod.TWO_POINT:
            return await self._two_point_crossover(parent1, parent2)
        elif self.config.crossover_method == CrossoverMethod.UNIFORM:
            return await self._uniform_crossover(parent1, parent2)
        elif self.config.crossover_method == CrossoverMethod.ARITHMETIC:
            return await self._arithmetic_crossover(parent1, parent2)
        else:
            return await self._uniform_crossover(parent1, parent2)  # Default
    
    async def _uniform_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Uniform crossover."""
        child1_genes = []
        child2_genes = []
        
        for i in range(len(parent1.genes)):
            if random.random() < 0.5:
                child1_genes.append(parent1.genes[i])
                child2_genes.append(parent2.genes[i])
            else:
                child1_genes.append(parent2.genes[i])
                child2_genes.append(parent1.genes[i])
        
        child1 = Individual(genes=child1_genes)
        child2 = Individual(genes=child2_genes)
        
        return child1, child2
    
    async def _single_point_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Single-point crossover."""
        crossover_point = random.randint(1, len(parent1.genes) - 1)
        
        child1_genes = parent1.genes[:crossover_point] + parent2.genes[crossover_point:]
        child2_genes = parent2.genes[:crossover_point] + parent1.genes[crossover_point:]
        
        child1 = Individual(genes=child1_genes)
        child2 = Individual(genes=child2_genes)
        
        return child1, child2
    
    async def _two_point_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Two-point crossover."""
        point1 = random.randint(1, len(parent1.genes) - 2)
        point2 = random.randint(point1 + 1, len(parent1.genes) - 1)
        
        child1_genes = (parent1.genes[:point1] + 
                       parent2.genes[point1:point2] + 
                       parent1.genes[point2:])
        child2_genes = (parent2.genes[:point1] + 
                       parent1.genes[point1:point2] + 
                       parent2.genes[point2:])
        
        child1 = Individual(genes=child1_genes)
        child2 = Individual(genes=child2_genes)
        
        return child1, child2
    
    async def _arithmetic_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Arithmetic crossover."""
        alpha = random.random()
        
        child1_genes = [alpha * p1 + (1 - alpha) * p2 for p1, p2 in zip(parent1.genes, parent2.genes)]
        child2_genes = [(1 - alpha) * p1 + alpha * p2 for p1, p2 in zip(parent1.genes, parent2.genes)]
        
        child1 = Individual(genes=child1_genes)
        child2 = Individual(genes=child2_genes)
        
        return child1, child2
    
    async def _mutate(self, individual: Individual, problem: OptimizationProblem):
        """Mutate individual."""
        if self.config.mutation_method == MutationMethod.GAUSSIAN:
            await self._gaussian_mutation(individual, problem)
        elif self.config.mutation_method == MutationMethod.UNIFORM:
            await self._uniform_mutation(individual, problem)
        elif self.config.mutation_method == MutationMethod.POLYNOMIAL:
            await self._polynomial_mutation(individual, problem)
        else:
            await self._gaussian_mutation(individual, problem)  # Default
    
    async def _gaussian_mutation(self, individual: Individual, problem: OptimizationProblem):
        """Gaussian mutation."""
        for i in range(len(individual.genes)):
            if random.random() < 0.1:  # Gene mutation probability
                variable = problem.variables[i]
                sigma = (variable['max'] - variable['min']) * 0.1  # 10% of range
                mutation = random.gauss(0, sigma)
                
                individual.genes[i] += mutation
                
                # Ensure bounds
                individual.genes[i] = max(variable['min'], 
                                        min(variable['max'], individual.genes[i]))
    
    async def _uniform_mutation(self, individual: Individual, problem: OptimizationProblem):
        """Uniform mutation."""
        for i in range(len(individual.genes)):
            if random.random() < 0.1:  # Gene mutation probability
                variable = problem.variables[i]
                individual.genes[i] = random.uniform(variable['min'], variable['max'])
    
    async def _polynomial_mutation(self, individual: Individual, problem: OptimizationProblem):
        """Polynomial mutation."""
        eta = 20  # Distribution index
        
        for i in range(len(individual.genes)):
            if random.random() < 0.1:  # Gene mutation probability
                variable = problem.variables[i]
                y = individual.genes[i]
                yl = variable['min']
                yu = variable['max']
                
                delta1 = (y - yl) / (yu - yl)
                delta2 = (yu - y) / (yu - yl)
                
                rnd = random.random()
                mut_pow = 1.0 / (eta + 1.0)
                
                if rnd <= 0.5:
                    xy = 1.0 - delta1
                    val = 2.0 * rnd + (1.0 - 2.0 * rnd) * (xy ** (eta + 1.0))
                    deltaq = val ** mut_pow - 1.0
                else:
                    xy = 1.0 - delta2
                    val = 2.0 * (1.0 - rnd) + 2.0 * (rnd - 0.5) * (xy ** (eta + 1.0))
                    deltaq = 1.0 - val ** mut_pow
                
                y = y + deltaq * (yu - yl)
                individual.genes[i] = max(yl, min(yu, y))
    
    async def _update_best_individual(self):
        """Update best individual found so far."""
        current_best = max(self.population, key=lambda x: x.fitness)
        
        if self.best_individual is None or current_best.fitness > self.best_individual.fitness:
            self.best_individual = copy.deepcopy(current_best)
            self.stagnation_counter = 0
        else:
            self.stagnation_counter += 1
    
    async def _record_generation_stats(self) -> Dict[str, Any]:
        """Record statistics for current generation."""
        fitnesses = [ind.fitness for ind in self.population]
        
        return {
            'generation': self.current_generation,
            'best_fitness': max(fitnesses),
            'average_fitness': np.mean(fitnesses),
            'worst_fitness': min(fitnesses),
            'fitness_std': np.std(fitnesses),
            'diversity': await self._calculate_diversity(),
            'timestamp': datetime.now().isoformat()
        }
    
    async def _calculate_diversity(self) -> float:
        """Calculate population diversity."""
        if len(self.population) < 2:
            return 0.0
        
        total_distance = 0.0
        comparisons = 0
        
        for i in range(len(self.population)):
            for j in range(i + 1, len(self.population)):
                distance = np.linalg.norm(
                    np.array(self.population[i].genes) - np.array(self.population[j].genes)
                )
                total_distance += distance
                comparisons += 1
        
        return total_distance / comparisons if comparisons > 0 else 0.0
    
    async def _check_convergence(self) -> bool:
        """Check if algorithm has converged."""
        if self.stagnation_counter >= self.config.max_stagnation:
            return True
        
        if len(self.generation_history) >= 10:
            recent_best = [gen['best_fitness'] for gen in self.generation_history[-10:]]
            if max(recent_best) - min(recent_best) < self.config.convergence_threshold:
                return True
        
        return False
    
    async def _generate_optimization_results(self, problem: OptimizationProblem,
                                           fitness_function: str,
                                           optimization_time: float) -> Dict[str, Any]:
        """Generate comprehensive optimization results."""
        return {
            'problem_id': problem.problem_id,
            'optimization_method': 'genetic_algorithm',
            'fitness_function': fitness_function,
            'best_solution': {
                'variables': dict(zip([v['name'] for v in problem.variables], self.best_individual.genes)),
                'fitness_value': self.best_individual.fitness,
                'generation_found': self.best_individual.generation
            },
            'algorithm_performance': {
                'total_generations': self.current_generation + 1,
                'total_evaluations': (self.current_generation + 1) * self.config.population_size,
                'optimization_time_seconds': optimization_time,
                'convergence_achieved': self.stagnation_counter >= self.config.max_stagnation,
                'final_diversity': await self._calculate_diversity()
            },
            'evolution_statistics': {
                'initial_best_fitness': self.generation_history[0]['best_fitness'] if self.generation_history else 0,
                'final_best_fitness': self.best_individual.fitness,
                'improvement': self.best_individual.fitness - (self.generation_history[0]['best_fitness'] if self.generation_history else 0),
                'average_generation_improvement': (self.best_individual.fitness - (self.generation_history[0]['best_fitness'] if self.generation_history else 0)) / max(1, self.current_generation)
            },
            'configuration_used': {
                'population_size': self.config.population_size,
                'crossover_rate': self.config.crossover_rate,
                'mutation_rate': self.config.mutation_rate,
                'selection_method': self.config.selection_method.value,
                'crossover_method': self.config.crossover_method.value,
                'mutation_method': self.config.mutation_method.value
            }
        }


# Convenience functions
async def optimize_water_treatment(variables: List[Dict[str, Any]], 
                                 objective: str = 'multi_objective') -> Dict[str, Any]:
    """Optimize water treatment process using genetic algorithm."""
    # Create optimization problem
    problem = OptimizationProblem(
        problem_id="water_treatment_optimization",
        variables=variables,
        objectives=[objective],
        constraints=[],
        problem_type='maximization'
    )
    
    # Configure genetic algorithm
    config = GeneticAlgorithmConfig(
        population_size=50,
        num_generations=200,
        crossover_rate=0.8,
        mutation_rate=0.1
    )
    
    # Run optimization
    ga = WaterManagementGA(config)
    return await ga.optimize(problem, objective)


async def optimize_energy_efficiency(pump_speed_range: Tuple[float, float] = (0.3, 1.0),
                                   motor_efficiency_range: Tuple[float, float] = (0.7, 0.95),
                                   load_range: Tuple[float, float] = (0.5, 1.0)) -> Dict[str, Any]:
    """Optimize energy efficiency using genetic algorithm."""
    variables = [
        {'name': 'pump_speed', 'min': pump_speed_range[0], 'max': pump_speed_range[1], 'type': 'continuous'},
        {'name': 'motor_efficiency', 'min': motor_efficiency_range[0], 'max': motor_efficiency_range[1], 'type': 'continuous'},
        {'name': 'system_load', 'min': load_range[0], 'max': load_range[1], 'type': 'continuous'}
    ]
    
    return await optimize_water_treatment(variables, 'energy_efficiency')
