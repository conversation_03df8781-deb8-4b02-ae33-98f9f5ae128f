"""
Unified API for Marine Conservation and Water Management Platform
"""

from fastapi import <PERSON>AP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pydantic import BaseModel

from ..integrated_platform.unified_environmental_platform import UnifiedEnvironmentalPlatform

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models for API
class AreaRequest(BaseModel):
    min_lon: float
    min_lat: float
    max_lon: float
    max_lat: float

class MonitoringAreaRequest(BaseModel):
    area_id: str
    name: str
    min_lon: float
    min_lat: float
    max_lon: float
    max_lat: float

class OperationRequest(BaseModel):
    area: AreaRequest
    operation_type: str = "comprehensive_analysis"

# Initialize FastAPI app
app = FastAPI(
    title="Unified Environmental Platform API",
    description="API for Marine Conservation and Water Management Integration",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize unified platform
unified_platform = UnifiedEnvironmentalPlatform()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove dead connections
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.on_event("startup")
async def startup_event():
    """Initialize the unified platform on startup"""
    logger.info("🚀 Starting Unified Environmental Platform API")
    success = await unified_platform.initialize()
    if success:
        logger.info("✅ Unified platform initialized successfully")
        # Start real-time data broadcasting
        asyncio.create_task(broadcast_real_time_data())
    else:
        logger.error("❌ Failed to initialize unified platform")

async def broadcast_real_time_data():
    """Broadcast real-time data to all connected WebSocket clients"""
    while True:
        try:
            if manager.active_connections:
                # Get latest dashboard data
                dashboard_data = await unified_platform.get_unified_dashboard_data()
                
                # Broadcast to all connected clients
                message = json.dumps({
                    "type": "dashboard_update",
                    "data": dashboard_data,
                    "timestamp": datetime.now().isoformat()
                })
                
                await manager.broadcast(message)
            
            # Wait 30 seconds before next broadcast
            await asyncio.sleep(30)
            
        except Exception as e:
            logger.error(f"❌ Error broadcasting real-time data: {e}")
            await asyncio.sleep(60)  # Wait longer on error

# API Routes

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Unified Environmental Platform API",
        "version": "1.0.0",
        "status": "operational",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/status")
async def get_system_status():
    """Get overall system status"""
    try:
        status = await unified_platform.get_system_status()
        return {"success": True, "data": status}
    except Exception as e:
        logger.error(f"❌ Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/dashboard")
async def get_dashboard_data(
    min_lon: Optional[float] = 119.0,
    min_lat: Optional[float] = 23.0,
    max_lon: Optional[float] = 121.0,
    max_lat: Optional[float] = 25.0
):
    """Get unified dashboard data"""
    try:
        area_bbox = (min_lon, min_lat, max_lon, max_lat)
        dashboard_data = await unified_platform.get_unified_dashboard_data(area_bbox)
        return {"success": True, "data": dashboard_data}
    except Exception as e:
        logger.error(f"❌ Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/operation")
async def run_operation(request: OperationRequest):
    """Run a unified operation"""
    try:
        area_bbox = (request.area.min_lon, request.area.min_lat, request.area.max_lon, request.area.max_lat)
        result = await unified_platform.run_unified_operation(area_bbox, request.operation_type)
        
        return {
            "success": True,
            "data": {
                "operation_id": result.operation_id,
                "duration_seconds": result.duration_seconds,
                "success": result.success,
                "marine_results": result.marine_results,
                "water_results": result.water_results,
                "integrated_analysis": result.integrated_analysis
            }
        }
    except Exception as e:
        logger.error(f"❌ Error running operation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/monitoring/area")
async def add_monitoring_area(request: MonitoringAreaRequest):
    """Add a new monitoring area"""
    try:
        area_bbox = (request.min_lon, request.min_lat, request.max_lon, request.max_lat)
        success = await unified_platform.add_monitoring_area(request.area_id, area_bbox, request.name)
        
        if success:
            return {"success": True, "message": f"Monitoring area '{request.name}' added successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to add monitoring area")
            
    except Exception as e:
        logger.error(f"❌ Error adding monitoring area: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/history")
async def get_operation_history(limit: int = 10):
    """Get operation history"""
    try:
        history = unified_platform.get_operation_history(limit)
        return {"success": True, "data": history}
    except Exception as e:
        logger.error(f"❌ Error getting operation history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time data"""
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            
            # Handle different message types
            try:
                message = json.loads(data)
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong", "timestamp": datetime.now().isoformat()}))
                elif message.get("type") == "request_update":
                    # Send immediate dashboard update
                    dashboard_data = await unified_platform.get_unified_dashboard_data()
                    response = json.dumps({
                        "type": "dashboard_update",
                        "data": dashboard_data,
                        "timestamp": datetime.now().isoformat()
                    })
                    await websocket.send_text(response)
            except json.JSONDecodeError:
                # Handle non-JSON messages
                await websocket.send_text(json.dumps({"type": "error", "message": "Invalid JSON"}))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "platform_initialized": unified_platform.is_initialized
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
