import React, { useState } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Alert,
  Divider,
  Paper,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Insights as InsightsIcon,
  Sync as SyncIcon,
  EmojiObjects as IdeaIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, Sankey, ComposedChart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

const IntegratedAnalytics = ({ data, marineData, waterData, onRefresh }) => {
  const [selectedCorrelation, setSelectedCorrelation] = useState(null);

  // Sample data for integrated analytics
  const radarData = [
    { subject: 'Marine Health', A: data?.environmental_score * 100 || 88, fullMark: 100 },
    { subject: 'Water Quality', A: waterData?.treatment_efficiency * 100 || 92, fullMark: 100 },
    { subject: 'Energy Efficiency', A: waterData?.energy_efficiency * 100 || 87, fullMark: 100 },
    { subject: 'Sustainability', A: waterData?.sustainability_score * 100 || 78, fullMark: 100 },
    { subject: 'Risk Management', A: 85, fullMark: 100 },
    { subject: 'Integration', A: data?.synergy_score * 100 || 75, fullMark: 100 },
  ];

  const correlationData = [
    { factor: 'Debris Count', marine: marineData?.debris_count || 10, water: 92, correlation: 0.65 },
    { factor: 'Vessel Traffic', marine: marineData?.vessel_count || 15, water: 89, correlation: 0.42 },
    { factor: 'Energy Usage', marine: 85, water: waterData?.energy_efficiency * 100 || 87, correlation: 0.78 },
    { factor: 'Weather Impact', marine: 88, water: 91, correlation: 0.55 },
  ];

  const getCorrelationColor = (value) => {
    if (value >= 0.7) return 'success';
    if (value >= 0.5) return 'warning';
    return 'error';
  };

  const getOptimizationPotential = (category) => {
    const potentials = {
      'energy_sharing': data?.resource_optimization?.energy_sharing_potential * 100 || 15,
      'data_integration': data?.resource_optimization?.data_integration_benefits * 100 || 25,
      'coordinated_response': data?.resource_optimization?.coordinated_response_efficiency * 100 || 30,
    };
    return potentials[category] || 0;
  };

  if (!data) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography variant="h6" color="text.secondary">
          Loading integrated analytics...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          📊 Integrated Analytics Dashboard
        </Typography>
        <Button variant="contained" onClick={onRefresh} startIcon={<AssessmentIcon />}>
          Generate Insights
        </Button>
      </Box>

      {/* Environmental Score Alert */}
      <Alert 
        severity={data.environmental_score >= 0.8 ? 'success' : data.environmental_score >= 0.6 ? 'warning' : 'error'} 
        sx={{ mb: 3 }}
      >
        Environmental Score: {data.environmental_score ? `${(data.environmental_score * 100).toFixed(1)}%` : 'N/A'} - 
        {data.environmental_score >= 0.8 ? ' Excellent integration performance' : 
         data.environmental_score >= 0.6 ? ' Good performance with room for improvement' : 
         ' Requires immediate attention'}
      </Alert>

      {/* Key Integration Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Synergy Score
                  </Typography>
                  <Typography variant="h4">
                    {data.synergy_score ? `${(data.synergy_score * 100).toFixed(0)}%` : '75%'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    System coordination
                  </Typography>
                </Box>
                <SyncIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Resource Efficiency
                  </Typography>
                  <Typography variant="h4">
                    {data.resource_efficiency ? `${(data.resource_efficiency * 100).toFixed(0)}%` : '80%'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Resource utilization
                  </Typography>
                </Box>
                <SpeedIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Data Quality
                  </Typography>
                  <Typography variant="h4">
                    {data.data_integration_quality ? `${(data.data_integration_quality * 100).toFixed(0)}%` : '90%'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Integration quality
                  </Typography>
                </Box>
                <InsightsIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Optimization Potential
                  </Typography>
                  <Typography variant="h4">
                    {getOptimizationPotential('coordinated_response').toFixed(0)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Improvement opportunity
                  </Typography>
                </Box>
                <TrendingUpIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Analytics Charts */}
      <Grid container spacing={3}>
        {/* System Performance Radar */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Integrated System Performance
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar name="Performance" dataKey="A" stroke="#2196f3" fill="#2196f3" fillOpacity={0.3} />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Cross-System Correlations */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Cross-System Correlations
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={correlationData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="factor" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="marine" fill="#2196f3" name="Marine Impact" />
                  <Bar dataKey="water" fill="#4caf50" name="Water Impact" />
                  <Line type="monotone" dataKey="correlation" stroke="#ff9800" name="Correlation" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Correlations List */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Correlations
              </Typography>
              {data.correlations && data.correlations.length > 0 ? (
                <List>
                  {data.correlations.map((correlation, index) => (
                    <React.Fragment key={index}>
                      <ListItem 
                        button
                        onClick={() => setSelectedCorrelation(correlation)}
                        selected={selectedCorrelation === correlation}
                      >
                        <ListItemIcon>
                          <InsightsIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={correlation}
                          secondary="Click for detailed analysis"
                        />
                      </ListItem>
                      {index < data.correlations.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary">
                  No significant correlations detected
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Optimization Opportunities */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Optimization Opportunities
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <TrendingUpIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Energy Sharing"
                    secondary={`${getOptimizationPotential('energy_sharing')}% potential savings`}
                  />
                  <Chip 
                    label={`${getOptimizationPotential('energy_sharing')}%`} 
                    color="success" 
                    size="small" 
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <InsightsIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Data Integration"
                    secondary={`${getOptimizationPotential('data_integration')}% better insights`}
                  />
                  <Chip 
                    label={`${getOptimizationPotential('data_integration')}%`} 
                    color="info" 
                    size="small" 
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <SpeedIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Response Coordination"
                    secondary={`${getOptimizationPotential('coordinated_response')}% faster response`}
                  />
                  <Chip 
                    label={`${getOptimizationPotential('coordinated_response')}%`} 
                    color="warning" 
                    size="small" 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Synergy Opportunities */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Synergy Opportunities
              </Typography>
              <Grid container spacing={2}>
                {data.synergy_opportunities && data.synergy_opportunities.map((opportunity, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                      <IdeaIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                      <Typography variant="body1" gutterBottom>
                        {opportunity}
                      </Typography>
                      <Chip label="High Impact" color="primary" size="small" />
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recommendations */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Integrated Recommendations
              </Typography>
              {data.recommendations && data.recommendations.length > 0 ? (
                <List>
                  {data.recommendations.map((recommendation, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemIcon>
                          <IdeaIcon color="secondary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={recommendation}
                          secondary={`Priority: High | Impact: Significant`}
                        />
                      </ListItem>
                      {index < data.recommendations.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary">
                  No specific recommendations at this time
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Selected Correlation Details */}
      {selectedCorrelation && (
        <Box mt={3}>
          <Alert severity="info">
            <Typography variant="subtitle1" gutterBottom>
              Correlation Analysis: {selectedCorrelation}
            </Typography>
            <Typography variant="body2">
              This correlation indicates a significant relationship between marine conservation and water management systems. 
              Consider implementing coordinated monitoring and response strategies to optimize both systems simultaneously.
            </Typography>
          </Alert>
        </Box>
      )}
    </Box>
  );
};

export default IntegratedAnalytics;
