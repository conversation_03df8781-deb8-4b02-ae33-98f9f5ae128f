#!/usr/bin/env python3
"""
Frontend and Backend Integration Test
Tests both systems and their integration
"""

import asyncio
import sys
import logging
import time
import requests
import json
from pathlib import Path
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FrontendBackendTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_path = Path("frontend")
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now()
        })
    
    def test_backend_health(self):
        """Test backend health endpoint"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            success = response.status_code == 200
            data = response.json() if success else {}
            
            self.log_test_result(
                "Backend Health Check", 
                success, 
                f"Status: {response.status_code}, Platform initialized: {data.get('platform_initialized', False)}"
            )
            return success
        except Exception as e:
            self.log_test_result("Backend Health Check", False, f"Error: {e}")
            return False
    
    def test_backend_api_endpoints(self):
        """Test main backend API endpoints"""
        endpoints = [
            ("/api/status", "System Status"),
            ("/api/dashboard", "Dashboard Data"),
            ("/", "Root Endpoint")
        ]
        
        all_success = True
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=10)
                success = response.status_code == 200
                
                if success and endpoint == "/api/dashboard":
                    data = response.json()
                    success = data.get('success', False) and 'data' in data
                
                self.log_test_result(
                    f"Backend {name}", 
                    success, 
                    f"Status: {response.status_code}"
                )
                all_success = all_success and success
                
            except Exception as e:
                self.log_test_result(f"Backend {name}", False, f"Error: {e}")
                all_success = False
        
        return all_success
    
    def test_frontend_structure(self):
        """Test frontend file structure"""
        required_files = [
            "package.json",
            "public/index.html",
            "src/App.js",
            "src/index.js",
            "src/components/Dashboard.js",
            "src/components/Navbar.js",
            "src/services/apiService.js",
            "src/services/websocketService.js"
        ]
        
        all_exist = True
        for file_path in required_files:
            full_path = self.frontend_path / file_path
            exists = full_path.exists()
            
            self.log_test_result(
                f"Frontend File: {file_path}", 
                exists, 
                "Found" if exists else "Missing"
            )
            all_exist = all_exist and exists
        
        return all_exist
    
    def test_frontend_package_json(self):
        """Test frontend package.json configuration"""
        try:
            package_json_path = self.frontend_path / "package.json"
            if not package_json_path.exists():
                self.log_test_result("Frontend package.json", False, "File not found")
                return False
            
            with open(package_json_path, 'r') as f:
                package_data = json.load(f)
            
            # Check required dependencies
            dependencies = package_data.get('dependencies', {})
            required_deps = ['react', 'react-dom', '@mui/material', 'axios', 'recharts']
            
            missing_deps = [dep for dep in required_deps if dep not in dependencies]
            
            success = len(missing_deps) == 0
            message = f"Dependencies: {len(dependencies)}, Missing: {missing_deps}" if missing_deps else f"All {len(required_deps)} required dependencies found"
            
            self.log_test_result("Frontend Dependencies", success, message)
            return success
            
        except Exception as e:
            self.log_test_result("Frontend package.json", False, f"Error: {e}")
            return False
    
    def test_api_data_structure(self):
        """Test API data structure compatibility with frontend"""
        try:
            response = requests.get(f"{self.backend_url}/api/dashboard", timeout=15)
            if response.status_code != 200:
                self.log_test_result("API Data Structure", False, f"HTTP {response.status_code}")
                return False
            
            data = response.json()
            if not data.get('success'):
                self.log_test_result("API Data Structure", False, "API returned success=false")
                return False
            
            dashboard_data = data.get('data', {})
            
            # Check required data structure for frontend
            required_keys = [
                'timestamp', 'system_status', 'marine_conservation', 
                'water_management', 'integrated_analytics', 'real_time'
            ]
            
            missing_keys = [key for key in required_keys if key not in dashboard_data]
            
            success = len(missing_keys) == 0
            message = f"Data keys: {len(dashboard_data)}, Missing: {missing_keys}" if missing_keys else f"All {len(required_keys)} required keys found"
            
            self.log_test_result("API Data Structure", success, message)
            return success
            
        except Exception as e:
            self.log_test_result("API Data Structure", False, f"Error: {e}")
            return False
    
    def test_websocket_endpoint(self):
        """Test WebSocket endpoint availability"""
        try:
            # Simple test to see if WebSocket endpoint exists
            # We can't easily test WebSocket connection without additional libraries
            # So we'll just check if the endpoint is documented in the API
            response = requests.get(f"{self.backend_url}/docs", timeout=5)
            success = response.status_code == 200
            
            self.log_test_result(
                "WebSocket Endpoint", 
                success, 
                "API docs accessible (WebSocket endpoint should be available)"
            )
            return success
            
        except Exception as e:
            self.log_test_result("WebSocket Endpoint", False, f"Error: {e}")
            return False
    
    def test_cors_configuration(self):
        """Test CORS configuration for frontend-backend communication"""
        try:
            # Test with Origin header to simulate frontend request
            headers = {'Origin': 'http://localhost:3000'}
            response = requests.get(f"{self.backend_url}/api/status", headers=headers, timeout=5)
            
            # Check if CORS headers are present
            cors_headers = [
                'access-control-allow-origin',
                'access-control-allow-methods',
                'access-control-allow-headers'
            ]
            
            has_cors = any(header in response.headers for header in cors_headers)
            success = response.status_code == 200 and has_cors
            
            self.log_test_result(
                "CORS Configuration", 
                success, 
                f"Status: {response.status_code}, CORS headers: {has_cors}"
            )
            return success
            
        except Exception as e:
            self.log_test_result("CORS Configuration", False, f"Error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all frontend and backend tests"""
        logger.info("🧪 FRONTEND AND BACKEND INTEGRATION TESTS")
        logger.info("=" * 60)
        
        # Backend tests
        logger.info("\n🔧 BACKEND TESTS")
        logger.info("-" * 30)
        backend_tests = [
            self.test_backend_health,
            self.test_backend_api_endpoints,
            self.test_api_data_structure,
            self.test_websocket_endpoint,
            self.test_cors_configuration
        ]
        
        backend_results = []
        for test in backend_tests:
            try:
                result = test()
                backend_results.append(result)
            except Exception as e:
                logger.error(f"Test failed with exception: {e}")
                backend_results.append(False)
        
        # Frontend tests
        logger.info("\n🎨 FRONTEND TESTS")
        logger.info("-" * 30)
        frontend_tests = [
            self.test_frontend_structure,
            self.test_frontend_package_json
        ]
        
        frontend_results = []
        for test in frontend_tests:
            try:
                result = test()
                frontend_results.append(result)
            except Exception as e:
                logger.error(f"Test failed with exception: {e}")
                frontend_results.append(False)
        
        # Generate report
        self.generate_test_report(backend_results, frontend_results)
        
        return all(backend_results) and all(frontend_results)
    
    def generate_test_report(self, backend_results, frontend_results):
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        backend_passed = sum(backend_results)
        frontend_passed = sum(frontend_results)
        
        logger.info("\n📊 INTEGRATION TEST REPORT")
        logger.info("=" * 60)
        logger.info(f"Backend Tests: {backend_passed}/{len(backend_results)} passed")
        logger.info(f"Frontend Tests: {frontend_passed}/{len(frontend_results)} passed")
        logger.info(f"Total Tests: {passed_tests}/{total_tests} passed")
        logger.info(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            logger.info("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    logger.info(f"  - {result['test']}: {result['message']}")
        
        logger.info("")
        if failed_tests == 0:
            logger.info("🎉 ALL TESTS PASSED! Frontend and Backend are ready!")
        else:
            logger.info(f"⚠️ {failed_tests} tests failed. Check the issues above.")
        
        # Integration status
        if all(backend_results):
            logger.info("✅ Backend: Fully functional")
        else:
            logger.info("❌ Backend: Issues detected")
        
        if all(frontend_results):
            logger.info("✅ Frontend: Structure ready")
        else:
            logger.info("❌ Frontend: Issues detected")

def main():
    """Main test execution"""
    logger.info("🌊💧 Unified Environmental Platform - Frontend & Backend Integration Test")
    logger.info("=" * 80)
    
    tester = FrontendBackendTester()
    success = tester.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
