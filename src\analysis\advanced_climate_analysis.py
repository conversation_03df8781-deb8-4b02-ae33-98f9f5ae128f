"""
Advanced Climate Analysis Suite.

Comprehensive climate change impact modeling, regional analysis,
risk assessment, and advanced projection systems.
"""

import numpy as np
import pandas as pd
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import scipy.stats as stats
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import xarray as xr

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ClimateScenario(Enum):
    """Climate change scenarios."""
    RCP26 = "rcp26"  # Low emissions
    RCP45 = "rcp45"  # Medium emissions
    RCP60 = "rcp60"  # Medium-high emissions
    RCP85 = "rcp85"  # High emissions


class ClimateRiskLevel(Enum):
    """Climate risk levels."""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class ClimateProjection:
    """Climate projection data structure."""
    scenario: ClimateScenario
    time_horizon: str
    temperature_change: float
    precipitation_change: float
    extreme_events_frequency: float
    confidence_level: float
    regional_variations: Dict[str, float]


class ClimateChangeImpactModeler:
    """Advanced climate change impact modeling."""
    
    def __init__(self):
        self.impact_models = {
            'temperature': self._temperature_impact_model,
            'precipitation': self._precipitation_impact_model,
            'extreme_events': self._extreme_events_impact_model,
            'sea_level': self._sea_level_impact_model,
            'drought': self._drought_impact_model,
            'flooding': self._flooding_impact_model
        }
        
    @log_async_function_call
    async def model_climate_impacts(self, location: Dict[str, float],
                                  scenarios: List[ClimateScenario],
                                  time_horizons: List[str]) -> Dict[str, Any]:
        """Model comprehensive climate change impacts."""
        try:
            logger.info("Starting comprehensive climate impact modeling")
            
            impact_results = {}
            
            for scenario in scenarios:
                scenario_results = {}
                
                for horizon in time_horizons:
                    horizon_results = {}
                    
                    # Model each impact type
                    for impact_type, model_func in self.impact_models.items():
                        impact_result = await model_func(location, scenario, horizon)
                        horizon_results[impact_type] = impact_result
                    
                    # Calculate composite impact score
                    composite_score = await self._calculate_composite_impact(horizon_results)
                    horizon_results['composite_impact'] = composite_score
                    
                    scenario_results[horizon] = horizon_results
                
                impact_results[scenario.value] = scenario_results
            
            # Generate adaptation recommendations
            adaptation_strategies = await self._generate_adaptation_strategies(impact_results)
            
            # Calculate risk assessment
            risk_assessment = await self._assess_climate_risks(impact_results)
            
            return {
                'status': 'success',
                'location': location,
                'impact_projections': impact_results,
                'adaptation_strategies': adaptation_strategies,
                'risk_assessment': risk_assessment,
                'modeling_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Climate impact modeling failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _temperature_impact_model(self, location: Dict[str, float],
                                      scenario: ClimateScenario,
                                      horizon: str) -> Dict[str, float]:
        """Model temperature change impacts."""
        # Base temperature changes by scenario and horizon
        temp_changes = {
            'rcp26': {'2030': 1.2, '2050': 1.8, '2080': 2.1},
            'rcp45': {'2030': 1.5, '2050': 2.4, '2080': 3.2},
            'rcp60': {'2030': 1.7, '2050': 2.8, '2080': 4.1},
            'rcp85': {'2030': 2.1, '2050': 3.8, '2080': 5.4}
        }
        
        base_change = temp_changes[scenario.value].get(horizon, 2.0)
        
        # Regional adjustments
        lat = location.get('lat', 40.0)
        regional_factor = 1.0 + (abs(lat) - 40) * 0.02  # Higher latitudes warm more
        
        temperature_change = base_change * regional_factor
        
        # Calculate impacts
        heat_stress_days = max(0, (temperature_change - 1.0) * 15)
        cooling_demand_increase = temperature_change * 0.08  # 8% per degree
        agricultural_impact = min(0.3, temperature_change * 0.05)  # Max 30% impact
        
        return {
            'temperature_change_celsius': temperature_change,
            'heat_stress_days_increase': heat_stress_days,
            'cooling_demand_increase_percent': cooling_demand_increase * 100,
            'agricultural_productivity_impact': agricultural_impact,
            'confidence': 0.85
        }
    
    async def _precipitation_impact_model(self, location: Dict[str, float],
                                        scenario: ClimateScenario,
                                        horizon: str) -> Dict[str, float]:
        """Model precipitation change impacts."""
        # Precipitation changes vary by region and scenario
        precip_changes = {
            'rcp26': {'2030': 0.05, '2050': 0.08, '2080': 0.12},
            'rcp45': {'2030': 0.08, '2050': 0.15, '2080': 0.22},
            'rcp60': {'2030': 0.12, '2050': 0.20, '2080': 0.28},
            'rcp85': {'2030': 0.15, '2050': 0.28, '2080': 0.35}
        }
        
        base_change = precip_changes[scenario.value].get(horizon, 0.15)
        
        # Regional variations
        lat = location.get('lat', 40.0)
        if lat > 60:  # Arctic regions
            regional_factor = 1.5
        elif 30 <= lat <= 60:  # Temperate regions
            regional_factor = 1.0
        else:  # Tropical/subtropical
            regional_factor = 0.8
        
        precipitation_change = base_change * regional_factor
        
        # Calculate impacts
        water_availability_change = precipitation_change * 0.8  # Not all precip becomes available water
        flood_risk_increase = max(0, precipitation_change * 2.0)  # Exponential relationship
        drought_risk_change = -precipitation_change * 1.5  # Inverse relationship
        
        return {
            'precipitation_change_percent': precipitation_change * 100,
            'water_availability_change_percent': water_availability_change * 100,
            'flood_risk_increase_factor': flood_risk_increase,
            'drought_risk_change_factor': drought_risk_change,
            'confidence': 0.75
        }
    
    async def _extreme_events_impact_model(self, location: Dict[str, float],
                                         scenario: ClimateScenario,
                                         horizon: str) -> Dict[str, float]:
        """Model extreme weather events impacts."""
        # Extreme events frequency multipliers
        extreme_multipliers = {
            'rcp26': {'2030': 1.3, '2050': 1.6, '2080': 1.9},
            'rcp45': {'2030': 1.5, '2050': 2.1, '2080': 2.8},
            'rcp60': {'2030': 1.8, '2050': 2.5, '2080': 3.4},
            'rcp85': {'2030': 2.2, '2050': 3.2, '2080': 4.5}
        }
        
        multiplier = extreme_multipliers[scenario.value].get(horizon, 2.0)
        
        # Calculate specific extreme event impacts
        heatwave_frequency = (multiplier - 1) * 5  # Additional heatwaves per year
        storm_intensity = (multiplier - 1) * 0.3  # 30% intensity increase per unit
        wildfire_risk = (multiplier - 1) * 0.4  # 40% risk increase per unit
        
        return {
            'extreme_events_frequency_multiplier': multiplier,
            'additional_heatwaves_per_year': heatwave_frequency,
            'storm_intensity_increase_percent': storm_intensity * 100,
            'wildfire_risk_increase_percent': wildfire_risk * 100,
            'infrastructure_stress_factor': multiplier * 0.8,
            'confidence': 0.70
        }
    
    async def _sea_level_impact_model(self, location: Dict[str, float],
                                    scenario: ClimateScenario,
                                    horizon: str) -> Dict[str, float]:
        """Model sea level rise impacts."""
        # Sea level rise projections (meters)
        sea_level_rise = {
            'rcp26': {'2030': 0.08, '2050': 0.18, '2080': 0.32},
            'rcp45': {'2030': 0.10, '2050': 0.24, '2080': 0.47},
            'rcp60': {'2030': 0.12, '2050': 0.28, '2080': 0.58},
            'rcp85': {'2030': 0.15, '2050': 0.38, '2080': 0.84}
        }
        
        rise = sea_level_rise[scenario.value].get(horizon, 0.3)
        
        # Calculate coastal impacts (simplified)
        lat = location.get('lat', 40.0)
        coastal_proximity = max(0, 1 - abs(lat - 40) / 50)  # Simplified coastal factor
        
        coastal_flooding_risk = rise * coastal_proximity * 10  # Risk factor
        saltwater_intrusion = rise * coastal_proximity * 5  # Intrusion factor
        
        return {
            'sea_level_rise_meters': rise,
            'coastal_flooding_risk_factor': coastal_flooding_risk,
            'saltwater_intrusion_risk': saltwater_intrusion,
            'coastal_infrastructure_impact': coastal_proximity * rise * 0.2,
            'confidence': 0.80
        }
    
    async def _drought_impact_model(self, location: Dict[str, float],
                                  scenario: ClimateScenario,
                                  horizon: str) -> Dict[str, float]:
        """Model drought impacts."""
        # Get temperature and precipitation impacts
        temp_impact = await self._temperature_impact_model(location, scenario, horizon)
        precip_impact = await self._precipitation_impact_model(location, scenario, horizon)
        
        # Calculate drought severity
        temp_factor = temp_impact['temperature_change_celsius'] * 0.3
        precip_factor = -precip_impact['precipitation_change_percent'] / 100 * 0.5
        
        drought_severity = max(0, temp_factor + precip_factor)
        
        # Calculate impacts
        water_supply_stress = drought_severity * 0.8
        agricultural_losses = drought_severity * 0.6
        ecosystem_stress = drought_severity * 0.7
        
        return {
            'drought_severity_index': drought_severity,
            'water_supply_stress_factor': water_supply_stress,
            'agricultural_losses_percent': agricultural_losses * 100,
            'ecosystem_stress_factor': ecosystem_stress,
            'confidence': 0.75
        }
    
    async def _flooding_impact_model(self, location: Dict[str, float],
                                   scenario: ClimateScenario,
                                   horizon: str) -> Dict[str, float]:
        """Model flooding impacts."""
        # Get precipitation and extreme events impacts
        precip_impact = await self._precipitation_impact_model(location, scenario, horizon)
        extreme_impact = await self._extreme_events_impact_model(location, scenario, horizon)
        
        # Calculate flood risk
        precip_factor = max(0, precip_impact['precipitation_change_percent'] / 100 * 0.4)
        extreme_factor = (extreme_impact['extreme_events_frequency_multiplier'] - 1) * 0.3
        
        flood_risk = precip_factor + extreme_factor
        
        # Calculate impacts
        infrastructure_damage = flood_risk * 0.6
        economic_losses = flood_risk * 0.8
        displacement_risk = flood_risk * 0.4
        
        return {
            'flood_risk_increase_factor': flood_risk,
            'infrastructure_damage_risk': infrastructure_damage,
            'economic_losses_factor': economic_losses,
            'population_displacement_risk': displacement_risk,
            'confidence': 0.70
        }
    
    async def _calculate_composite_impact(self, impact_results: Dict[str, Dict]) -> Dict[str, float]:
        """Calculate composite climate impact score."""
        # Weight different impact types
        weights = {
            'temperature': 0.20,
            'precipitation': 0.20,
            'extreme_events': 0.25,
            'sea_level': 0.10,
            'drought': 0.15,
            'flooding': 0.10
        }
        
        composite_score = 0
        total_weight = 0
        
        for impact_type, weight in weights.items():
            if impact_type in impact_results:
                # Extract a representative impact value
                impact_data = impact_results[impact_type]
                
                if impact_type == 'temperature':
                    impact_value = impact_data.get('temperature_change_celsius', 0) / 5.0  # Normalize to 0-1
                elif impact_type == 'precipitation':
                    impact_value = abs(impact_data.get('precipitation_change_percent', 0)) / 50.0
                elif impact_type == 'extreme_events':
                    impact_value = (impact_data.get('extreme_events_frequency_multiplier', 1) - 1) / 3.0
                elif impact_type == 'sea_level':
                    impact_value = impact_data.get('sea_level_rise_meters', 0) / 1.0
                elif impact_type == 'drought':
                    impact_value = impact_data.get('drought_severity_index', 0)
                elif impact_type == 'flooding':
                    impact_value = impact_data.get('flood_risk_increase_factor', 0)
                else:
                    impact_value = 0
                
                composite_score += weight * min(1.0, impact_value)
                total_weight += weight
        
        normalized_score = composite_score / total_weight if total_weight > 0 else 0
        
        # Determine risk level
        if normalized_score < 0.25:
            risk_level = ClimateRiskLevel.LOW
        elif normalized_score < 0.5:
            risk_level = ClimateRiskLevel.MODERATE
        elif normalized_score < 0.75:
            risk_level = ClimateRiskLevel.HIGH
        else:
            risk_level = ClimateRiskLevel.EXTREME
        
        return {
            'composite_impact_score': normalized_score,
            'risk_level': risk_level.value,
            'impact_category': self._categorize_impact(normalized_score)
        }
    
    def _categorize_impact(self, score: float) -> str:
        """Categorize impact based on score."""
        if score < 0.2:
            return "Minimal Impact"
        elif score < 0.4:
            return "Low Impact"
        elif score < 0.6:
            return "Moderate Impact"
        elif score < 0.8:
            return "High Impact"
        else:
            return "Severe Impact"
    
    async def _generate_adaptation_strategies(self, impact_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate climate adaptation strategies."""
        strategies = []
        
        # Analyze impacts across scenarios
        for scenario, scenario_data in impact_results.items():
            for horizon, horizon_data in scenario_data.items():
                # Temperature adaptation
                if 'temperature' in horizon_data:
                    temp_data = horizon_data['temperature']
                    if temp_data.get('temperature_change_celsius', 0) > 2.0:
                        strategies.append({
                            'strategy': 'Enhanced Cooling Systems',
                            'priority': 'high',
                            'timeframe': horizon,
                            'description': 'Upgrade cooling infrastructure to handle increased temperatures',
                            'estimated_cost': 'high',
                            'effectiveness': 0.8
                        })
                
                # Water management adaptation
                if 'precipitation' in horizon_data:
                    precip_data = horizon_data['precipitation']
                    if abs(precip_data.get('precipitation_change_percent', 0)) > 15:
                        strategies.append({
                            'strategy': 'Adaptive Water Management',
                            'priority': 'high',
                            'timeframe': horizon,
                            'description': 'Implement flexible water storage and distribution systems',
                            'estimated_cost': 'medium',
                            'effectiveness': 0.9
                        })
                
                # Extreme events adaptation
                if 'extreme_events' in horizon_data:
                    extreme_data = horizon_data['extreme_events']
                    if extreme_data.get('extreme_events_frequency_multiplier', 1) > 2.0:
                        strategies.append({
                            'strategy': 'Resilient Infrastructure',
                            'priority': 'critical',
                            'timeframe': horizon,
                            'description': 'Strengthen infrastructure to withstand extreme weather',
                            'estimated_cost': 'very_high',
                            'effectiveness': 0.85
                        })
        
        # Remove duplicates and prioritize
        unique_strategies = []
        seen_strategies = set()
        
        for strategy in strategies:
            strategy_key = (strategy['strategy'], strategy['timeframe'])
            if strategy_key not in seen_strategies:
                unique_strategies.append(strategy)
                seen_strategies.add(strategy_key)
        
        # Sort by priority
        priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
        unique_strategies.sort(key=lambda x: priority_order.get(x['priority'], 4))
        
        return unique_strategies[:10]  # Return top 10 strategies
    
    async def _assess_climate_risks(self, impact_results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall climate risks."""
        risk_scores = []
        
        for scenario, scenario_data in impact_results.items():
            for horizon, horizon_data in scenario_data.items():
                if 'composite_impact' in horizon_data:
                    score = horizon_data['composite_impact']['composite_impact_score']
                    risk_scores.append(score)
        
        if not risk_scores:
            return {'overall_risk': 'unknown', 'confidence': 0.0}
        
        avg_risk = np.mean(risk_scores)
        max_risk = np.max(risk_scores)
        risk_trend = np.polyfit(range(len(risk_scores)), risk_scores, 1)[0]  # Linear trend
        
        # Determine overall risk level
        if avg_risk < 0.3:
            overall_risk = 'low'
        elif avg_risk < 0.6:
            overall_risk = 'moderate'
        elif avg_risk < 0.8:
            overall_risk = 'high'
        else:
            overall_risk = 'extreme'
        
        return {
            'overall_risk': overall_risk,
            'average_risk_score': avg_risk,
            'maximum_risk_score': max_risk,
            'risk_trend': 'increasing' if risk_trend > 0.01 else 'stable' if risk_trend > -0.01 else 'decreasing',
            'confidence': 0.8,
            'key_concerns': self._identify_key_concerns(impact_results)
        }
    
    def _identify_key_concerns(self, impact_results: Dict[str, Any]) -> List[str]:
        """Identify key climate concerns."""
        concerns = []
        
        # Analyze patterns across scenarios
        high_temp_scenarios = 0
        high_precip_scenarios = 0
        high_extreme_scenarios = 0
        
        for scenario, scenario_data in impact_results.items():
            for horizon, horizon_data in scenario_data.items():
                if 'temperature' in horizon_data:
                    if horizon_data['temperature'].get('temperature_change_celsius', 0) > 3.0:
                        high_temp_scenarios += 1
                
                if 'precipitation' in horizon_data:
                    if abs(horizon_data['precipitation'].get('precipitation_change_percent', 0)) > 20:
                        high_precip_scenarios += 1
                
                if 'extreme_events' in horizon_data:
                    if horizon_data['extreme_events'].get('extreme_events_frequency_multiplier', 1) > 2.5:
                        high_extreme_scenarios += 1
        
        total_scenarios = len(impact_results) * 3  # Assuming 3 time horizons
        
        if high_temp_scenarios / total_scenarios > 0.5:
            concerns.append("Significant temperature increases across multiple scenarios")
        
        if high_precip_scenarios / total_scenarios > 0.5:
            concerns.append("Major precipitation pattern changes")
        
        if high_extreme_scenarios / total_scenarios > 0.5:
            concerns.append("Substantial increase in extreme weather events")
        
        return concerns


class RegionalClimateAnalyzer:
    """Regional climate analysis and comparison."""
    
    def __init__(self):
        self.regional_factors = {
            'arctic': {'temp_amplification': 2.0, 'precip_sensitivity': 1.5},
            'temperate': {'temp_amplification': 1.0, 'precip_sensitivity': 1.0},
            'tropical': {'temp_amplification': 0.8, 'precip_sensitivity': 1.3},
            'arid': {'temp_amplification': 1.2, 'precip_sensitivity': 2.0},
            'coastal': {'temp_amplification': 0.9, 'precip_sensitivity': 1.1}
        }
    
    @log_async_function_call
    async def analyze_regional_climate(self, regions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze climate patterns across multiple regions."""
        try:
            regional_analysis = {}
            
            for region in regions:
                region_name = region.get('name', 'unknown')
                location = region.get('location', {})
                climate_type = region.get('climate_type', 'temperate')
                
                # Analyze each region
                impact_modeler = ClimateChangeImpactModeler()
                
                scenarios = [ClimateScenario.RCP26, ClimateScenario.RCP45, ClimateScenario.RCP85]
                horizons = ['2030', '2050', '2080']
                
                region_impacts = await impact_modeler.model_climate_impacts(
                    location, scenarios, horizons
                )
                
                # Add regional-specific analysis
                regional_factors = self._get_regional_factors(climate_type)
                adjusted_impacts = self._apply_regional_adjustments(region_impacts, regional_factors)
                
                regional_analysis[region_name] = {
                    'location': location,
                    'climate_type': climate_type,
                    'impact_analysis': adjusted_impacts,
                    'regional_factors': regional_factors,
                    'vulnerability_assessment': await self._assess_regional_vulnerability(adjusted_impacts)
                }
            
            # Comparative analysis
            comparative_analysis = await self._compare_regional_impacts(regional_analysis)
            
            return {
                'status': 'success',
                'regional_analysis': regional_analysis,
                'comparative_analysis': comparative_analysis,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Regional climate analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _get_regional_factors(self, climate_type: str) -> Dict[str, float]:
        """Get regional climate factors."""
        return self.regional_factors.get(climate_type, self.regional_factors['temperate'])
    
    def _apply_regional_adjustments(self, impacts: Dict[str, Any], 
                                  factors: Dict[str, float]) -> Dict[str, Any]:
        """Apply regional adjustments to climate impacts."""
        # This would apply regional factors to the impact projections
        # For now, return the original impacts
        return impacts
    
    async def _assess_regional_vulnerability(self, impacts: Dict[str, Any]) -> Dict[str, Any]:
        """Assess regional vulnerability to climate change."""
        # Calculate vulnerability based on impacts
        vulnerability_score = 0.5  # Placeholder
        
        return {
            'vulnerability_score': vulnerability_score,
            'vulnerability_level': 'moderate',
            'key_vulnerabilities': ['temperature stress', 'water availability'],
            'adaptive_capacity': 'medium'
        }
    
    async def _compare_regional_impacts(self, regional_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Compare impacts across regions."""
        if len(regional_analysis) < 2:
            return {'comparison': 'insufficient_data'}
        
        # Extract impact scores for comparison
        region_scores = {}
        for region_name, region_data in regional_analysis.items():
            # Extract composite impact scores
            scores = []
            impact_data = region_data.get('impact_analysis', {}).get('impact_projections', {})
            
            for scenario, scenario_data in impact_data.items():
                for horizon, horizon_data in scenario_data.items():
                    if 'composite_impact' in horizon_data:
                        scores.append(horizon_data['composite_impact']['composite_impact_score'])
            
            if scores:
                region_scores[region_name] = np.mean(scores)
        
        # Rank regions by impact
        sorted_regions = sorted(region_scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'most_impacted_region': sorted_regions[0][0] if sorted_regions else 'unknown',
            'least_impacted_region': sorted_regions[-1][0] if sorted_regions else 'unknown',
            'regional_ranking': sorted_regions,
            'impact_variation': np.std(list(region_scores.values())) if region_scores else 0
        }


# Convenience functions
async def model_climate_change_impacts(location: Dict[str, float],
                                     scenarios: List[ClimateScenario] = None,
                                     horizons: List[str] = None) -> Dict[str, Any]:
    """Model climate change impacts for a location."""
    if scenarios is None:
        scenarios = [ClimateScenario.RCP26, ClimateScenario.RCP45, ClimateScenario.RCP85]
    if horizons is None:
        horizons = ['2030', '2050', '2080']
    
    modeler = ClimateChangeImpactModeler()
    return await modeler.model_climate_impacts(location, scenarios, horizons)


async def analyze_regional_climate_patterns(regions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze climate patterns across multiple regions."""
    analyzer = RegionalClimateAnalyzer()
    return await analyzer.analyze_regional_climate(regions)
