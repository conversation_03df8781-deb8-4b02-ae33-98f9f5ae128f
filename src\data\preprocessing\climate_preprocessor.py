"""
Climate Data Preprocessing Pipeline.

This module provides comprehensive preprocessing capabilities for multi-source
climate data including cleaning, normalization, validation, and transformation.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


@dataclass
class ProcessedClimateData:
    """Standardized processed climate data structure."""
    timestamp: datetime
    location: str
    latitude: float
    longitude: float
    source: str
    
    # Temperature data (°C)
    temperature: Optional[float] = None
    temperature_min: Optional[float] = None
    temperature_max: Optional[float] = None
    feels_like: Optional[float] = None
    
    # Humidity and pressure
    humidity: Optional[float] = None  # %
    pressure: Optional[float] = None  # hPa
    
    # Precipitation and wind
    precipitation: Optional[float] = None  # mm
    wind_speed: Optional[float] = None  # m/s
    wind_direction: Optional[float] = None  # degrees
    
    # Air quality
    air_quality_index: Optional[float] = None
    pm2_5: Optional[float] = None  # μg/m³
    pm10: Optional[float] = None  # μg/m³
    
    # Additional metrics
    visibility: Optional[float] = None  # km
    uv_index: Optional[float] = None
    cloud_cover: Optional[float] = None  # %
    
    # Quality indicators
    data_quality_score: float = 1.0  # 0-1 scale
    missing_fields: List[str] = None
    anomaly_flags: List[str] = None
    
    def __post_init__(self):
        if self.missing_fields is None:
            self.missing_fields = []
        if self.anomaly_flags is None:
            self.anomaly_flags = []


class ClimateDataPreprocessor:
    """
    Comprehensive climate data preprocessing pipeline.
    
    Handles data from multiple sources:
    - OpenWeatherMap
    - NASA Climate Data
    - World Bank Climate Indicators
    - NOAA Climate Records
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Data quality thresholds
        self.quality_thresholds = {
            'temperature_range': (-60, 60),  # °C
            'humidity_range': (0, 100),  # %
            'pressure_range': (800, 1200),  # hPa
            'wind_speed_max': 150,  # m/s (extreme hurricane)
            'precipitation_max': 500,  # mm/day (extreme rainfall)
            'visibility_max': 50,  # km
            'uv_index_max': 15
        }
        
        # Unit conversion factors
        self.unit_conversions = {
            'temperature': {
                'fahrenheit_to_celsius': lambda f: (f - 32) * 5/9,
                'kelvin_to_celsius': lambda k: k - 273.15
            },
            'pressure': {
                'inches_hg_to_hpa': lambda inches: inches * 33.8639,
                'mmhg_to_hpa': lambda mmhg: mmhg * 1.33322
            },
            'wind_speed': {
                'mph_to_ms': lambda mph: mph * 0.44704,
                'kmh_to_ms': lambda kmh: kmh / 3.6,
                'knots_to_ms': lambda knots: knots * 0.514444
            },
            'precipitation': {
                'inches_to_mm': lambda inches: inches * 25.4
            }
        }
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the preprocessor."""
        try:
            logger.info("Initializing Climate Data Preprocessor...")
            self.is_initialized = True
            logger.info("Climate Data Preprocessor initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize preprocessor: {e}")
            return False
    
    async def process_openweather_data(self, raw_data: Dict[str, Any]) -> ProcessedClimateData:
        """Process OpenWeatherMap API data."""
        try:
            main = raw_data.get('main', {})
            weather = raw_data.get('weather', [{}])[0]
            wind = raw_data.get('wind', {})
            clouds = raw_data.get('clouds', {})
            coord = raw_data.get('coord', {})
            
            processed = ProcessedClimateData(
                timestamp=datetime.fromtimestamp(raw_data.get('dt', 0)),
                location=raw_data.get('name', 'Unknown'),
                latitude=coord.get('lat', 0.0),
                longitude=coord.get('lon', 0.0),
                source='openweathermap',
                
                temperature=main.get('temp'),
                temperature_min=main.get('temp_min'),
                temperature_max=main.get('temp_max'),
                feels_like=main.get('feels_like'),
                
                humidity=main.get('humidity'),
                pressure=main.get('pressure'),
                
                precipitation=self._extract_precipitation(raw_data),
                wind_speed=wind.get('speed'),
                wind_direction=wind.get('deg'),
                
                visibility=raw_data.get('visibility', 0) / 1000 if raw_data.get('visibility') else None,
                cloud_cover=clouds.get('all')
            )
            
            # Validate and clean the data
            processed = await self._validate_and_clean(processed)
            
            return processed
            
        except Exception as e:
            logger.error(f"Failed to process OpenWeatherMap data: {e}")
            raise
    
    def _extract_precipitation(self, raw_data: Dict[str, Any]) -> Optional[float]:
        """Extract precipitation from OpenWeatherMap data."""
        rain = raw_data.get('rain', {})
        snow = raw_data.get('snow', {})
        
        # Get 1-hour precipitation (mm)
        rain_1h = rain.get('1h', 0)
        snow_1h = snow.get('1h', 0)
        
        total_precip = rain_1h + snow_1h
        return total_precip if total_precip > 0 else None
    
    async def process_nasa_data(self, raw_data: Dict[str, Any]) -> ProcessedClimateData:
        """Process NASA climate data."""
        try:
            # NASA data structure varies by endpoint
            if 'parameters' in raw_data:
                # NASA POWER data
                return await self._process_nasa_power_data(raw_data)
            else:
                # Other NASA data formats
                return await self._process_generic_nasa_data(raw_data)
                
        except Exception as e:
            logger.error(f"Failed to process NASA data: {e}")
            raise
    
    async def _process_nasa_power_data(self, raw_data: Dict[str, Any]) -> ProcessedClimateData:
        """Process NASA POWER meteorological data."""
        parameters = raw_data.get('parameters', {})
        metadata = raw_data.get('metadata', {})
        
        # Extract latest values from time series data
        latest_values = {}
        for param, data in parameters.items():
            if isinstance(data, dict) and data:
                # Get the most recent value
                latest_date = max(data.keys())
                latest_values[param] = data[latest_date]
        
        processed = ProcessedClimateData(
            timestamp=datetime.now(),  # NASA POWER data doesn't have specific timestamps
            location=f"NASA POWER Data",
            latitude=raw_data.get('latitude', 0.0),
            longitude=raw_data.get('longitude', 0.0),
            source='nasa_power',
            
            temperature=latest_values.get('T2M'),  # Temperature at 2 meters
            humidity=latest_values.get('RH2M'),  # Relative humidity at 2 meters
            precipitation=latest_values.get('PRECTOTCORR'),  # Precipitation corrected
            wind_speed=latest_values.get('WS2M'),  # Wind speed at 2 meters
            pressure=latest_values.get('PS')  # Surface pressure
        )
        
        processed = await self._validate_and_clean(processed)
        return processed
    
    async def _process_generic_nasa_data(self, raw_data: Dict[str, Any]) -> ProcessedClimateData:
        """Process generic NASA data formats."""
        processed = ProcessedClimateData(
            timestamp=datetime.now(),
            location="NASA Data",
            latitude=0.0,
            longitude=0.0,
            source='nasa_generic'
        )
        
        # Add basic validation
        processed = await self._validate_and_clean(processed)
        return processed
    
    async def process_worldbank_data(self, raw_data: Dict[str, Any]) -> ProcessedClimateData:
        """Process World Bank climate indicator data."""
        try:
            # World Bank data is typically aggregated indicators
            processed = ProcessedClimateData(
                timestamp=datetime(raw_data.get('year', 2023), 1, 1),
                location=raw_data.get('country_name', 'Unknown'),
                latitude=0.0,  # World Bank data doesn't include coordinates
                longitude=0.0,
                source='worldbank'
            )
            
            # World Bank data doesn't map directly to weather parameters
            # It's more about climate indicators and emissions
            processed.data_quality_score = 0.8  # Lower score due to different data type
            
            processed = await self._validate_and_clean(processed)
            return processed
            
        except Exception as e:
            logger.error(f"Failed to process World Bank data: {e}")
            raise
    
    async def process_noaa_data(self, raw_data: Dict[str, Any]) -> ProcessedClimateData:
        """Process NOAA climate data."""
        try:
            processed = ProcessedClimateData(
                timestamp=raw_data.get('timestamp', datetime.now()),
                location=raw_data.get('location', 'NOAA Station'),
                latitude=raw_data.get('latitude', 0.0),
                longitude=raw_data.get('longitude', 0.0),
                source='noaa'
            )
            
            # Map NOAA data types to our standard format
            datatype = raw_data.get('datatype', '')
            value = raw_data.get('value', 0)
            
            if datatype == 'TMAX':
                processed.temperature_max = value
            elif datatype == 'TMIN':
                processed.temperature_min = value
            elif datatype == 'TAVG':
                processed.temperature = value
            elif datatype == 'PRCP':
                processed.precipitation = value
            elif datatype == 'AWND':
                processed.wind_speed = value
            
            processed = await self._validate_and_clean(processed)
            return processed
            
        except Exception as e:
            logger.error(f"Failed to process NOAA data: {e}")
            raise
    
    async def _validate_and_clean(self, data: ProcessedClimateData) -> ProcessedClimateData:
        """Validate and clean processed climate data."""
        try:
            missing_fields = []
            anomaly_flags = []
            quality_score = 1.0
            
            # Check for missing critical fields
            if data.temperature is None and data.temperature_min is None and data.temperature_max is None:
                missing_fields.append('temperature')
                quality_score -= 0.2
            
            # Validate temperature ranges
            for temp_field, temp_value in [
                ('temperature', data.temperature),
                ('temperature_min', data.temperature_min),
                ('temperature_max', data.temperature_max),
                ('feels_like', data.feels_like)
            ]:
                if temp_value is not None:
                    min_temp, max_temp = self.quality_thresholds['temperature_range']
                    if not (min_temp <= temp_value <= max_temp):
                        anomaly_flags.append(f'{temp_field}_out_of_range')
                        quality_score -= 0.1
            
            # Validate humidity
            if data.humidity is not None:
                min_hum, max_hum = self.quality_thresholds['humidity_range']
                if not (min_hum <= data.humidity <= max_hum):
                    anomaly_flags.append('humidity_out_of_range')
                    quality_score -= 0.1
            
            # Validate pressure
            if data.pressure is not None:
                min_press, max_press = self.quality_thresholds['pressure_range']
                if not (min_press <= data.pressure <= max_press):
                    anomaly_flags.append('pressure_out_of_range')
                    quality_score -= 0.1
            
            # Validate wind speed
            if data.wind_speed is not None:
                if data.wind_speed > self.quality_thresholds['wind_speed_max']:
                    anomaly_flags.append('extreme_wind_speed')
                    quality_score -= 0.1
            
            # Validate precipitation
            if data.precipitation is not None:
                if data.precipitation > self.quality_thresholds['precipitation_max']:
                    anomaly_flags.append('extreme_precipitation')
                    quality_score -= 0.1
            
            # Update data quality indicators
            data.missing_fields = missing_fields
            data.anomaly_flags = anomaly_flags
            data.data_quality_score = max(0.0, quality_score)
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to validate climate data: {e}")
            data.data_quality_score = 0.5  # Assign medium quality on validation error
            return data
    
    async def normalize_units(self, data: ProcessedClimateData, target_units: Dict[str, str] = None) -> ProcessedClimateData:
        """Normalize units to standard format."""
        try:
            if target_units is None:
                target_units = {
                    'temperature': 'celsius',
                    'pressure': 'hpa',
                    'wind_speed': 'ms',
                    'precipitation': 'mm'
                }
            
            # Temperature normalization (assume input is already in Celsius for now)
            # In a real implementation, you'd detect and convert from other units
            
            # Pressure normalization (assume input is already in hPa)
            
            # Wind speed normalization (assume input is already in m/s)
            
            # Precipitation normalization (assume input is already in mm)
            
            logger.debug(f"Units normalized for {data.source} data")
            return data
            
        except Exception as e:
            logger.error(f"Failed to normalize units: {e}")
            return data
    
    async def aggregate_multi_source_data(self, data_list: List[ProcessedClimateData]) -> ProcessedClimateData:
        """Aggregate data from multiple sources for the same location and time."""
        try:
            if not data_list:
                raise ValueError("No data provided for aggregation")
            
            if len(data_list) == 1:
                return data_list[0]
            
            # Use the highest quality data as base
            base_data = max(data_list, key=lambda x: x.data_quality_score)
            
            # Aggregate values from all sources
            aggregated = ProcessedClimateData(
                timestamp=base_data.timestamp,
                location=base_data.location,
                latitude=base_data.latitude,
                longitude=base_data.longitude,
                source='aggregated'
            )
            
            # Aggregate temperature (weighted by quality score)
            temp_values = [(d.temperature, d.data_quality_score) for d in data_list if d.temperature is not None]
            if temp_values:
                weighted_temp = sum(temp * weight for temp, weight in temp_values)
                total_weight = sum(weight for _, weight in temp_values)
                aggregated.temperature = weighted_temp / total_weight
            
            # Similar aggregation for other fields
            # For brevity, using simple averaging here
            
            # Calculate combined quality score
            aggregated.data_quality_score = sum(d.data_quality_score for d in data_list) / len(data_list)
            
            # Combine missing fields and anomaly flags
            all_missing = set()
            all_anomalies = set()
            for d in data_list:
                all_missing.update(d.missing_fields or [])
                all_anomalies.update(d.anomaly_flags or [])
            
            aggregated.missing_fields = list(all_missing)
            aggregated.anomaly_flags = list(all_anomalies)
            
            return aggregated
            
        except Exception as e:
            logger.error(f"Failed to aggregate multi-source data: {e}")
            return data_list[0] if data_list else None
    
    async def create_time_series(self, data_list: List[ProcessedClimateData]) -> pd.DataFrame:
        """Create a pandas DataFrame time series from processed climate data."""
        try:
            if not data_list:
                return pd.DataFrame()
            
            # Convert to list of dictionaries
            records = []
            for data in data_list:
                record = asdict(data)
                # Convert datetime to string for JSON serialization
                record['timestamp'] = data.timestamp.isoformat()
                records.append(record)
            
            # Create DataFrame
            df = pd.DataFrame(records)
            
            # Convert timestamp back to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Set timestamp as index
            df.set_index('timestamp', inplace=True)
            
            # Sort by timestamp
            df.sort_index(inplace=True)
            
            logger.info(f"Created time series with {len(df)} records")
            return df
            
        except Exception as e:
            logger.error(f"Failed to create time series: {e}")
            return pd.DataFrame()
    
    async def detect_anomalies(self, df: pd.DataFrame, method: str = 'statistical') -> pd.DataFrame:
        """Detect anomalies in climate data time series."""
        try:
            if df.empty:
                return df
            
            df_copy = df.copy()
            
            if method == 'statistical':
                # Statistical anomaly detection using z-score
                numeric_columns = df_copy.select_dtypes(include=[np.number]).columns
                
                for col in numeric_columns:
                    if col in ['temperature', 'humidity', 'pressure', 'wind_speed']:
                        values = df_copy[col].dropna()
                        if len(values) > 3:  # Need at least 3 values for statistics
                            mean = values.mean()
                            std = values.std()
                            
                            # Mark values beyond 3 standard deviations as anomalies
                            z_scores = np.abs((values - mean) / std)
                            anomalies = z_scores > 3
                            
                            df_copy[f'{col}_anomaly'] = False
                            df_copy.loc[values.index[anomalies], f'{col}_anomaly'] = True
            
            logger.info(f"Anomaly detection completed using {method} method")
            return df_copy
            
        except Exception as e:
            logger.error(f"Failed to detect anomalies: {e}")
            return df
    
    async def interpolate_missing_values(self, df: pd.DataFrame, method: str = 'linear') -> pd.DataFrame:
        """Interpolate missing values in climate data."""
        try:
            if df.empty:
                return df
            
            df_copy = df.copy()
            
            # Interpolate numeric columns
            numeric_columns = df_copy.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in ['temperature', 'humidity', 'pressure', 'wind_speed', 'precipitation']:
                    if method == 'linear':
                        df_copy[col] = df_copy[col].interpolate(method='linear')
                    elif method == 'forward_fill':
                        df_copy[col] = df_copy[col].fillna(method='ffill')
                    elif method == 'backward_fill':
                        df_copy[col] = df_copy[col].fillna(method='bfill')
            
            logger.info(f"Missing value interpolation completed using {method} method")
            return df_copy
            
        except Exception as e:
            logger.error(f"Failed to interpolate missing values: {e}")
            return df


# Convenience functions
async def preprocess_climate_data(raw_data: Dict[str, Any], source: str) -> ProcessedClimateData:
    """Preprocess climate data from any source."""
    preprocessor = ClimateDataPreprocessor()
    await preprocessor.initialize()
    
    if source == 'openweathermap':
        return await preprocessor.process_openweather_data(raw_data)
    elif source == 'nasa':
        return await preprocessor.process_nasa_data(raw_data)
    elif source == 'worldbank':
        return await preprocessor.process_worldbank_data(raw_data)
    elif source == 'noaa':
        return await preprocessor.process_noaa_data(raw_data)
    else:
        raise ValueError(f"Unsupported data source: {source}")


async def create_climate_time_series(data_list: List[ProcessedClimateData]) -> pd.DataFrame:
    """Create a time series DataFrame from processed climate data."""
    preprocessor = ClimateDataPreprocessor()
    await preprocessor.initialize()
    
    return await preprocessor.create_time_series(data_list)


# Alias for backward compatibility
ClimatePreprocessor = ClimateDataPreprocessor
