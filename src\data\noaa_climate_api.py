"""NOAA Climate API Integration for Weather and Climate Data."""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class NOAAClimateAPI:
    """NOAA Climate Data API integration."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://www.ncdc.noaa.gov/cdo-web/api/v2"
        self.session = None
    
    @log_async_function_call
    async def get_climate_data(self, location: Dict[str, float], 
                              start_date: str, end_date: str) -> Dict[str, Any]:
        """Get NOAA climate data."""
        try:
            # First, find nearby weather stations
            stations = await self._find_nearby_stations(location)
            
            if not stations:
                return {'status': 'error', 'error': 'No nearby NOAA stations found'}
            
            # Get data from the closest station
            station_id = stations[0]['id']
            climate_data = await self._get_station_data(station_id, start_date, end_date)
            
            if climate_data:
                processed_data = await self._process_noaa_data(climate_data)
                
                return {
                    'status': 'success',
                    'source': 'NOAA',
                    'location': location,
                    'station_id': station_id,
                    'station_name': stations[0].get('name', 'Unknown'),
                    'date_range': {'start': start_date, 'end': end_date},
                    'climate_data': processed_data,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {'status': 'error', 'error': 'No data available for the specified period'}
            
        except Exception as e:
            logger.error(f"NOAA Climate API error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _find_nearby_stations(self, location: Dict[str, float]) -> List[Dict[str, Any]]:
        """Find nearby NOAA weather stations."""
        try:
            url = f"{self.base_url}/stations"
            headers = {'token': self.api_key}
            params = {
                'extent': f"{location['lat']-0.5},{location['lon']-0.5},{location['lat']+0.5},{location['lon']+0.5}",
                'limit': 10,
                'datasetid': 'GHCND'  # Global Historical Climatology Network Daily
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('results', [])
                    else:
                        logger.warning(f"NOAA stations API returned status {response.status}")
                        return []
        
        except Exception as e:
            logger.error(f"Error finding NOAA stations: {e}")
            return []
    
    async def _get_station_data(self, station_id: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """Get data from specific NOAA station."""
        try:
            url = f"{self.base_url}/data"
            headers = {'token': self.api_key}
            params = {
                'datasetid': 'GHCND',
                'stationid': station_id,
                'startdate': start_date,
                'enddate': end_date,
                'datatypeid': 'TMAX,TMIN,PRCP,SNOW,SNWD',  # Max temp, min temp, precipitation, snow
                'limit': 1000,
                'units': 'metric'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"NOAA data API returned status {response.status}")
                        return None
        
        except Exception as e:
            logger.error(f"Error getting NOAA station data: {e}")
            return None
    
    async def _process_noaa_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process raw NOAA climate data."""
        results = raw_data.get('results', [])
        
        processed = {
            'temperature_max': [],
            'temperature_min': [],
            'precipitation': [],
            'snow': [],
            'snow_depth': [],
            'dates': []
        }
        
        # Group data by date
        daily_data = {}
        
        for record in results:
            date = record['date'][:10]  # Extract date part
            datatype = record['datatype']
            value = record['value']
            
            if date not in daily_data:
                daily_data[date] = {}
            
            daily_data[date][datatype] = value
        
        # Convert to arrays
        for date in sorted(daily_data.keys()):
            day_data = daily_data[date]
            
            processed['dates'].append(date)
            processed['temperature_max'].append(day_data.get('TMAX', None))
            processed['temperature_min'].append(day_data.get('TMIN', None))
            processed['precipitation'].append(day_data.get('PRCP', None))
            processed['snow'].append(day_data.get('SNOW', None))
            processed['snow_depth'].append(day_data.get('SNWD', None))
        
        # Calculate statistics
        stats = await self._calculate_climate_statistics(processed)
        processed['statistics'] = stats
        
        return processed
    
    async def _calculate_climate_statistics(self, data: Dict[str, List]) -> Dict[str, Any]:
        """Calculate climate statistics from processed data."""
        stats = {}
        
        # Temperature statistics
        tmax_values = [v for v in data['temperature_max'] if v is not None]
        tmin_values = [v for v in data['temperature_min'] if v is not None]
        
        if tmax_values:
            stats['temperature_max'] = {
                'mean': sum(tmax_values) / len(tmax_values),
                'min': min(tmax_values),
                'max': max(tmax_values),
                'count': len(tmax_values)
            }
        
        if tmin_values:
            stats['temperature_min'] = {
                'mean': sum(tmin_values) / len(tmin_values),
                'min': min(tmin_values),
                'max': max(tmin_values),
                'count': len(tmin_values)
            }
        
        # Precipitation statistics
        precip_values = [v for v in data['precipitation'] if v is not None and v > 0]
        
        if precip_values:
            stats['precipitation'] = {
                'total': sum(precip_values),
                'mean_daily': sum(precip_values) / len(precip_values),
                'max_daily': max(precip_values),
                'wet_days': len(precip_values),
                'dry_days': len([v for v in data['precipitation'] if v is not None and v == 0])
            }
        
        # Snow statistics
        snow_values = [v for v in data['snow'] if v is not None and v > 0]
        
        if snow_values:
            stats['snow'] = {
                'total': sum(snow_values),
                'max_daily': max(snow_values),
                'snow_days': len(snow_values)
            }
        
        return stats
    
    @log_async_function_call
    async def get_climate_normals(self, location: Dict[str, float]) -> Dict[str, Any]:
        """Get climate normals (30-year averages) for location."""
        try:
            # Find stations with climate normals
            stations = await self._find_normals_stations(location)
            
            if not stations:
                return {'status': 'error', 'error': 'No climate normals stations found'}
            
            station_id = stations[0]['id']
            normals_data = await self._get_normals_data(station_id)
            
            if normals_data:
                processed_normals = await self._process_normals_data(normals_data)
                
                return {
                    'status': 'success',
                    'source': 'NOAA Climate Normals',
                    'location': location,
                    'station_id': station_id,
                    'period': '1991-2020',
                    'climate_normals': processed_normals,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {'status': 'error', 'error': 'No climate normals data available'}
            
        except Exception as e:
            logger.error(f"NOAA Climate Normals error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _find_normals_stations(self, location: Dict[str, float]) -> List[Dict[str, Any]]:
        """Find stations with climate normals data."""
        try:
            url = f"{self.base_url}/stations"
            headers = {'token': self.api_key}
            params = {
                'extent': f"{location['lat']-1},{location['lon']-1},{location['lat']+1},{location['lon']+1}",
                'limit': 10,
                'datasetid': 'NORMAL_MLY'  # Monthly climate normals
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('results', [])
                    return []
        
        except Exception as e:
            logger.error(f"Error finding normals stations: {e}")
            return []
    
    async def _get_normals_data(self, station_id: str) -> Dict[str, Any]:
        """Get climate normals data from station."""
        try:
            url = f"{self.base_url}/data"
            headers = {'token': self.api_key}
            params = {
                'datasetid': 'NORMAL_MLY',
                'stationid': station_id,
                'startdate': '2010-01-01',
                'enddate': '2010-12-31',
                'datatypeid': 'MLY-TAVG-NORMAL,MLY-PRCP-NORMAL',
                'limit': 1000
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    return None
        
        except Exception as e:
            logger.error(f"Error getting normals data: {e}")
            return None
    
    async def _process_normals_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process climate normals data."""
        results = raw_data.get('results', [])
        
        monthly_normals = {
            'temperature': [None] * 12,
            'precipitation': [None] * 12,
            'months': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        }
        
        for record in results:
            date = record['date']
            month = int(date[5:7]) - 1  # Convert to 0-based index
            datatype = record['datatype']
            value = record['value']
            
            if datatype == 'MLY-TAVG-NORMAL':
                monthly_normals['temperature'][month] = value
            elif datatype == 'MLY-PRCP-NORMAL':
                monthly_normals['precipitation'][month] = value
        
        # Calculate annual statistics
        temp_values = [v for v in monthly_normals['temperature'] if v is not None]
        precip_values = [v for v in monthly_normals['precipitation'] if v is not None]
        
        annual_stats = {}
        
        if temp_values:
            annual_stats['temperature'] = {
                'annual_mean': sum(temp_values) / len(temp_values),
                'warmest_month': max(temp_values),
                'coldest_month': min(temp_values),
                'temperature_range': max(temp_values) - min(temp_values)
            }
        
        if precip_values:
            annual_stats['precipitation'] = {
                'annual_total': sum(precip_values),
                'wettest_month': max(precip_values),
                'driest_month': min(precip_values),
                'precipitation_variability': max(precip_values) - min(precip_values)
            }
        
        monthly_normals['annual_statistics'] = annual_stats
        
        return monthly_normals


# Convenience functions
async def get_noaa_climate_data(api_key: str, location: Dict[str, float], 
                               days_back: int = 30) -> Dict[str, Any]:
    """Get comprehensive NOAA climate data."""
    api = NOAAClimateAPI(api_key)
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
    
    current_data = await api.get_climate_data(location, start_date, end_date)
    normals_data = await api.get_climate_normals(location)
    
    return {
        'current_climate_data': current_data,
        'climate_normals': normals_data,
        'collection_timestamp': datetime.now().isoformat()
    }
