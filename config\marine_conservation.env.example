# Marine Conservation API Configuration
# Copy this file to .env and fill in your actual credentials

# =============================================================================
# SATELLITE IMAGERY APIs
# =============================================================================

# Sentinel Hub API (OAuth2 Authentication)
SENTINEL_HUB_CLIENT_ID=91e73709-3e73-4525-9cfc-957258864901
SENTINEL_HUB_CLIENT_SECRET=your_sentinel_hub_client_secret_here
SENTINEL_HUB_BASE_URL=https://services.sentinel-hub.com

# Planet Labs API (API Key Authentication)
PLANET_LABS_API_KEY=PLAKf8364d269a8d4764816d44ace4f14977
PLANET_LABS_BASE_URL=https://api.planet.com

# =============================================================================
# ENVIRONMENTAL MONITORING APIs
# =============================================================================

# NASA Open Data APIs (API Key Authentication)
NASA_API_KEY=AxwO7eGNcFT3TOZ3H4AVEx8OsaTwzxorspqxWlkL
NASA_BASE_URL=https://api.nasa.gov

# NOAA Ocean Service API (User-Agent Authentication)
NOAA_USER_AGENT=WaterManagementSystem/1.0 (<EMAIL>)
NOAA_BASE_URL=https://api.weather.gov

# Copernicus Marine Service API (Username/Password Authentication)
COPERNICUS_USERNAME=your_copernicus_username
COPERNICUS_PASSWORD=your_copernicus_password
COPERNICUS_BASE_URL=https://marine.copernicus.eu

# =============================================================================
# MARITIME TRAFFIC APIs
# =============================================================================

# AISStream.io API (API Key Authentication)
AISSTREAM_API_KEY=989d91ab25d59efbe59279756d4b355f5b79c4c8
AISSTREAM_BASE_URL=https://stream.aisstream.io
AISSTREAM_WEBSOCKET_URL=wss://stream.aisstream.io/v0/stream

# =============================================================================
# GEOGRAPHIC AND INFRASTRUCTURE APIs
# =============================================================================

# OpenStreetMap Overpass API (Open Access)
OSM_OVERPASS_BASE_URL=https://overpass-api.de/api
OSM_USER_AGENT=WaterManagementSystem/1.0

# =============================================================================
# TAIWAN GOVERNMENT APIs
# =============================================================================

# Taiwan EPA APIs (Open Access)
TAIWAN_EPA_BASE_URL=https://data.epa.gov.tw
TAIWAN_EPA_API_VERSION=v1

# Taiwan Ocean Affairs Council APIs (Open Access)
TAIWAN_OAC_BASE_URL=https://www.oac.gov.tw
TAIWAN_OAC_API_VERSION=v1

# =============================================================================
# MARINE CONSERVATION PLATFORM SETTINGS
# =============================================================================

# General Configuration
MARINE_CONSERVATION_ENABLED=true
MARINE_CONSERVATION_LOG_LEVEL=INFO
MARINE_CONSERVATION_DATA_RETENTION_DAYS=365

# AI and ML Configuration
MARINE_AI_MODEL_PATH=./models/marine_conservation/
MARINE_DEBRIS_DETECTION_THRESHOLD=0.75
MARINE_VESSEL_TRACKING_ENABLED=true

# Taiwan Government Integration
TAIWAN_COLLABORATION_ENABLED=true
TAIWAN_LANGUAGE_SUPPORT=zh-TW,en-US

# Data Processing Configuration
REAL_TIME_PROCESSING_ENABLED=true
BATCH_PROCESSING_INTERVAL_HOURS=6
DATA_QUALITY_THRESHOLD=0.85

# Notification Settings
MARINE_ALERTS_ENABLED=true
MARINE_ALERT_EMAIL=<EMAIL>
MARINE_ALERT_WEBHOOK_URL=https://your-webhook-url.com/marine-alerts

# =============================================================================
# RATE LIMITING AND QUOTAS
# =============================================================================

# API Rate Limits (requests per hour)
SENTINEL_HUB_RATE_LIMIT=100
PLANET_LABS_RATE_LIMIT=1000
NASA_API_RATE_LIMIT=1000
NOAA_API_RATE_LIMIT=10000
COPERNICUS_RATE_LIMIT=500
AISSTREAM_RATE_LIMIT=5000
OSM_OVERPASS_RATE_LIMIT=10000

# Data Quotas (MB per day)
SATELLITE_DATA_QUOTA_MB=10240
OCEANOGRAPHIC_DATA_QUOTA_MB=5120
MARITIME_DATA_QUOTA_MB=2048

# =============================================================================
# CACHING AND STORAGE
# =============================================================================

# Redis Configuration for Marine Data
MARINE_REDIS_HOST=localhost
MARINE_REDIS_PORT=6379
MARINE_REDIS_DB=2
MARINE_REDIS_PASSWORD=your_redis_password

# Database Configuration
MARINE_DB_HOST=localhost
MARINE_DB_PORT=5432
MARINE_DB_NAME=marine_conservation
MARINE_DB_USER=marine_user
MARINE_DB_PASSWORD=your_marine_db_password

# File Storage
MARINE_DATA_STORAGE_PATH=./data/marine_conservation/
MARINE_SATELLITE_IMAGES_PATH=./data/satellite_images/
MARINE_PROCESSED_DATA_PATH=./data/processed/marine/

# =============================================================================
# SECURITY AND ENCRYPTION
# =============================================================================

# Encryption Keys
MARINE_DATA_ENCRYPTION_KEY=your_32_character_encryption_key_here
MARINE_API_SECRET_KEY=your_api_secret_key_for_internal_auth

# SSL/TLS Configuration
MARINE_SSL_ENABLED=true
MARINE_SSL_CERT_PATH=./certs/marine_conservation.crt
MARINE_SSL_KEY_PATH=./certs/marine_conservation.key

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Logging Configuration
MARINE_LOG_FILE=./logs/marine_conservation.log
MARINE_LOG_MAX_SIZE_MB=100
MARINE_LOG_BACKUP_COUNT=5

# Monitoring
MARINE_METRICS_ENABLED=true
MARINE_METRICS_PORT=9090
MARINE_HEALTH_CHECK_INTERVAL_SECONDS=30

# =============================================================================
# DEVELOPMENT AND TESTING
# =============================================================================

# Development Mode
MARINE_DEVELOPMENT_MODE=false
MARINE_DEBUG_ENABLED=false
MARINE_MOCK_APIS_ENABLED=false

# Testing Configuration
MARINE_TEST_DATA_PATH=./tests/data/marine/
MARINE_TEST_API_TIMEOUT_SECONDS=30
MARINE_TEST_COVERAGE_THRESHOLD=85

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Marine Conservation Features
FEATURE_DEBRIS_DETECTION=true
FEATURE_VESSEL_TRACKING=true
FEATURE_TAIWAN_COLLABORATION=true
FEATURE_REAL_TIME_ALERTS=true
FEATURE_PREDICTIVE_ANALYTICS=true
FEATURE_BLOCKCHAIN_TRACKING=false
FEATURE_AR_VR_VISUALIZATION=false

# AI and ML Features
FEATURE_COMPUTER_VISION=true
FEATURE_NATURAL_LANGUAGE_PROCESSING=true
FEATURE_REINFORCEMENT_LEARNING=true
FEATURE_FEDERATED_LEARNING=false

# Integration Features
FEATURE_SOCIAL_MEDIA_INTEGRATION=false
FEATURE_MOBILE_APP_SYNC=false
FEATURE_IOT_SENSOR_SIMULATION=true

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Concurrent Processing
MARINE_MAX_CONCURRENT_API_CALLS=10
MARINE_MAX_CONCURRENT_IMAGE_PROCESSING=5
MARINE_WORKER_THREADS=4

# Memory Management
MARINE_MAX_MEMORY_USAGE_MB=2048
MARINE_CACHE_SIZE_MB=512
MARINE_IMAGE_PROCESSING_MEMORY_MB=1024

# Network Configuration
MARINE_API_TIMEOUT_SECONDS=30
MARINE_RETRY_ATTEMPTS=3
MARINE_RETRY_DELAY_SECONDS=5

# =============================================================================
# BACKUP AND DISASTER RECOVERY
# =============================================================================

# Backup Configuration
MARINE_BACKUP_ENABLED=true
MARINE_BACKUP_INTERVAL_HOURS=24
MARINE_BACKUP_RETENTION_DAYS=30
MARINE_BACKUP_STORAGE_PATH=./backups/marine_conservation/

# Disaster Recovery
MARINE_DR_ENABLED=false
MARINE_DR_REPLICA_URL=https://backup-server.yourdomain.com
MARINE_DR_SYNC_INTERVAL_HOURS=6
