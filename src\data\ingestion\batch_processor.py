"""
Batch Climate Data Processing Module.

This module handles large-scale batch processing of climate data,
including scheduling, parallel processing, and data persistence.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
import pandas as pd
import json
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import sqlite3
from pathlib import Path

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.ingestion.climate_ingestion_manager import ClimateDataIngestionManager, IngestionConfig, IngestionResult
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class BatchConfig:
    """Configuration for batch processing."""
    batch_size: int = 50
    max_workers: int = 4
    processing_interval: int = 3600  # seconds
    max_retries: int = 3
    retry_delay: float = 5.0
    enable_persistence: bool = True
    output_format: str = 'sqlite'  # 'sqlite', 'csv', 'json'
    output_path: str = 'data/processed'
    enable_scheduling: bool = False
    schedule_interval: int = 86400  # 24 hours in seconds
    cleanup_old_data: bool = True
    data_retention_days: int = 30


@dataclass
class BatchResult:
    """Result of batch processing operation."""
    batch_id: str
    success: bool
    total_batches: int
    successful_batches: int
    failed_batches: int
    total_records: int
    processing_time: float
    errors: List[str]
    warnings: List[str]
    output_files: List[str]
    timestamp: datetime


class BatchClimateProcessor:
    """
    Batch processor for large-scale climate data operations.
    
    Features:
    - Parallel batch processing
    - Data persistence to multiple formats
    - Automatic scheduling
    - Error handling and retry logic
    - Data cleanup and retention management
    """
    
    def __init__(self, config: BatchConfig = None):
        self.settings = get_settings()
        self.config = config or BatchConfig()
        self.is_initialized = False
        self.is_running = False
        
        # Processing components
        self.executor = None
        self.scheduler_task = None
        
        # Database connection for persistence
        self.db_connection = None
        
        # Statistics
        self.stats = {
            'total_batches_processed': 0,
            'total_records_processed': 0,
            'average_processing_time': 0.0,
            'last_processing_time': None,
            'errors_count': 0
        }
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the batch processor."""
        try:
            logger.info("Initializing Batch Climate Processor...")
            
            # Create output directory
            output_path = Path(self.config.output_path)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Initialize thread pool executor
            self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
            
            # Initialize database if persistence is enabled
            if self.config.enable_persistence and self.config.output_format == 'sqlite':
                await self._initialize_database()
            
            self.is_initialized = True
            logger.info("Batch Climate Processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize batch processor: {e}")
            return False
    
    async def _initialize_database(self):
        """Initialize SQLite database for data persistence."""
        try:
            db_path = Path(self.config.output_path) / "climate_data.db"
            
            # Create database connection
            self.db_connection = sqlite3.connect(str(db_path), check_same_thread=False)
            
            # Create tables
            cursor = self.db_connection.cursor()
            
            # Climate data table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS climate_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    location TEXT NOT NULL,
                    latitude REAL,
                    longitude REAL,
                    source TEXT NOT NULL,
                    temperature REAL,
                    temperature_min REAL,
                    temperature_max REAL,
                    humidity REAL,
                    pressure REAL,
                    precipitation REAL,
                    wind_speed REAL,
                    wind_direction REAL,
                    data_quality_score REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Batch processing log table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS batch_processing_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    batch_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    records_processed INTEGER,
                    processing_time REAL,
                    errors TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_climate_timestamp ON climate_data(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_climate_location ON climate_data(location)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_climate_source ON climate_data(source)")
            
            self.db_connection.commit()
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def process_batch(self, locations: List[Dict[str, Any]], 
                          sources: List[str] = None) -> BatchResult:
        """Process a single batch of climate data."""
        batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = asyncio.get_event_loop().time()
        
        try:
            logger.info(f"Starting batch processing: {batch_id}")
            
            # Split locations into smaller batches
            batches = self._create_location_batches(locations)
            
            # Process batches in parallel
            batch_results = []
            errors = []
            warnings = []
            
            # Create semaphore for controlling concurrency
            semaphore = asyncio.Semaphore(self.config.max_workers)
            
            async def process_single_batch(batch_locations):
                async with semaphore:
                    try:
                        config = IngestionConfig(
                            sources=sources or ['openweathermap', 'nasa'],
                            locations=batch_locations,
                            batch_size=len(batch_locations)
                        )
                        
                        manager = ClimateDataIngestionManager(config)
                        result = await manager.ingest_climate_data()
                        await manager.shutdown()
                        
                        return result
                        
                    except Exception as e:
                        logger.error(f"Batch processing failed: {e}")
                        return None
            
            # Execute all batches concurrently
            tasks = [process_single_batch(batch) for batch in batches]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            total_records = 0
            successful_batches = 0
            failed_batches = 0
            output_files = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    errors.append(f"Batch {i} failed: {result}")
                    failed_batches += 1
                elif result and result.success:
                    batch_results.append(result)
                    total_records += result.total_records
                    successful_batches += 1
                    
                    # Persist data if enabled
                    if self.config.enable_persistence:
                        output_file = await self._persist_batch_data(result, batch_id, i)
                        if output_file:
                            output_files.append(output_file)
                else:
                    failed_batches += 1
                    if result:
                        errors.extend(result.errors)
                        warnings.extend(result.warnings)
            
            # Calculate processing time
            processing_time = asyncio.get_event_loop().time() - start_time
            
            # Create batch result
            batch_result = BatchResult(
                batch_id=batch_id,
                success=failed_batches == 0,
                total_batches=len(batches),
                successful_batches=successful_batches,
                failed_batches=failed_batches,
                total_records=total_records,
                processing_time=processing_time,
                errors=errors,
                warnings=warnings,
                output_files=output_files,
                timestamp=datetime.now()
            )
            
            # Log batch processing
            await self._log_batch_processing(batch_result)
            
            # Update statistics
            self._update_stats(batch_result)
            
            logger.info(f"Batch processing completed: {batch_id}, {total_records} records, {processing_time:.2f}s")
            return batch_result
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Batch processing failed: {e}")
            
            return BatchResult(
                batch_id=batch_id,
                success=False,
                total_batches=0,
                successful_batches=0,
                failed_batches=1,
                total_records=0,
                processing_time=processing_time,
                errors=[str(e)],
                warnings=[],
                output_files=[],
                timestamp=datetime.now()
            )
    
    def _create_location_batches(self, locations: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Split locations into smaller batches."""
        batches = []
        for i in range(0, len(locations), self.config.batch_size):
            batch = locations[i:i + self.config.batch_size]
            batches.append(batch)
        return batches
    
    async def _persist_batch_data(self, result: IngestionResult, batch_id: str, batch_index: int) -> Optional[str]:
        """Persist batch data to storage."""
        try:
            if self.config.output_format == 'sqlite' and self.db_connection:
                # This would require access to the actual processed data
                # For now, we'll log the metadata
                cursor = self.db_connection.cursor()
                cursor.execute("""
                    INSERT INTO batch_processing_log 
                    (batch_id, status, records_processed, processing_time, errors)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    f"{batch_id}_{batch_index}",
                    "success" if result.success else "failed",
                    result.total_records,
                    result.execution_time,
                    json.dumps(result.errors)
                ))
                self.db_connection.commit()
                
                return f"batch_{batch_id}_{batch_index}.db"
            
            elif self.config.output_format == 'json':
                # Save as JSON file
                output_file = Path(self.config.output_path) / f"batch_{batch_id}_{batch_index}.json"
                
                batch_data = {
                    'batch_id': f"{batch_id}_{batch_index}",
                    'result': asdict(result),
                    'timestamp': datetime.now().isoformat()
                }
                
                with open(output_file, 'w') as f:
                    json.dump(batch_data, f, indent=2, default=str)
                
                return str(output_file)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to persist batch data: {e}")
            return None
    
    async def _log_batch_processing(self, result: BatchResult):
        """Log batch processing results."""
        try:
            if self.db_connection:
                cursor = self.db_connection.cursor()
                cursor.execute("""
                    INSERT INTO batch_processing_log 
                    (batch_id, status, records_processed, processing_time, errors)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    result.batch_id,
                    "success" if result.success else "failed",
                    result.total_records,
                    result.processing_time,
                    json.dumps(result.errors)
                ))
                self.db_connection.commit()
                
        except Exception as e:
            logger.warning(f"Failed to log batch processing: {e}")
    
    def _update_stats(self, result: BatchResult):
        """Update processing statistics."""
        self.stats['total_batches_processed'] += 1
        self.stats['total_records_processed'] += result.total_records
        self.stats['last_processing_time'] = datetime.now().isoformat()
        self.stats['errors_count'] += len(result.errors)
        
        # Update average processing time
        current_avg = self.stats['average_processing_time']
        total_batches = self.stats['total_batches_processed']
        
        self.stats['average_processing_time'] = (
            (current_avg * (total_batches - 1) + result.processing_time) / total_batches
        )
    
    async def start_scheduled_processing(self, locations: List[Dict[str, Any]], 
                                       sources: List[str] = None):
        """Start scheduled batch processing."""
        if not self.config.enable_scheduling:
            logger.warning("Scheduling is disabled in configuration")
            return
        
        if self.is_running:
            logger.warning("Scheduled processing is already running")
            return
        
        self.is_running = True
        logger.info(f"Starting scheduled processing every {self.config.schedule_interval} seconds")
        
        async def scheduler():
            while self.is_running:
                try:
                    logger.info("Starting scheduled batch processing...")
                    result = await self.process_batch(locations, sources)
                    
                    if result.success:
                        logger.info(f"Scheduled processing completed: {result.total_records} records")
                    else:
                        logger.error(f"Scheduled processing failed: {result.errors}")
                    
                    # Cleanup old data if enabled
                    if self.config.cleanup_old_data:
                        await self._cleanup_old_data()
                    
                except Exception as e:
                    logger.error(f"Scheduled processing error: {e}")
                
                # Wait for next interval
                await asyncio.sleep(self.config.schedule_interval)
        
        self.scheduler_task = asyncio.create_task(scheduler())
    
    async def stop_scheduled_processing(self):
        """Stop scheduled batch processing."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Scheduled processing stopped")
    
    async def _cleanup_old_data(self):
        """Clean up old data based on retention policy."""
        try:
            if not self.db_connection:
                return
            
            cutoff_date = datetime.now() - timedelta(days=self.config.data_retention_days)
            cutoff_str = cutoff_date.isoformat()
            
            cursor = self.db_connection.cursor()
            
            # Delete old climate data
            cursor.execute("DELETE FROM climate_data WHERE created_at < ?", (cutoff_str,))
            deleted_climate = cursor.rowcount
            
            # Delete old batch logs
            cursor.execute("DELETE FROM batch_processing_log WHERE created_at < ?", (cutoff_str,))
            deleted_logs = cursor.rowcount
            
            self.db_connection.commit()
            
            if deleted_climate > 0 or deleted_logs > 0:
                logger.info(f"Cleaned up {deleted_climate} climate records and {deleted_logs} batch logs")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get batch processing statistics."""
        return {
            'stats': self.stats.copy(),
            'config': asdict(self.config),
            'is_running': self.is_running,
            'is_initialized': self.is_initialized,
            'timestamp': datetime.now().isoformat()
        }
    
    async def shutdown(self):
        """Shutdown the batch processor."""
        try:
            # Stop scheduled processing
            await self.stop_scheduled_processing()
            
            # Shutdown thread pool
            if self.executor:
                self.executor.shutdown(wait=True)
            
            # Close database connection
            if self.db_connection:
                self.db_connection.close()
            
            logger.info("Batch Climate Processor shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during batch processor shutdown: {e}")


# Convenience functions
async def process_climate_data_batch(locations: List[Dict[str, Any]], 
                                   sources: List[str] = None) -> BatchResult:
    """Process a batch of climate data for specified locations."""
    processor = BatchClimateProcessor()
    await processor.initialize()
    
    result = await processor.process_batch(locations, sources)
    await processor.shutdown()
    
    return result


async def start_continuous_climate_monitoring(locations: List[Dict[str, Any]], 
                                            interval_hours: int = 24):
    """Start continuous climate data monitoring."""
    config = BatchConfig(
        enable_scheduling=True,
        schedule_interval=interval_hours * 3600,
        enable_persistence=True,
        cleanup_old_data=True
    )
    
    processor = BatchClimateProcessor(config)
    await processor.initialize()
    
    await processor.start_scheduled_processing(locations)
    
    return processor
