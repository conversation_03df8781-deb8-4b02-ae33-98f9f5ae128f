#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unified AI-Powered Data Validation and Quality Assurance System
Comprehensive validation for all marine conservation data sources
"""

import asyncio
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# Import all API data types
from .apis.sentinel_hub_api import MarineDebrisDetection
from .apis.noaa_ocean_api import OceanCurrentData, WaterTemperatureData, TidalData, WeatherData
from .apis.copernicus_marine_api import OceanographicData, MarineEcosystemData, SeaIceData
from .apis.planet_labs_api import PlanetImagery
from .apis.nasa_open_api import NASAEarthData, NASAImageryData
from .apis.openstreetmap_api import CoastalInfrastructure, MarineArea
from .apis.aisstream_api import VesselData, MaritimeTrafficAnalysis

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataQuality(Enum):
    """Data quality levels"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    INVALID = "invalid"


class ValidationResult(Enum):
    """Validation result types"""
    PASS = "pass"
    WARNING = "warning"
    FAIL = "fail"
    ERROR = "error"


@dataclass
class ValidationReport:
    """Comprehensive validation report"""
    data_source: str
    data_type: str
    total_records: int
    valid_records: int
    invalid_records: int
    quality_score: float
    quality_level: DataQuality
    validation_results: List[Dict[str, Any]]
    anomalies_detected: List[Dict[str, Any]]
    recommendations: List[str]
    timestamp: datetime


class MarineDataValidator:
    """AI-powered marine conservation data validator"""
    
    def __init__(self):
        self.validation_rules = self._initialize_validation_rules()
        self.quality_thresholds = {
            DataQuality.EXCELLENT: 0.95,
            DataQuality.GOOD: 0.85,
            DataQuality.FAIR: 0.70,
            DataQuality.POOR: 0.50,
            DataQuality.INVALID: 0.0
        }
    
    def _initialize_validation_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize validation rules for different data types"""
        return {
            'marine_debris': {
                'confidence_min': 0.1,
                'confidence_max': 1.0,
                'size_min': 0.1,  # square meters
                'size_max': 10000.0,
                'lat_range': (-90, 90),
                'lon_range': (-180, 180)
            },
            'ocean_current': {
                'speed_max': 5.0,  # m/s
                'direction_range': (0, 360),
                'lat_range': (-90, 90),
                'lon_range': (-180, 180)
            },
            'water_temperature': {
                'temp_range': (-2, 35),  # Celsius
                'lat_range': (-90, 90),
                'lon_range': (-180, 180)
            },
            'oceanographic': {
                'sst_range': (-2, 35),
                'salinity_range': (0, 50),
                'current_speed_max': 5.0,
                'lat_range': (-90, 90),
                'lon_range': (-180, 180)
            },
            'vessel_data': {
                'speed_max': 50.0,  # knots
                'course_range': (0, 360),
                'length_range': (1, 500),  # meters
                'lat_range': (-90, 90),
                'lon_range': (-180, 180)
            },
            'satellite_imagery': {
                'cloud_coverage_max': 1.0,
                'resolution_min': 0.1,  # meters
                'resolution_max': 1000.0,
                'lat_range': (-90, 90),
                'lon_range': (-180, 180)
            }
        }
    
    async def validate_marine_debris_data(
        self, 
        debris_data: List[MarineDebrisDetection]
    ) -> ValidationReport:
        """Validate marine debris detection data"""
        validation_results = []
        anomalies = []
        valid_count = 0
        
        rules = self.validation_rules['marine_debris']
        
        for i, debris in enumerate(debris_data):
            result = {
                'record_id': i,
                'checks': {},
                'overall_result': ValidationResult.PASS
            }
            
            # Validate confidence
            if not (rules['confidence_min'] <= debris.confidence <= rules['confidence_max']):
                result['checks']['confidence'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
                anomalies.append({
                    'record_id': i,
                    'type': 'invalid_confidence',
                    'value': debris.confidence,
                    'expected_range': (rules['confidence_min'], rules['confidence_max'])
                })
            else:
                result['checks']['confidence'] = ValidationResult.PASS
            
            # Validate coordinates
            lat, lon = debris.location
            if not (rules['lat_range'][0] <= lat <= rules['lat_range'][1]):
                result['checks']['latitude'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['latitude'] = ValidationResult.PASS
            
            if not (rules['lon_range'][0] <= lon <= rules['lon_range'][1]):
                result['checks']['longitude'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['longitude'] = ValidationResult.PASS
            
            # Validate size estimate
            if not (rules['size_min'] <= debris.size_estimate <= rules['size_max']):
                result['checks']['size'] = ValidationResult.WARNING
                if result['overall_result'] == ValidationResult.PASS:
                    result['overall_result'] = ValidationResult.WARNING
            else:
                result['checks']['size'] = ValidationResult.PASS
            
            # Check for temporal anomalies
            if debris.timestamp > datetime.now():
                result['checks']['timestamp'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
                anomalies.append({
                    'record_id': i,
                    'type': 'future_timestamp',
                    'value': debris.timestamp.isoformat()
                })
            else:
                result['checks']['timestamp'] = ValidationResult.PASS
            
            if result['overall_result'] in [ValidationResult.PASS, ValidationResult.WARNING]:
                valid_count += 1
            
            validation_results.append(result)
        
        # Calculate quality score
        quality_score = valid_count / len(debris_data) if debris_data else 0.0
        quality_level = self._determine_quality_level(quality_score)
        
        # Generate recommendations
        recommendations = self._generate_debris_recommendations(validation_results, anomalies)
        
        return ValidationReport(
            data_source="Marine Debris Detection",
            data_type="marine_debris",
            total_records=len(debris_data),
            valid_records=valid_count,
            invalid_records=len(debris_data) - valid_count,
            quality_score=quality_score,
            quality_level=quality_level,
            validation_results=validation_results,
            anomalies_detected=anomalies,
            recommendations=recommendations,
            timestamp=datetime.now()
        )
    
    async def validate_oceanographic_data(
        self, 
        ocean_data: List[OceanographicData]
    ) -> ValidationReport:
        """Validate oceanographic data"""
        validation_results = []
        anomalies = []
        valid_count = 0
        
        rules = self.validation_rules['oceanographic']
        
        for i, data in enumerate(ocean_data):
            result = {
                'record_id': i,
                'checks': {},
                'overall_result': ValidationResult.PASS
            }
            
            # Validate SST
            if data.sea_surface_temperature is not None:
                if not (rules['sst_range'][0] <= data.sea_surface_temperature <= rules['sst_range'][1]):
                    result['checks']['sst'] = ValidationResult.FAIL
                    result['overall_result'] = ValidationResult.FAIL
                    anomalies.append({
                        'record_id': i,
                        'type': 'invalid_sst',
                        'value': data.sea_surface_temperature,
                        'expected_range': rules['sst_range']
                    })
                else:
                    result['checks']['sst'] = ValidationResult.PASS
            
            # Validate salinity
            if data.salinity is not None:
                if not (rules['salinity_range'][0] <= data.salinity <= rules['salinity_range'][1]):
                    result['checks']['salinity'] = ValidationResult.FAIL
                    result['overall_result'] = ValidationResult.FAIL
                else:
                    result['checks']['salinity'] = ValidationResult.PASS
            
            # Validate current speed
            if data.current_speed is not None:
                if data.current_speed > rules['current_speed_max']:
                    result['checks']['current_speed'] = ValidationResult.WARNING
                    if result['overall_result'] == ValidationResult.PASS:
                        result['overall_result'] = ValidationResult.WARNING
                else:
                    result['checks']['current_speed'] = ValidationResult.PASS
            
            # Validate coordinates
            if not (rules['lat_range'][0] <= data.latitude <= rules['lat_range'][1]):
                result['checks']['latitude'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['latitude'] = ValidationResult.PASS
            
            if not (rules['lon_range'][0] <= data.longitude <= rules['lon_range'][1]):
                result['checks']['longitude'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['longitude'] = ValidationResult.PASS
            
            if result['overall_result'] in [ValidationResult.PASS, ValidationResult.WARNING]:
                valid_count += 1
            
            validation_results.append(result)
        
        quality_score = valid_count / len(ocean_data) if ocean_data else 0.0
        quality_level = self._determine_quality_level(quality_score)
        
        recommendations = self._generate_oceanographic_recommendations(validation_results, anomalies)
        
        return ValidationReport(
            data_source="Oceanographic Data",
            data_type="oceanographic",
            total_records=len(ocean_data),
            valid_records=valid_count,
            invalid_records=len(ocean_data) - valid_count,
            quality_score=quality_score,
            quality_level=quality_level,
            validation_results=validation_results,
            anomalies_detected=anomalies,
            recommendations=recommendations,
            timestamp=datetime.now()
        )
    
    async def validate_vessel_data(
        self, 
        vessel_data: List[VesselData]
    ) -> ValidationReport:
        """Validate AIS vessel data"""
        validation_results = []
        anomalies = []
        valid_count = 0
        
        rules = self.validation_rules['vessel_data']
        
        for i, vessel in enumerate(vessel_data):
            result = {
                'record_id': i,
                'checks': {},
                'overall_result': ValidationResult.PASS
            }
            
            # Validate speed
            if vessel.speed > rules['speed_max']:
                result['checks']['speed'] = ValidationResult.WARNING
                if result['overall_result'] == ValidationResult.PASS:
                    result['overall_result'] = ValidationResult.WARNING
                anomalies.append({
                    'record_id': i,
                    'type': 'high_speed',
                    'value': vessel.speed,
                    'threshold': rules['speed_max']
                })
            else:
                result['checks']['speed'] = ValidationResult.PASS
            
            # Validate course
            if not (rules['course_range'][0] <= vessel.course <= rules['course_range'][1]):
                result['checks']['course'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['course'] = ValidationResult.PASS
            
            # Validate vessel dimensions
            if vessel.length and not (rules['length_range'][0] <= vessel.length <= rules['length_range'][1]):
                result['checks']['length'] = ValidationResult.WARNING
                if result['overall_result'] == ValidationResult.PASS:
                    result['overall_result'] = ValidationResult.WARNING
            else:
                result['checks']['length'] = ValidationResult.PASS
            
            # Validate coordinates
            if not (rules['lat_range'][0] <= vessel.latitude <= rules['lat_range'][1]):
                result['checks']['latitude'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['latitude'] = ValidationResult.PASS
            
            if not (rules['lon_range'][0] <= vessel.longitude <= rules['lon_range'][1]):
                result['checks']['longitude'] = ValidationResult.FAIL
                result['overall_result'] = ValidationResult.FAIL
            else:
                result['checks']['longitude'] = ValidationResult.PASS
            
            # Validate MMSI format
            if not vessel.mmsi.isdigit() or len(vessel.mmsi) != 9:
                result['checks']['mmsi'] = ValidationResult.WARNING
                if result['overall_result'] == ValidationResult.PASS:
                    result['overall_result'] = ValidationResult.WARNING
            else:
                result['checks']['mmsi'] = ValidationResult.PASS
            
            if result['overall_result'] in [ValidationResult.PASS, ValidationResult.WARNING]:
                valid_count += 1
            
            validation_results.append(result)
        
        quality_score = valid_count / len(vessel_data) if vessel_data else 0.0
        quality_level = self._determine_quality_level(quality_score)
        
        recommendations = self._generate_vessel_recommendations(validation_results, anomalies)
        
        return ValidationReport(
            data_source="AIS Vessel Data",
            data_type="vessel_data",
            total_records=len(vessel_data),
            valid_records=valid_count,
            invalid_records=len(vessel_data) - valid_count,
            quality_score=quality_score,
            quality_level=quality_level,
            validation_results=validation_results,
            anomalies_detected=anomalies,
            recommendations=recommendations,
            timestamp=datetime.now()
        )
    
    async def validate_comprehensive_dataset(
        self,
        dataset: Dict[str, Any]
    ) -> Dict[str, ValidationReport]:
        """Validate comprehensive marine conservation dataset"""
        validation_reports = {}
        
        # Validate different data types
        if 'marine_debris' in dataset:
            validation_reports['marine_debris'] = await self.validate_marine_debris_data(
                dataset['marine_debris']
            )
        
        if 'oceanographic' in dataset:
            validation_reports['oceanographic'] = await self.validate_oceanographic_data(
                dataset['oceanographic']
            )
        
        if 'vessels' in dataset:
            validation_reports['vessels'] = await self.validate_vessel_data(
                dataset['vessels']
            )
        
        # Cross-validation checks
        cross_validation = await self._perform_cross_validation(dataset)
        if cross_validation:
            validation_reports['cross_validation'] = cross_validation
        
        return validation_reports
    
    async def _perform_cross_validation(self, dataset: Dict[str, Any]) -> Optional[ValidationReport]:
        """Perform cross-validation between different data sources"""
        anomalies = []
        recommendations = []
        
        # Check temporal consistency
        timestamps = []
        for data_type, data_list in dataset.items():
            if isinstance(data_list, list) and data_list:
                for item in data_list:
                    if hasattr(item, 'timestamp'):
                        timestamps.append(item.timestamp)
        
        if timestamps:
            time_range = max(timestamps) - min(timestamps)
            if time_range > timedelta(days=7):
                anomalies.append({
                    'type': 'large_temporal_range',
                    'range_days': time_range.days,
                    'recommendation': 'Consider filtering data to smaller time window'
                })
        
        # Check spatial consistency
        locations = []
        for data_type, data_list in dataset.items():
            if isinstance(data_list, list) and data_list:
                for item in data_list:
                    if hasattr(item, 'latitude') and hasattr(item, 'longitude'):
                        locations.append((item.latitude, item.longitude))
        
        if len(locations) > 1:
            # Check for outlier locations
            lats = [loc[0] for loc in locations]
            lons = [loc[1] for loc in locations]
            
            lat_std = np.std(lats)
            lon_std = np.std(lons)
            
            if lat_std > 10 or lon_std > 10:  # Large geographic spread
                anomalies.append({
                    'type': 'large_spatial_spread',
                    'lat_std': lat_std,
                    'lon_std': lon_std,
                    'recommendation': 'Verify geographic consistency of data sources'
                })
        
        if anomalies:
            return ValidationReport(
                data_source="Cross-Validation",
                data_type="cross_validation",
                total_records=len(dataset),
                valid_records=len(dataset) - len(anomalies),
                invalid_records=len(anomalies),
                quality_score=1.0 - (len(anomalies) / len(dataset)),
                quality_level=self._determine_quality_level(1.0 - (len(anomalies) / len(dataset))),
                validation_results=[],
                anomalies_detected=anomalies,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
        
        return None
    
    def _determine_quality_level(self, score: float) -> DataQuality:
        """Determine quality level based on score"""
        for quality, threshold in self.quality_thresholds.items():
            if score >= threshold:
                return quality
        return DataQuality.INVALID
    
    def _generate_debris_recommendations(
        self, 
        validation_results: List[Dict], 
        anomalies: List[Dict]
    ) -> List[str]:
        """Generate recommendations for marine debris data"""
        recommendations = []
        
        confidence_failures = sum(1 for r in validation_results 
                                 if r['checks'].get('confidence') == ValidationResult.FAIL)
        if confidence_failures > 0:
            recommendations.append(
                f"Review confidence thresholds - {confidence_failures} records have invalid confidence values"
            )
        
        size_warnings = sum(1 for r in validation_results 
                           if r['checks'].get('size') == ValidationResult.WARNING)
        if size_warnings > 0:
            recommendations.append(
                f"Verify size estimates - {size_warnings} records have unusual size values"
            )
        
        if len(anomalies) > len(validation_results) * 0.1:
            recommendations.append("High anomaly rate detected - consider data source quality review")
        
        return recommendations
    
    def _generate_oceanographic_recommendations(
        self, 
        validation_results: List[Dict], 
        anomalies: List[Dict]
    ) -> List[str]:
        """Generate recommendations for oceanographic data"""
        recommendations = []
        
        sst_failures = sum(1 for r in validation_results 
                          if r['checks'].get('sst') == ValidationResult.FAIL)
        if sst_failures > 0:
            recommendations.append(
                f"Check SST sensor calibration - {sst_failures} records have invalid temperature values"
            )
        
        current_warnings = sum(1 for r in validation_results 
                              if r['checks'].get('current_speed') == ValidationResult.WARNING)
        if current_warnings > 0:
            recommendations.append(
                f"Review current speed measurements - {current_warnings} records show unusually high values"
            )
        
        return recommendations
    
    def _generate_vessel_recommendations(
        self, 
        validation_results: List[Dict], 
        anomalies: List[Dict]
    ) -> List[str]:
        """Generate recommendations for vessel data"""
        recommendations = []
        
        speed_warnings = sum(1 for r in validation_results 
                            if r['checks'].get('speed') == ValidationResult.WARNING)
        if speed_warnings > 0:
            recommendations.append(
                f"Investigate high-speed vessels - {speed_warnings} records show unusual speeds"
            )
        
        mmsi_warnings = sum(1 for r in validation_results 
                           if r['checks'].get('mmsi') == ValidationResult.WARNING)
        if mmsi_warnings > 0:
            recommendations.append(
                f"Verify MMSI format - {mmsi_warnings} records have invalid MMSI numbers"
            )
        
        return recommendations


# Convenience function
async def validate_marine_conservation_data(dataset: Dict[str, Any]) -> Dict[str, ValidationReport]:
    """Validate comprehensive marine conservation dataset"""
    validator = MarineDataValidator()
    return await validator.validate_comprehensive_dataset(dataset)


if __name__ == "__main__":
    async def test_validation():
        print("🔍 Testing Marine Data Validation System")
        
        # Create sample data for testing
        from .apis.sentinel_hub_api import MarineDebrisDetection
        from .apis.aisstream_api import VesselData
        
        sample_debris = [
            MarineDebrisDetection(
                location=(40.0, -70.0),
                confidence=0.85,
                debris_type="plastic",
                size_estimate=25.0,
                timestamp=datetime.now()
            )
        ]
        
        sample_vessels = [
            VesselData(
                mmsi="123456789",
                vessel_name="Test Vessel",
                latitude=40.1,
                longitude=-70.1,
                timestamp=datetime.now(),
                speed=12.5,
                course=45.0,
                vessel_type="cargo",
                length=200.0,
                width=30.0
            )
        ]
        
        dataset = {
            'marine_debris': sample_debris,
            'vessels': sample_vessels
        }
        
        try:
            reports = await validate_marine_conservation_data(dataset)
            
            print("✅ Validation completed")
            for data_type, report in reports.items():
                print(f"   {data_type}: {report.quality_level.value} "
                      f"({report.quality_score:.2f}) - "
                      f"{report.valid_records}/{report.total_records} valid")
                
        except Exception as e:
            print(f"❌ Validation failed: {e}")
    
    asyncio.run(test_validation())
