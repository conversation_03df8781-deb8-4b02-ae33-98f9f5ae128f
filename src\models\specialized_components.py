"""
Specialized Water Treatment Components and Industrial Configurations.

Advanced component modeling for specialized treatment technologies,
industrial-specific configurations, and custom component libraries.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
import asyncio
from abc import ABC, abstractmethod

from src.models.treatment_components import ComponentType, WaterTreatmentComponent
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class IndustryType(Enum):
    """Industrial sector types."""
    MUNICIPAL = "municipal"
    PHARMACEUTICAL = "pharmaceutical"
    FOOD_BEVERAGE = "food_beverage"
    CHEMICAL = "chemical"
    TEXTILE = "textile"
    MINING = "mining"
    POWER_GENERATION = "power_generation"
    SEMICONDUCTOR = "semiconductor"
    PULP_PAPER = "pulp_paper"
    OIL_GAS = "oil_gas"


class TreatmentTechnology(Enum):
    """Advanced treatment technologies."""
    MEMBRANE_BIOREACTOR = "membrane_bioreactor"
    REVERSE_OSMOSIS = "reverse_osmosis"
    ELECTROCOAGULATION = "electrocoagulation"
    ADVANCED_OXIDATION = "advanced_oxidation"
    ION_EXCHANGE = "ion_exchange"
    ACTIVATED_CARBON = "activated_carbon"
    OZONATION = "ozonation"
    ELECTROCHEMICAL = "electrochemical"
    CRYSTALLIZATION = "crystallization"
    EVAPORATION = "evaporation"


@dataclass
class SpecializedComponentSpec:
    """Specification for specialized components."""
    component_id: str
    technology: TreatmentTechnology
    industry_applications: List[IndustryType]
    capacity_range: Tuple[float, float]  # Min, max capacity
    efficiency_range: Tuple[float, float]  # Min, max efficiency
    energy_consumption_range: Tuple[float, float]  # kWh/m³
    chemical_requirements: Dict[str, float]
    operating_conditions: Dict[str, Tuple[float, float]]  # Parameter: (min, max)
    capital_cost_range: Tuple[float, float]
    operational_cost_range: Tuple[float, float]
    maintenance_requirements: Dict[str, Any]
    environmental_impact: Dict[str, float]
    regulatory_compliance: List[str]


class AdvancedTreatmentComponent(WaterTreatmentComponent):
    """Advanced treatment component with specialized capabilities."""
    
    def __init__(self, spec: SpecializedComponentSpec):
        # Convert to base component specification
        from src.models.treatment_components import ComponentSpecification
        
        base_spec = ComponentSpecification(
            component_id=spec.component_id,
            component_type=ComponentType.ADVANCED,
            name=f"Advanced {spec.technology.value.replace('_', ' ').title()}",
            capacity=np.mean(spec.capacity_range),
            efficiency=np.mean(spec.efficiency_range),
            energy_consumption=np.mean(spec.energy_consumption_range),
            chemical_consumption=spec.chemical_requirements,
            maintenance_interval=spec.maintenance_requirements.get('interval', 30),
            lifespan=spec.maintenance_requirements.get('lifespan', 15),
            capital_cost=np.mean(spec.capital_cost_range),
            operational_cost=np.mean(spec.operational_cost_range)
        )
        
        super().__init__(base_spec)
        self.specialized_spec = spec
        self.technology = spec.technology
        self.industry_applications = spec.industry_applications
    
    async def calculate_specialized_performance(self, operating_conditions: Dict[str, Any],
                                              industry_context: IndustryType) -> Dict[str, Any]:
        """Calculate performance with industry-specific considerations."""
        try:
            # Base performance calculation
            base_performance = self.calculate_performance(operating_conditions)
            
            # Industry-specific adjustments
            industry_factors = await self._get_industry_factors(industry_context)
            
            # Apply industry adjustments
            adjusted_performance = self._apply_industry_adjustments(
                base_performance, industry_factors, operating_conditions
            )
            
            # Calculate specialized metrics
            specialized_metrics = await self._calculate_specialized_metrics(
                operating_conditions, industry_context
            )
            
            # Combine results
            return {
                **adjusted_performance,
                'specialized_metrics': specialized_metrics,
                'industry_context': industry_context.value,
                'technology': self.technology.value,
                'compliance_status': await self._check_regulatory_compliance(
                    adjusted_performance, industry_context
                )
            }
            
        except Exception as e:
            logger.error(f"Specialized performance calculation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _get_industry_factors(self, industry: IndustryType) -> Dict[str, float]:
        """Get industry-specific performance factors."""
        industry_factors = {
            IndustryType.MUNICIPAL: {
                'efficiency_factor': 1.0,
                'energy_factor': 1.0,
                'maintenance_factor': 1.0,
                'quality_requirement': 0.85
            },
            IndustryType.PHARMACEUTICAL: {
                'efficiency_factor': 1.2,
                'energy_factor': 1.3,
                'maintenance_factor': 0.8,
                'quality_requirement': 0.99
            },
            IndustryType.FOOD_BEVERAGE: {
                'efficiency_factor': 1.1,
                'energy_factor': 1.1,
                'maintenance_factor': 0.9,
                'quality_requirement': 0.95
            },
            IndustryType.CHEMICAL: {
                'efficiency_factor': 1.15,
                'energy_factor': 1.25,
                'maintenance_factor': 0.7,
                'quality_requirement': 0.92
            },
            IndustryType.SEMICONDUCTOR: {
                'efficiency_factor': 1.3,
                'energy_factor': 1.4,
                'maintenance_factor': 0.6,
                'quality_requirement': 0.999
            }
        }
        
        return industry_factors.get(industry, industry_factors[IndustryType.MUNICIPAL])
    
    def _apply_industry_adjustments(self, base_performance: Dict[str, Any],
                                  industry_factors: Dict[str, float],
                                  operating_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Apply industry-specific adjustments to performance."""
        adjusted = base_performance.copy()
        
        # Adjust efficiency
        if 'efficiency' in adjusted:
            adjusted['efficiency'] *= industry_factors.get('efficiency_factor', 1.0)
            adjusted['efficiency'] = min(0.999, adjusted['efficiency'])  # Cap at 99.9%
        
        # Adjust energy consumption
        if 'energy_consumption' in adjusted:
            adjusted['energy_consumption'] *= industry_factors.get('energy_factor', 1.0)
        
        # Adjust maintenance requirements
        if 'maintenance_factor' in adjusted:
            adjusted['maintenance_factor'] *= industry_factors.get('maintenance_factor', 1.0)
        
        return adjusted
    
    async def _calculate_specialized_metrics(self, operating_conditions: Dict[str, Any],
                                           industry: IndustryType) -> Dict[str, Any]:
        """Calculate technology and industry-specific metrics."""
        metrics = {}
        
        # Technology-specific metrics
        if self.technology == TreatmentTechnology.MEMBRANE_BIOREACTOR:
            metrics.update(await self._mbr_specific_metrics(operating_conditions))
        elif self.technology == TreatmentTechnology.REVERSE_OSMOSIS:
            metrics.update(await self._ro_specific_metrics(operating_conditions))
        elif self.technology == TreatmentTechnology.ADVANCED_OXIDATION:
            metrics.update(await self._aop_specific_metrics(operating_conditions))
        elif self.technology == TreatmentTechnology.ELECTROCOAGULATION:
            metrics.update(await self._ec_specific_metrics(operating_conditions))
        
        # Industry-specific metrics
        if industry == IndustryType.PHARMACEUTICAL:
            metrics.update(await self._pharma_specific_metrics(operating_conditions))
        elif industry == IndustryType.SEMICONDUCTOR:
            metrics.update(await self._semiconductor_specific_metrics(operating_conditions))
        
        return metrics
    
    async def _mbr_specific_metrics(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate MBR-specific metrics."""
        flow_rate = conditions.get('flow_rate', 1000)
        mlss = conditions.get('mlss', 8000)  # Mixed liquor suspended solids
        
        return {
            'membrane_flux': flow_rate / 100,  # L/m²/h
            'transmembrane_pressure': 0.2 + (mlss / 10000) * 0.3,  # bar
            'sludge_retention_time': 20 + (mlss / 1000),  # days
            'biogas_production': flow_rate * 0.3,  # m³/day
            'membrane_cleaning_frequency': max(1, 30 - (mlss / 500))  # days
        }
    
    async def _ro_specific_metrics(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate RO-specific metrics."""
        flow_rate = conditions.get('flow_rate', 1000)
        tds = conditions.get('tds', 500)  # Total dissolved solids
        
        return {
            'recovery_rate': max(0.6, 0.9 - (tds / 10000)),  # %
            'salt_rejection': min(0.99, 0.95 + (tds / 50000)),  # %
            'operating_pressure': 10 + (tds / 100),  # bar
            'concentrate_volume': flow_rate * (1 - max(0.6, 0.9 - (tds / 10000))),
            'membrane_replacement_interval': max(12, 36 - (tds / 200))  # months
        }
    
    async def _aop_specific_metrics(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Advanced Oxidation Process metrics."""
        flow_rate = conditions.get('flow_rate', 1000)
        cod = conditions.get('cod', 100)  # Chemical oxygen demand
        
        return {
            'hydroxyl_radical_exposure': cod * 0.1,  # mg·min/L
            'ozone_consumption': cod * 2.5,  # mg O₃/mg COD
            'hydrogen_peroxide_dose': cod * 1.5,  # mg/L
            'uv_dose': 40 + (cod / 10),  # mJ/cm²
            'mineralization_rate': min(0.8, 0.4 + (cod / 500))  # %
        }
    
    async def _ec_specific_metrics(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Electrocoagulation metrics."""
        flow_rate = conditions.get('flow_rate', 1000)
        conductivity = conditions.get('conductivity', 1000)  # µS/cm
        
        return {
            'current_density': 20 + (conductivity / 100),  # A/m²
            'electrode_consumption': flow_rate * 0.1,  # kg/day
            'power_consumption': (20 + conductivity / 100) * 5,  # kWh/m³
            'sludge_production': flow_rate * 0.02,  # kg/m³
            'electrode_replacement_interval': max(6, 24 - (conductivity / 500))  # months
        }
    
    async def _pharma_specific_metrics(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate pharmaceutical industry metrics."""
        return {
            'api_removal_efficiency': 0.995,  # Active pharmaceutical ingredients
            'endocrine_disruptor_removal': 0.98,
            'antibiotic_resistance_reduction': 0.99,
            'sterility_assurance_level': 6,  # Log reduction
            'pyrogen_removal': 0.999
        }
    
    async def _semiconductor_specific_metrics(self, conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate semiconductor industry metrics."""
        return {
            'ultrapure_water_quality': 18.2,  # MΩ·cm resistivity
            'particle_removal_efficiency': 0.9999,  # >0.1 µm particles
            'toc_level': 1.0,  # ppb total organic carbon
            'bacteria_level': 0.1,  # CFU/mL
            'silica_content': 0.5  # ppb
        }
    
    async def _check_regulatory_compliance(self, performance: Dict[str, Any],
                                         industry: IndustryType) -> Dict[str, Any]:
        """Check regulatory compliance for industry."""
        compliance_status = {}
        
        # Industry-specific regulations
        if industry == IndustryType.PHARMACEUTICAL:
            compliance_status.update({
                'fda_compliance': performance.get('efficiency', 0) > 0.99,
                'ich_guidelines': True,
                'usp_water_standards': performance.get('specialized_metrics', {}).get('toc_level', 10) < 5
            })
        elif industry == IndustryType.FOOD_BEVERAGE:
            compliance_status.update({
                'fda_food_safety': performance.get('efficiency', 0) > 0.95,
                'haccp_compliance': True,
                'who_guidelines': True
            })
        elif industry == IndustryType.MUNICIPAL:
            compliance_status.update({
                'epa_standards': performance.get('efficiency', 0) > 0.85,
                'who_guidelines': True,
                'local_regulations': True
            })
        
        overall_compliance = all(compliance_status.values())
        
        return {
            'overall_compliance': overall_compliance,
            'specific_compliance': compliance_status,
            'compliance_score': sum(compliance_status.values()) / len(compliance_status) if compliance_status else 1.0
        }


class IndustrialSystemConfigurator:
    """Configure water treatment systems for specific industries."""
    
    def __init__(self):
        self.industry_requirements = {
            IndustryType.PHARMACEUTICAL: {
                'water_quality_requirements': {
                    'conductivity': 1.3,  # µS/cm
                    'toc': 0.5,  # mg/L
                    'bacteria': 10,  # CFU/100mL
                    'endotoxin': 0.25  # EU/mL
                },
                'preferred_technologies': [
                    TreatmentTechnology.REVERSE_OSMOSIS,
                    TreatmentTechnology.ION_EXCHANGE,
                    TreatmentTechnology.OZONATION
                ],
                'regulatory_standards': ['FDA', 'ICH', 'USP'],
                'redundancy_requirements': 'high'
            },
            IndustryType.SEMICONDUCTOR: {
                'water_quality_requirements': {
                    'resistivity': 18.2,  # MΩ·cm
                    'toc': 1.0,  # ppb
                    'particles': 0.1,  # particles/mL >0.1µm
                    'bacteria': 0.1,  # CFU/mL
                    'silica': 0.5  # ppb
                },
                'preferred_technologies': [
                    TreatmentTechnology.REVERSE_OSMOSIS,
                    TreatmentTechnology.ION_EXCHANGE,
                    TreatmentTechnology.ELECTROCHEMICAL
                ],
                'regulatory_standards': ['SEMI', 'ASTM'],
                'redundancy_requirements': 'critical'
            },
            IndustryType.FOOD_BEVERAGE: {
                'water_quality_requirements': {
                    'turbidity': 0.1,  # NTU
                    'chlorine': 0.2,  # mg/L
                    'bacteria': 0,  # CFU/100mL
                    'ph': (6.5, 8.5)
                },
                'preferred_technologies': [
                    TreatmentTechnology.ACTIVATED_CARBON,
                    TreatmentTechnology.OZONATION,
                    TreatmentTechnology.MEMBRANE_BIOREACTOR
                ],
                'regulatory_standards': ['FDA', 'HACCP', 'WHO'],
                'redundancy_requirements': 'medium'
            }
        }
    
    @log_async_function_call
    async def configure_industrial_system(self, industry: IndustryType,
                                        capacity: float,
                                        specific_requirements: Dict[str, Any] = None) -> Dict[str, Any]:
        """Configure a complete treatment system for specific industry."""
        try:
            logger.info(f"Configuring system for {industry.value} industry")
            
            # Get industry requirements
            requirements = self.industry_requirements.get(industry, {})
            if specific_requirements:
                requirements.update(specific_requirements)
            
            # Select appropriate technologies
            selected_technologies = await self._select_technologies(industry, requirements, capacity)
            
            # Design system configuration
            system_config = await self._design_system_configuration(
                selected_technologies, requirements, capacity
            )
            
            # Calculate system performance
            system_performance = await self._calculate_system_performance(
                system_config, industry
            )
            
            # Generate compliance assessment
            compliance_assessment = await self._assess_system_compliance(
                system_config, industry, requirements
            )
            
            # Calculate costs
            cost_analysis = await self._calculate_system_costs(system_config, industry)
            
            return {
                'status': 'success',
                'industry': industry.value,
                'capacity': capacity,
                'system_configuration': system_config,
                'performance_metrics': system_performance,
                'compliance_assessment': compliance_assessment,
                'cost_analysis': cost_analysis,
                'design_timestamp': pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Industrial system configuration failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _select_technologies(self, industry: IndustryType,
                                 requirements: Dict[str, Any],
                                 capacity: float) -> List[TreatmentTechnology]:
        """Select appropriate technologies for the industry."""
        preferred_techs = requirements.get('preferred_technologies', [])
        
        # Add capacity-based selections
        if capacity > 10000:  # Large scale
            if TreatmentTechnology.MEMBRANE_BIOREACTOR not in preferred_techs:
                preferred_techs.append(TreatmentTechnology.MEMBRANE_BIOREACTOR)
        
        if capacity < 1000:  # Small scale
            if TreatmentTechnology.ELECTROCOAGULATION not in preferred_techs:
                preferred_techs.append(TreatmentTechnology.ELECTROCOAGULATION)
        
        # Ensure minimum treatment train
        if len(preferred_techs) < 3:
            additional_techs = [
                TreatmentTechnology.ACTIVATED_CARBON,
                TreatmentTechnology.OZONATION,
                TreatmentTechnology.ION_EXCHANGE
            ]
            for tech in additional_techs:
                if tech not in preferred_techs and len(preferred_techs) < 3:
                    preferred_techs.append(tech)
        
        return preferred_techs[:5]  # Limit to 5 technologies
    
    async def _design_system_configuration(self, technologies: List[TreatmentTechnology],
                                         requirements: Dict[str, Any],
                                         capacity: float) -> Dict[str, Any]:
        """Design the complete system configuration."""
        configuration = {
            'treatment_train': [],
            'redundancy_level': requirements.get('redundancy_requirements', 'medium'),
            'automation_level': 'high',
            'monitoring_systems': [],
            'backup_systems': []
        }
        
        # Design treatment train
        for i, tech in enumerate(technologies):
            component_config = {
                'stage': i + 1,
                'technology': tech.value,
                'capacity': capacity,
                'redundancy': self._get_redundancy_config(
                    tech, requirements.get('redundancy_requirements', 'medium')
                ),
                'monitoring': self._get_monitoring_config(tech),
                'automation': self._get_automation_config(tech)
            }
            configuration['treatment_train'].append(component_config)
        
        # Add monitoring systems
        configuration['monitoring_systems'] = [
            'real_time_quality_monitoring',
            'flow_monitoring',
            'pressure_monitoring',
            'energy_monitoring',
            'chemical_monitoring'
        ]
        
        # Add backup systems based on redundancy requirements
        redundancy_level = requirements.get('redundancy_requirements', 'medium')
        if redundancy_level in ['high', 'critical']:
            configuration['backup_systems'] = [
                'backup_power_supply',
                'emergency_treatment_bypass',
                'backup_chemical_dosing'
            ]
            
            if redundancy_level == 'critical':
                configuration['backup_systems'].extend([
                    'redundant_control_systems',
                    'emergency_water_storage',
                    'backup_monitoring_systems'
                ])
        
        return configuration
    
    def _get_redundancy_config(self, tech: TreatmentTechnology, level: str) -> Dict[str, Any]:
        """Get redundancy configuration for technology."""
        redundancy_configs = {
            'low': {'parallel_units': 1, 'standby_units': 0},
            'medium': {'parallel_units': 2, 'standby_units': 1},
            'high': {'parallel_units': 3, 'standby_units': 1},
            'critical': {'parallel_units': 4, 'standby_units': 2}
        }
        
        return redundancy_configs.get(level, redundancy_configs['medium'])
    
    def _get_monitoring_config(self, tech: TreatmentTechnology) -> List[str]:
        """Get monitoring configuration for technology."""
        base_monitoring = ['flow_rate', 'pressure', 'temperature']
        
        tech_specific = {
            TreatmentTechnology.REVERSE_OSMOSIS: ['conductivity', 'recovery_rate', 'membrane_pressure'],
            TreatmentTechnology.MEMBRANE_BIOREACTOR: ['mlss', 'transmembrane_pressure', 'biogas_flow'],
            TreatmentTechnology.OZONATION: ['ozone_concentration', 'ct_value', 'residual_ozone'],
            TreatmentTechnology.ION_EXCHANGE: ['conductivity', 'regeneration_cycle', 'breakthrough'],
            TreatmentTechnology.ACTIVATED_CARBON: ['breakthrough_curve', 'bed_depth', 'contact_time']
        }
        
        return base_monitoring + tech_specific.get(tech, [])
    
    def _get_automation_config(self, tech: TreatmentTechnology) -> Dict[str, Any]:
        """Get automation configuration for technology."""
        return {
            'control_level': 'automatic',
            'feedback_control': True,
            'predictive_control': True,
            'remote_monitoring': True,
            'alarm_systems': True
        }
    
    async def _calculate_system_performance(self, config: Dict[str, Any],
                                          industry: IndustryType) -> Dict[str, Any]:
        """Calculate overall system performance."""
        # Simplified performance calculation
        treatment_stages = len(config.get('treatment_train', []))
        redundancy_factor = 1.1 if config.get('redundancy_level') == 'high' else 1.0
        
        overall_efficiency = min(0.999, 0.8 + (treatment_stages * 0.03)) * redundancy_factor
        energy_consumption = treatment_stages * 0.5 + (redundancy_factor - 1) * 2.0
        
        return {
            'overall_efficiency': overall_efficiency,
            'energy_consumption_kwh_per_m3': energy_consumption,
            'treatment_stages': treatment_stages,
            'redundancy_factor': redundancy_factor,
            'estimated_uptime': 0.995 if redundancy_factor > 1.0 else 0.98,
            'quality_assurance_level': self._get_quality_assurance_level(industry)
        }
    
    def _get_quality_assurance_level(self, industry: IndustryType) -> float:
        """Get quality assurance level for industry."""
        qa_levels = {
            IndustryType.PHARMACEUTICAL: 0.999,
            IndustryType.SEMICONDUCTOR: 0.9999,
            IndustryType.FOOD_BEVERAGE: 0.995,
            IndustryType.CHEMICAL: 0.99,
            IndustryType.MUNICIPAL: 0.95
        }
        
        return qa_levels.get(industry, 0.95)
    
    async def _assess_system_compliance(self, config: Dict[str, Any],
                                      industry: IndustryType,
                                      requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Assess system compliance with regulations."""
        standards = requirements.get('regulatory_standards', [])
        
        compliance_assessment = {
            'overall_compliance': True,
            'standard_compliance': {},
            'compliance_score': 1.0,
            'non_compliance_issues': []
        }
        
        for standard in standards:
            # Simplified compliance check
            compliance_assessment['standard_compliance'][standard] = True
        
        return compliance_assessment
    
    async def _calculate_system_costs(self, config: Dict[str, Any],
                                    industry: IndustryType) -> Dict[str, Any]:
        """Calculate system costs."""
        treatment_stages = len(config.get('treatment_train', []))
        redundancy_level = config.get('redundancy_level', 'medium')
        
        # Base costs
        base_capital_cost = treatment_stages * 100000  # $100k per stage
        base_operational_cost = treatment_stages * 10000  # $10k per stage annually
        
        # Industry factors
        industry_factors = {
            IndustryType.PHARMACEUTICAL: 2.0,
            IndustryType.SEMICONDUCTOR: 2.5,
            IndustryType.FOOD_BEVERAGE: 1.3,
            IndustryType.CHEMICAL: 1.5,
            IndustryType.MUNICIPAL: 1.0
        }
        
        industry_factor = industry_factors.get(industry, 1.0)
        
        # Redundancy factors
        redundancy_factors = {
            'low': 1.0,
            'medium': 1.3,
            'high': 1.6,
            'critical': 2.0
        }
        
        redundancy_factor = redundancy_factors.get(redundancy_level, 1.3)
        
        capital_cost = base_capital_cost * industry_factor * redundancy_factor
        operational_cost = base_operational_cost * industry_factor * redundancy_factor
        
        return {
            'capital_cost': capital_cost,
            'annual_operational_cost': operational_cost,
            'total_cost_10_years': capital_cost + (operational_cost * 10),
            'cost_per_m3': operational_cost / (365 * 1000),  # Assuming 1000 m³/day
            'payback_period_years': capital_cost / (operational_cost * 0.2),  # 20% savings assumption
            'cost_breakdown': {
                'equipment': capital_cost * 0.6,
                'installation': capital_cost * 0.25,
                'commissioning': capital_cost * 0.15,
                'energy': operational_cost * 0.4,
                'chemicals': operational_cost * 0.3,
                'maintenance': operational_cost * 0.2,
                'labor': operational_cost * 0.1
            }
        }


# Convenience functions
async def create_specialized_component(technology: TreatmentTechnology,
                                     industry_applications: List[IndustryType],
                                     capacity: float) -> AdvancedTreatmentComponent:
    """Create a specialized treatment component."""
    spec = SpecializedComponentSpec(
        component_id=f"{technology.value}_{capacity}",
        technology=technology,
        industry_applications=industry_applications,
        capacity_range=(capacity * 0.8, capacity * 1.2),
        efficiency_range=(0.85, 0.98),
        energy_consumption_range=(0.3, 1.5),
        chemical_requirements={},
        operating_conditions={},
        capital_cost_range=(50000, 200000),
        operational_cost_range=(5000, 25000),
        maintenance_requirements={'interval': 30, 'lifespan': 15},
        environmental_impact={},
        regulatory_compliance=[]
    )
    
    return AdvancedTreatmentComponent(spec)


async def configure_industrial_water_system(industry: IndustryType,
                                          capacity: float,
                                          requirements: Dict[str, Any] = None) -> Dict[str, Any]:
    """Configure a complete industrial water treatment system."""
    configurator = IndustrialSystemConfigurator()
    return await configurator.configure_industrial_system(industry, capacity, requirements)
