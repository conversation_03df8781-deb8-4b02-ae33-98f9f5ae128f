#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Planet Labs API Integration for High-Resolution Marine Imagery
Daily satellite imagery for marine debris detection and monitoring
"""

import os
import json
import asyncio
import aiohttp
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PlanetImagery:
    """Planet Labs satellite imagery data"""
    image_id: str
    latitude: float
    longitude: float
    timestamp: datetime
    cloud_coverage: float
    resolution: float  # meters per pixel
    image_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    asset_type: str = "analytic"
    satellite: Optional[str] = None
    quality_score: Optional[float] = None


class PlanetLabsAPI:
    """Planet Labs API client for high-resolution marine imagery"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("PLANET_LABS_API_KEY", "PLAKf8364d269a8d4764816d44ace4f14977")
        self.base_url = "https://api.planet.com"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={
                'Authorization': f'api-key {self.api_key}',
                'Content-Type': 'application/json'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def search_marine_imagery(
        self, 
        bbox: Tuple[float, float, float, float],
        start_date: datetime,
        end_date: datetime,
        max_cloud_coverage: float = 0.2
    ) -> List[PlanetImagery]:
        """Search for marine imagery in specified area and time range"""
        try:
            search_request = {
                "item_types": ["PSScene"],
                "filter": {
                    "type": "AndFilter",
                    "config": [
                        {
                            "type": "GeometryFilter",
                            "field_name": "geometry",
                            "config": {
                                "type": "Polygon",
                                "coordinates": [[
                                    [bbox[0], bbox[1]],
                                    [bbox[2], bbox[1]],
                                    [bbox[2], bbox[3]],
                                    [bbox[0], bbox[3]],
                                    [bbox[0], bbox[1]]
                                ]]
                            }
                        },
                        {
                            "type": "DateRangeFilter",
                            "field_name": "acquired",
                            "config": {
                                "gte": start_date.isoformat() + "Z",
                                "lte": end_date.isoformat() + "Z"
                            }
                        },
                        {
                            "type": "RangeFilter",
                            "field_name": "cloud_cover",
                            "config": {"lte": max_cloud_coverage}
                        }
                    ]
                }
            }
            
            async with self.session.post(
                f"{self.base_url}/data/v1/quick-search",
                json=search_request
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_imagery_results(data)
                else:
                    logger.error(f"❌ Planet imagery search failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error searching Planet imagery: {e}")
            return []
    
    def _parse_imagery_results(self, data: Dict) -> List[PlanetImagery]:
        """Parse Planet Labs search results"""
        imagery_list = []
        
        try:
            for feature in data.get("features", []):
                properties = feature.get("properties", {})
                geometry = feature.get("geometry", {})
                
                # Calculate center coordinates
                coords = geometry.get("coordinates", [[]])[0]
                if coords:
                    lons = [coord[0] for coord in coords]
                    lats = [coord[1] for coord in coords]
                    center_lon = sum(lons) / len(lons)
                    center_lat = sum(lats) / len(lats)
                else:
                    center_lon, center_lat = 0.0, 0.0
                
                imagery = PlanetImagery(
                    image_id=properties.get("id", ""),
                    latitude=center_lat,
                    longitude=center_lon,
                    timestamp=datetime.fromisoformat(properties.get("acquired", "").replace("Z", "")),
                    cloud_coverage=properties.get("cloud_cover", 1.0),
                    resolution=properties.get("gsd", 3.0),  # Ground sample distance
                    satellite=properties.get("satellite_id", ""),
                    quality_score=properties.get("quality_category", 0.8)
                )
                
                imagery_list.append(imagery)
                
        except Exception as e:
            logger.error(f"❌ Error parsing imagery results: {e}")
        
        return imagery_list
    
    async def get_marine_debris_imagery(
        self,
        bbox: Tuple[float, float, float, float],
        days_back: int = 7
    ) -> List[PlanetImagery]:
        """Get marine debris imagery (alias for detect_marine_debris_imagery)"""
        return await self.detect_marine_debris_imagery(bbox, days_back)

    async def detect_marine_debris_imagery(
        self,
        bbox: Tuple[float, float, float, float],
        days_back: int = 7
    ) -> List[PlanetImagery]:
        """Detect marine debris using Planet Labs imagery"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        imagery = await self.search_marine_imagery(bbox, start_date, end_date, max_cloud_coverage=0.1)

        # Filter for high-quality marine debris detection
        debris_imagery = []
        for img in imagery:
            if img.resolution <= 5.0 and img.cloud_coverage <= 0.1:
                debris_imagery.append(img)

        logger.info(f"✅ Found {len(debris_imagery)} high-quality images for debris detection")
        return debris_imagery


# Convenience functions
async def get_satellite_imagery(
    bbox: Tuple[float, float, float, float],
    days_back: int = 7,
    api_key: str = None
) -> List[PlanetImagery]:
    """Get satellite imagery for marine debris detection"""
    api = PlanetLabsAPI(api_key)
    return await api.get_marine_debris_imagery(bbox, days_back)

async def get_planet_marine_imagery(
    bbox: Tuple[float, float, float, float],
    days_back: int = 7,
    api_key: str = None
) -> List[PlanetImagery]:
    """Get Planet Labs marine imagery for debris detection"""
    async with PlanetLabsAPI(api_key) as api:
        return await api.detect_marine_debris_imagery(bbox, days_back)


if __name__ == "__main__":
    async def test_planet_labs():
        print("🛰️ Testing Planet Labs API")
        test_bbox = (2.0, 41.0, 3.0, 42.0)  # Mediterranean
        
        try:
            imagery = await get_planet_marine_imagery(test_bbox, days_back=14)
            print(f"✅ Found {len(imagery)} Planet Labs images")
            
            for img in imagery[:3]:
                print(f"   Image: {img.image_id}")
                print(f"   Resolution: {img.resolution}m")
                print(f"   Cloud cover: {img.cloud_coverage:.1%}")
                print(f"   Date: {img.timestamp}")
                print()
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_planet_labs())
