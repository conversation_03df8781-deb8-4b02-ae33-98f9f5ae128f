"""Precipitation Analysis Module for Climate Data Processing."""

import numpy as np
import pandas as pd
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)


class PrecipitationAnalyzer:
    """Advanced precipitation data analysis."""
    
    def __init__(self):
        self.analysis_results = {}
    
    async def analyze_precipitation_patterns(self, precipitation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze precipitation patterns and trends."""
        try:
            # Extract precipitation values
            precip = precipitation_data.get('precipitation_values', [])
            timestamps = precipitation_data.get('timestamps', [])
            
            if not precip:
                return {'status': 'error', 'error': 'No precipitation data provided'}
            
            # Calculate statistics
            total_precip = np.sum(precip)
            mean_precip = np.mean(precip)
            max_precip = np.max(precip)
            
            # Dry/wet day analysis
            dry_days = sum(1 for p in precip if p < 0.1)  # Days with < 0.1mm
            wet_days = len(precip) - dry_days
            
            # Extreme precipitation events
            extreme_events = sum(1 for p in precip if p > 50)  # Days with > 50mm
            
            # Seasonal patterns (if timestamps available)
            seasonal_analysis = {}
            if timestamps and len(timestamps) == len(precip):
                # Group by month
                df = pd.DataFrame({'timestamp': pd.to_datetime(timestamps), 'precipitation': precip})
                df['month'] = df['timestamp'].dt.month
                monthly_totals = df.groupby('month')['precipitation'].sum().to_dict()
                seasonal_analysis = {
                    'monthly_totals': monthly_totals,
                    'wettest_month': max(monthly_totals, key=monthly_totals.get),
                    'driest_month': min(monthly_totals, key=monthly_totals.get)
                }
            
            return {
                'status': 'success',
                'statistics': {
                    'total_precipitation': total_precip,
                    'mean_daily_precipitation': mean_precip,
                    'max_daily_precipitation': max_precip,
                    'dry_days': dry_days,
                    'wet_days': wet_days,
                    'extreme_events': extreme_events
                },
                'seasonal_analysis': seasonal_analysis,
                'water_availability_impact': self._assess_water_availability_impact(total_precip, extreme_events),
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Precipitation analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _assess_water_availability_impact(self, total_precip: float, extreme_events: int) -> Dict[str, Any]:
        """Assess impact on water availability."""
        if total_precip < 500:  # mm/year
            availability_status = 'low'
            impact_score = 0.3
        elif total_precip < 1000:
            availability_status = 'moderate'
            impact_score = 0.6
        else:
            availability_status = 'high'
            impact_score = 0.9
        
        # Adjust for extreme events
        if extreme_events > 10:
            flood_risk = 'high'
            impact_score *= 0.8  # Reduce due to flood risk
        elif extreme_events > 5:
            flood_risk = 'moderate'
            impact_score *= 0.9
        else:
            flood_risk = 'low'
        
        return {
            'availability_status': availability_status,
            'impact_score': impact_score,
            'flood_risk': flood_risk,
            'recommendations': self._generate_recommendations(availability_status, flood_risk)
        }
    
    def _generate_recommendations(self, availability_status: str, flood_risk: str) -> List[str]:
        """Generate recommendations based on precipitation analysis."""
        recommendations = []
        
        if availability_status == 'low':
            recommendations.extend([
                'Implement water conservation measures',
                'Consider alternative water sources',
                'Optimize water treatment efficiency'
            ])
        
        if flood_risk == 'high':
            recommendations.extend([
                'Enhance flood protection systems',
                'Implement stormwater management',
                'Design resilient infrastructure'
            ])
        
        return recommendations


# Convenience function
async def analyze_precipitation_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze precipitation data."""
    analyzer = PrecipitationAnalyzer()
    return await analyzer.analyze_precipitation_patterns(data)
