{"name": "water-management-frontend", "version": "1.0.0", "description": "Node.js frontend for Water Management Decarbonisation System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "webpack --mode production", "build:dev": "webpack --mode development", "watch": "webpack --mode development --watch", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["water-management", "climate", "ai", "dashboard", "nodejs", "express"], "author": "Water Management Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-static-gzip": "^2.1.7", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "socket.io": "^4.7.4", "ws": "^8.14.2", "chart.js": "^4.4.0", "leaflet": "^1.9.4", "three": "^0.158.0", "d3": "^7.8.5", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.7.6", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@testing-library/jest-dom": "^6.1.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/water-management-frontend.git"}, "bugs": {"url": "https://github.com/your-org/water-management-frontend/issues"}, "homepage": "https://github.com/your-org/water-management-frontend#readme"}