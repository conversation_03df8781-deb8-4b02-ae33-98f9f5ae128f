import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`✅ API Response: ${response.config.url} - ${response.status}`);
        return response.data;
      },
      (error) => {
        console.error('❌ API Response Error:', error.response?.data || error.message);
        throw new Error(error.response?.data?.detail || error.message || 'API request failed');
      }
    );
  }

  // System Status
  async getSystemStatus() {
    try {
      const response = await this.client.get('/api/status');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get system status:', error);
      throw error;
    }
  }

  // Dashboard Data
  async getDashboardData(area = null) {
    try {
      const params = {};
      if (area) {
        params.min_lon = area.min_lon;
        params.min_lat = area.min_lat;
        params.max_lon = area.max_lon;
        params.max_lat = area.max_lat;
      }

      const response = await this.client.get('/api/dashboard', { params });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get dashboard data:', error);
      throw error;
    }
  }

  // Run Operation
  async runOperation(area, operationType = 'comprehensive_analysis') {
    try {
      const payload = {
        area: {
          min_lon: area.min_lon,
          min_lat: area.min_lat,
          max_lon: area.max_lon,
          max_lat: area.max_lat,
        },
        operation_type: operationType,
      };

      const response = await this.client.post('/api/operation', payload);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to run operation:', error);
      throw error;
    }
  }

  // Add Monitoring Area
  async addMonitoringArea(areaId, name, area) {
    try {
      const payload = {
        area_id: areaId,
        name: name,
        min_lon: area.min_lon,
        min_lat: area.min_lat,
        max_lon: area.max_lon,
        max_lat: area.max_lat,
      };

      const response = await this.client.post('/api/monitoring/area', payload);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to add monitoring area:', error);
      throw error;
    }
  }

  // Get Operation History
  async getOperationHistory(limit = 10) {
    try {
      const response = await this.client.get('/api/history', {
        params: { limit },
      });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to get operation history:', error);
      throw error;
    }
  }

  // Health Check
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response;
    } catch (error) {
      console.error('❌ Health check failed:', error);
      throw error;
    }
  }

  // Marine Conservation specific endpoints
  async getMarineData(area) {
    try {
      const dashboardData = await this.getDashboardData(area);
      return dashboardData.marine_conservation;
    } catch (error) {
      console.error('❌ Failed to get marine data:', error);
      throw error;
    }
  }

  // Water Management specific endpoints
  async getWaterData(area) {
    try {
      const dashboardData = await this.getDashboardData(area);
      return dashboardData.water_management;
    } catch (error) {
      console.error('❌ Failed to get water data:', error);
      throw error;
    }
  }

  // Integrated Analytics
  async getIntegratedAnalytics(area) {
    try {
      const dashboardData = await this.getDashboardData(area);
      return dashboardData.integrated_analytics;
    } catch (error) {
      console.error('❌ Failed to get integrated analytics:', error);
      throw error;
    }
  }

  // Utility methods
  formatArea(bbox) {
    return {
      min_lon: bbox[0],
      min_lat: bbox[1],
      max_lon: bbox[2],
      max_lat: bbox[3],
    };
  }

  // Default areas
  getDefaultAreas() {
    return {
      taiwan_strait: {
        name: 'Taiwan Strait',
        min_lon: 119.0,
        min_lat: 23.0,
        max_lon: 121.0,
        max_lat: 25.0,
      },
      mediterranean: {
        name: 'Mediterranean Sea',
        min_lon: 3.0,
        min_lat: 30.0,
        max_lon: 36.0,
        max_lat: 46.0,
      },
      pacific_coast: {
        name: 'Pacific Coast',
        min_lon: -125.0,
        min_lat: 32.0,
        max_lon: -117.0,
        max_lat: 42.0,
      },
    };
  }

  // Error handling utility
  handleApiError(error) {
    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const message = error.response.data?.detail || error.response.data?.message || 'Server error';
      
      switch (status) {
        case 400:
          return `Bad Request: ${message}`;
        case 401:
          return 'Unauthorized: Please check your credentials';
        case 403:
          return 'Forbidden: You do not have permission to access this resource';
        case 404:
          return 'Not Found: The requested resource was not found';
        case 500:
          return `Server Error: ${message}`;
        default:
          return `HTTP ${status}: ${message}`;
      }
    } else if (error.request) {
      // Request was made but no response received
      return 'Network Error: Unable to connect to the server';
    } else {
      // Something else happened
      return `Error: ${error.message}`;
    }
  }
}

export const apiService = new ApiService();
