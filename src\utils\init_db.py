"""
Database initialization script for the Water Management System.

This script can be run independently to set up the database schema
and initial data without requiring Dock<PERSON>.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.utils.config import get_settings
    from src.utils.logging_config import setup_logging
except ImportError:
    # Fallback for direct execution
    import logging
    def setup_logging():
        logging.basicConfig(level=logging.INFO)
    def get_settings():
        return None

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


async def create_database_schema():
    """Create database schema and initial data."""
    try:
        # For now, we'll use SQLite as a fallback if PostgreSQL is not available
        import sqlite3
        from datetime import datetime, timedelta
        
        logger.info("Setting up SQLite database for development...")
        
        # Create data directory if it doesn't exist
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # Connect to SQLite database
        db_path = data_dir / "watermanagement.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create climate_data table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS climate_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                location TEXT NOT NULL,
                latitude REAL NOT NULL,
                longitude REAL NOT NULL,
                temperature REAL,
                humidity REAL,
                precipitation REAL,
                wind_speed REAL,
                pressure REAL,
                source TEXT NOT NULL,
                metadata TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create water_treatment_systems table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS water_treatment_systems (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                location TEXT NOT NULL,
                capacity_liters_per_day INTEGER,
                energy_consumption_kwh REAL,
                efficiency_percentage REAL,
                configuration TEXT,
                status TEXT DEFAULT 'active',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create optimization_results table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS optimization_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                system_id INTEGER,
                optimization_type TEXT NOT NULL,
                input_parameters TEXT,
                results TEXT,
                performance_metrics TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (system_id) REFERENCES water_treatment_systems (id)
            )
        """)
        
        # Create agent_logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS agent_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_type TEXT NOT NULL,
                task_id TEXT,
                input_data TEXT,
                output_data TEXT,
                execution_time_seconds REAL,
                success INTEGER,
                error_message TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_climate_data_timestamp ON climate_data(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_climate_data_location ON climate_data(location)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_optimization_results_system_id ON optimization_results(system_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_agent_logs_agent_type ON agent_logs(agent_type)")
        
        # Insert sample water treatment systems
        sample_systems = [
            ('Primary Treatment Plant', 'New York', 100000, 250.5, 92.3, '{"filters": 3, "uv_treatment": true, "chemical_dosing": true}'),
            ('Secondary Treatment Facility', 'London', 75000, 180.2, 89.7, '{"filters": 2, "uv_treatment": false, "chemical_dosing": true}'),
            ('Pilot Decarbonisation Plant', 'Tokyo', 50000, 120.8, 95.1, '{"filters": 4, "uv_treatment": true, "chemical_dosing": false, "renewable_energy": true}')
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO water_treatment_systems 
            (name, location, capacity_liters_per_day, energy_consumption_kwh, efficiency_percentage, configuration)
            VALUES (?, ?, ?, ?, ?, ?)
        """, sample_systems)
        
        # Insert sample climate data
        now = datetime.now()
        sample_climate_data = [
            (now - timedelta(hours=1), 'New York', 40.7128, -74.0060, 22.5, 65, 0, 12.3, 1013.2, 'sample', '{"quality": "good"}'),
            (now - timedelta(hours=2), 'New York', 40.7128, -74.0060, 21.8, 68, 2.3, 11.8, 1012.8, 'sample', '{"quality": "good"}'),
            (now - timedelta(hours=3), 'New York', 40.7128, -74.0060, 20.9, 72, 0, 10.5, 1014.1, 'sample', '{"quality": "good"}'),
            (now - timedelta(hours=1), 'London', 51.5074, -0.1278, 18.2, 78, 5.7, 15.2, 1008.5, 'sample', '{"quality": "good"}'),
            (now - timedelta(hours=2), 'London', 51.5074, -0.1278, 17.5, 82, 8.1, 16.8, 1007.2, 'sample', '{"quality": "good"}'),
            (now - timedelta(hours=1), 'Tokyo', 35.6762, 139.6503, 26.8, 58, 0, 8.7, 1016.3, 'sample', '{"quality": "good"}')
        ]
        
        # Convert datetime objects to ISO format strings
        formatted_climate_data = []
        for row in sample_climate_data:
            formatted_row = (row[0].isoformat(),) + row[1:]
            formatted_climate_data.append(formatted_row)
        
        cursor.executemany("""
            INSERT OR IGNORE INTO climate_data 
            (timestamp, location, latitude, longitude, temperature, humidity, precipitation, wind_speed, pressure, source, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, formatted_climate_data)
        
        # Commit changes
        conn.commit()
        
        # Get table counts
        cursor.execute("SELECT COUNT(*) FROM climate_data")
        climate_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM water_treatment_systems")
        systems_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM optimization_results")
        optimization_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM agent_logs")
        logs_count = cursor.fetchone()[0]
        
        conn.close()
        
        logger.info(f"Database initialized successfully at {db_path}")
        logger.info(f"Tables created with data:")
        logger.info(f"  - climate_data: {climate_count} records")
        logger.info(f"  - water_treatment_systems: {systems_count} records")
        logger.info(f"  - optimization_results: {optimization_count} records")
        logger.info(f"  - agent_logs: {logs_count} records")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


async def test_database_connection():
    """Test database connection and basic operations."""
    try:
        import sqlite3
        from pathlib import Path
        
        db_path = Path("data") / "watermanagement.db"
        
        if not db_path.exists():
            logger.error("Database file not found. Please run database initialization first.")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT COUNT(*) FROM climate_data")
        count = cursor.fetchone()[0]
        
        # Test recent data query
        cursor.execute("""
            SELECT location, AVG(temperature) as avg_temp, COUNT(*) as records
            FROM climate_data 
            GROUP BY location
        """)
        results = cursor.fetchall()
        
        conn.close()
        
        logger.info("Database connection test successful")
        logger.info(f"Total climate records: {count}")
        logger.info("Average temperatures by location:")
        for location, avg_temp, records in results:
            logger.info(f"  {location}: {avg_temp:.1f}°C ({records} records)")
        
        return True
        
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


async def main():
    """Main function to initialize and test database."""
    logger.info("Starting database initialization...")
    
    # Initialize database schema
    init_success = await create_database_schema()
    
    if init_success:
        logger.info("Database schema created successfully")
        
        # Test database connection
        test_success = await test_database_connection()
        
        if test_success:
            logger.info("Database initialization completed successfully!")
            logger.info("You can now start the application with: python src/main.py")
            return True
        else:
            logger.error("Database connection test failed")
            return False
    else:
        logger.error("Database initialization failed")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
