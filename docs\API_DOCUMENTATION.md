# Water Management Decarbonisation System - API Documentation

## 🔗 **COMPREHENSIVE API REFERENCE**

This document provides complete API documentation for the Water Management Decarbonisation System, including all endpoints, request/response formats, and integration examples.

---

## 📋 **API OVERVIEW**

### **Base Information**
- **Base URL**: `http://localhost:8501/api/v1` (development)
- **Production URL**: `https://your-domain.com/api/v1`
- **API Version**: v1.0
- **Authentication**: API Key based
- **Content Type**: `application/json`
- **Rate Limiting**: 1000 requests/hour per API key

### **Authentication**
```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

---

## 🌍 **CLIMATE DATA APIs**

### **1. Collect Climate Data**

**Endpoint**: `POST /climate/collect`

**Description**: Collect climate data from multiple sources for a specific location.

**Request Body**:
```json
{
  "location": {
    "lat": 40.7128,
    "lon": -74.0060,
    "name": "New York City"
  },
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  },
  "data_sources": ["openweathermap", "nasa", "noaa"],
  "parameters": ["temperature", "precipitation", "humidity", "pressure"]
}
```

**Response**:
```json
{
  "status": "success",
  "request_id": "req_123456789",
  "data": {
    "location": {
      "lat": 40.7128,
      "lon": -74.0060,
      "name": "New York City"
    },
    "collection_summary": {
      "total_records": 744,
      "data_quality_score": 0.95,
      "sources_used": 3,
      "collection_time": "2024-01-15T10:30:00Z"
    },
    "climate_data": [
      {
        "timestamp": "2024-01-01T00:00:00Z",
        "temperature": 2.5,
        "precipitation": 0.0,
        "humidity": 65.2,
        "pressure": 1013.25,
        "source": "openweathermap",
        "quality_score": 0.98
      }
    ]
  }
}
```

### **2. Get Climate Analysis**

**Endpoint**: `GET /climate/analysis/{location_id}`

**Description**: Get comprehensive climate analysis for a location.

**Parameters**:
- `location_id` (path): Location identifier
- `analysis_type` (query): Type of analysis (trends, extremes, seasonal)
- `time_period` (query): Analysis time period (30d, 90d, 1y)

**Response**:
```json
{
  "status": "success",
  "analysis": {
    "location_id": "loc_nyc_001",
    "analysis_type": "comprehensive",
    "time_period": "90d",
    "temperature_analysis": {
      "average": 15.2,
      "min": -5.1,
      "max": 32.8,
      "trend": "increasing",
      "trend_rate": 0.02
    },
    "precipitation_analysis": {
      "total": 245.6,
      "average_daily": 2.7,
      "max_daily": 45.2,
      "pattern": "irregular"
    },
    "extreme_events": [
      {
        "event_type": "heatwave",
        "start_date": "2024-01-15",
        "end_date": "2024-01-18",
        "severity": "moderate",
        "impact_score": 0.6
      }
    ]
  }
}
```

---

## 💧 **WATER TREATMENT APIs**

### **3. Optimize Treatment Process**

**Endpoint**: `POST /treatment/optimize`

**Description**: Optimize water treatment process parameters using AI.

**Request Body**:
```json
{
  "facility_id": "facility_001",
  "current_parameters": {
    "flow_rate": 2000.0,
    "chemical_dose": 1.5,
    "energy_consumption": 45.0,
    "efficiency": 0.88
  },
  "optimization_targets": {
    "efficiency": 0.92,
    "energy_reduction": 0.15,
    "cost_reduction": 0.10
  },
  "constraints": {
    "max_chemical_dose": 3.0,
    "min_flow_rate": 1500.0,
    "regulatory_compliance": true
  }
}
```

**Response**:
```json
{
  "status": "success",
  "optimization_id": "opt_123456789",
  "optimized_parameters": {
    "flow_rate": 1950.0,
    "chemical_dose": 1.3,
    "energy_consumption": 38.5,
    "efficiency": 0.92
  },
  "expected_improvements": {
    "efficiency_gain": 0.04,
    "energy_savings": 0.14,
    "cost_savings": 0.12,
    "carbon_reduction": 0.18
  },
  "implementation_plan": {
    "priority": "high",
    "estimated_time": "2 hours",
    "required_actions": [
      "Adjust flow control valve to 1950 L/min",
      "Reduce chemical dosing to 1.3 mg/L",
      "Monitor efficiency for 24 hours"
    ]
  }
}
```

### **4. Get System Status**

**Endpoint**: `GET /treatment/status/{facility_id}`

**Description**: Get real-time status of water treatment facility.

**Response**:
```json
{
  "status": "success",
  "facility": {
    "facility_id": "facility_001",
    "name": "NYC Water Treatment Plant #1",
    "operational_status": "active",
    "last_updated": "2024-01-15T10:30:00Z"
  },
  "real_time_data": {
    "flow_rate": 1950.0,
    "inlet_quality": {
      "turbidity": 2.1,
      "ph": 7.2,
      "bacteria_count": 150
    },
    "outlet_quality": {
      "turbidity": 0.1,
      "ph": 7.4,
      "bacteria_count": 0
    },
    "energy_consumption": 38.5,
    "efficiency": 0.92
  },
  "alerts": [
    {
      "level": "warning",
      "message": "Chemical inventory low",
      "timestamp": "2024-01-15T09:45:00Z"
    }
  ]
}
```

---

## 🤖 **AI AGENTS APIs**

### **5. Climate Analysis Agent**

**Endpoint**: `POST /agents/climate/analyze`

**Description**: Analyze climate impact on water management systems.

**Request Body**:
```json
{
  "system_data": {
    "location": {"lat": 40.7128, "lon": -74.0060},
    "facility_type": "municipal",
    "capacity": 50000,
    "current_efficiency": 0.88
  },
  "climate_data": {
    "temperature": 25.5,
    "precipitation": 2.3,
    "humidity": 72.1
  },
  "analysis_scope": "comprehensive"
}
```

**Response**:
```json
{
  "status": "success",
  "agent_id": "climate_agent_001",
  "analysis": {
    "climate_impact_score": 0.75,
    "risk_assessment": {
      "drought_risk": 0.3,
      "flood_risk": 0.2,
      "temperature_stress": 0.4
    },
    "recommendations": [
      "Implement water conservation measures",
      "Upgrade cooling systems for temperature resilience",
      "Develop drought contingency plans"
    ],
    "adaptation_strategies": {
      "short_term": ["Adjust treatment parameters", "Monitor water sources"],
      "long_term": ["Infrastructure upgrades", "Alternative water sources"]
    }
  }
}
```

### **6. Sustainability Assessment Agent**

**Endpoint**: `POST /agents/sustainability/assess`

**Description**: Assess sustainability metrics and ESG performance.

**Request Body**:
```json
{
  "facility_data": {
    "energy_consumption": 150.0,
    "renewable_energy": 30.0,
    "water_processed": 2000.0,
    "waste_generated": 75.0,
    "population_served": 25000
  },
  "assessment_scope": "comprehensive"
}
```

**Response**:
```json
{
  "status": "success",
  "sustainability_metrics": {
    "overall_sustainability_score": 7.8,
    "esg_rating": "BBB",
    "environmental_score": 8.2,
    "social_score": 7.5,
    "governance_score": 7.7
  },
  "carbon_analysis": {
    "total_emissions": 450.5,
    "emissions_per_m3": 0.225,
    "carbon_intensity": "medium",
    "reduction_potential": 0.25
  },
  "recommendations": [
    "Increase renewable energy to 50%",
    "Implement circular economy practices",
    "Enhance community engagement programs"
  ]
}
```

---

## 🧠 **KNOWLEDGE GRAPH APIs**

### **7. Query Knowledge Graph**

**Endpoint**: `GET /knowledge/query`

**Description**: Query the domain-specific knowledge graph.

**Parameters**:
- `entity_type` (query): Type of entity to search
- `search_term` (query): Search term
- `limit` (query): Maximum results (default: 10)

**Response**:
```json
{
  "status": "success",
  "query": {
    "entity_type": "component",
    "search_term": "filtration",
    "limit": 10
  },
  "results": [
    {
      "entity_id": "ent_filter_001",
      "name": "Sand Filter",
      "type": "component",
      "description": "Granular media filtration system",
      "properties": {
        "efficiency": 0.95,
        "capacity": 1000,
        "maintenance_interval": 30
      },
      "relationships": [
        {
          "type": "part_of",
          "target": "Secondary Treatment",
          "confidence": 0.9
        }
      ]
    }
  ]
}
```

### **8. Add Knowledge Entity**

**Endpoint**: `POST /knowledge/entities`

**Description**: Add new entity to knowledge graph.

**Request Body**:
```json
{
  "name": "Advanced Membrane Filter",
  "entity_type": "component",
  "description": "High-efficiency membrane filtration system",
  "properties": {
    "efficiency": 0.98,
    "cost": 25000,
    "lifespan": 15
  },
  "aliases": ["membrane filter", "ultrafiltration"]
}
```

**Response**:
```json
{
  "status": "success",
  "entity": {
    "entity_id": "ent_membrane_001",
    "name": "Advanced Membrane Filter",
    "entity_type": "component",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

---

## 🔧 **OPTIMIZATION APIs**

### **9. Neural Network Optimization**

**Endpoint**: `POST /optimization/neural-network`

**Description**: Optimize system parameters using neural networks.

**Request Body**:
```json
{
  "optimization_type": "treatment_efficiency",
  "current_state": {
    "flow_rate": 2000.0,
    "chemical_dose": 1.5,
    "energy_consumption": 45.0
  },
  "targets": {
    "efficiency": 0.92,
    "energy_reduction": 0.15
  },
  "constraints": {
    "max_flow_rate": 3000.0,
    "min_efficiency": 0.85
  }
}
```

**Response**:
```json
{
  "status": "success",
  "optimization_result": {
    "optimized_parameters": {
      "flow_rate": 1850.0,
      "chemical_dose": 1.15,
      "energy_consumption": 42.5
    },
    "predicted_performance": {
      "efficiency": 0.92,
      "energy_savings": 0.14,
      "confidence": 0.89
    },
    "implementation_priority": "high"
  }
}
```

### **10. Genetic Algorithm Optimization**

**Endpoint**: `POST /optimization/genetic-algorithm`

**Description**: Multi-objective optimization using genetic algorithms.

**Request Body**:
```json
{
  "objectives": ["efficiency", "cost", "energy", "quality"],
  "system_parameters": {
    "flow_rate": 2000.0,
    "efficiency": 0.85
  },
  "optimization_config": {
    "population_size": 100,
    "generations": 500,
    "mutation_rate": 0.1
  }
}
```

**Response**:
```json
{
  "status": "success",
  "optimization_results": {
    "pareto_front": [
      {
        "parameters": {"flow_rate": 1950.0, "chemical_dose": 1.3},
        "objectives": {"efficiency": 0.92, "cost": 850.0, "energy": 38.5},
        "rank": 1
      }
    ],
    "recommended_solution": {
      "parameters": {"flow_rate": 1950.0, "chemical_dose": 1.3},
      "trade_offs": "Optimal balance between efficiency and cost"
    }
  }
}
```

---

## 📊 **MONITORING APIs**

### **11. System Health**

**Endpoint**: `GET /health`

**Description**: Get system health status.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "ai_agents": "healthy",
    "apis": "healthy"
  },
  "metrics": {
    "uptime": "99.9%",
    "response_time": "150ms",
    "memory_usage": "65%",
    "cpu_usage": "45%"
  }
}
```

### **12. Performance Metrics**

**Endpoint**: `GET /metrics`

**Description**: Get detailed performance metrics.

**Response**:
```json
{
  "status": "success",
  "metrics": {
    "api_requests": {
      "total": 15420,
      "success_rate": 0.998,
      "average_response_time": 145
    },
    "data_processing": {
      "records_processed": 1250000,
      "processing_rate": 850,
      "error_rate": 0.002
    },
    "ai_agents": {
      "total_analyses": 3420,
      "average_confidence": 0.89,
      "optimization_success_rate": 0.94
    }
  }
}
```

---

## 🔒 **AUTHENTICATION & SECURITY**

### **API Key Management**

**Generate API Key**: `POST /auth/api-keys`
```json
{
  "name": "Production API Key",
  "permissions": ["read", "write", "optimize"],
  "rate_limit": 1000
}
```

**Response**:
```json
{
  "api_key": "wm_live_1234567890abcdef",
  "permissions": ["read", "write", "optimize"],
  "created_at": "2024-01-15T10:30:00Z",
  "expires_at": "2025-01-15T10:30:00Z"
}
```

---

## 📝 **ERROR HANDLING**

### **Error Response Format**
```json
{
  "status": "error",
  "error": {
    "code": "INVALID_PARAMETERS",
    "message": "Invalid optimization parameters provided",
    "details": {
      "field": "flow_rate",
      "issue": "Value must be between 500 and 5000"
    }
  },
  "request_id": "req_123456789",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### **Common Error Codes**
- `INVALID_API_KEY`: Invalid or expired API key
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INVALID_PARAMETERS`: Invalid request parameters
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `INTERNAL_ERROR`: Internal server error
- `SERVICE_UNAVAILABLE`: Service temporarily unavailable

---

## 🔗 **WEBHOOKS**

### **Webhook Configuration**

**Endpoint**: `POST /webhooks`

**Request Body**:
```json
{
  "url": "https://your-app.com/webhook",
  "events": ["optimization_complete", "alert_triggered", "analysis_ready"],
  "secret": "your_webhook_secret"
}
```

### **Webhook Payload Example**
```json
{
  "event": "optimization_complete",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "optimization_id": "opt_123456789",
    "facility_id": "facility_001",
    "status": "success",
    "improvements": {
      "efficiency_gain": 0.04,
      "energy_savings": 0.14
    }
  }
}
```

---

## 📚 **SDK & EXAMPLES**

### **Python SDK Example**
```python
from water_management_sdk import WaterManagementClient

client = WaterManagementClient(api_key="your_api_key")

# Collect climate data
climate_data = client.climate.collect({
    "location": {"lat": 40.7128, "lon": -74.0060},
    "date_range": {"start_date": "2024-01-01", "end_date": "2024-01-31"}
})

# Optimize treatment
optimization = client.treatment.optimize({
    "facility_id": "facility_001",
    "optimization_targets": {"efficiency": 0.92}
})
```

### **JavaScript SDK Example**
```javascript
const WaterManagement = require('water-management-sdk');

const client = new WaterManagement({
  apiKey: 'your_api_key'
});

// Get system status
const status = await client.treatment.getStatus('facility_001');

// Run sustainability assessment
const assessment = await client.agents.sustainability.assess({
  facility_data: {
    energy_consumption: 150.0,
    renewable_energy: 30.0
  }
});
```

---

## 🎯 **RATE LIMITS**

| Plan | Requests/Hour | Requests/Day | Concurrent |
|------|---------------|--------------|------------|
| Free | 100 | 1,000 | 2 |
| Basic | 1,000 | 10,000 | 5 |
| Pro | 10,000 | 100,000 | 20 |
| Enterprise | Unlimited | Unlimited | Unlimited |

---

**📞 For API support, contact: <EMAIL>**
