#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Validation System for Marine Conservation APIs
Validates and cleans data from multiple marine data sources
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import numpy as np

logger = logging.getLogger(__name__)

class DataValidator:
    """Comprehensive data validation system for marine conservation data"""
    
    def __init__(self):
        self.validation_rules = {
            'coordinates': {
                'latitude': {'min': -90, 'max': 90},
                'longitude': {'min': -180, 'max': 180}
            },
            'water_quality': {
                'temperature': {'min': -2, 'max': 40},  # Celsius
                'ph': {'min': 0, 'max': 14},
                'salinity': {'min': 0, 'max': 50},  # PSU
                'dissolved_oxygen': {'min': 0, 'max': 20}  # mg/L
            },
            'debris': {
                'size': {'min': 0, 'max': 1000},  # meters
                'confidence': {'min': 0, 'max': 1}
            },
            'vessel': {
                'speed': {'min': 0, 'max': 100},  # knots
                'length': {'min': 1, 'max': 500},  # meters
                'course': {'min': 0, 'max': 360}  # degrees
            }
        }
        
        self.data_types = {
            'marine_conditions': ['temperature', 'salinity', 'ph', 'dissolved_oxygen'],
            'debris_detection': ['latitude', 'longitude', 'size', 'confidence'],
            'vessel_tracking': ['latitude', 'longitude', 'speed', 'course', 'length'],
            'satellite_imagery': ['timestamp', 'cloud_cover', 'resolution']
        }
    
    def validate_marine_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate marine conservation data"""
        try:
            if not data:
                return {'valid': False, 'errors': ['Empty data provided']}
            
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'cleaned_data': {},
                'validation_timestamp': datetime.now()
            }
            
            # Validate coordinates if present
            if 'latitude' in data and 'longitude' in data:
                coord_validation = self._validate_coordinates(data['latitude'], data['longitude'])
                if not coord_validation['valid']:
                    validation_result['valid'] = False
                    validation_result['errors'].extend(coord_validation['errors'])
                else:
                    validation_result['cleaned_data']['latitude'] = coord_validation['cleaned_latitude']
                    validation_result['cleaned_data']['longitude'] = coord_validation['cleaned_longitude']
            
            # Validate water quality parameters
            water_quality_params = ['temperature', 'ph', 'salinity', 'dissolved_oxygen']
            for param in water_quality_params:
                if param in data:
                    param_validation = self._validate_water_quality_param(param, data[param])
                    if not param_validation['valid']:
                        validation_result['warnings'].extend(param_validation['warnings'])
                    validation_result['cleaned_data'][param] = param_validation['cleaned_value']
            
            # Validate debris data
            if 'debris_items' in data:
                debris_validation = self._validate_debris_data(data['debris_items'])
                validation_result['cleaned_data']['debris_items'] = debris_validation['cleaned_items']
                validation_result['warnings'].extend(debris_validation['warnings'])
            
            # Validate vessel data
            vessel_params = ['speed', 'course', 'length']
            for param in vessel_params:
                if param in data:
                    vessel_validation = self._validate_vessel_param(param, data[param])
                    if not vessel_validation['valid']:
                        validation_result['warnings'].extend(vessel_validation['warnings'])
                    validation_result['cleaned_data'][param] = vessel_validation['cleaned_value']
            
            # Copy other valid data
            for key, value in data.items():
                if key not in validation_result['cleaned_data'] and value is not None:
                    validation_result['cleaned_data'][key] = value
            
            logger.info(f"✅ Data validation completed: {len(validation_result['errors'])} errors, {len(validation_result['warnings'])} warnings")
            return validation_result
            
        except Exception as e:
            logger.error(f"❌ Error in data validation: {e}")
            return {
                'valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': [],
                'cleaned_data': {},
                'validation_timestamp': datetime.now()
            }
    
    def _validate_coordinates(self, lat: float, lon: float) -> Dict[str, Any]:
        """Validate coordinate data"""
        try:
            errors = []
            
            # Convert to float if needed
            lat = float(lat)
            lon = float(lon)
            
            # Check latitude bounds
            if lat < -90 or lat > 90:
                errors.append(f"Invalid latitude: {lat} (must be between -90 and 90)")
            
            # Check longitude bounds
            if lon < -180 or lon > 180:
                errors.append(f"Invalid longitude: {lon} (must be between -180 and 180)")
            
            # Normalize longitude to -180 to 180 range
            while lon > 180:
                lon -= 360
            while lon < -180:
                lon += 360
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'cleaned_latitude': lat,
                'cleaned_longitude': lon
            }
            
        except (ValueError, TypeError) as e:
            return {
                'valid': False,
                'errors': [f"Invalid coordinate format: {e}"],
                'cleaned_latitude': 0.0,
                'cleaned_longitude': 0.0
            }
    
    def _validate_water_quality_param(self, param: str, value: Union[float, int]) -> Dict[str, Any]:
        """Validate water quality parameter"""
        try:
            warnings = []
            value = float(value)
            
            if param in self.validation_rules['water_quality']:
                rules = self.validation_rules['water_quality'][param]
                
                if value < rules['min']:
                    warnings.append(f"{param} value {value} below expected minimum {rules['min']}")
                    value = max(value, rules['min'])
                
                if value > rules['max']:
                    warnings.append(f"{param} value {value} above expected maximum {rules['max']}")
                    value = min(value, rules['max'])
            
            return {
                'valid': True,
                'warnings': warnings,
                'cleaned_value': value
            }
            
        except (ValueError, TypeError):
            return {
                'valid': False,
                'warnings': [f"Invalid {param} value format"],
                'cleaned_value': 0.0
            }
    
    def _validate_debris_data(self, debris_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate debris detection data"""
        cleaned_items = []
        warnings = []
        
        for i, item in enumerate(debris_items):
            try:
                cleaned_item = {}
                
                # Validate coordinates
                if 'latitude' in item and 'longitude' in item:
                    coord_validation = self._validate_coordinates(item['latitude'], item['longitude'])
                    if coord_validation['valid']:
                        cleaned_item['latitude'] = coord_validation['cleaned_latitude']
                        cleaned_item['longitude'] = coord_validation['cleaned_longitude']
                    else:
                        warnings.extend([f"Item {i}: {error}" for error in coord_validation['errors']])
                        continue
                
                # Validate size
                if 'size' in item:
                    try:
                        size = float(item['size'])
                        if size < 0:
                            size = 0
                            warnings.append(f"Item {i}: Negative size corrected to 0")
                        elif size > 1000:
                            size = 1000
                            warnings.append(f"Item {i}: Size capped at 1000m")
                        cleaned_item['size'] = size
                    except (ValueError, TypeError):
                        cleaned_item['size'] = 1.0  # Default size
                        warnings.append(f"Item {i}: Invalid size format, using default")
                
                # Validate confidence
                if 'confidence' in item:
                    try:
                        confidence = float(item['confidence'])
                        confidence = max(0.0, min(1.0, confidence))  # Clamp to 0-1
                        cleaned_item['confidence'] = confidence
                    except (ValueError, TypeError):
                        cleaned_item['confidence'] = 0.5  # Default confidence
                        warnings.append(f"Item {i}: Invalid confidence format, using default")
                
                # Copy other fields
                for key, value in item.items():
                    if key not in cleaned_item and value is not None:
                        cleaned_item[key] = value
                
                cleaned_items.append(cleaned_item)
                
            except Exception as e:
                warnings.append(f"Item {i}: Validation error - {str(e)}")
                continue
        
        return {
            'cleaned_items': cleaned_items,
            'warnings': warnings
        }
    
    def _validate_vessel_param(self, param: str, value: Union[float, int]) -> Dict[str, Any]:
        """Validate vessel parameter"""
        try:
            warnings = []
            value = float(value)
            
            if param in self.validation_rules['vessel']:
                rules = self.validation_rules['vessel'][param]
                
                if value < rules['min']:
                    warnings.append(f"{param} value {value} below expected minimum {rules['min']}")
                    value = max(value, rules['min'])
                
                if value > rules['max']:
                    warnings.append(f"{param} value {value} above expected maximum {rules['max']}")
                    value = min(value, rules['max'])
            
            return {
                'valid': True,
                'warnings': warnings,
                'cleaned_value': value
            }
            
        except (ValueError, TypeError):
            return {
                'valid': False,
                'warnings': [f"Invalid {param} value format"],
                'cleaned_value': 0.0
            }
    
    def validate_api_response(self, api_name: str, response_data: Any) -> Dict[str, Any]:
        """Validate API response data"""
        try:
            if response_data is None:
                return {
                    'valid': False,
                    'errors': [f"{api_name} returned None"],
                    'cleaned_data': None
                }
            
            if isinstance(response_data, list) and len(response_data) == 0:
                return {
                    'valid': True,
                    'errors': [],
                    'warnings': [f"{api_name} returned empty list"],
                    'cleaned_data': response_data
                }
            
            return {
                'valid': True,
                'errors': [],
                'warnings': [],
                'cleaned_data': response_data
            }
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Error validating {api_name} response: {str(e)}"],
                'cleaned_data': None
            }


# Convenience functions
def validate_marine_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate marine conservation data"""
    validator = DataValidator()
    return validator.validate_marine_data(data)

def validate_coordinates(lat: float, lon: float) -> Dict[str, Any]:
    """Validate coordinate pair"""
    validator = DataValidator()
    return validator._validate_coordinates(lat, lon)
