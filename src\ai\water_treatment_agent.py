"""
Water Treatment Optimization Agent.

This module provides an intelligent water treatment optimization agent that leverages
AI climate insights, machine learning, and optimization algorithms to maximize
treatment efficiency, minimize energy consumption, and ensure optimal water quality.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import json
from scipy.optimize import minimize, differential_evolution
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData
from src.ai.climate_analysis_agent import analyze_climate_with_ai, ClimateInsight

logger = logging.getLogger(__name__)


@dataclass
class TreatmentParameters:
    """Water treatment system parameters."""
    flow_rate: float  # m³/h
    chemical_dosing_rate: float  # mg/L
    energy_consumption: float  # kWh
    temperature_setpoint: float  # °C
    ph_setpoint: float
    dissolved_oxygen_setpoint: float  # mg/L
    retention_time: float  # hours
    filtration_rate: float  # m³/h
    backwash_frequency: float  # cycles/day
    pump_speed: float  # %


@dataclass
class TreatmentPerformance:
    """Water treatment performance metrics."""
    efficiency: float  # %
    energy_efficiency: float  # m³/kWh
    water_quality_score: float  # 0-1
    cost_per_cubic_meter: float  # $/m³
    carbon_footprint: float  # kg CO2/m³
    compliance_score: float  # 0-1
    maintenance_score: float  # 0-1
    overall_score: float  # 0-1


@dataclass
class OptimizationRecommendation:
    """Treatment optimization recommendation."""
    recommendation_id: str
    recommendation_type: str  # 'operational', 'maintenance', 'infrastructure', 'emergency'
    title: str
    description: str
    priority: str  # 'critical', 'high', 'medium', 'low'
    expected_improvement: Dict[str, float]
    implementation_cost: float
    payback_period: float  # months
    climate_factors: List[str]
    actionable_steps: List[str]
    monitoring_requirements: List[str]
    timestamp: datetime


@dataclass
class OptimizationResult:
    """Result of water treatment optimization."""
    location: str
    optimization_period: Dict[str, str]
    current_parameters: TreatmentParameters
    optimized_parameters: TreatmentParameters
    current_performance: TreatmentPerformance
    optimized_performance: TreatmentPerformance
    improvement_metrics: Dict[str, float]
    recommendations: List[OptimizationRecommendation]
    climate_insights_used: List[ClimateInsight]
    optimization_confidence: float
    implementation_timeline: Dict[str, str]
    timestamp: datetime


class WaterTreatmentOptimizationAgent:
    """
    Intelligent water treatment optimization agent.
    
    Provides:
    - AI-driven treatment parameter optimization
    - Climate-adaptive operational strategies
    - Energy efficiency maximization
    - Water quality optimization
    - Cost minimization with performance constraints
    - Predictive maintenance scheduling
    - Real-time adaptive control recommendations
    - Multi-objective optimization with trade-off analysis
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Optimization models
        self.models = {
            'efficiency_predictor': None,
            'energy_predictor': None,
            'quality_predictor': None,
            'cost_predictor': None
        }
        
        # Treatment system constraints
        self.constraints = {
            'flow_rate': {'min': 10, 'max': 1000},  # m³/h
            'chemical_dosing_rate': {'min': 0.1, 'max': 50},  # mg/L
            'energy_consumption': {'min': 0.1, 'max': 100},  # kWh
            'temperature_setpoint': {'min': 5, 'max': 35},  # °C
            'ph_setpoint': {'min': 6.0, 'max': 9.0},
            'dissolved_oxygen_setpoint': {'min': 2.0, 'max': 12.0},  # mg/L
            'retention_time': {'min': 0.5, 'max': 24},  # hours
            'filtration_rate': {'min': 5, 'max': 500},  # m³/h
            'backwash_frequency': {'min': 0.5, 'max': 10},  # cycles/day
            'pump_speed': {'min': 20, 'max': 100}  # %
        }
        
        # Performance targets
        self.targets = {
            'efficiency': 0.95,  # 95% treatment efficiency
            'energy_efficiency': 0.8,  # 0.8 m³/kWh
            'water_quality_score': 0.9,  # 90% quality score
            'cost_per_cubic_meter': 0.5,  # $0.50/m³
            'carbon_footprint': 0.3,  # 0.3 kg CO2/m³
            'compliance_score': 1.0,  # 100% compliance
            'maintenance_score': 0.85  # 85% maintenance score
        }
        
        # Optimization weights for multi-objective optimization
        self.optimization_weights = {
            'efficiency': 0.25,
            'energy_efficiency': 0.20,
            'water_quality': 0.20,
            'cost': 0.15,
            'carbon_footprint': 0.10,
            'compliance': 0.10
        }
        
        # Climate adaptation factors
        self.climate_factors = {
            'temperature_sensitivity': 0.02,  # 2% efficiency change per °C
            'precipitation_impact': 0.01,  # 1% capacity change per mm
            'seasonal_adjustment': 0.15,  # 15% seasonal variation
            'extreme_weather_buffer': 0.20  # 20% buffer for extreme events
        }
        
        # Scalers for model inputs
        self.scalers = {}
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the water treatment optimization agent."""
        try:
            logger.info("Initializing Water Treatment Optimization Agent...")
            
            # Initialize optimization models
            await self._initialize_models()
            
            self.is_initialized = True
            logger.info("Water Treatment Optimization Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize water treatment optimization agent: {e}")
            return False
    
    async def _initialize_models(self):
        """Initialize machine learning models for optimization."""
        try:
            # Treatment efficiency prediction model
            self.models['efficiency_predictor'] = RandomForestRegressor(
                n_estimators=100, max_depth=10, random_state=42
            )
            
            # Energy consumption prediction model
            self.models['energy_predictor'] = RandomForestRegressor(
                n_estimators=100, max_depth=10, random_state=42
            )
            
            # Water quality prediction model
            self.models['quality_predictor'] = RandomForestRegressor(
                n_estimators=100, max_depth=10, random_state=42
            )
            
            # Cost prediction model
            self.models['cost_predictor'] = RandomForestRegressor(
                n_estimators=100, max_depth=10, random_state=42
            )
            
            # Initialize scalers
            self.scalers = {
                'parameters': StandardScaler(),
                'climate': StandardScaler(),
                'performance': StandardScaler()
            }
            
            logger.info("Optimization models initialized successfully")
            
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
    
    async def optimize_treatment_system(self, climate_data: List[ProcessedClimateData],
                                      current_parameters: TreatmentParameters = None,
                                      location: str = None) -> OptimizationResult:
        """Optimize water treatment system based on climate insights."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not climate_data:
                raise ValueError("No climate data provided for optimization")
            
            logger.info(f"Starting water treatment optimization for {len(climate_data)} climate data points")
            
            # Get AI climate insights
            climate_analysis = await analyze_climate_with_ai(climate_data, location)
            optimization_location = location or self._extract_location(climate_data)
            
            # Use default parameters if none provided
            if current_parameters is None:
                current_parameters = self._get_default_parameters()
            
            # Generate synthetic training data for models
            await self._train_optimization_models(climate_data)
            
            # Perform multi-objective optimization
            optimized_parameters = await self._optimize_parameters(
                current_parameters, climate_analysis
            )
            
            # Calculate performance metrics
            current_performance = await self._calculate_performance(
                current_parameters, climate_analysis
            )
            optimized_performance = await self._calculate_performance(
                optimized_parameters, climate_analysis
            )
            
            # Calculate improvement metrics
            improvement_metrics = await self._calculate_improvements(
                current_performance, optimized_performance
            )
            
            # Generate optimization recommendations
            recommendations = await self._generate_optimization_recommendations(
                current_parameters, optimized_parameters, climate_analysis
            )
            
            # Calculate optimization confidence
            optimization_confidence = await self._calculate_optimization_confidence(
                climate_analysis, improvement_metrics
            )
            
            # Generate implementation timeline
            implementation_timeline = await self._generate_implementation_timeline(
                recommendations
            )
            
            # Create result
            result = OptimizationResult(
                location=optimization_location,
                optimization_period={
                    'start': min(item.timestamp for item in climate_data).isoformat(),
                    'end': max(item.timestamp for item in climate_data).isoformat(),
                    'duration_days': (max(item.timestamp for item in climate_data) - 
                                    min(item.timestamp for item in climate_data)).days
                },
                current_parameters=current_parameters,
                optimized_parameters=optimized_parameters,
                current_performance=current_performance,
                optimized_performance=optimized_performance,
                improvement_metrics=improvement_metrics,
                recommendations=recommendations,
                climate_insights_used=climate_analysis.insights if climate_analysis else [],
                optimization_confidence=optimization_confidence,
                implementation_timeline=implementation_timeline,
                timestamp=datetime.now()
            )
            
            logger.info(f"Water treatment optimization completed for {optimization_location}")
            logger.info(f"Generated {len(recommendations)} optimization recommendations")
            return result
            
        except Exception as e:
            logger.error(f"Water treatment optimization failed: {e}")
            raise
    
    def _extract_location(self, climate_data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in climate_data if item.location]
        if locations:
            return max(set(locations), key=locations.count)
        return "Unknown Location"
    
    def _get_default_parameters(self) -> TreatmentParameters:
        """Get default treatment parameters."""
        return TreatmentParameters(
            flow_rate=100.0,  # m³/h
            chemical_dosing_rate=10.0,  # mg/L
            energy_consumption=20.0,  # kWh
            temperature_setpoint=20.0,  # °C
            ph_setpoint=7.5,
            dissolved_oxygen_setpoint=6.0,  # mg/L
            retention_time=4.0,  # hours
            filtration_rate=80.0,  # m³/h
            backwash_frequency=2.0,  # cycles/day
            pump_speed=75.0  # %
        )
    
    async def _train_optimization_models(self, climate_data: List[ProcessedClimateData]):
        """Train optimization models using synthetic data."""
        try:
            # Generate synthetic training data
            training_data = await self._generate_training_data(climate_data)
            
            if len(training_data) < 50:  # Need minimum data for training
                logger.warning("Insufficient data for model training, using default models")
                return
            
            # Prepare features and targets
            X_params = np.array([list(params.values()) for params, _, _, _ in training_data])
            X_climate = np.array([climate for _, climate, _, _ in training_data])
            X_combined = np.hstack([X_params, X_climate])
            
            y_efficiency = np.array([perf['efficiency'] for _, _, perf, _ in training_data])
            y_energy = np.array([perf['energy_efficiency'] for _, _, perf, _ in training_data])
            y_quality = np.array([perf['water_quality_score'] for _, _, perf, _ in training_data])
            y_cost = np.array([perf['cost_per_cubic_meter'] for _, _, perf, _ in training_data])
            
            # Scale features
            X_scaled = self.scalers['parameters'].fit_transform(X_combined)
            
            # Train models
            self.models['efficiency_predictor'].fit(X_scaled, y_efficiency)
            self.models['energy_predictor'].fit(X_scaled, y_energy)
            self.models['quality_predictor'].fit(X_scaled, y_quality)
            self.models['cost_predictor'].fit(X_scaled, y_cost)
            
            logger.info(f"Optimization models trained on {len(training_data)} samples")
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
    
    async def _generate_training_data(self, climate_data: List[ProcessedClimateData]) -> List[Tuple]:
        """Generate synthetic training data for optimization models."""
        try:
            training_data = []
            
            # Extract climate features
            avg_temp = np.mean([item.temperature for item in climate_data if item.temperature])
            avg_precip = np.mean([item.precipitation for item in climate_data if item.precipitation])
            
            # Generate diverse parameter combinations
            for _ in range(200):  # Generate 200 training samples
                # Random parameters within constraints
                params = {}
                for param, bounds in self.constraints.items():
                    params[param] = np.random.uniform(bounds['min'], bounds['max'])
                
                # Climate features
                climate_features = [
                    avg_temp + np.random.normal(0, 2),  # Temperature variation
                    avg_precip + np.random.normal(0, 1),  # Precipitation variation
                    np.random.uniform(0, 1),  # Seasonal factor
                    np.random.uniform(0, 1)   # Weather stability
                ]
                
                # Simulate performance based on parameters and climate
                performance = self._simulate_performance(params, climate_features)
                
                # Calculate cost
                cost = self._simulate_cost(params, performance)
                
                training_data.append((params, climate_features, performance, cost))
            
            return training_data
            
        except Exception as e:
            logger.error(f"Training data generation failed: {e}")
            return []
    
    def _simulate_performance(self, params: Dict[str, float], climate: List[float]) -> Dict[str, float]:
        """Simulate treatment performance based on parameters and climate."""
        try:
            # Base efficiency calculation
            base_efficiency = 0.8 + (params['retention_time'] / 24) * 0.15
            base_efficiency = min(0.98, base_efficiency)
            
            # Temperature impact
            temp_impact = 1.0 - abs(climate[0] - 20) * 0.01  # Optimal at 20°C
            
            # Flow rate impact
            flow_impact = 1.0 - max(0, (params['flow_rate'] - 100) / 1000) * 0.1
            
            # Chemical dosing impact
            dosing_impact = min(1.0, params['chemical_dosing_rate'] / 15)
            
            # Calculate final efficiency
            efficiency = base_efficiency * temp_impact * flow_impact * dosing_impact
            efficiency = max(0.5, min(0.98, efficiency))
            
            # Energy efficiency
            energy_efficiency = (params['flow_rate'] / params['energy_consumption']) * 0.01
            energy_efficiency = max(0.1, min(2.0, energy_efficiency))
            
            # Water quality score
            quality_score = efficiency * 0.9 + dosing_impact * 0.1
            quality_score = max(0.6, min(1.0, quality_score))
            
            # Add some noise
            efficiency += np.random.normal(0, 0.02)
            energy_efficiency += np.random.normal(0, 0.05)
            quality_score += np.random.normal(0, 0.02)
            
            return {
                'efficiency': max(0.5, min(0.98, efficiency)),
                'energy_efficiency': max(0.1, min(2.0, energy_efficiency)),
                'water_quality_score': max(0.6, min(1.0, quality_score)),
                'carbon_footprint': params['energy_consumption'] * 0.5 / params['flow_rate'],
                'compliance_score': min(1.0, quality_score + 0.05),
                'maintenance_score': 1.0 - (params['pump_speed'] / 100) * 0.2
            }
            
        except Exception as e:
            logger.error(f"Performance simulation failed: {e}")
            return {
                'efficiency': 0.8,
                'energy_efficiency': 0.5,
                'water_quality_score': 0.8,
                'carbon_footprint': 0.4,
                'compliance_score': 0.9,
                'maintenance_score': 0.8
            }
    
    def _simulate_cost(self, params: Dict[str, float], performance: Dict[str, float]) -> float:
        """Simulate treatment cost based on parameters and performance."""
        try:
            # Base cost components
            energy_cost = params['energy_consumption'] * 0.1  # $0.1/kWh
            chemical_cost = params['chemical_dosing_rate'] * params['flow_rate'] * 0.001  # Chemical cost
            maintenance_cost = (1.0 - performance['maintenance_score']) * 0.2
            
            # Total cost per cubic meter
            total_cost = (energy_cost + chemical_cost + maintenance_cost) / params['flow_rate']
            
            return max(0.1, min(2.0, total_cost))
            
        except Exception as e:
            logger.error(f"Cost simulation failed: {e}")
            return 0.5

    async def _optimize_parameters(self, current_parameters: TreatmentParameters,
                                 climate_analysis) -> TreatmentParameters:
        """Optimize treatment parameters using multi-objective optimization."""
        try:
            # Convert current parameters to array
            param_names = list(self.constraints.keys())
            current_values = [getattr(current_parameters, param) for param in param_names]

            # Define bounds for optimization
            bounds = [(self.constraints[param]['min'], self.constraints[param]['max'])
                     for param in param_names]

            # Extract climate features for optimization
            climate_features = self._extract_climate_features(climate_analysis)

            # Define objective function
            def objective_function(params):
                return self._calculate_objective(params, climate_features)

            # Perform optimization using differential evolution
            result = differential_evolution(
                objective_function,
                bounds,
                seed=42,
                maxiter=100,
                popsize=15
            )

            if result.success:
                optimized_values = result.x
                logger.info(f"Optimization converged with objective value: {result.fun:.4f}")
            else:
                logger.warning("Optimization did not converge, using current parameters")
                optimized_values = current_values

            # Create optimized parameters object
            optimized_params = {}
            for i, param in enumerate(param_names):
                optimized_params[param] = float(optimized_values[i])

            return TreatmentParameters(**optimized_params)

        except Exception as e:
            logger.error(f"Parameter optimization failed: {e}")
            return current_parameters

    def _extract_climate_features(self, climate_analysis) -> List[float]:
        """Extract climate features for optimization."""
        try:
            if not climate_analysis:
                return [20.0, 3.0, 0.5, 0.8]  # Default values

            # Extract temperature and precipitation from climate data
            temp_predictions = climate_analysis.predictive_models.get('temperature_prediction', {})
            precip_predictions = climate_analysis.predictive_models.get('precipitation_prediction', {})

            avg_temp = temp_predictions.get('predicted_value', 20.0)
            avg_precip = precip_predictions.get('predicted_value', 3.0)

            # Extract seasonal factor from patterns
            seasonal_factor = 0.5  # Default
            if climate_analysis.pattern_analysis and 'seasonal_patterns' in climate_analysis.pattern_analysis:
                seasonal_factor = 0.6  # Moderate seasonal variation

            # Extract weather stability from anomaly detection
            weather_stability = 0.8  # Default
            if climate_analysis.anomaly_detection and 'anomaly_detection' in climate_analysis.anomaly_detection:
                anomaly_pct = climate_analysis.anomaly_detection['anomaly_detection'].get('anomaly_percentage', 5)
                weather_stability = max(0.2, 1.0 - (anomaly_pct / 100))

            return [avg_temp, avg_precip, seasonal_factor, weather_stability]

        except Exception as e:
            logger.error(f"Climate feature extraction failed: {e}")
            return [20.0, 3.0, 0.5, 0.8]

    def _calculate_objective(self, params: np.ndarray, climate_features: List[float]) -> float:
        """Calculate multi-objective optimization function."""
        try:
            # Convert parameters to dictionary
            param_names = list(self.constraints.keys())
            param_dict = {param_names[i]: params[i] for i in range(len(params))}

            # Simulate performance
            performance = self._simulate_performance(param_dict, climate_features)
            cost = self._simulate_cost(param_dict, performance)

            # Calculate weighted objective (minimize)
            objective = 0.0

            # Efficiency (maximize -> minimize negative)
            objective -= self.optimization_weights['efficiency'] * performance['efficiency']

            # Energy efficiency (maximize -> minimize negative)
            objective -= self.optimization_weights['energy_efficiency'] * performance['energy_efficiency']

            # Water quality (maximize -> minimize negative)
            objective -= self.optimization_weights['water_quality'] * performance['water_quality_score']

            # Cost (minimize)
            objective += self.optimization_weights['cost'] * cost

            # Carbon footprint (minimize)
            objective += self.optimization_weights['carbon_footprint'] * performance['carbon_footprint']

            # Compliance (maximize -> minimize negative)
            objective -= self.optimization_weights['compliance'] * performance['compliance_score']

            # Add penalty for extreme parameter values
            penalty = 0.0
            for i, param in enumerate(param_names):
                bounds = self.constraints[param]
                param_range = bounds['max'] - bounds['min']
                normalized_param = (params[i] - bounds['min']) / param_range

                # Penalty for extreme values (prefer middle range)
                if normalized_param < 0.1 or normalized_param > 0.9:
                    penalty += 0.1

            objective += penalty

            return objective

        except Exception as e:
            logger.error(f"Objective calculation failed: {e}")
            return 1.0  # High objective value (bad)

    async def _calculate_performance(self, parameters: TreatmentParameters,
                                   climate_analysis) -> TreatmentPerformance:
        """Calculate treatment performance metrics."""
        try:
            # Convert parameters to dictionary
            param_dict = asdict(parameters)

            # Extract climate features
            climate_features = self._extract_climate_features(climate_analysis)

            # Simulate performance
            performance = self._simulate_performance(param_dict, climate_features)
            cost = self._simulate_cost(param_dict, performance)

            # Calculate overall score
            overall_score = (
                performance['efficiency'] * 0.25 +
                performance['energy_efficiency'] * 0.2 +
                performance['water_quality_score'] * 0.2 +
                (1.0 - min(1.0, cost)) * 0.15 +  # Invert cost for score
                (1.0 - performance['carbon_footprint']) * 0.1 +
                performance['compliance_score'] * 0.1
            )

            return TreatmentPerformance(
                efficiency=performance['efficiency'],
                energy_efficiency=performance['energy_efficiency'],
                water_quality_score=performance['water_quality_score'],
                cost_per_cubic_meter=cost,
                carbon_footprint=performance['carbon_footprint'],
                compliance_score=performance['compliance_score'],
                maintenance_score=performance['maintenance_score'],
                overall_score=overall_score
            )

        except Exception as e:
            logger.error(f"Performance calculation failed: {e}")
            return TreatmentPerformance(
                efficiency=0.8, energy_efficiency=0.5, water_quality_score=0.8,
                cost_per_cubic_meter=0.5, carbon_footprint=0.4, compliance_score=0.9,
                maintenance_score=0.8, overall_score=0.7
            )

    async def _calculate_improvements(self, current: TreatmentPerformance,
                                    optimized: TreatmentPerformance) -> Dict[str, float]:
        """Calculate improvement metrics."""
        try:
            improvements = {}

            # Calculate percentage improvements
            improvements['efficiency_improvement'] = (
                (optimized.efficiency - current.efficiency) / current.efficiency * 100
            )
            improvements['energy_efficiency_improvement'] = (
                (optimized.energy_efficiency - current.energy_efficiency) / current.energy_efficiency * 100
            )
            improvements['water_quality_improvement'] = (
                (optimized.water_quality_score - current.water_quality_score) / current.water_quality_score * 100
            )
            improvements['cost_reduction'] = (
                (current.cost_per_cubic_meter - optimized.cost_per_cubic_meter) / current.cost_per_cubic_meter * 100
            )
            improvements['carbon_reduction'] = (
                (current.carbon_footprint - optimized.carbon_footprint) / current.carbon_footprint * 100
            )
            improvements['overall_improvement'] = (
                (optimized.overall_score - current.overall_score) / current.overall_score * 100
            )

            return improvements

        except Exception as e:
            logger.error(f"Improvement calculation failed: {e}")
            return {}

    async def _generate_optimization_recommendations(self, current_parameters: TreatmentParameters,
                                                   optimized_parameters: TreatmentParameters,
                                                   climate_analysis) -> List[OptimizationRecommendation]:
        """Generate optimization recommendations."""
        try:
            recommendations = []

            # Parameter change recommendations
            param_changes = await self._analyze_parameter_changes(current_parameters, optimized_parameters)
            recommendations.extend(param_changes)

            # Climate-based recommendations
            climate_recommendations = await self._generate_climate_recommendations(climate_analysis)
            recommendations.extend(climate_recommendations)

            # Operational recommendations
            operational_recommendations = await self._generate_operational_recommendations(
                optimized_parameters, climate_analysis
            )
            recommendations.extend(operational_recommendations)

            # Maintenance recommendations
            maintenance_recommendations = await self._generate_maintenance_recommendations(
                optimized_parameters
            )
            recommendations.extend(maintenance_recommendations)

            # Sort by priority and expected improvement
            recommendations.sort(key=lambda x: (
                {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}.get(x.priority, 0),
                sum(x.expected_improvement.values())
            ), reverse=True)

            return recommendations[:15]  # Return top 15 recommendations

        except Exception as e:
            logger.error(f"Recommendation generation failed: {e}")
            return []

    async def _analyze_parameter_changes(self, current: TreatmentParameters,
                                       optimized: TreatmentParameters) -> List[OptimizationRecommendation]:
        """Analyze parameter changes and generate recommendations."""
        recommendations = []

        try:
            current_dict = asdict(current)
            optimized_dict = asdict(optimized)

            for param, current_value in current_dict.items():
                optimized_value = optimized_dict[param]
                change_pct = abs((optimized_value - current_value) / current_value * 100)

                if change_pct > 5:  # Significant change (>5%)
                    direction = "increase" if optimized_value > current_value else "decrease"

                    recommendation = OptimizationRecommendation(
                        recommendation_id=f"param_change_{param}",
                        recommendation_type='operational',
                        title=f"Optimize {param.replace('_', ' ').title()}",
                        description=f"{direction.title()} {param.replace('_', ' ')} from {current_value:.2f} to {optimized_value:.2f} ({change_pct:.1f}% change)",
                        priority='high' if change_pct > 20 else 'medium',
                        expected_improvement={
                            'efficiency': 2.0 if change_pct > 20 else 1.0,
                            'cost_reduction': 1.5 if change_pct > 20 else 0.8
                        },
                        implementation_cost=100 * (change_pct / 100),  # Cost proportional to change
                        payback_period=6.0,  # 6 months average
                        climate_factors=['operational_optimization'],
                        actionable_steps=[
                            f"Gradually adjust {param.replace('_', ' ')} to target value",
                            f"Monitor system performance during adjustment",
                            f"Validate {param.replace('_', ' ')} optimization results"
                        ],
                        monitoring_requirements=[
                            f"Monitor {param.replace('_', ' ')} continuously",
                            "Track performance metrics",
                            "Record energy consumption"
                        ],
                        timestamp=datetime.now()
                    )
                    recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Parameter change analysis failed: {e}")
            return []

    async def _generate_climate_recommendations(self, climate_analysis) -> List[OptimizationRecommendation]:
        """Generate climate-based recommendations."""
        recommendations = []

        try:
            if not climate_analysis:
                return recommendations

            # Temperature-based recommendations
            temp_predictions = climate_analysis.predictive_models.get('temperature_prediction', {})
            predicted_temp = temp_predictions.get('predicted_value', 20.0)

            if predicted_temp > 30:
                recommendation = OptimizationRecommendation(
                    recommendation_id="climate_high_temp",
                    recommendation_type='operational',
                    title="High Temperature Adaptation",
                    description=f"Predicted high temperature ({predicted_temp:.1f}°C) requires cooling system optimization",
                    priority='high',
                    expected_improvement={'efficiency': 3.0, 'energy_efficiency': 2.0},
                    implementation_cost=500,
                    payback_period=4.0,
                    climate_factors=['high_temperature'],
                    actionable_steps=[
                        "Activate enhanced cooling systems",
                        "Reduce chemical reaction rates",
                        "Increase monitoring frequency"
                    ],
                    monitoring_requirements=[
                        "Monitor temperature continuously",
                        "Track cooling system performance",
                        "Monitor treatment efficiency"
                    ],
                    timestamp=datetime.now()
                )
                recommendations.append(recommendation)

            elif predicted_temp < 5:
                recommendation = OptimizationRecommendation(
                    recommendation_id="climate_low_temp",
                    recommendation_type='operational',
                    title="Low Temperature Adaptation",
                    description=f"Predicted low temperature ({predicted_temp:.1f}°C) requires heating system activation",
                    priority='high',
                    expected_improvement={'efficiency': 2.5, 'compliance': 1.5},
                    implementation_cost=400,
                    payback_period=5.0,
                    climate_factors=['low_temperature'],
                    actionable_steps=[
                        "Activate heating systems",
                        "Increase chemical dosing rates",
                        "Extend retention times"
                    ],
                    monitoring_requirements=[
                        "Monitor temperature continuously",
                        "Track heating system performance",
                        "Monitor biological activity"
                    ],
                    timestamp=datetime.now()
                )
                recommendations.append(recommendation)

            # Anomaly-based recommendations
            if climate_analysis.anomaly_detection:
                anomaly_data = climate_analysis.anomaly_detection.get('anomaly_detection', {})
                anomaly_pct = anomaly_data.get('anomaly_percentage', 0)

                if anomaly_pct > 15:
                    recommendation = OptimizationRecommendation(
                        recommendation_id="climate_high_variability",
                        recommendation_type='operational',
                        title="High Climate Variability Adaptation",
                        description=f"High climate variability ({anomaly_pct:.1f}% anomalies) requires adaptive control",
                        priority='medium',
                        expected_improvement={'resilience': 4.0, 'compliance': 2.0},
                        implementation_cost=800,
                        payback_period=8.0,
                        climate_factors=['climate_variability'],
                        actionable_steps=[
                            "Implement adaptive control systems",
                            "Increase system buffer capacity",
                            "Enhance monitoring systems"
                        ],
                        monitoring_requirements=[
                            "Monitor climate parameters continuously",
                            "Track system adaptability",
                            "Monitor performance stability"
                        ],
                        timestamp=datetime.now()
                    )
                    recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Climate recommendation generation failed: {e}")
            return []

    async def _generate_operational_recommendations(self, optimized_parameters: TreatmentParameters,
                                                  climate_analysis) -> List[OptimizationRecommendation]:
        """Generate operational recommendations."""
        recommendations = []

        try:
            # Energy efficiency recommendation
            if optimized_parameters.energy_consumption > 50:
                recommendation = OptimizationRecommendation(
                    recommendation_id="operational_energy_efficiency",
                    recommendation_type='operational',
                    title="Energy Efficiency Optimization",
                    description="High energy consumption detected, implement energy-saving measures",
                    priority='medium',
                    expected_improvement={'energy_efficiency': 15.0, 'cost_reduction': 10.0},
                    implementation_cost=1000,
                    payback_period=12.0,
                    climate_factors=['energy_optimization'],
                    actionable_steps=[
                        "Optimize pump scheduling",
                        "Implement variable frequency drives",
                        "Upgrade to energy-efficient equipment"
                    ],
                    monitoring_requirements=[
                        "Monitor energy consumption hourly",
                        "Track equipment efficiency",
                        "Monitor cost savings"
                    ],
                    timestamp=datetime.now()
                )
                recommendations.append(recommendation)

            # Water quality optimization
            if optimized_parameters.chemical_dosing_rate > 30:
                recommendation = OptimizationRecommendation(
                    recommendation_id="operational_chemical_optimization",
                    recommendation_type='operational',
                    title="Chemical Dosing Optimization",
                    description="High chemical dosing rate, optimize chemical usage",
                    priority='medium',
                    expected_improvement={'cost_reduction': 8.0, 'environmental_impact': 5.0},
                    implementation_cost=300,
                    payback_period=6.0,
                    climate_factors=['chemical_optimization'],
                    actionable_steps=[
                        "Implement real-time dosing control",
                        "Optimize chemical mixing",
                        "Consider alternative chemicals"
                    ],
                    monitoring_requirements=[
                        "Monitor chemical consumption",
                        "Track water quality parameters",
                        "Monitor cost impact"
                    ],
                    timestamp=datetime.now()
                )
                recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Operational recommendation generation failed: {e}")
            return []

    async def _generate_maintenance_recommendations(self, optimized_parameters: TreatmentParameters) -> List[OptimizationRecommendation]:
        """Generate maintenance recommendations."""
        recommendations = []

        try:
            # High pump speed maintenance
            if optimized_parameters.pump_speed > 90:
                recommendation = OptimizationRecommendation(
                    recommendation_id="maintenance_pump_stress",
                    recommendation_type='maintenance',
                    title="Pump Maintenance Schedule",
                    description="High pump speed operation requires enhanced maintenance",
                    priority='medium',
                    expected_improvement={'maintenance_score': 10.0, 'reliability': 8.0},
                    implementation_cost=500,
                    payback_period=18.0,
                    climate_factors=['equipment_stress'],
                    actionable_steps=[
                        "Increase pump inspection frequency",
                        "Monitor vibration and temperature",
                        "Schedule preventive maintenance"
                    ],
                    monitoring_requirements=[
                        "Monitor pump performance daily",
                        "Track maintenance costs",
                        "Monitor equipment reliability"
                    ],
                    timestamp=datetime.now()
                )
                recommendations.append(recommendation)

            # High backwash frequency
            if optimized_parameters.backwash_frequency > 6:
                recommendation = OptimizationRecommendation(
                    recommendation_id="maintenance_filter_optimization",
                    recommendation_type='maintenance',
                    title="Filter System Optimization",
                    description="High backwash frequency indicates filter optimization needed",
                    priority='low',
                    expected_improvement={'efficiency': 3.0, 'water_savings': 5.0},
                    implementation_cost=200,
                    payback_period=10.0,
                    climate_factors=['filter_optimization'],
                    actionable_steps=[
                        "Optimize filter media",
                        "Adjust backwash timing",
                        "Monitor filter performance"
                    ],
                    monitoring_requirements=[
                        "Monitor filter pressure drop",
                        "Track backwash water usage",
                        "Monitor filtration efficiency"
                    ],
                    timestamp=datetime.now()
                )
                recommendations.append(recommendation)

            return recommendations

        except Exception as e:
            logger.error(f"Maintenance recommendation generation failed: {e}")
            return []

    async def _calculate_optimization_confidence(self, climate_analysis, improvement_metrics: Dict[str, float]) -> float:
        """Calculate confidence in optimization results."""
        try:
            confidence = 0.7  # Base confidence

            # Adjust based on climate analysis quality
            if climate_analysis and climate_analysis.model_performance:
                avg_performance = np.mean(list(climate_analysis.model_performance.values()))
                confidence += (avg_performance - 0.5) * 0.3  # Adjust based on model performance

            # Adjust based on improvement magnitude
            if improvement_metrics:
                avg_improvement = np.mean([abs(imp) for imp in improvement_metrics.values()])
                if avg_improvement > 10:  # High improvement
                    confidence += 0.1
                elif avg_improvement < 2:  # Low improvement
                    confidence -= 0.1

            return max(0.3, min(1.0, confidence))

        except Exception as e:
            logger.error(f"Confidence calculation failed: {e}")
            return 0.5

    async def _generate_implementation_timeline(self, recommendations: List[OptimizationRecommendation]) -> Dict[str, str]:
        """Generate implementation timeline."""
        try:
            timeline = {}

            # Immediate actions (0-1 month)
            immediate = [r for r in recommendations if r.priority == 'critical']
            if immediate:
                timeline['immediate'] = f"{len(immediate)} critical recommendations"

            # Short-term actions (1-3 months)
            short_term = [r for r in recommendations if r.priority == 'high']
            if short_term:
                timeline['short_term'] = f"{len(short_term)} high-priority recommendations"

            # Medium-term actions (3-6 months)
            medium_term = [r for r in recommendations if r.priority == 'medium']
            if medium_term:
                timeline['medium_term'] = f"{len(medium_term)} medium-priority recommendations"

            # Long-term actions (6+ months)
            long_term = [r for r in recommendations if r.priority == 'low']
            if long_term:
                timeline['long_term'] = f"{len(long_term)} low-priority recommendations"

            return timeline

        except Exception as e:
            logger.error(f"Timeline generation failed: {e}")
            return {}


# Convenience functions
async def optimize_water_treatment(climate_data: List[ProcessedClimateData],
                                 current_parameters: TreatmentParameters = None,
                                 location: str = None) -> OptimizationResult:
    """Optimize water treatment system using AI insights."""
    agent = WaterTreatmentOptimizationAgent()
    await agent.initialize()

    return await agent.optimize_treatment_system(climate_data, current_parameters, location)


async def get_treatment_recommendations(climate_data: List[ProcessedClimateData],
                                      location: str = None) -> List[OptimizationRecommendation]:
    """Get water treatment optimization recommendations."""
    agent = WaterTreatmentOptimizationAgent()
    await agent.initialize()

    result = await agent.optimize_treatment_system(climate_data, None, location)
    return result.recommendations if result else []


async def calculate_treatment_performance(parameters: TreatmentParameters,
                                        climate_data: List[ProcessedClimateData]) -> TreatmentPerformance:
    """Calculate treatment performance for given parameters."""
    agent = WaterTreatmentOptimizationAgent()
    await agent.initialize()

    climate_analysis = await analyze_climate_with_ai(climate_data)
    return await agent._calculate_performance(parameters, climate_analysis)
