version: '3.8'

services:
  # =============================================================================
  # DATABASE SERVICES
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: watermanagement-postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-watermanagement}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - watermanagement-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: watermanagement-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - watermanagement-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-password}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # APPLICATION SERVICES
  # =============================================================================
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: watermanagement-backend
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-watermanagement}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-password}@redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OPENWEATHER_API_KEY=${OPENWEATHER_API_KEY}
      - NASA_API_KEY=${NASA_API_KEY}
      - NOAA_API_TOKEN=${NOAA_API_TOKEN}
    ports:
      - "8000:8000"
    volumes:
      - ./src:/app/src
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - watermanagement-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: watermanagement-frontend
    environment:
      - REACT_APP_API_URL=http://backend:8000
      - REACT_APP_ENVIRONMENT=${APP_ENVIRONMENT:-development}
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - watermanagement-network
    restart: unless-stopped

  streamlit:
    build:
      context: .
      dockerfile: Dockerfile.streamlit
    container_name: watermanagement-streamlit
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-watermanagement}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-password}@redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    ports:
      - "8501:8501"
    volumes:
      - ./src:/app/src
      - ./data:/app/data
    depends_on:
      - postgres
      - redis
    networks:
      - watermanagement-network
    restart: unless-stopped

  # =============================================================================
  # MONITORING AND OBSERVABILITY
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: watermanagement-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - watermanagement-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: watermanagement-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - watermanagement-network
    restart: unless-stopped

  # =============================================================================
  # MESSAGE QUEUE AND TASK PROCESSING
  # =============================================================================
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: watermanagement-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-admin}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-password}
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - watermanagement-network
    restart: unless-stopped

  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: watermanagement-celery-worker
    command: celery -A src.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-watermanagement}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-password}@redis:6379/0
      - CELERY_BROKER_URL=pyamqp://${RABBITMQ_USER:-admin}:${RABBITMQ_PASSWORD:-password}@rabbitmq:5672//
    volumes:
      - ./src:/app/src
      - ./data:/app/data
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - watermanagement-network
    restart: unless-stopped

  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: watermanagement-celery-beat
    command: celery -A src.tasks.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-watermanagement}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-password}@redis:6379/0
      - CELERY_BROKER_URL=pyamqp://${RABBITMQ_USER:-admin}:${RABBITMQ_PASSWORD:-password}@rabbitmq:5672//
    volumes:
      - ./src:/app/src
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - watermanagement-network
    restart: unless-stopped

  # =============================================================================
  # MACHINE LEARNING SERVICES
  # =============================================================================
  mlflow:
    image: python:3.9-slim
    container_name: watermanagement-mlflow
    command: >
      bash -c "pip install mlflow psycopg2-binary &&
               mlflow server --host 0.0.0.0 --port 5000 
               --backend-store-uri postgresql://${DB_USER:-postgres}:${DB_PASSWORD:-password}@postgres:5432/${DB_NAME:-watermanagement}
               --default-artifact-root ./mlruns"
    ports:
      - "5000:5000"
    volumes:
      - mlflow_data:/mlruns
    depends_on:
      - postgres
    networks:
      - watermanagement-network
    restart: unless-stopped

  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: watermanagement-jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-watermanagement}
    volumes:
      - ./notebooks:/home/<USER>/work/notebooks
      - ./src:/home/<USER>/work/src
      - ./data:/home/<USER>/work/data
    networks:
      - watermanagement-network
    restart: unless-stopped

  # =============================================================================
  # REVERSE PROXY AND LOAD BALANCER
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: watermanagement-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
      - streamlit
    networks:
      - watermanagement-network
    restart: unless-stopped

# =============================================================================
# NETWORKS AND VOLUMES
# =============================================================================
networks:
  watermanagement-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:
  mlflow_data:
