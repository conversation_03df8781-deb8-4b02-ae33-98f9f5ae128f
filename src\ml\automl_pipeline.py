"""AutoML Pipeline for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import pickle
from pathlib import Path

# ML libraries
try:
    from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, RandomizedSearchCV
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
    from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
    from sklearn.svm import SVR
    from sklearn.neural_network import MLPRegressor
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
    from sklearn.feature_selection import SelectKBest, f_regression, RFE
    from sklearn.pipeline import Pipeline
    from sklearn.compose import ColumnTransformer
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("Scikit-learn not available. AutoML functionality will be limited.")

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Types of ML models."""
    REGRESSION = "regression"
    CLASSIFICATION = "classification"
    TIME_SERIES = "time_series"
    CLUSTERING = "clustering"
    ANOMALY_DETECTION = "anomaly_detection"


class OptimizationStrategy(Enum):
    """Hyperparameter optimization strategies."""
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"
    BAYESIAN_OPTIMIZATION = "bayesian_optimization"
    EVOLUTIONARY = "evolutionary"
    AUTO = "auto"


class FeatureSelectionMethod(Enum):
    """Feature selection methods."""
    UNIVARIATE = "univariate"
    RECURSIVE = "recursive"
    LASSO = "lasso"
    TREE_BASED = "tree_based"
    CORRELATION = "correlation"


@dataclass
class ModelCandidate:
    """ML model candidate."""
    model_id: str
    model_name: str
    model_type: ModelType
    algorithm: str
    hyperparameters: Dict[str, Any]
    cv_score: float
    training_time: float
    prediction_time: float
    model_size: int
    feature_importance: Dict[str, float] = field(default_factory=dict)


@dataclass
class AutoMLExperiment:
    """AutoML experiment configuration and results."""
    experiment_id: str
    problem_type: ModelType
    target_variable: str
    feature_columns: List[str]
    optimization_metric: str
    time_budget: int  # minutes
    model_candidates: List[ModelCandidate] = field(default_factory=list)
    best_model: Optional[ModelCandidate] = None
    experiment_start: datetime = field(default_factory=datetime.now)
    experiment_end: Optional[datetime] = None
    status: str = "running"


class AutoMLPipeline:
    """Automated Machine Learning Pipeline for water management."""
    
    def __init__(self):
        self.experiments: Dict[str, AutoMLExperiment] = {}
        self.trained_models: Dict[str, Any] = {}
        self.feature_processors: Dict[str, Any] = {}
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'class': RandomForestRegressor if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'n_estimators': [50, 100, 200],
                    'max_depth': [None, 10, 20, 30],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                }
            },
            'gradient_boosting': {
                'class': GradientBoostingRegressor if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'n_estimators': [50, 100, 200],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0]
                }
            },
            'linear_regression': {
                'class': LinearRegression if SKLEARN_AVAILABLE else None,
                'param_grid': {}
            },
            'ridge_regression': {
                'class': Ridge if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'alpha': [0.1, 1.0, 10.0, 100.0]
                }
            },
            'lasso_regression': {
                'class': Lasso if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'alpha': [0.1, 1.0, 10.0, 100.0]
                }
            },
            'elastic_net': {
                'class': ElasticNet if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'alpha': [0.1, 1.0, 10.0],
                    'l1_ratio': [0.1, 0.5, 0.7, 0.9]
                }
            },
            'svr': {
                'class': SVR if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'C': [0.1, 1, 10, 100],
                    'gamma': ['scale', 'auto', 0.001, 0.01],
                    'kernel': ['rbf', 'linear', 'poly']
                }
            },
            'mlp_regressor': {
                'class': MLPRegressor if SKLEARN_AVAILABLE else None,
                'param_grid': {
                    'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
                    'activation': ['relu', 'tanh'],
                    'alpha': [0.0001, 0.001, 0.01],
                    'learning_rate': ['constant', 'adaptive']
                }
            }
        }
    
    @log_async_function_call
    async def start_automl_experiment(self, experiment_config: Dict[str, Any]) -> Dict[str, Any]:
        """Start automated ML experiment."""
        try:
            if not SKLEARN_AVAILABLE:
                return {'status': 'error', 'error': 'Scikit-learn not available for AutoML'}
            
            experiment_id = experiment_config.get('experiment_id', f"automl_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            # Create experiment
            experiment = AutoMLExperiment(
                experiment_id=experiment_id,
                problem_type=ModelType(experiment_config['problem_type']),
                target_variable=experiment_config['target_variable'],
                feature_columns=experiment_config.get('feature_columns', []),
                optimization_metric=experiment_config.get('optimization_metric', 'r2'),
                time_budget=experiment_config.get('time_budget', 60)  # 60 minutes default
            )
            
            self.experiments[experiment_id] = experiment
            
            # Load and prepare data
            data_result = await self._prepare_data(experiment_config)
            if data_result['status'] != 'success':
                return data_result
            
            X_train, X_test, y_train, y_test = data_result['data_splits']
            
            # Feature engineering and selection
            feature_result = await self._engineer_features(X_train, y_train, experiment)
            if feature_result['status'] != 'success':
                return feature_result
            
            X_train_processed = feature_result['X_processed']
            feature_processor = feature_result['processor']
            
            # Model selection and training
            model_result = await self._train_candidate_models(
                X_train_processed, y_train, experiment
            )
            if model_result['status'] != 'success':
                return model_result
            
            # Select best model
            best_model = await self._select_best_model(experiment)
            
            # Final evaluation
            evaluation_result = await self._evaluate_final_model(
                best_model, X_test, y_test, feature_processor, experiment
            )
            
            # Update experiment status
            experiment.experiment_end = datetime.now()
            experiment.status = "completed"
            experiment.best_model = best_model
            
            return {
                'status': 'success',
                'experiment_id': experiment_id,
                'experiment_summary': {
                    'problem_type': experiment.problem_type.value,
                    'models_evaluated': len(experiment.model_candidates),
                    'best_model': best_model.algorithm if best_model else None,
                    'best_score': best_model.cv_score if best_model else None,
                    'experiment_duration': (experiment.experiment_end - experiment.experiment_start).total_seconds() / 60
                },
                'model_performance': evaluation_result,
                'feature_importance': best_model.feature_importance if best_model else {},
                'recommendations': await self._generate_recommendations(experiment)
            }
            
        except Exception as e:
            logger.error(f"AutoML experiment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _prepare_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for AutoML experiment."""
        try:
            # Generate synthetic data for demonstration
            # In practice, would load real data from config['data_source']
            n_samples = config.get('n_samples', 1000)
            n_features = len(config.get('feature_columns', [])) or 10
            
            # Generate synthetic water management data
            np.random.seed(42)
            
            # Features: pH, turbidity, flow_rate, temperature, etc.
            X = np.random.randn(n_samples, n_features)
            
            # Add some realistic relationships
            if n_features >= 5:
                # pH affects water quality
                X[:, 0] = np.random.normal(7.2, 0.5, n_samples)  # pH around 7.2
                # Turbidity
                X[:, 1] = np.random.exponential(2.0, n_samples)  # Turbidity
                # Flow rate
                X[:, 2] = np.random.normal(1000, 200, n_samples)  # Flow rate
                # Temperature
                X[:, 3] = np.random.normal(20, 5, n_samples)  # Temperature
                # Energy consumption
                X[:, 4] = np.random.normal(300, 50, n_samples)  # Energy
            
            # Target variable (e.g., water quality score)
            if config['problem_type'] == 'regression':
                # Water quality score based on features
                y = (
                    10 * (1 - np.abs(X[:, 0] - 7.2) / 2) +  # pH contribution
                    5 * (1 / (1 + X[:, 1])) +  # Turbidity contribution (lower is better)
                    0.001 * X[:, 2] +  # Flow rate contribution
                    0.1 * (25 - np.abs(X[:, 3] - 20)) +  # Temperature contribution
                    np.random.normal(0, 1, n_samples)  # Noise
                )
                y = np.clip(y, 0, 100)  # Quality score 0-100
            else:
                # Classification target
                y = (X[:, 0] > 7.0).astype(int)  # Binary classification
            
            # Create DataFrame
            feature_names = config.get('feature_columns') or [f'feature_{i}' for i in range(n_features)]
            df = pd.DataFrame(X, columns=feature_names)
            df[config['target_variable']] = y
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                df[feature_names], df[config['target_variable']], 
                test_size=0.2, random_state=42
            )
            
            return {
                'status': 'success',
                'data_splits': (X_train, X_test, y_train, y_test),
                'data_info': {
                    'n_samples': n_samples,
                    'n_features': n_features,
                    'train_size': len(X_train),
                    'test_size': len(X_test),
                    'feature_names': feature_names
                }
            }
            
        except Exception as e:
            logger.error(f"Data preparation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _engineer_features(self, X_train: pd.DataFrame, y_train: pd.Series, 
                                experiment: AutoMLExperiment) -> Dict[str, Any]:
        """Perform feature engineering and selection."""
        try:
            # Create preprocessing pipeline
            numeric_features = X_train.select_dtypes(include=[np.number]).columns.tolist()
            
            # Preprocessing steps
            preprocessor = ColumnTransformer(
                transformers=[
                    ('num', StandardScaler(), numeric_features)
                ],
                remainder='passthrough'
            )
            
            # Fit preprocessor
            X_processed = preprocessor.fit_transform(X_train)
            
            # Feature selection
            if len(numeric_features) > 10:  # Only if we have many features
                selector = SelectKBest(score_func=f_regression, k=min(10, len(numeric_features)))
                X_processed = selector.fit_transform(X_processed, y_train)
                
                # Update feature names
                selected_features = [numeric_features[i] for i in selector.get_support(indices=True)]
                experiment.feature_columns = selected_features
            
            # Store processor
            self.feature_processors[experiment.experiment_id] = preprocessor
            
            return {
                'status': 'success',
                'X_processed': X_processed,
                'processor': preprocessor,
                'selected_features': experiment.feature_columns
            }
            
        except Exception as e:
            logger.error(f"Feature engineering failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _train_candidate_models(self, X_train: np.ndarray, y_train: pd.Series,
                                    experiment: AutoMLExperiment) -> Dict[str, Any]:
        """Train multiple candidate models."""
        try:
            candidates = []
            
            for model_name, config in self.model_configs.items():
                if config['class'] is None:
                    continue
                
                try:
                    start_time = datetime.now()
                    
                    # Create model
                    model = config['class']()
                    
                    # Hyperparameter optimization
                    if config['param_grid']:
                        # Use RandomizedSearchCV for faster optimization
                        search = RandomizedSearchCV(
                            model, config['param_grid'], 
                            n_iter=10, cv=3, scoring='r2', 
                            random_state=42, n_jobs=-1
                        )
                        search.fit(X_train, y_train)
                        best_model = search.best_estimator_
                        cv_score = search.best_score_
                        best_params = search.best_params_
                    else:
                        # Simple cross-validation
                        cv_scores = cross_val_score(model, X_train, y_train, cv=3, scoring='r2')
                        cv_score = cv_scores.mean()
                        model.fit(X_train, y_train)
                        best_model = model
                        best_params = {}
                    
                    training_time = (datetime.now() - start_time).total_seconds()
                    
                    # Estimate prediction time
                    pred_start = datetime.now()
                    _ = best_model.predict(X_train[:100])  # Sample prediction
                    prediction_time = (datetime.now() - pred_start).total_seconds()
                    
                    # Calculate model size (approximate)
                    model_size = len(pickle.dumps(best_model))
                    
                    # Feature importance (if available)
                    feature_importance = {}
                    if hasattr(best_model, 'feature_importances_'):
                        feature_names = experiment.feature_columns or [f'feature_{i}' for i in range(X_train.shape[1])]
                        feature_importance = dict(zip(feature_names, best_model.feature_importances_))
                    elif hasattr(best_model, 'coef_'):
                        feature_names = experiment.feature_columns or [f'feature_{i}' for i in range(X_train.shape[1])]
                        feature_importance = dict(zip(feature_names, np.abs(best_model.coef_)))
                    
                    # Create candidate
                    candidate = ModelCandidate(
                        model_id=f"{experiment.experiment_id}_{model_name}",
                        model_name=model_name,
                        model_type=experiment.problem_type,
                        algorithm=model_name,
                        hyperparameters=best_params,
                        cv_score=cv_score,
                        training_time=training_time,
                        prediction_time=prediction_time,
                        model_size=model_size,
                        feature_importance=feature_importance
                    )
                    
                    candidates.append(candidate)
                    
                    # Store trained model
                    self.trained_models[candidate.model_id] = best_model
                    
                    logger.info(f"Trained {model_name}: CV Score = {cv_score:.4f}")
                    
                except Exception as model_error:
                    logger.warning(f"Failed to train {model_name}: {model_error}")
                    continue
            
            experiment.model_candidates = candidates
            
            return {
                'status': 'success',
                'models_trained': len(candidates),
                'candidate_scores': {c.algorithm: c.cv_score for c in candidates}
            }
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _select_best_model(self, experiment: AutoMLExperiment) -> Optional[ModelCandidate]:
        """Select best model based on optimization metric."""
        if not experiment.model_candidates:
            return None
        
        # Sort by CV score (assuming higher is better for r2)
        sorted_candidates = sorted(
            experiment.model_candidates, 
            key=lambda x: x.cv_score, 
            reverse=True
        )
        
        return sorted_candidates[0]
    
    async def _evaluate_final_model(self, best_model: ModelCandidate, X_test: pd.DataFrame,
                                  y_test: pd.Series, feature_processor: Any,
                                  experiment: AutoMLExperiment) -> Dict[str, Any]:
        """Evaluate final model on test set."""
        try:
            if best_model is None:
                return {'status': 'error', 'error': 'No best model found'}
            
            # Get trained model
            trained_model = self.trained_models[best_model.model_id]
            
            # Preprocess test data
            X_test_processed = feature_processor.transform(X_test)
            
            # Make predictions
            y_pred = trained_model.predict(X_test_processed)
            
            # Calculate metrics
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            return {
                'status': 'success',
                'test_metrics': {
                    'mean_squared_error': mse,
                    'mean_absolute_error': mae,
                    'r2_score': r2,
                    'rmse': np.sqrt(mse)
                },
                'prediction_stats': {
                    'mean_prediction': np.mean(y_pred),
                    'std_prediction': np.std(y_pred),
                    'min_prediction': np.min(y_pred),
                    'max_prediction': np.max(y_pred)
                }
            }
            
        except Exception as e:
            logger.error(f"Model evaluation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_recommendations(self, experiment: AutoMLExperiment) -> List[str]:
        """Generate recommendations based on experiment results."""
        recommendations = []
        
        if not experiment.model_candidates:
            recommendations.append("No models were successfully trained. Check data quality and feature engineering.")
            return recommendations
        
        best_model = experiment.best_model
        if best_model:
            recommendations.append(f"Best performing model: {best_model.algorithm} with CV score: {best_model.cv_score:.4f}")
            
            # Performance recommendations
            if best_model.cv_score < 0.7:
                recommendations.append("Model performance is below 70%. Consider feature engineering or more data.")
            elif best_model.cv_score > 0.9:
                recommendations.append("Excellent model performance! Consider deploying to production.")
            
            # Efficiency recommendations
            if best_model.training_time > 60:  # More than 1 minute
                recommendations.append("Training time is high. Consider simpler models for faster iteration.")
            
            if best_model.model_size > 1000000:  # More than 1MB
                recommendations.append("Model size is large. Consider model compression for deployment.")
            
            # Feature importance recommendations
            if best_model.feature_importance:
                top_features = sorted(best_model.feature_importance.items(), 
                                    key=lambda x: x[1], reverse=True)[:3]
                feature_names = [f[0] for f in top_features]
                recommendations.append(f"Most important features: {', '.join(feature_names)}")
        
        # Model diversity recommendations
        algorithms_used = set(c.algorithm for c in experiment.model_candidates)
        if len(algorithms_used) < 3:
            recommendations.append("Consider trying more diverse algorithms for better model selection.")
        
        return recommendations
    
    @log_async_function_call
    async def get_experiment_status(self, experiment_id: str) -> Dict[str, Any]:
        """Get status of AutoML experiment."""
        try:
            if experiment_id not in self.experiments:
                return {'status': 'error', 'error': 'Experiment not found'}
            
            experiment = self.experiments[experiment_id]
            
            # Calculate progress
            if experiment.status == "completed":
                progress = 100
            elif experiment.model_candidates:
                progress = min(90, len(experiment.model_candidates) * 10)  # Rough estimate
            else:
                progress = 10  # Just started
            
            return {
                'status': 'success',
                'experiment_info': {
                    'experiment_id': experiment_id,
                    'problem_type': experiment.problem_type.value,
                    'target_variable': experiment.target_variable,
                    'status': experiment.status,
                    'progress_percentage': progress,
                    'start_time': experiment.experiment_start.isoformat(),
                    'end_time': experiment.experiment_end.isoformat() if experiment.experiment_end else None
                },
                'model_candidates': [
                    {
                        'algorithm': c.algorithm,
                        'cv_score': c.cv_score,
                        'training_time': c.training_time,
                        'model_size_kb': c.model_size / 1024
                    }
                    for c in experiment.model_candidates
                ],
                'best_model': {
                    'algorithm': experiment.best_model.algorithm,
                    'cv_score': experiment.best_model.cv_score,
                    'hyperparameters': experiment.best_model.hyperparameters
                } if experiment.best_model else None
            }
            
        except Exception as e:
            logger.error(f"Status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def predict_with_automl_model(self, experiment_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make predictions using trained AutoML model."""
        try:
            if experiment_id not in self.experiments:
                return {'status': 'error', 'error': 'Experiment not found'}
            
            experiment = self.experiments[experiment_id]
            if not experiment.best_model:
                return {'status': 'error', 'error': 'No trained model available'}
            
            # Get trained model and preprocessor
            model = self.trained_models[experiment.best_model.model_id]
            preprocessor = self.feature_processors[experiment_id]
            
            # Prepare input data
            feature_names = experiment.feature_columns
            input_df = pd.DataFrame([input_data], columns=feature_names)
            
            # Preprocess
            X_processed = preprocessor.transform(input_df)
            
            # Make prediction
            prediction = model.predict(X_processed)[0]
            
            # Calculate prediction confidence (simplified)
            if hasattr(model, 'predict_proba'):
                confidence = np.max(model.predict_proba(X_processed)[0])
            else:
                # For regression, use a simple confidence measure
                confidence = 0.8  # Placeholder
            
            return {
                'status': 'success',
                'prediction': float(prediction),
                'confidence': float(confidence),
                'model_info': {
                    'algorithm': experiment.best_model.algorithm,
                    'cv_score': experiment.best_model.cv_score,
                    'features_used': feature_names
                },
                'prediction_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience functions
async def run_automl_experiment(problem_type: str, target_variable: str, 
                              feature_columns: List[str] = None,
                              time_budget: int = 30) -> Dict[str, Any]:
    """Run complete AutoML experiment."""
    pipeline = AutoMLPipeline()
    
    config = {
        'problem_type': problem_type,
        'target_variable': target_variable,
        'feature_columns': feature_columns or ['ph', 'turbidity', 'flow_rate', 'temperature', 'energy'],
        'time_budget': time_budget,
        'n_samples': 1000
    }
    
    return await pipeline.start_automl_experiment(config)


async def predict_water_quality(input_features: Dict[str, float]) -> Dict[str, Any]:
    """Predict water quality using AutoML model."""
    pipeline = AutoMLPipeline()
    
    # First run experiment if not exists
    experiment_result = await run_automl_experiment('regression', 'water_quality_score')
    if experiment_result['status'] != 'success':
        return experiment_result
    
    experiment_id = experiment_result['experiment_id']
    
    # Make prediction
    return await pipeline.predict_with_automl_model(experiment_id, input_features)
