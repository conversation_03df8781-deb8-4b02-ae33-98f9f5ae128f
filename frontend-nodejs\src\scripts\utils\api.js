/**
 * API Client
 * Handles all HTTP requests to the backend API
 */

class APIClient {
    constructor(baseUrl = '/api') {
        this.baseUrl = baseUrl;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }

    // Add request interceptor
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }

    // Add response interceptor
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }

    // Apply request interceptors
    async applyRequestInterceptors(config) {
        let modifiedConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors) {
            modifiedConfig = await interceptor(modifiedConfig);
        }
        
        return modifiedConfig;
    }

    // Apply response interceptors
    async applyResponseInterceptors(response) {
        let modifiedResponse = response;
        
        for (const interceptor of this.responseInterceptors) {
            modifiedResponse = await interceptor(modifiedResponse);
        }
        
        return modifiedResponse;
    }

    // Build URL
    buildUrl(endpoint) {
        const url = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
        return `${this.baseUrl}/${url}`;
    }

    // Make HTTP request
    async request(method, endpoint, options = {}) {
        const config = await this.applyRequestInterceptors({
            method: method.toUpperCase(),
            url: this.buildUrl(endpoint),
            headers: { ...this.defaultHeaders, ...options.headers },
            body: options.body,
            ...options
        });

        try {
            console.log(`🔄 API ${config.method}: ${config.url}`);
            
            const fetchOptions = {
                method: config.method,
                headers: config.headers
            };

            if (config.body && config.method !== 'GET') {
                fetchOptions.body = typeof config.body === 'string' 
                    ? config.body 
                    : JSON.stringify(config.body);
            }

            const response = await fetch(config.url, fetchOptions);
            
            let data;
            const contentType = response.headers.get('content-type');
            
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                data = await response.text();
            }

            const result = {
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                data: data,
                ok: response.ok
            };

            if (!response.ok) {
                throw new APIError(
                    data.message || `HTTP ${response.status}: ${response.statusText}`,
                    response.status,
                    data
                );
            }

            console.log(`✅ API ${config.method}: ${config.url} - ${response.status}`);
            
            return await this.applyResponseInterceptors(result);

        } catch (error) {
            console.error(`❌ API ${config.method}: ${config.url} - ${error.message}`);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError(
                error.message || 'Network error',
                0,
                null
            );
        }
    }

    // GET request
    async get(endpoint, options = {}) {
        const response = await this.request('GET', endpoint, options);
        return response.data;
    }

    // POST request
    async post(endpoint, data = null, options = {}) {
        const response = await this.request('POST', endpoint, {
            ...options,
            body: data
        });
        return response.data;
    }

    // PUT request
    async put(endpoint, data = null, options = {}) {
        const response = await this.request('PUT', endpoint, {
            ...options,
            body: data
        });
        return response.data;
    }

    // DELETE request
    async delete(endpoint, options = {}) {
        const response = await this.request('DELETE', endpoint, options);
        return response.data;
    }

    // PATCH request
    async patch(endpoint, data = null, options = {}) {
        const response = await this.request('PATCH', endpoint, {
            ...options,
            body: data
        });
        return response.data;
    }

    // Upload file
    async upload(endpoint, file, options = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // Add additional fields if provided
        if (options.fields) {
            Object.entries(options.fields).forEach(([key, value]) => {
                formData.append(key, value);
            });
        }

        const uploadOptions = {
            ...options,
            headers: {
                // Don't set Content-Type for FormData - browser will set it with boundary
                ...options.headers
            },
            body: formData
        };

        // Remove Content-Type header for file uploads
        delete uploadOptions.headers['Content-Type'];

        const response = await this.request('POST', endpoint, uploadOptions);
        return response.data;
    }

    // Download file
    async download(endpoint, filename, options = {}) {
        try {
            const response = await fetch(this.buildUrl(endpoint), {
                method: 'GET',
                headers: { ...this.defaultHeaders, ...options.headers }
            });

            if (!response.ok) {
                throw new Error(`Download failed: ${response.statusText}`);
            }

            const blob = await response.blob();
            
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || 'download';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            return true;
        } catch (error) {
            console.error('Download error:', error);
            throw error;
        }
    }

    // Batch requests
    async batch(requests) {
        const promises = requests.map(req => 
            this.request(req.method, req.endpoint, req.options)
                .catch(error => ({ error, request: req }))
        );

        const results = await Promise.all(promises);
        
        return results.map((result, index) => ({
            request: requests[index],
            success: !result.error,
            data: result.error ? null : result.data,
            error: result.error || null
        }));
    }

    // Health check
    async healthCheck() {
        try {
            const response = await this.get('/health');
            return {
                healthy: true,
                data: response
            };
        } catch (error) {
            return {
                healthy: false,
                error: error.message
            };
        }
    }

    // Set authentication token
    setAuthToken(token) {
        if (token) {
            this.defaultHeaders['Authorization'] = `Bearer ${token}`;
        } else {
            delete this.defaultHeaders['Authorization'];
        }
    }

    // Set custom header
    setHeader(name, value) {
        if (value) {
            this.defaultHeaders[name] = value;
        } else {
            delete this.defaultHeaders[name];
        }
    }
}

// Custom API Error class
class APIError extends Error {
    constructor(message, status = 0, data = null) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.data = data;
    }
}

// Export to global scope
window.APIClient = APIClient;
window.APIError = APIError;
