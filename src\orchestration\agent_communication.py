"""
Multi-Agent Communication Protocols.

This module provides comprehensive communication protocols for coordinating
multiple AI agents in the water management decarbonisation system, enabling
seamless data sharing, decision coordination, and collaborative optimization.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from datetime import datetime, timedelta
import json
import uuid
from dataclasses import dataclass, asdict
from enum import Enum
import weakref
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of inter-agent messages."""
    DATA_SHARE = "data_share"
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    COORDINATION = "coordination"
    OPTIMIZATION = "optimization"
    ALERT = "alert"
    HEARTBEAT = "heartbeat"
    SHUTDOWN = "shutdown"


class MessagePriority(Enum):
    """Message priority levels."""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4


class AgentStatus(Enum):
    """Agent status states."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    IDLE = "idle"
    ERROR = "error"
    SHUTDOWN = "shutdown"


@dataclass
class AgentMessage:
    """Inter-agent communication message."""
    message_id: str
    sender_id: str
    receiver_id: str
    message_type: MessageType
    priority: MessagePriority
    payload: Dict[str, Any]
    timestamp: datetime
    expires_at: Optional[datetime] = None
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    requires_response: bool = False
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class AgentCapability:
    """Agent capability description."""
    capability_id: str
    name: str
    description: str
    input_types: List[str]
    output_types: List[str]
    processing_time_estimate: float  # seconds
    resource_requirements: Dict[str, Any]
    dependencies: List[str]


@dataclass
class AgentRegistration:
    """Agent registration information."""
    agent_id: str
    agent_type: str
    name: str
    description: str
    capabilities: List[AgentCapability]
    status: AgentStatus
    last_heartbeat: datetime
    endpoint: Optional[str] = None
    metadata: Dict[str, Any] = None


@dataclass
class CommunicationMetrics:
    """Communication performance metrics."""
    total_messages_sent: int = 0
    total_messages_received: int = 0
    total_messages_failed: int = 0
    average_response_time: float = 0.0
    message_throughput: float = 0.0
    error_rate: float = 0.0
    active_connections: int = 0
    last_updated: datetime = None


class MessageBus:
    """
    Central message bus for inter-agent communication.
    
    Provides:
    - Asynchronous message routing and delivery
    - Message queuing and buffering
    - Priority-based message handling
    - Message persistence and reliability
    - Broadcast and multicast capabilities
    - Message filtering and routing rules
    - Performance monitoring and metrics
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_running = False
        
        # Message queues by agent ID
        self.message_queues: Dict[str, deque] = defaultdict(deque)
        
        # Message routing table
        self.routing_table: Dict[str, str] = {}
        
        # Message handlers by message type
        self.message_handlers: Dict[MessageType, List[Callable]] = defaultdict(list)
        
        # Active subscriptions
        self.subscriptions: Dict[str, List[str]] = defaultdict(list)
        
        # Message history for debugging
        self.message_history: deque = deque(maxlen=1000)
        
        # Performance metrics
        self.metrics = CommunicationMetrics()
        
        # Threading for async processing
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.processing_lock = threading.Lock()
        
        # Message delivery tracking
        self.pending_responses: Dict[str, asyncio.Future] = {}
        self.delivery_confirmations: Dict[str, bool] = {}
        
    async def start(self):
        """Start the message bus."""
        try:
            logger.info("Starting multi-agent message bus...")
            self.is_running = True
            
            # Start background tasks
            asyncio.create_task(self._process_message_queues())
            asyncio.create_task(self._cleanup_expired_messages())
            asyncio.create_task(self._update_metrics())
            
            logger.info("Message bus started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start message bus: {e}")
            return False
    
    async def stop(self):
        """Stop the message bus."""
        try:
            logger.info("Stopping multi-agent message bus...")
            self.is_running = False
            
            # Send shutdown notifications
            await self._broadcast_shutdown()
            
            # Wait for pending operations
            await asyncio.sleep(1.0)
            
            # Cleanup
            self.executor.shutdown(wait=True)
            
            logger.info("Message bus stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping message bus: {e}")
    
    async def send_message(self, message: AgentMessage) -> bool:
        """Send a message to another agent."""
        try:
            if not self.is_running:
                logger.warning("Message bus not running, cannot send message")
                return False
            
            # Validate message
            if not self._validate_message(message):
                logger.error(f"Invalid message: {message.message_id}")
                return False
            
            # Add to message history
            self.message_history.append(message)
            
            # Route message
            success = await self._route_message(message)
            
            # Update metrics
            if success:
                self.metrics.total_messages_sent += 1
            else:
                self.metrics.total_messages_failed += 1
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to send message {message.message_id}: {e}")
            self.metrics.total_messages_failed += 1
            return False
    
    async def send_request(self, sender_id: str, receiver_id: str, 
                          request_type: str, payload: Dict[str, Any],
                          timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """Send a request and wait for response."""
        try:
            message_id = str(uuid.uuid4())
            correlation_id = str(uuid.uuid4())
            
            # Create request message
            request = AgentMessage(
                message_id=message_id,
                sender_id=sender_id,
                receiver_id=receiver_id,
                message_type=MessageType.REQUEST,
                priority=MessagePriority.MEDIUM,
                payload={
                    'request_type': request_type,
                    'data': payload
                },
                timestamp=datetime.now(),
                correlation_id=correlation_id,
                requires_response=True,
                expires_at=datetime.now() + timedelta(seconds=timeout)
            )
            
            # Create future for response
            response_future = asyncio.Future()
            self.pending_responses[correlation_id] = response_future
            
            # Send request
            success = await self.send_message(request)
            if not success:
                del self.pending_responses[correlation_id]
                return None
            
            # Wait for response
            try:
                response = await asyncio.wait_for(response_future, timeout=timeout)
                return response
            except asyncio.TimeoutError:
                logger.warning(f"Request {message_id} timed out")
                return None
            finally:
                if correlation_id in self.pending_responses:
                    del self.pending_responses[correlation_id]
                    
        except Exception as e:
            logger.error(f"Failed to send request: {e}")
            return None
    
    async def broadcast_message(self, sender_id: str, message_type: MessageType,
                              payload: Dict[str, Any], 
                              exclude_agents: List[str] = None) -> int:
        """Broadcast a message to all registered agents."""
        try:
            exclude_agents = exclude_agents or []
            exclude_agents.append(sender_id)  # Don't send to sender
            
            sent_count = 0
            
            # Get all registered agents
            registered_agents = await self._get_registered_agents()
            
            for agent_id in registered_agents:
                if agent_id not in exclude_agents:
                    message = AgentMessage(
                        message_id=str(uuid.uuid4()),
                        sender_id=sender_id,
                        receiver_id=agent_id,
                        message_type=message_type,
                        priority=MessagePriority.MEDIUM,
                        payload=payload,
                        timestamp=datetime.now()
                    )
                    
                    if await self.send_message(message):
                        sent_count += 1
            
            logger.info(f"Broadcast message sent to {sent_count} agents")
            return sent_count
            
        except Exception as e:
            logger.error(f"Failed to broadcast message: {e}")
            return 0
    
    async def subscribe(self, agent_id: str, message_types: List[MessageType]):
        """Subscribe an agent to specific message types."""
        try:
            for message_type in message_types:
                if agent_id not in self.subscriptions[message_type.value]:
                    self.subscriptions[message_type.value].append(agent_id)
            
            logger.info(f"Agent {agent_id} subscribed to {len(message_types)} message types")
            
        except Exception as e:
            logger.error(f"Failed to subscribe agent {agent_id}: {e}")
    
    async def unsubscribe(self, agent_id: str, message_types: List[MessageType] = None):
        """Unsubscribe an agent from message types."""
        try:
            if message_types is None:
                # Unsubscribe from all
                for subscribers in self.subscriptions.values():
                    if agent_id in subscribers:
                        subscribers.remove(agent_id)
            else:
                for message_type in message_types:
                    if agent_id in self.subscriptions[message_type.value]:
                        self.subscriptions[message_type.value].remove(agent_id)
            
            logger.info(f"Agent {agent_id} unsubscribed from message types")
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe agent {agent_id}: {e}")
    
    def _validate_message(self, message: AgentMessage) -> bool:
        """Validate message format and content."""
        try:
            # Check required fields
            if not all([message.message_id, message.sender_id, message.receiver_id]):
                return False
            
            # Check message type
            if not isinstance(message.message_type, MessageType):
                return False
            
            # Check priority
            if not isinstance(message.priority, MessagePriority):
                return False
            
            # Check expiration
            if message.expires_at and message.expires_at <= datetime.now():
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Message validation failed: {e}")
            return False
    
    async def _route_message(self, message: AgentMessage) -> bool:
        """Route message to appropriate destination."""
        try:
            receiver_id = message.receiver_id
            
            # Check if receiver exists
            if not await self._agent_exists(receiver_id):
                logger.warning(f"Receiver {receiver_id} not found")
                return False
            
            # Add to receiver's queue
            with self.processing_lock:
                self.message_queues[receiver_id].append(message)
            
            # Handle priority messages immediately
            if message.priority == MessagePriority.CRITICAL:
                await self._process_priority_message(message)
            
            return True
            
        except Exception as e:
            logger.error(f"Message routing failed: {e}")
            return False
    
    async def _process_message_queues(self):
        """Background task to process message queues."""
        while self.is_running:
            try:
                await asyncio.sleep(0.1)  # Process every 100ms
                
                with self.processing_lock:
                    for agent_id, queue in self.message_queues.items():
                        if queue:
                            # Process messages by priority
                            messages = list(queue)
                            queue.clear()
                            
                            # Sort by priority
                            messages.sort(key=lambda m: m.priority.value)
                            
                            for message in messages:
                                await self._deliver_message(message)
                
            except Exception as e:
                logger.error(f"Error processing message queues: {e}")
                await asyncio.sleep(1.0)
    
    async def _deliver_message(self, message: AgentMessage):
        """Deliver message to target agent."""
        try:
            # Check if message expired
            if message.expires_at and message.expires_at <= datetime.now():
                logger.warning(f"Message {message.message_id} expired")
                return
            
            # Handle different message types
            if message.message_type == MessageType.RESPONSE:
                await self._handle_response_message(message)
            elif message.message_type == MessageType.REQUEST:
                await self._handle_request_message(message)
            else:
                await self._handle_general_message(message)
            
            # Mark as delivered
            self.delivery_confirmations[message.message_id] = True
            self.metrics.total_messages_received += 1
            
        except Exception as e:
            logger.error(f"Message delivery failed for {message.message_id}: {e}")
            self.metrics.total_messages_failed += 1
    
    async def _handle_response_message(self, message: AgentMessage):
        """Handle response messages."""
        try:
            correlation_id = message.correlation_id
            if correlation_id and correlation_id in self.pending_responses:
                future = self.pending_responses[correlation_id]
                if not future.done():
                    future.set_result(message.payload)
                    
        except Exception as e:
            logger.error(f"Failed to handle response message: {e}")
    
    async def _handle_request_message(self, message: AgentMessage):
        """Handle request messages."""
        try:
            # This would be implemented by specific agents
            # For now, just log the request
            logger.info(f"Received request from {message.sender_id}: {message.payload}")
            
        except Exception as e:
            logger.error(f"Failed to handle request message: {e}")
    
    async def _handle_general_message(self, message: AgentMessage):
        """Handle general messages."""
        try:
            # Route to subscribed handlers
            message_type = message.message_type.value
            if message_type in self.subscriptions:
                for handler in self.message_handlers.get(message.message_type, []):
                    try:
                        await handler(message)
                    except Exception as e:
                        logger.error(f"Message handler failed: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to handle general message: {e}")
    
    async def _process_priority_message(self, message: AgentMessage):
        """Process high-priority messages immediately."""
        try:
            if message.priority == MessagePriority.CRITICAL:
                await self._deliver_message(message)
                
        except Exception as e:
            logger.error(f"Priority message processing failed: {e}")
    
    async def _cleanup_expired_messages(self):
        """Background task to cleanup expired messages."""
        while self.is_running:
            try:
                await asyncio.sleep(60.0)  # Cleanup every minute
                
                current_time = datetime.now()
                
                # Cleanup message queues
                with self.processing_lock:
                    for queue in self.message_queues.values():
                        expired_messages = []
                        for message in queue:
                            if message.expires_at and message.expires_at <= current_time:
                                expired_messages.append(message)
                        
                        for expired in expired_messages:
                            queue.remove(expired)
                
                # Cleanup pending responses
                expired_responses = []
                for correlation_id, future in self.pending_responses.items():
                    if future.done():
                        expired_responses.append(correlation_id)
                
                for correlation_id in expired_responses:
                    del self.pending_responses[correlation_id]
                
                # Cleanup delivery confirmations (keep last 1000)
                if len(self.delivery_confirmations) > 1000:
                    oldest_keys = list(self.delivery_confirmations.keys())[:500]
                    for key in oldest_keys:
                        del self.delivery_confirmations[key]
                
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
    
    async def _update_metrics(self):
        """Background task to update performance metrics."""
        while self.is_running:
            try:
                await asyncio.sleep(10.0)  # Update every 10 seconds
                
                # Calculate metrics
                total_messages = (self.metrics.total_messages_sent + 
                                self.metrics.total_messages_received)
                
                if total_messages > 0:
                    self.metrics.error_rate = (self.metrics.total_messages_failed / 
                                             total_messages) * 100
                
                self.metrics.active_connections = len(self.message_queues)
                self.metrics.last_updated = datetime.now()
                
                # Log metrics periodically
                if total_messages % 100 == 0 and total_messages > 0:
                    logger.info(f"Message bus metrics: {total_messages} total messages, "
                              f"{self.metrics.error_rate:.1f}% error rate, "
                              f"{self.metrics.active_connections} active connections")
                
            except Exception as e:
                logger.error(f"Error updating metrics: {e}")
    
    async def _broadcast_shutdown(self):
        """Broadcast shutdown notification to all agents."""
        try:
            shutdown_message = {
                'event': 'message_bus_shutdown',
                'timestamp': datetime.now().isoformat()
            }
            
            await self.broadcast_message(
                sender_id='message_bus',
                message_type=MessageType.SHUTDOWN,
                payload=shutdown_message
            )
            
        except Exception as e:
            logger.error(f"Failed to broadcast shutdown: {e}")
    
    async def _get_registered_agents(self) -> List[str]:
        """Get list of registered agent IDs."""
        # This would integrate with agent registry
        # For now, return agents with active queues
        return list(self.message_queues.keys())
    
    async def _agent_exists(self, agent_id: str) -> bool:
        """Check if agent exists and is active."""
        # This would integrate with agent registry
        # For now, assume all agents exist
        return True
    
    def get_metrics(self) -> CommunicationMetrics:
        """Get current communication metrics."""
        return self.metrics
    
    def get_queue_status(self) -> Dict[str, int]:
        """Get message queue status for all agents."""
        with self.processing_lock:
            return {agent_id: len(queue) for agent_id, queue in self.message_queues.items()}


class AgentRegistry:
    """
    Central registry for managing agent registrations and capabilities.

    Provides:
    - Agent registration and deregistration
    - Capability discovery and matching
    - Health monitoring and status tracking
    - Load balancing and resource management
    - Service discovery for agent coordination
    """

    def __init__(self):
        self.settings = get_settings()

        # Registered agents
        self.agents: Dict[str, AgentRegistration] = {}

        # Capability index for fast lookup
        self.capability_index: Dict[str, List[str]] = defaultdict(list)

        # Agent health monitoring
        self.health_check_interval = 30.0  # seconds
        self.heartbeat_timeout = 60.0  # seconds

        # Load balancing
        self.agent_loads: Dict[str, float] = defaultdict(float)

    async def register_agent(self, registration: AgentRegistration) -> bool:
        """Register a new agent."""
        try:
            agent_id = registration.agent_id

            # Validate registration
            if not self._validate_registration(registration):
                logger.error(f"Invalid agent registration: {agent_id}")
                return False

            # Store registration
            self.agents[agent_id] = registration

            # Index capabilities
            for capability in registration.capabilities:
                self.capability_index[capability.capability_id].append(agent_id)

            # Initialize load tracking
            self.agent_loads[agent_id] = 0.0

            logger.info(f"Agent {agent_id} registered successfully with {len(registration.capabilities)} capabilities")
            return True

        except Exception as e:
            logger.error(f"Failed to register agent {registration.agent_id}: {e}")
            return False

    async def deregister_agent(self, agent_id: str) -> bool:
        """Deregister an agent."""
        try:
            if agent_id not in self.agents:
                logger.warning(f"Agent {agent_id} not found for deregistration")
                return False

            registration = self.agents[agent_id]

            # Remove from capability index
            for capability in registration.capabilities:
                if agent_id in self.capability_index[capability.capability_id]:
                    self.capability_index[capability.capability_id].remove(agent_id)

            # Remove from registry
            del self.agents[agent_id]

            # Remove load tracking
            if agent_id in self.agent_loads:
                del self.agent_loads[agent_id]

            logger.info(f"Agent {agent_id} deregistered successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to deregister agent {agent_id}: {e}")
            return False

    async def update_agent_status(self, agent_id: str, status: AgentStatus) -> bool:
        """Update agent status."""
        try:
            if agent_id not in self.agents:
                logger.warning(f"Agent {agent_id} not found for status update")
                return False

            self.agents[agent_id].status = status
            self.agents[agent_id].last_heartbeat = datetime.now()

            logger.debug(f"Agent {agent_id} status updated to {status.value}")
            return True

        except Exception as e:
            logger.error(f"Failed to update agent status: {e}")
            return False

    async def heartbeat(self, agent_id: str) -> bool:
        """Process agent heartbeat."""
        try:
            if agent_id not in self.agents:
                logger.warning(f"Heartbeat from unknown agent: {agent_id}")
                return False

            self.agents[agent_id].last_heartbeat = datetime.now()

            # Update status to active if not in error state
            if self.agents[agent_id].status != AgentStatus.ERROR:
                self.agents[agent_id].status = AgentStatus.ACTIVE

            return True

        except Exception as e:
            logger.error(f"Failed to process heartbeat from {agent_id}: {e}")
            return False

    async def find_agents_by_capability(self, capability_id: str) -> List[str]:
        """Find agents that provide a specific capability."""
        try:
            agents = self.capability_index.get(capability_id, [])

            # Filter by active status
            active_agents = []
            for agent_id in agents:
                if agent_id in self.agents:
                    agent = self.agents[agent_id]
                    if agent.status in [AgentStatus.ACTIVE, AgentStatus.IDLE]:
                        active_agents.append(agent_id)

            return active_agents

        except Exception as e:
            logger.error(f"Failed to find agents by capability {capability_id}: {e}")
            return []

    async def get_best_agent_for_capability(self, capability_id: str) -> Optional[str]:
        """Get the best available agent for a capability based on load."""
        try:
            available_agents = await self.find_agents_by_capability(capability_id)

            if not available_agents:
                return None

            # Select agent with lowest load
            best_agent = min(available_agents, key=lambda a: self.agent_loads.get(a, 0.0))

            return best_agent

        except Exception as e:
            logger.error(f"Failed to get best agent for capability {capability_id}: {e}")
            return None

    async def update_agent_load(self, agent_id: str, load: float):
        """Update agent load for load balancing."""
        try:
            if agent_id in self.agents:
                self.agent_loads[agent_id] = max(0.0, min(1.0, load))

        except Exception as e:
            logger.error(f"Failed to update agent load: {e}")

    async def get_agent_info(self, agent_id: str) -> Optional[AgentRegistration]:
        """Get agent registration information."""
        return self.agents.get(agent_id)

    async def get_all_agents(self) -> Dict[str, AgentRegistration]:
        """Get all registered agents."""
        return self.agents.copy()

    async def get_agents_by_type(self, agent_type: str) -> List[AgentRegistration]:
        """Get agents by type."""
        return [agent for agent in self.agents.values() if agent.agent_type == agent_type]

    async def check_agent_health(self):
        """Check health of all registered agents."""
        try:
            current_time = datetime.now()
            unhealthy_agents = []

            for agent_id, agent in self.agents.items():
                time_since_heartbeat = (current_time - agent.last_heartbeat).total_seconds()

                if time_since_heartbeat > self.heartbeat_timeout:
                    # Mark as error if no heartbeat
                    agent.status = AgentStatus.ERROR
                    unhealthy_agents.append(agent_id)
                    logger.warning(f"Agent {agent_id} marked as unhealthy (no heartbeat for {time_since_heartbeat:.1f}s)")

            return unhealthy_agents

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return []

    def _validate_registration(self, registration: AgentRegistration) -> bool:
        """Validate agent registration."""
        try:
            # Check required fields
            if not all([registration.agent_id, registration.agent_type, registration.name]):
                return False

            # Check capabilities
            for capability in registration.capabilities:
                if not all([capability.capability_id, capability.name]):
                    return False

            return True

        except Exception as e:
            logger.error(f"Registration validation failed: {e}")
            return False

    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        try:
            stats = {
                'total_agents': len(self.agents),
                'agents_by_status': defaultdict(int),
                'agents_by_type': defaultdict(int),
                'total_capabilities': len(self.capability_index),
                'average_load': 0.0
            }

            for agent in self.agents.values():
                stats['agents_by_status'][agent.status.value] += 1
                stats['agents_by_type'][agent.agent_type] += 1

            if self.agent_loads:
                stats['average_load'] = sum(self.agent_loads.values()) / len(self.agent_loads)

            return dict(stats)

        except Exception as e:
            logger.error(f"Failed to get registry stats: {e}")
            return {}


class CoordinationProtocol:
    """
    Coordination protocol for multi-agent collaboration.

    Provides:
    - Task coordination and workflow management
    - Conflict resolution and priority handling
    - Resource allocation and scheduling
    - Consensus building and decision making
    - Performance monitoring and optimization
    """

    def __init__(self, message_bus: MessageBus, agent_registry: AgentRegistry):
        self.message_bus = message_bus
        self.agent_registry = agent_registry

        # Active coordination sessions
        self.coordination_sessions: Dict[str, Dict[str, Any]] = {}

        # Task queue and scheduling
        self.task_queue: deque = deque()
        self.active_tasks: Dict[str, Dict[str, Any]] = {}

        # Conflict resolution
        self.conflict_resolution_rules: List[Callable] = []

        # Performance tracking
        self.coordination_metrics: Dict[str, Any] = defaultdict(float)

    async def coordinate_task(self, task_id: str, task_type: str,
                            required_capabilities: List[str],
                            task_data: Dict[str, Any],
                            priority: MessagePriority = MessagePriority.MEDIUM) -> Optional[str]:
        """Coordinate a multi-agent task."""
        try:
            logger.info(f"Coordinating task {task_id} of type {task_type}")

            # Find suitable agents
            agent_assignments = await self._assign_agents_to_task(required_capabilities)

            if not agent_assignments:
                logger.error(f"No suitable agents found for task {task_id}")
                return None

            # Create coordination session
            session_id = str(uuid.uuid4())
            session = {
                'session_id': session_id,
                'task_id': task_id,
                'task_type': task_type,
                'required_capabilities': required_capabilities,
                'agent_assignments': agent_assignments,
                'task_data': task_data,
                'priority': priority,
                'status': 'initializing',
                'created_at': datetime.now(),
                'participants': list(agent_assignments.keys())
            }

            self.coordination_sessions[session_id] = session

            # Initiate coordination
            success = await self._initiate_coordination(session)

            if success:
                session['status'] = 'active'
                self.active_tasks[task_id] = session
                logger.info(f"Task {task_id} coordination initiated successfully")
                return session_id
            else:
                del self.coordination_sessions[session_id]
                logger.error(f"Failed to initiate coordination for task {task_id}")
                return None

        except Exception as e:
            logger.error(f"Task coordination failed: {e}")
            return None

    async def _assign_agents_to_task(self, required_capabilities: List[str]) -> Dict[str, str]:
        """Assign agents to task based on required capabilities."""
        try:
            assignments = {}

            for capability in required_capabilities:
                best_agent = await self.agent_registry.get_best_agent_for_capability(capability)

                if best_agent:
                    assignments[best_agent] = capability
                    # Update agent load
                    current_load = self.agent_registry.agent_loads.get(best_agent, 0.0)
                    await self.agent_registry.update_agent_load(best_agent, current_load + 0.1)
                else:
                    logger.warning(f"No agent available for capability: {capability}")

            return assignments

        except Exception as e:
            logger.error(f"Agent assignment failed: {e}")
            return {}

    async def _initiate_coordination(self, session: Dict[str, Any]) -> bool:
        """Initiate coordination session with assigned agents."""
        try:
            session_id = session['session_id']
            participants = session['participants']

            # Send coordination invitation to all participants
            coordination_payload = {
                'session_id': session_id,
                'task_id': session['task_id'],
                'task_type': session['task_type'],
                'task_data': session['task_data'],
                'role_assignments': session['agent_assignments'],
                'coordination_protocol': 'collaborative_optimization'
            }

            success_count = 0

            for agent_id in participants:
                message = AgentMessage(
                    message_id=str(uuid.uuid4()),
                    sender_id='coordination_protocol',
                    receiver_id=agent_id,
                    message_type=MessageType.COORDINATION,
                    priority=session['priority'],
                    payload=coordination_payload,
                    timestamp=datetime.now(),
                    correlation_id=session_id,
                    requires_response=True
                )

                if await self.message_bus.send_message(message):
                    success_count += 1

            # Consider successful if majority of agents were contacted
            return success_count >= len(participants) * 0.6

        except Exception as e:
            logger.error(f"Coordination initiation failed: {e}")
            return False

    async def handle_coordination_response(self, message: AgentMessage):
        """Handle coordination response from agents."""
        try:
            session_id = message.correlation_id
            if session_id not in self.coordination_sessions:
                logger.warning(f"Unknown coordination session: {session_id}")
                return

            session = self.coordination_sessions[session_id]
            response_data = message.payload

            # Process agent response
            agent_id = message.sender_id
            if 'responses' not in session:
                session['responses'] = {}

            session['responses'][agent_id] = {
                'status': response_data.get('status', 'unknown'),
                'data': response_data.get('data', {}),
                'timestamp': datetime.now()
            }

            # Check if all responses received
            if len(session['responses']) >= len(session['participants']):
                await self._process_coordination_responses(session)

        except Exception as e:
            logger.error(f"Failed to handle coordination response: {e}")

    async def _process_coordination_responses(self, session: Dict[str, Any]):
        """Process all coordination responses and determine next steps."""
        try:
            session_id = session['session_id']
            responses = session['responses']

            # Analyze responses
            accepted_agents = []
            rejected_agents = []

            for agent_id, response in responses.items():
                if response['status'] == 'accepted':
                    accepted_agents.append(agent_id)
                else:
                    rejected_agents.append(agent_id)

            # Determine if coordination can proceed
            if len(accepted_agents) >= len(session['required_capabilities']):
                # Proceed with coordination
                session['status'] = 'coordinating'
                session['active_agents'] = accepted_agents

                await self._execute_coordinated_task(session)
            else:
                # Insufficient agents, try to find alternatives
                session['status'] = 'failed'
                logger.warning(f"Coordination session {session_id} failed: insufficient agents")

        except Exception as e:
            logger.error(f"Failed to process coordination responses: {e}")

    async def _execute_coordinated_task(self, session: Dict[str, Any]):
        """Execute the coordinated task with active agents."""
        try:
            session_id = session['session_id']
            active_agents = session['active_agents']

            # Send execution instructions to active agents
            execution_payload = {
                'session_id': session_id,
                'task_id': session['task_id'],
                'execution_phase': 'start',
                'coordination_data': session['task_data'],
                'active_participants': active_agents
            }

            for agent_id in active_agents:
                message = AgentMessage(
                    message_id=str(uuid.uuid4()),
                    sender_id='coordination_protocol',
                    receiver_id=agent_id,
                    message_type=MessageType.COORDINATION,
                    priority=session['priority'],
                    payload=execution_payload,
                    timestamp=datetime.now(),
                    correlation_id=session_id
                )

                await self.message_bus.send_message(message)

            session['status'] = 'executing'
            session['execution_started'] = datetime.now()

            logger.info(f"Coordinated task execution started for session {session_id}")

        except Exception as e:
            logger.error(f"Coordinated task execution failed: {e}")

    async def resolve_conflict(self, conflict_data: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve conflicts between agents."""
        try:
            conflict_type = conflict_data.get('type', 'unknown')
            involved_agents = conflict_data.get('agents', [])

            logger.info(f"Resolving {conflict_type} conflict involving {len(involved_agents)} agents")

            # Apply conflict resolution rules
            resolution = None
            for rule in self.conflict_resolution_rules:
                try:
                    resolution = await rule(conflict_data)
                    if resolution:
                        break
                except Exception as e:
                    logger.error(f"Conflict resolution rule failed: {e}")

            # Default resolution strategy
            if not resolution:
                resolution = await self._default_conflict_resolution(conflict_data)

            # Notify involved agents of resolution
            if resolution:
                await self._notify_conflict_resolution(involved_agents, resolution)

            return resolution or {}

        except Exception as e:
            logger.error(f"Conflict resolution failed: {e}")
            return {}

    async def _default_conflict_resolution(self, conflict_data: Dict[str, Any]) -> Dict[str, Any]:
        """Default conflict resolution strategy."""
        try:
            # Simple priority-based resolution
            conflict_type = conflict_data.get('type', 'unknown')

            if conflict_type == 'resource_conflict':
                # Assign resource to highest priority agent
                agents = conflict_data.get('agents', [])
                if agents:
                    # For now, assign to first agent (could be improved with priority logic)
                    return {
                        'resolution_type': 'priority_assignment',
                        'assigned_agent': agents[0],
                        'resource': conflict_data.get('resource'),
                        'timestamp': datetime.now()
                    }

            elif conflict_type == 'coordination_conflict':
                # Use consensus or voting mechanism
                return {
                    'resolution_type': 'consensus_required',
                    'voting_required': True,
                    'timestamp': datetime.now()
                }

            return {
                'resolution_type': 'manual_intervention_required',
                'timestamp': datetime.now()
            }

        except Exception as e:
            logger.error(f"Default conflict resolution failed: {e}")
            return {}

    async def _notify_conflict_resolution(self, agents: List[str], resolution: Dict[str, Any]):
        """Notify agents of conflict resolution."""
        try:
            notification_payload = {
                'event': 'conflict_resolved',
                'resolution': resolution,
                'timestamp': datetime.now().isoformat()
            }

            for agent_id in agents:
                message = AgentMessage(
                    message_id=str(uuid.uuid4()),
                    sender_id='coordination_protocol',
                    receiver_id=agent_id,
                    message_type=MessageType.NOTIFICATION,
                    priority=MessagePriority.HIGH,
                    payload=notification_payload,
                    timestamp=datetime.now()
                )

                await self.message_bus.send_message(message)

        except Exception as e:
            logger.error(f"Failed to notify conflict resolution: {e}")

    def add_conflict_resolution_rule(self, rule: Callable):
        """Add a custom conflict resolution rule."""
        self.conflict_resolution_rules.append(rule)

    def get_coordination_metrics(self) -> Dict[str, Any]:
        """Get coordination performance metrics."""
        return dict(self.coordination_metrics)

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active coordination sessions."""
        return {sid: session for sid, session in self.coordination_sessions.items()
                if session.get('status') in ['active', 'coordinating', 'executing']}


# Convenience functions for easy integration
async def create_communication_system() -> Tuple[MessageBus, AgentRegistry, CoordinationProtocol]:
    """Create and initialize the complete communication system."""
    try:
        # Create components
        message_bus = MessageBus()
        agent_registry = AgentRegistry()
        coordination_protocol = CoordinationProtocol(message_bus, agent_registry)

        # Start message bus
        await message_bus.start()

        logger.info("Multi-agent communication system created successfully")
        return message_bus, agent_registry, coordination_protocol

    except Exception as e:
        logger.error(f"Failed to create communication system: {e}")
        raise


async def shutdown_communication_system(message_bus: MessageBus):
    """Shutdown the communication system gracefully."""
    try:
        await message_bus.stop()
        logger.info("Communication system shutdown completed")

    except Exception as e:
        logger.error(f"Error during communication system shutdown: {e}")
