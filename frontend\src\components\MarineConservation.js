import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Alert,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  Waves as WavesIcon,
  DirectionsBoat as BoatIcon,
  Delete as DebrisIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  LocationOn as LocationIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ScatterChart, Scatter } from 'recharts';

const MarineConservation = ({ data, onRefresh }) => {
  const [selectedHotspot, setSelectedHotspot] = useState(null);

  // Sample data for charts
  const debrisData = [
    { type: 'Plastic', count: data?.debris_count * 0.6 || 15, severity: 'high' },
    { type: 'Metal', count: data?.debris_count * 0.2 || 5, severity: 'medium' },
    { type: 'Glass', count: data?.debris_count * 0.1 || 2, severity: 'low' },
    { type: 'Other', count: data?.debris_count * 0.1 || 3, severity: 'medium' },
  ];

  const vesselData = [
    { x: 119.5, y: 23.5, size: 20, type: 'Cargo' },
    { x: 120.2, y: 24.1, size: 15, type: 'Fishing' },
    { x: 120.8, y: 23.8, size: 10, type: 'Recreational' },
    { x: 119.8, y: 24.3, size: 25, type: 'Tanker' },
  ];

  const getRiskColor = (level) => {
    switch (level) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  if (!data) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography variant="h6" color="text.secondary">
          Loading marine conservation data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          🌊 Marine Conservation Dashboard
        </Typography>
        <Button variant="contained" onClick={onRefresh} startIcon={<AssessmentIcon />}>
          Run Analysis
        </Button>
      </Box>

      {/* Status Alert */}
      {data.status !== 'active' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Marine Conservation System Status: {data.status}
        </Alert>
      )}

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Debris Detected
                  </Typography>
                  <Typography variant="h4">
                    {data.debris_count || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Items tracked
                  </Typography>
                </Box>
                <DebrisIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Vessels Tracked
                  </Typography>
                  <Typography variant="h4">
                    {data.vessel_count || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active vessels
                  </Typography>
                </Box>
                <BoatIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Marine Health
                  </Typography>
                  <Typography variant="h4">
                    {data.health_score ? `${(data.health_score * 100).toFixed(0)}%` : 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Ecosystem health
                  </Typography>
                </Box>
                <WavesIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.health_score * 100 || 0} 
                color="info"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Risk Level
                  </Typography>
                  <Chip 
                    label={data.risk_level || 'Unknown'} 
                    color={getRiskColor(data.risk_level)}
                    sx={{ fontSize: '1.2rem', height: 40 }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Current assessment
                  </Typography>
                </Box>
                <WarningIcon color={getRiskColor(data.risk_level)} sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts and Analysis */}
      <Grid container spacing={3}>
        {/* Debris Analysis */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Debris Type Analysis
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={debrisData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="type" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#2196f3" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Vessel Tracking */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Vessel Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <ScatterChart data={vesselData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="x" domain={[119, 121]} />
                  <YAxis dataKey="y" domain={[23, 25]} />
                  <Tooltip 
                    formatter={(value, name) => [value, name]}
                    labelFormatter={(label) => `Position: ${label}`}
                  />
                  <Scatter dataKey="size" fill="#4caf50" />
                </ScatterChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Hotspots */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Debris Hotspots
              </Typography>
              {data.hotspots && data.hotspots.length > 0 ? (
                <List>
                  {data.hotspots.slice(0, 5).map((hotspot, index) => (
                    <ListItem 
                      key={index}
                      button
                      onClick={() => setSelectedHotspot(hotspot)}
                      selected={selectedHotspot === hotspot}
                    >
                      <ListItemIcon>
                        <LocationIcon color="error" />
                      </ListItemIcon>
                      <ListItemText
                        primary={`Hotspot ${index + 1}`}
                        secondary={`Severity: ${hotspot.severity || 'Medium'} | Items: ${hotspot.count || 'Unknown'}`}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary">
                  No hotspots detected in current area
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Conservation Actions */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recommended Actions
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <TrendingUpIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Deploy cleanup vessels"
                    secondary="High-priority debris areas identified"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <WavesIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Monitor vessel traffic"
                    secondary="Increased activity in sensitive areas"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <AssessmentIcon color="secondary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Update conservation plan"
                    secondary="Based on latest ecosystem assessment"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Selected Hotspot Details */}
      {selectedHotspot && (
        <Box mt={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Hotspot Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Location: {selectedHotspot.location || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Severity: {selectedHotspot.severity || 'Medium'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Debris Count: {selectedHotspot.count || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Last Updated: {new Date().toLocaleString()}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default MarineConservation;
