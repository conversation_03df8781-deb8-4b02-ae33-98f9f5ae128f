#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Taiwan Government-Private Sector AI Collaboration Platform
Public-private partnership platform for marine conservation in Taiwan
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StakeholderType(Enum):
    """Types of stakeholders in the collaboration"""
    GOVERNMENT_AGENCY = "government_agency"
    PRIVATE_COMPANY = "private_company"
    NGO = "ngo"
    RESEARCH_INSTITUTION = "research_institution"
    COMMUNITY_GROUP = "community_group"


class ProjectStatus(Enum):
    """Project status levels"""
    PROPOSED = "proposed"
    APPROVED = "approved"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SUSPENDED = "suspended"


@dataclass
class Stakeholder:
    """Stakeholder in the collaboration platform"""
    stakeholder_id: str
    name: str
    type: StakeholderType
    contact_info: Dict[str, str]
    capabilities: List[str]
    resources: Dict[str, Any]
    participation_level: str  # "lead", "partner", "contributor"
    registration_date: datetime


@dataclass
class CollaborationProject:
    """Marine conservation collaboration project"""
    project_id: str
    title: str
    description: str
    objectives: List[str]
    target_areas: List[Tuple[float, float, float, float]]  # Bounding boxes
    stakeholders: List[str]  # Stakeholder IDs
    lead_agency: str
    status: ProjectStatus
    start_date: datetime
    end_date: Optional[datetime]
    budget: Dict[str, float]
    resources_needed: List[str]
    expected_outcomes: List[str]
    progress_metrics: Dict[str, Any]
    created_date: datetime


@dataclass
class DataSharingAgreement:
    """Data sharing agreement between stakeholders"""
    agreement_id: str
    parties: List[str]  # Stakeholder IDs
    data_types: List[str]
    access_levels: Dict[str, str]
    usage_restrictions: List[str]
    duration: int  # days
    created_date: datetime
    signed_date: Optional[datetime]


class TaiwanGovernmentPlatform:
    """Taiwan Government-Private Sector AI Collaboration Platform"""
    
    def __init__(self):
        self.stakeholders: Dict[str, Stakeholder] = {}
        self.projects: Dict[str, CollaborationProject] = {}
        self.data_agreements: Dict[str, DataSharingAgreement] = {}
        self.government_agencies = self._initialize_government_agencies()
        self.platform_metrics = {
            'total_stakeholders': 0,
            'active_projects': 0,
            'data_sharing_agreements': 0,
            'successful_collaborations': 0
        }
    
    def _initialize_government_agencies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize Taiwan government agencies involved in marine conservation"""
        return {
            'epa': {
                'name': 'Environmental Protection Administration',
                'name_zh': '行政院環境保護署',
                'responsibilities': ['pollution_control', 'environmental_monitoring', 'waste_management'],
                'contact': '<EMAIL>',
                'api_endpoints': ['water_quality', 'pollution_reports', 'compliance_data']
            },
            'oac': {
                'name': 'Ocean Affairs Council',
                'name_zh': '海洋委員會',
                'responsibilities': ['marine_policy', 'coastal_management', 'marine_protection'],
                'contact': '<EMAIL>',
                'api_endpoints': ['marine_protected_areas', 'coastal_data', 'policy_updates']
            },
            'cga': {
                'name': 'Coast Guard Administration',
                'name_zh': '海洋委員會海巡署',
                'responsibilities': ['maritime_security', 'pollution_response', 'vessel_monitoring'],
                'contact': '<EMAIL>',
                'api_endpoints': ['vessel_tracking', 'incident_reports', 'patrol_data']
            },
            'fisheries': {
                'name': 'Fisheries Agency',
                'name_zh': '行政院農業委員會漁業署',
                'responsibilities': ['fishing_regulation', 'marine_resources', 'vessel_licensing'],
                'contact': '<EMAIL>',
                'api_endpoints': ['fishing_data', 'vessel_registry', 'catch_reports']
            }
        }
    
    async def register_stakeholder(
        self,
        name: str,
        stakeholder_type: StakeholderType,
        contact_info: Dict[str, str],
        capabilities: List[str],
        resources: Dict[str, Any] = None
    ) -> str:
        """Register a new stakeholder in the platform"""
        stakeholder_id = f"{stakeholder_type.value}_{len(self.stakeholders) + 1:04d}"
        
        stakeholder = Stakeholder(
            stakeholder_id=stakeholder_id,
            name=name,
            type=stakeholder_type,
            contact_info=contact_info,
            capabilities=capabilities,
            resources=resources or {},
            participation_level="contributor",
            registration_date=datetime.now()
        )
        
        self.stakeholders[stakeholder_id] = stakeholder
        self.platform_metrics['total_stakeholders'] += 1
        
        logger.info(f"✅ Registered stakeholder: {name} ({stakeholder_id})")
        return stakeholder_id
    
    async def create_collaboration_project(
        self,
        title: str,
        description: str,
        objectives: List[str],
        target_areas: List[Tuple[float, float, float, float]],
        lead_agency_id: str,
        stakeholder_ids: List[str],
        budget: Dict[str, float],
        duration_days: int = 365
    ) -> str:
        """Create a new collaboration project"""
        project_id = f"proj_{datetime.now().strftime('%Y%m%d')}_{len(self.projects) + 1:03d}"
        
        # Validate stakeholders
        for stakeholder_id in stakeholder_ids:
            if stakeholder_id not in self.stakeholders:
                raise ValueError(f"Unknown stakeholder: {stakeholder_id}")
        
        if lead_agency_id not in self.stakeholders:
            raise ValueError(f"Unknown lead agency: {lead_agency_id}")
        
        project = CollaborationProject(
            project_id=project_id,
            title=title,
            description=description,
            objectives=objectives,
            target_areas=target_areas,
            stakeholders=stakeholder_ids,
            lead_agency=lead_agency_id,
            status=ProjectStatus.PROPOSED,
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=duration_days),
            budget=budget,
            resources_needed=[],
            expected_outcomes=[],
            progress_metrics={},
            created_date=datetime.now()
        )
        
        self.projects[project_id] = project
        self.platform_metrics['active_projects'] += 1
        
        logger.info(f"✅ Created collaboration project: {title} ({project_id})")
        return project_id
    
    async def create_data_sharing_agreement(
        self,
        parties: List[str],
        data_types: List[str],
        access_levels: Dict[str, str],
        usage_restrictions: List[str] = None,
        duration_days: int = 365
    ) -> str:
        """Create a data sharing agreement between stakeholders"""
        agreement_id = f"dsa_{datetime.now().strftime('%Y%m%d')}_{len(self.data_agreements) + 1:03d}"
        
        # Validate parties
        for party_id in parties:
            if party_id not in self.stakeholders:
                raise ValueError(f"Unknown stakeholder: {party_id}")
        
        agreement = DataSharingAgreement(
            agreement_id=agreement_id,
            parties=parties,
            data_types=data_types,
            access_levels=access_levels,
            usage_restrictions=usage_restrictions or [],
            duration=duration_days,
            created_date=datetime.now(),
            signed_date=None
        )
        
        self.data_agreements[agreement_id] = agreement
        self.platform_metrics['data_sharing_agreements'] += 1
        
        logger.info(f"✅ Created data sharing agreement: {agreement_id}")
        return agreement_id
    
    async def approve_project(self, project_id: str, approver_id: str) -> bool:
        """Approve a collaboration project"""
        if project_id not in self.projects:
            return False
        
        project = self.projects[project_id]
        
        # Check if approver is a government agency
        approver = self.stakeholders.get(approver_id)
        if not approver or approver.type != StakeholderType.GOVERNMENT_AGENCY:
            logger.error(f"❌ Only government agencies can approve projects")
            return False
        
        project.status = ProjectStatus.APPROVED
        logger.info(f"✅ Project approved: {project.title} by {approver.name}")
        return True
    
    async def get_stakeholder_recommendations(
        self,
        project_requirements: List[str],
        target_area: Tuple[float, float, float, float]
    ) -> List[Dict[str, Any]]:
        """Get stakeholder recommendations for a project"""
        recommendations = []
        
        for stakeholder_id, stakeholder in self.stakeholders.items():
            match_score = 0.0
            matching_capabilities = []
            
            # Check capability match
            for requirement in project_requirements:
                for capability in stakeholder.capabilities:
                    if requirement.lower() in capability.lower():
                        match_score += 1.0
                        matching_capabilities.append(capability)
            
            # Normalize score
            if project_requirements:
                match_score = match_score / len(project_requirements)
            
            # Bonus for government agencies (regulatory support)
            if stakeholder.type == StakeholderType.GOVERNMENT_AGENCY:
                match_score += 0.2
            
            # Bonus for research institutions (technical expertise)
            if stakeholder.type == StakeholderType.RESEARCH_INSTITUTION:
                match_score += 0.1
            
            if match_score > 0.3:  # Minimum threshold
                recommendations.append({
                    'stakeholder_id': stakeholder_id,
                    'name': stakeholder.name,
                    'type': stakeholder.type.value,
                    'match_score': match_score,
                    'matching_capabilities': matching_capabilities,
                    'recommended_role': self._suggest_role(stakeholder, match_score)
                })
        
        # Sort by match score
        recommendations.sort(key=lambda x: x['match_score'], reverse=True)
        return recommendations[:10]  # Top 10 recommendations
    
    def _suggest_role(self, stakeholder: Stakeholder, match_score: float) -> str:
        """Suggest role for stakeholder based on type and match score"""
        if stakeholder.type == StakeholderType.GOVERNMENT_AGENCY:
            return "lead" if match_score > 0.7 else "regulatory_partner"
        elif stakeholder.type == StakeholderType.PRIVATE_COMPANY:
            return "implementation_partner" if match_score > 0.6 else "resource_provider"
        elif stakeholder.type == StakeholderType.RESEARCH_INSTITUTION:
            return "technical_lead" if match_score > 0.8 else "research_partner"
        elif stakeholder.type == StakeholderType.NGO:
            return "community_liaison" if match_score > 0.5 else "advocacy_partner"
        else:
            return "contributor"
    
    async def generate_collaboration_report(self) -> Dict[str, Any]:
        """Generate comprehensive collaboration platform report"""
        # Calculate project success rate
        completed_projects = [p for p in self.projects.values() if p.status == ProjectStatus.COMPLETED]
        success_rate = len(completed_projects) / len(self.projects) if self.projects else 0
        
        # Analyze stakeholder participation
        stakeholder_analysis = {}
        for stype in StakeholderType:
            count = len([s for s in self.stakeholders.values() if s.type == stype])
            stakeholder_analysis[stype.value] = count
        
        # Active collaborations
        active_projects = [p for p in self.projects.values() if p.status == ProjectStatus.IN_PROGRESS]
        
        # Data sharing metrics
        active_agreements = [a for a in self.data_agreements.values() if a.signed_date]
        
        return {
            'platform_overview': {
                'total_stakeholders': len(self.stakeholders),
                'total_projects': len(self.projects),
                'active_projects': len(active_projects),
                'completed_projects': len(completed_projects),
                'success_rate': success_rate,
                'data_sharing_agreements': len(active_agreements)
            },
            'stakeholder_distribution': stakeholder_analysis,
            'government_agencies': list(self.government_agencies.keys()),
            'recent_projects': [
                {
                    'id': p.project_id,
                    'title': p.title,
                    'status': p.status.value,
                    'stakeholders': len(p.stakeholders)
                }
                for p in sorted(self.projects.values(), 
                               key=lambda x: x.created_date, reverse=True)[:5]
            ],
            'collaboration_metrics': {
                'average_project_duration': self._calculate_avg_project_duration(),
                'most_active_stakeholder_type': max(stakeholder_analysis.items(), 
                                                   key=lambda x: x[1])[0] if stakeholder_analysis else None,
                'data_sharing_adoption': len(active_agreements) / len(self.stakeholders) if self.stakeholders else 0
            },
            'recommendations': self._generate_platform_recommendations(),
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_avg_project_duration(self) -> float:
        """Calculate average project duration in days"""
        completed = [p for p in self.projects.values() if p.status == ProjectStatus.COMPLETED and p.end_date]
        if not completed:
            return 0.0
        
        durations = [(p.end_date - p.start_date).days for p in completed]
        return sum(durations) / len(durations)
    
    def _generate_platform_recommendations(self) -> List[str]:
        """Generate recommendations for platform improvement"""
        recommendations = []
        
        # Stakeholder diversity
        gov_count = len([s for s in self.stakeholders.values() if s.type == StakeholderType.GOVERNMENT_AGENCY])
        private_count = len([s for s in self.stakeholders.values() if s.type == StakeholderType.PRIVATE_COMPANY])
        
        if gov_count < 3:
            recommendations.append("Recruit more government agencies for regulatory support")
        
        if private_count < 5:
            recommendations.append("Engage more private sector partners for implementation capacity")
        
        # Project success
        success_rate = len([p for p in self.projects.values() if p.status == ProjectStatus.COMPLETED]) / len(self.projects) if self.projects else 0
        if success_rate < 0.7:
            recommendations.append("Improve project management and stakeholder coordination")
        
        # Data sharing
        if len(self.data_agreements) < len(self.stakeholders) * 0.3:
            recommendations.append("Promote data sharing agreements to enhance collaboration")
        
        return recommendations
    
    async def search_projects(
        self,
        keywords: List[str] = None,
        status: ProjectStatus = None,
        stakeholder_id: str = None,
        target_area: Tuple[float, float, float, float] = None
    ) -> List[Dict[str, Any]]:
        """Search projects based on criteria"""
        results = []
        
        for project in self.projects.values():
            match = True
            
            # Keyword search
            if keywords:
                project_text = f"{project.title} {project.description} {' '.join(project.objectives)}"
                if not any(keyword.lower() in project_text.lower() for keyword in keywords):
                    match = False
            
            # Status filter
            if status and project.status != status:
                match = False
            
            # Stakeholder filter
            if stakeholder_id and stakeholder_id not in project.stakeholders:
                match = False
            
            # Geographic filter (simplified)
            if target_area and project.target_areas:
                # Check if any project area overlaps with search area
                area_match = False
                for proj_area in project.target_areas:
                    if (proj_area[0] <= target_area[2] and proj_area[2] >= target_area[0] and
                        proj_area[1] <= target_area[3] and proj_area[3] >= target_area[1]):
                        area_match = True
                        break
                if not area_match:
                    match = False
            
            if match:
                results.append({
                    'project_id': project.project_id,
                    'title': project.title,
                    'description': project.description[:200] + "..." if len(project.description) > 200 else project.description,
                    'status': project.status.value,
                    'lead_agency': self.stakeholders[project.lead_agency].name if project.lead_agency in self.stakeholders else "Unknown",
                    'stakeholder_count': len(project.stakeholders),
                    'created_date': project.created_date.isoformat()
                })
        
        return results


# Convenience functions
async def create_taiwan_collaboration_platform() -> TaiwanGovernmentPlatform:
    """Create and initialize Taiwan collaboration platform"""
    platform = TaiwanGovernmentPlatform()
    
    # Register key government agencies
    await platform.register_stakeholder(
        name="Environmental Protection Administration",
        stakeholder_type=StakeholderType.GOVERNMENT_AGENCY,
        contact_info={"email": "<EMAIL>", "phone": "+886-2-2311-7722"},
        capabilities=["environmental_monitoring", "pollution_control", "regulatory_oversight"]
    )
    
    await platform.register_stakeholder(
        name="Ocean Affairs Council",
        stakeholder_type=StakeholderType.GOVERNMENT_AGENCY,
        contact_info={"email": "<EMAIL>", "phone": "+886-7-338-2057"},
        capabilities=["marine_policy", "coastal_management", "conservation_planning"]
    )
    
    return platform


if __name__ == "__main__":
    async def test_taiwan_platform():
        print("🇹🇼 Testing Taiwan Government Collaboration Platform")
        
        try:
            platform = await create_taiwan_collaboration_platform()
            
            # Register a private company
            company_id = await platform.register_stakeholder(
                name="Marine Tech Solutions Ltd.",
                stakeholder_type=StakeholderType.PRIVATE_COMPANY,
                contact_info={"email": "<EMAIL>", "phone": "+886-2-1234-5678"},
                capabilities=["ai_technology", "satellite_monitoring", "data_analytics"]
            )
            
            # Create a collaboration project
            project_id = await platform.create_collaboration_project(
                title="AI-Powered Marine Debris Detection for Taiwan Waters",
                description="Collaborative project to implement AI-based marine debris detection system",
                objectives=["Deploy satellite monitoring", "Develop AI algorithms", "Train local teams"],
                target_areas=[(120.0, 22.0, 122.0, 25.0)],  # Taiwan waters
                lead_agency_id=list(platform.stakeholders.keys())[1],  # Ocean Affairs Council
                stakeholder_ids=list(platform.stakeholders.keys()),
                budget={"total": 5000000, "government": 3000000, "private": 2000000}
            )
            
            # Generate report
            report = await platform.generate_collaboration_report()
            
            print("✅ Platform testing completed")
            print(f"   Stakeholders: {report['platform_overview']['total_stakeholders']}")
            print(f"   Projects: {report['platform_overview']['total_projects']}")
            print(f"   Government agencies: {len(report['government_agencies'])}")
            
            print("\n🏛️ Government Agencies:")
            for agency in platform.government_agencies.values():
                print(f"   • {agency['name']} ({agency['name_zh']})")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_taiwan_platform())
