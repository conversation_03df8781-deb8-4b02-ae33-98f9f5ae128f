import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Alert,
  LinearProgress,
  Divider,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Water as WaterIcon,
  Bolt as EnergyIcon,
  Eco as EcoIcon,
  ThermostatAuto as ThermostatIcon,
  Science as ScienceIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, RadialBarChart, RadialBar } from 'recharts';

const WaterManagement = ({ data, onRefresh }) => {
  const [tabValue, setTabValue] = useState(0);

  // Sample data for charts
  const treatmentData = [
    { time: '00:00', efficiency: 92, quality: 95, energy: 88 },
    { time: '04:00', efficiency: 89, quality: 93, energy: 91 },
    { time: '08:00', efficiency: 94, quality: 96, energy: 87 },
    { time: '12:00', efficiency: 91, quality: 94, energy: 89 },
    { time: '16:00', efficiency: 88, quality: 92, energy: 93 },
    { time: '20:00', efficiency: 93, quality: 95, energy: 90 },
  ];

  const energyData = [
    { name: 'Renewable', value: data?.carbon_footprint ? 65 : 65, fill: '#4caf50' },
    { name: 'Grid', value: data?.carbon_footprint ? 35 : 35, fill: '#ff9800' },
  ];

  const waterQualityData = [
    { parameter: 'pH', value: data?.water_quality?.ph || 7.2, target: 7.0, unit: '' },
    { parameter: 'Turbidity', value: data?.water_quality?.turbidity || 1.5, target: 1.0, unit: 'NTU' },
    { parameter: 'Chlorine', value: data?.water_quality?.chlorine_residual || 1.0, target: 1.2, unit: 'mg/L' },
  ];

  const getEfficiencyColor = (value) => {
    if (value >= 90) return 'success';
    if (value >= 80) return 'warning';
    return 'error';
  };

  const getQualityStatus = (value, target) => {
    const diff = Math.abs(value - target) / target;
    if (diff <= 0.1) return 'success';
    if (diff <= 0.2) return 'warning';
    return 'error';
  };

  if (!data) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography variant="h6" color="text.secondary">
          Loading water management data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          💧 Water Management Dashboard
        </Typography>
        <Button variant="contained" onClick={onRefresh} startIcon={<SettingsIcon />}>
          Optimize System
        </Button>
      </Box>

      {/* Status Alert */}
      {data.status !== 'active' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Water Management System Status: {data.status}
        </Alert>
      )}

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Treatment Efficiency
                  </Typography>
                  <Typography variant="h4">
                    {data.treatment_efficiency ? `${(data.treatment_efficiency * 100).toFixed(1)}%` : 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current performance
                  </Typography>
                </Box>
                <WaterIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.treatment_efficiency * 100 || 0} 
                color={getEfficiencyColor(data.treatment_efficiency * 100 || 0)}
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Energy Efficiency
                  </Typography>
                  <Typography variant="h4">
                    {data.energy_efficiency ? `${(data.energy_efficiency * 100).toFixed(1)}%` : 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Power optimization
                  </Typography>
                </Box>
                <EnergyIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.energy_efficiency * 100 || 0} 
                color="warning"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Water Quality
                  </Typography>
                  <Typography variant="h4">
                    {data.water_quality_score ? `${(data.water_quality_score * 100).toFixed(0)}%` : '85%'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Quality index
                  </Typography>
                </Box>
                <ScienceIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.water_quality_score * 100 || 85} 
                color="info"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Carbon Footprint
                  </Typography>
                  <Typography variant="h4">
                    {data.carbon_footprint ? `${data.carbon_footprint.toFixed(0)}` : '246'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    kg CO₂/day
                  </Typography>
                </Box>
                <EcoIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different views */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Treatment Process" />
          <Tab label="Energy Management" />
          <Tab label="Water Quality" />
          <Tab label="Sustainability" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {/* Treatment Performance */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Treatment Performance (24h)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={treatmentData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="efficiency" stroke="#2196f3" name="Efficiency %" />
                    <Line type="monotone" dataKey="quality" stroke="#4caf50" name="Quality %" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Treatment Status */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Treatment Status
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <SpeedIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Primary Treatment"
                      secondary="Active - 95% efficiency"
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <SpeedIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Secondary Treatment"
                      secondary="Active - 92% efficiency"
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <SpeedIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Disinfection"
                      secondary="Active - 98% efficiency"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          {/* Energy Consumption */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Energy Consumption Trends
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={treatmentData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="energy" stroke="#ff9800" fill="#ff9800" fillOpacity={0.3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Energy Mix */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Energy Sources
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <RadialBarChart data={energyData}>
                    <RadialBar dataKey="value" cornerRadius={10} fill="#8884d8" />
                    <Tooltip />
                  </RadialBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 2 && (
        <Grid container spacing={3}>
          {/* Water Quality Parameters */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Water Quality Parameters
                </Typography>
                <Grid container spacing={3}>
                  {waterQualityData.map((param, index) => (
                    <Grid item xs={12} sm={4} key={index}>
                      <Box textAlign="center">
                        <Typography variant="h4" color={getQualityStatus(param.value, param.target)}>
                          {param.value} {param.unit}
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {param.parameter}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Target: {param.target} {param.unit}
                        </Typography>
                        <Chip 
                          label={getQualityStatus(param.value, param.target) === 'success' ? 'Normal' : 'Monitor'}
                          color={getQualityStatus(param.value, param.target)}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 3 && (
        <Grid container spacing={3}>
          {/* Sustainability Metrics */}
          <Grid item xs={12} lg={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sustainability Score
                </Typography>
                <Box textAlign="center" py={3}>
                  <Typography variant="h2" color="success.main">
                    {data.sustainability_score ? `${(data.sustainability_score * 100).toFixed(0)}%` : '78%'}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Overall sustainability rating
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Recommendations */}
          <Grid item xs={12} lg={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sustainability Actions
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <TrendingUpIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Increase renewable energy"
                      secondary="Target: 80% renewable by next quarter"
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <EcoIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Optimize chemical usage"
                      secondary="Reduce by 15% through process improvement"
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <WaterIcon color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Water recycling program"
                      secondary="Implement advanced recycling systems"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default WaterManagement;
