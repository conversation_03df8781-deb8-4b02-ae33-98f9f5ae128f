#!/usr/bin/env python3
"""
Frontend Feature Audit - Check if frontend implements all backend features
"""

import requests
import json
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FrontendFeatureAudit:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.audit_results = {}
        
    def get_backend_data_structure(self):
        """Get the complete data structure from backend"""
        try:
            response = requests.get(f"{self.backend_url}/api/dashboard", timeout=10)
            if response.status_code == 200:
                return response.json().get('data', {})
            return {}
        except Exception as e:
            logger.error(f"Failed to get backend data: {e}")
            return {}
    
    def check_frontend_implementation(self):
        """Check if frontend implements all backend features"""
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code != 200:
                return False, "Frontend not accessible"
            
            frontend_content = response.text.lower()
            return True, frontend_content
        except Exception as e:
            return False, f"Error accessing frontend: {e}"
    
    def audit_marine_conservation_features(self, backend_data, frontend_content):
        """Audit marine conservation features in frontend"""
        marine_data = backend_data.get('marine_conservation', {})
        
        # Check if backend features are displayed in frontend
        features_to_check = {
            'debris_count': 'debris',
            'vessel_count': 'vessel',
            'health_score': 'health',
            'risk_level': 'risk',
            'biodiversity_index': 'biodiversity',
            'conservation_actions': 'conservation',
            'monitoring_stations': 'monitoring'
        }
        
        implemented = {}
        missing = {}
        
        for feature, search_term in features_to_check.items():
            if feature in marine_data:
                if search_term in frontend_content:
                    implemented[feature] = f"✅ Found '{search_term}' in frontend"
                else:
                    missing[feature] = f"❌ '{search_term}' not found in frontend"
        
        return {
            'category': 'Marine Conservation',
            'backend_features': len(marine_data),
            'implemented': implemented,
            'missing': missing,
            'implementation_rate': len(implemented) / len(features_to_check) * 100 if features_to_check else 0
        }
    
    def audit_water_management_features(self, backend_data, frontend_content):
        """Audit water management features in frontend"""
        water_data = backend_data.get('water_management', {})
        
        features_to_check = {
            'treatment_efficiency': 'treatment',
            'energy_efficiency': 'energy',
            'carbon_footprint': 'carbon',
            'daily_capacity': 'capacity',
            'active_plants': 'plants'
        }
        
        implemented = {}
        missing = {}
        
        for feature, search_term in features_to_check.items():
            if feature in water_data:
                if search_term in frontend_content:
                    implemented[feature] = f"✅ Found '{search_term}' in frontend"
                else:
                    missing[feature] = f"❌ '{search_term}' not found in frontend"
        
        return {
            'category': 'Water Management',
            'backend_features': len(water_data),
            'implemented': implemented,
            'missing': missing,
            'implementation_rate': len(implemented) / len(features_to_check) * 100 if features_to_check else 0
        }
    
    def audit_analytics_features(self, backend_data, frontend_content):
        """Audit integrated analytics features in frontend"""
        analytics_data = backend_data.get('integrated_analytics', {})
        
        features_to_check = {
            'environmental_score': 'environmental',
            'synergy_score': 'synergy',
            'correlations': 'correlation',
            'recommendations': 'recommendation',
            'cross_system_insights': 'insight'
        }
        
        implemented = {}
        missing = {}
        
        for feature, search_term in features_to_check.items():
            if feature in analytics_data:
                if search_term in frontend_content:
                    implemented[feature] = f"✅ Found '{search_term}' in frontend"
                else:
                    missing[feature] = f"❌ '{search_term}' not found in frontend"
        
        return {
            'category': 'Integrated Analytics',
            'backend_features': len(analytics_data),
            'implemented': implemented,
            'missing': missing,
            'implementation_rate': len(implemented) / len(features_to_check) * 100 if features_to_check else 0
        }
    
    def audit_ui_components(self, frontend_content):
        """Audit UI components and features"""
        ui_features = {
            'dashboard': 'dashboard',
            'charts': 'chart',
            'maps': 'map',
            'navigation': 'nav',
            'tabs': 'tab',
            'cards': 'card',
            'progress_bars': 'progress',
            'status_indicators': 'status',
            'real_time_updates': 'real',
            'responsive_design': 'responsive'
        }
        
        implemented = {}
        missing = {}
        
        for feature, search_term in ui_features.items():
            if search_term in frontend_content:
                implemented[feature] = f"✅ Found '{search_term}' in frontend"
            else:
                missing[feature] = f"❌ '{search_term}' not found in frontend"
        
        return {
            'category': 'UI Components',
            'total_features': len(ui_features),
            'implemented': implemented,
            'missing': missing,
            'implementation_rate': len(implemented) / len(ui_features) * 100
        }
    
    def check_advanced_features(self, frontend_content):
        """Check for advanced frontend features"""
        advanced_features = {
            'interactive_charts': ['chart.js', 'chartjs'],
            'mapping_system': ['leaflet', 'map'],
            'real_time_data': ['websocket', 'real-time', 'auto-refresh'],
            'responsive_design': ['responsive', 'mobile'],
            'modern_ui': ['gradient', 'animation', 'transition'],
            'data_visualization': ['visualization', 'chart', 'graph'],
            'user_interaction': ['click', 'hover', 'interactive'],
            'error_handling': ['error', 'catch', 'try'],
            'loading_states': ['loading', 'spinner', 'wait'],
            'accessibility': ['aria', 'accessibility', 'screen']
        }
        
        implemented = {}
        missing = {}
        
        for feature, search_terms in advanced_features.items():
            found = any(term in frontend_content for term in search_terms)
            if found:
                implemented[feature] = f"✅ Advanced feature implemented"
            else:
                missing[feature] = f"❌ Advanced feature missing"
        
        return {
            'category': 'Advanced Features',
            'total_features': len(advanced_features),
            'implemented': implemented,
            'missing': missing,
            'implementation_rate': len(implemented) / len(advanced_features) * 100
        }
    
    def run_complete_audit(self):
        """Run complete frontend feature audit"""
        logger.info("🔍 FRONTEND FEATURE AUDIT - UNIFIED ENVIRONMENTAL PLATFORM")
        logger.info("=" * 70)
        
        # Get backend data
        backend_data = self.get_backend_data_structure()
        if not backend_data:
            logger.error("❌ Cannot access backend data")
            return False
        
        # Check frontend accessibility
        frontend_accessible, frontend_content = self.check_frontend_implementation()
        if not frontend_accessible:
            logger.error(f"❌ Frontend not accessible: {frontend_content}")
            return False
        
        logger.info("✅ Both backend and frontend are accessible")
        logger.info("")
        
        # Audit each category
        marine_audit = self.audit_marine_conservation_features(backend_data, frontend_content)
        water_audit = self.audit_water_management_features(backend_data, frontend_content)
        analytics_audit = self.audit_analytics_features(backend_data, frontend_content)
        ui_audit = self.audit_ui_components(frontend_content)
        advanced_audit = self.check_advanced_features(frontend_content)
        
        # Store results
        self.audit_results = {
            'marine_conservation': marine_audit,
            'water_management': water_audit,
            'integrated_analytics': analytics_audit,
            'ui_components': ui_audit,
            'advanced_features': advanced_audit
        }
        
        # Generate report
        self.generate_audit_report()
        
        return True
    
    def generate_audit_report(self):
        """Generate comprehensive audit report"""
        logger.info("📊 FRONTEND IMPLEMENTATION AUDIT REPORT")
        logger.info("=" * 70)
        
        total_implemented = 0
        total_features = 0
        
        for category, audit in self.audit_results.items():
            logger.info(f"\n📂 {audit['category'].upper()}")
            logger.info("-" * 50)
            
            implemented_count = len(audit['implemented'])
            missing_count = len(audit['missing'])
            total_count = implemented_count + missing_count
            
            total_implemented += implemented_count
            total_features += total_count
            
            logger.info(f"Implementation Rate: {audit['implementation_rate']:.1f}%")
            logger.info(f"Implemented: {implemented_count}/{total_count}")
            
            if audit['implemented']:
                logger.info("\n✅ IMPLEMENTED FEATURES:")
                for feature, status in audit['implemented'].items():
                    logger.info(f"  • {feature}: {status}")
            
            if audit['missing']:
                logger.info("\n❌ MISSING FEATURES:")
                for feature, status in audit['missing'].items():
                    logger.info(f"  • {feature}: {status}")
        
        # Overall summary
        overall_rate = (total_implemented / total_features * 100) if total_features > 0 else 0
        
        logger.info("\n" + "=" * 70)
        logger.info("🎯 OVERALL FRONTEND IMPLEMENTATION SUMMARY")
        logger.info("=" * 70)
        logger.info(f"Total Features Checked: {total_features}")
        logger.info(f"Features Implemented: {total_implemented}")
        logger.info(f"Features Missing: {total_features - total_implemented}")
        logger.info(f"Overall Implementation Rate: {overall_rate:.1f}%")
        
        if overall_rate >= 90:
            logger.info("🎉 EXCELLENT: Frontend is comprehensively implemented!")
        elif overall_rate >= 75:
            logger.info("✅ GOOD: Frontend covers most features, minor gaps")
        elif overall_rate >= 50:
            logger.info("⚠️ PARTIAL: Frontend covers basic features, needs enhancement")
        else:
            logger.info("❌ INCOMPLETE: Frontend needs significant development")
        
        # Recommendations
        logger.info("\n💡 RECOMMENDATIONS:")
        if overall_rate < 100:
            logger.info("  • Enhance frontend to display all backend data")
            logger.info("  • Add missing feature visualizations")
            logger.info("  • Improve data integration between frontend and backend")
        else:
            logger.info("  • Frontend implementation is complete!")
            logger.info("  • Consider adding advanced visualizations")
            logger.info("  • Focus on performance optimization")

def main():
    """Main audit execution"""
    auditor = FrontendFeatureAudit()
    success = auditor.run_complete_audit()
    
    if success:
        # Save audit results
        with open('frontend_audit_report.json', 'w') as f:
            json.dump({
                'audit_date': datetime.now().isoformat(),
                'results': auditor.audit_results
            }, f, indent=2)
        
        logger.info(f"\n📄 Detailed audit report saved to: frontend_audit_report.json")
    
    return 0 if success else 1

if __name__ == "__main__":
    main()
