#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marine Conservation APIs Validation Script
Validates all API configurations and tests basic connectivity
"""

import sys
import os
import asyncio
import aiohttp
import json
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from config.marine_conservation_apis import marine_apis, APIAuthType
    print("✅ Successfully imported marine conservation API configuration")
except ImportError as e:
    print(f"❌ Failed to import API configuration: {e}")
    sys.exit(1)


class MarineAPIValidator:
    """Validates marine conservation APIs"""
    
    def __init__(self):
        self.session = None
        self.results = {}
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': 'WaterManagementSystem/1.0 (<EMAIL>)'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def validate_api_config(self, api_name: str, config) -> dict:
        """Validate a single API configuration"""
        result = {
            'api_name': api_name,
            'config_valid': False,
            'auth_valid': False,
            'connectivity': False,
            'response_time': None,
            'error': None,
            'details': {}
        }
        
        try:
            # Check configuration completeness
            result['config_valid'] = self._check_config_completeness(config)
            result['auth_valid'] = self._check_auth_completeness(config)
            
            # Test basic connectivity (if possible)
            if config.auth_type == APIAuthType.OPEN_ACCESS:
                connectivity_result = await self._test_connectivity(config)
                result.update(connectivity_result)
            else:
                result['connectivity'] = 'skipped'  # Skip auth-required APIs for basic validation
                result['details']['skip_reason'] = 'Authentication required - skipping connectivity test'
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _check_config_completeness(self, config) -> bool:
        """Check if API configuration is complete"""
        required_fields = ['name', 'base_url', 'auth_type']
        return all(hasattr(config, field) and getattr(config, field) for field in required_fields)
    
    def _check_auth_completeness(self, config) -> bool:
        """Check if authentication configuration is complete"""
        auth_type = config.auth_type
        
        if auth_type == APIAuthType.API_KEY:
            return bool(config.api_key)
        elif auth_type == APIAuthType.OAUTH2:
            return bool(config.client_id and config.client_secret)
        elif auth_type == APIAuthType.USERNAME_PASSWORD:
            return bool(config.username and config.password)
        elif auth_type == APIAuthType.TOKEN:
            return bool(config.token)
        elif auth_type == APIAuthType.USER_AGENT:
            return bool(config.user_agent)
        elif auth_type == APIAuthType.OPEN_ACCESS:
            return True
        else:
            return False
    
    async def _test_connectivity(self, config) -> dict:
        """Test basic connectivity to API endpoint"""
        result = {
            'connectivity': False,
            'response_time': None,
            'details': {}
        }
        
        try:
            start_time = datetime.now()
            
            # For OpenStreetMap Overpass API, test with a simple query
            if 'overpass' in config.base_url.lower():
                test_url = f"{config.base_url}/interpreter"
                test_data = "[out:json][timeout:5];(node[amenity=cafe](50.7,7.1,50.8,7.2););out;"
                
                async with self.session.post(test_url, data=test_data) as response:
                    end_time = datetime.now()
                    result['response_time'] = (end_time - start_time).total_seconds()
                    result['connectivity'] = response.status == 200
                    result['details']['status_code'] = response.status
                    result['details']['content_type'] = response.headers.get('content-type', 'unknown')
            
            # For other open access APIs, try a simple GET request
            else:
                async with self.session.get(config.base_url) as response:
                    end_time = datetime.now()
                    result['response_time'] = (end_time - start_time).total_seconds()
                    result['connectivity'] = response.status in [200, 301, 302, 403]  # 403 might be expected for some APIs
                    result['details']['status_code'] = response.status
                    result['details']['content_type'] = response.headers.get('content-type', 'unknown')
        
        except Exception as e:
            result['details']['error'] = str(e)
        
        return result
    
    async def validate_all_apis(self) -> dict:
        """Validate all configured APIs"""
        print("🔍 Starting marine conservation APIs validation...")
        print("=" * 60)
        
        all_apis = marine_apis.get_all_apis()
        validation_tasks = []
        
        for api_name, config in all_apis.items():
            task = self.validate_api_config(api_name, config)
            validation_tasks.append(task)
        
        # Run validations concurrently
        results = await asyncio.gather(*validation_tasks, return_exceptions=True)
        
        # Process results
        validation_summary = {
            'total_apis': len(all_apis),
            'config_valid': 0,
            'auth_valid': 0,
            'connectivity_tested': 0,
            'connectivity_successful': 0,
            'results': {},
            'timestamp': datetime.now().isoformat()
        }
        
        for result in results:
            if isinstance(result, Exception):
                print(f"❌ Validation error: {result}")
                continue
            
            api_name = result['api_name']
            validation_summary['results'][api_name] = result
            
            # Update counters
            if result['config_valid']:
                validation_summary['config_valid'] += 1
            if result['auth_valid']:
                validation_summary['auth_valid'] += 1
            if result['connectivity'] not in [False, 'skipped']:
                validation_summary['connectivity_tested'] += 1
                if result['connectivity']:
                    validation_summary['connectivity_successful'] += 1
            
            # Print individual results
            self._print_api_result(result)
        
        return validation_summary
    
    def _print_api_result(self, result: dict):
        """Print validation result for a single API"""
        api_name = result['api_name']
        config_status = "✅" if result['config_valid'] else "❌"
        auth_status = "✅" if result['auth_valid'] else "❌"
        
        if result['connectivity'] == 'skipped':
            conn_status = "⏭️"
            conn_detail = "Skipped (auth required)"
        elif result['connectivity']:
            conn_status = "✅"
            conn_detail = f"Connected ({result['response_time']:.2f}s)" if result['response_time'] else "Connected"
        else:
            conn_status = "❌"
            conn_detail = "Failed to connect"
        
        print(f"{config_status} {auth_status} {conn_status} {api_name:<25} | {conn_detail}")
        
        if result['error']:
            print(f"    Error: {result['error']}")


async def main():
    """Main validation function"""
    print("🌊 Marine Conservation APIs Validation")
    print("=" * 60)
    print("Legend: Config | Auth | Connectivity | API Name")
    print("-" * 60)
    
    async with MarineAPIValidator() as validator:
        summary = await validator.validate_all_apis()
    
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"Total APIs: {summary['total_apis']}")
    print(f"Config Valid: {summary['config_valid']}/{summary['total_apis']}")
    print(f"Auth Valid: {summary['auth_valid']}/{summary['total_apis']}")
    print(f"Connectivity Tested: {summary['connectivity_tested']}")
    print(f"Connectivity Successful: {summary['connectivity_successful']}/{summary['connectivity_tested']}")
    
    # Overall status
    config_success_rate = summary['config_valid'] / summary['total_apis'] * 100
    auth_success_rate = summary['auth_valid'] / summary['total_apis'] * 100
    
    print(f"\nConfiguration Success Rate: {config_success_rate:.1f}%")
    print(f"Authentication Success Rate: {auth_success_rate:.1f}%")
    
    if config_success_rate == 100 and auth_success_rate >= 80:
        print("\n🎉 VALIDATION PASSED - APIs ready for implementation!")
    elif config_success_rate >= 90:
        print("\n⚠️ VALIDATION PARTIAL - Some authentication issues detected")
    else:
        print("\n❌ VALIDATION FAILED - Configuration issues detected")
    
    # Save results to file
    results_file = project_root / "logs" / "marine_api_validation.json"
    results_file.parent.mkdir(exist_ok=True)
    
    with open(results_file, 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: {results_file}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ Validation interrupted by user")
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        sys.exit(1)
