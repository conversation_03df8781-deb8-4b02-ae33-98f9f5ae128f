"""
OpenWeatherMap API Collector for real-time weather data.

This module provides comprehensive integration with OpenWeatherMap API
for collecting current weather, forecasts, and historical data.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


@dataclass
class WeatherData:
    """Weather data structure."""
    timestamp: datetime
    location: str
    latitude: float
    longitude: float
    temperature: float
    feels_like: float
    humidity: int
    pressure: float
    visibility: Optional[float]
    uv_index: Optional[float]
    wind_speed: float
    wind_direction: int
    precipitation: float
    weather_main: str
    weather_description: str
    clouds: int
    source: str = "openweathermap"


class OpenWeatherMapCollector:
    """
    OpenWeatherMap API collector for weather data.
    
    Supports:
    - Current weather data
    - 5-day weather forecast
    - Air pollution data
    - UV index data
    - Weather alerts
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = self.settings.OPENWEATHER_API_KEY
        self.base_url = self.settings.OPENWEATHER_BASE_URL
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_initialized = False
        
        # Rate limiting
        self.requests_per_minute = 60
        self.request_timestamps = []
        
        # Default locations for monitoring
        self.default_locations = [
            {"name": "New York", "lat": 40.7128, "lon": -74.0060},
            {"name": "London", "lat": 51.5074, "lon": -0.1278},
            {"name": "Tokyo", "lat": 35.6762, "lon": 139.6503},
            {"name": "Sydney", "lat": -33.8688, "lon": 151.2093},
            {"name": "Mumbai", "lat": 19.0760, "lon": 72.8777}
        ]
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the OpenWeatherMap collector."""
        try:
            if not self.api_key:
                logger.warning("OpenWeatherMap API key not configured")
                return False
            
            logger.info("Initializing OpenWeatherMap collector...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={"User-Agent": "WaterManagement-DecarbonisationSystem/1.0"}
            )
            
            # Test API connection
            test_success = await self._test_api_connection()
            
            if test_success:
                self.is_initialized = True
                logger.info("OpenWeatherMap collector initialized successfully")
                return True
            else:
                logger.error("OpenWeatherMap API test failed")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize OpenWeatherMap collector: {e}")
            return False
    
    async def _test_api_connection(self) -> bool:
        """Test API connection with a simple request."""
        try:
            url = f"{self.base_url}/weather"
            params = {
                "q": "London",
                "appid": self.api_key,
                "units": "metric"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"API test successful: {data.get('name', 'Unknown')} weather retrieved")
                    return True
                else:
                    logger.error(f"API test failed with status {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"API connection test failed: {e}")
            return False
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        now = datetime.now()
        
        # Remove timestamps older than 1 minute
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if now - ts < timedelta(minutes=1)
        ]
        
        # Check if we're at the limit
        if len(self.request_timestamps) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.request_timestamps[0]).total_seconds()
            if sleep_time > 0:
                logger.warning(f"Rate limit reached, sleeping for {sleep_time:.1f} seconds")
                await asyncio.sleep(sleep_time)
        
        # Add current request timestamp
        self.request_timestamps.append(now)
    
    @cache_decorator(ttl=600, key_prefix="owm_current")  # Cache for 10 minutes
    async def get_current_weather(self, location: Dict[str, Any]) -> Optional[WeatherData]:
        """Get current weather data for a location."""
        try:
            if not self.is_initialized:
                logger.error("Collector not initialized")
                return None
            
            await self._check_rate_limit()
            
            url = f"{self.base_url}/weather"
            params = {
                "lat": location["lat"],
                "lon": location["lon"],
                "appid": self.api_key,
                "units": "metric"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_current_weather(data, location["name"])
                else:
                    logger.error(f"Weather API error {response.status} for {location['name']}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get current weather for {location.get('name', 'Unknown')}: {e}")
            return None
    
    def _parse_current_weather(self, data: Dict[str, Any], location_name: str) -> WeatherData:
        """Parse OpenWeatherMap current weather response."""
        main = data.get("main", {})
        weather = data.get("weather", [{}])[0]
        wind = data.get("wind", {})
        clouds = data.get("clouds", {})
        rain = data.get("rain", {})
        snow = data.get("snow", {})
        
        # Calculate precipitation (rain + snow)
        precipitation = rain.get("1h", 0) + snow.get("1h", 0)
        
        return WeatherData(
            timestamp=datetime.now(),
            location=location_name,
            latitude=data["coord"]["lat"],
            longitude=data["coord"]["lon"],
            temperature=main.get("temp", 0),
            feels_like=main.get("feels_like", 0),
            humidity=main.get("humidity", 0),
            pressure=main.get("pressure", 0),
            visibility=data.get("visibility", 0) / 1000 if data.get("visibility") else None,  # Convert to km
            uv_index=None,  # Not available in current weather API
            wind_speed=wind.get("speed", 0),
            wind_direction=wind.get("deg", 0),
            precipitation=precipitation,
            weather_main=weather.get("main", ""),
            weather_description=weather.get("description", ""),
            clouds=clouds.get("all", 0)
        )
    
    @cache_decorator(ttl=3600, key_prefix="owm_forecast")  # Cache for 1 hour
    async def get_weather_forecast(self, location: Dict[str, Any], days: int = 5) -> List[WeatherData]:
        """Get weather forecast for a location."""
        try:
            if not self.is_initialized:
                logger.error("Collector not initialized")
                return []
            
            await self._check_rate_limit()
            
            url = f"{self.base_url}/forecast"
            params = {
                "lat": location["lat"],
                "lon": location["lon"],
                "appid": self.api_key,
                "units": "metric",
                "cnt": min(days * 8, 40)  # 8 forecasts per day, max 40
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_forecast_data(data, location["name"])
                else:
                    logger.error(f"Forecast API error {response.status} for {location['name']}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to get forecast for {location.get('name', 'Unknown')}: {e}")
            return []
    
    def _parse_forecast_data(self, data: Dict[str, Any], location_name: str) -> List[WeatherData]:
        """Parse OpenWeatherMap forecast response."""
        forecasts = []
        
        for item in data.get("list", []):
            main = item.get("main", {})
            weather = item.get("weather", [{}])[0]
            wind = item.get("wind", {})
            clouds = item.get("clouds", {})
            rain = item.get("rain", {})
            snow = item.get("snow", {})
            
            # Calculate precipitation
            precipitation = rain.get("3h", 0) + snow.get("3h", 0)
            
            forecast = WeatherData(
                timestamp=datetime.fromtimestamp(item["dt"]),
                location=location_name,
                latitude=data["city"]["coord"]["lat"],
                longitude=data["city"]["coord"]["lon"],
                temperature=main.get("temp", 0),
                feels_like=main.get("feels_like", 0),
                humidity=main.get("humidity", 0),
                pressure=main.get("pressure", 0),
                visibility=item.get("visibility", 0) / 1000 if item.get("visibility") else None,
                uv_index=None,
                wind_speed=wind.get("speed", 0),
                wind_direction=wind.get("deg", 0),
                precipitation=precipitation,
                weather_main=weather.get("main", ""),
                weather_description=weather.get("description", ""),
                clouds=clouds.get("all", 0)
            )
            
            forecasts.append(forecast)
        
        return forecasts
    
    async def get_air_pollution(self, location: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get air pollution data for a location."""
        try:
            if not self.is_initialized:
                return None
            
            await self._check_rate_limit()
            
            url = "http://api.openweathermap.org/data/2.5/air_pollution"
            params = {
                "lat": location["lat"],
                "lon": location["lon"],
                "appid": self.api_key
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_air_pollution(data)
                else:
                    logger.error(f"Air pollution API error {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get air pollution data: {e}")
            return None
    
    def _parse_air_pollution(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse air pollution response."""
        if not data.get("list"):
            return {}
        
        pollution_data = data["list"][0]
        main = pollution_data.get("main", {})
        components = pollution_data.get("components", {})
        
        return {
            "timestamp": datetime.fromtimestamp(pollution_data["dt"]),
            "aqi": main.get("aqi", 0),  # Air Quality Index (1-5)
            "co": components.get("co", 0),  # Carbon monoxide
            "no": components.get("no", 0),  # Nitric oxide
            "no2": components.get("no2", 0),  # Nitrogen dioxide
            "o3": components.get("o3", 0),  # Ozone
            "so2": components.get("so2", 0),  # Sulphur dioxide
            "pm2_5": components.get("pm2_5", 0),  # PM2.5
            "pm10": components.get("pm10", 0),  # PM10
            "nh3": components.get("nh3", 0)  # Ammonia
        }
    
    async def collect_all_locations(self) -> List[WeatherData]:
        """Collect current weather data for all default locations."""
        weather_data = []
        
        for location in self.default_locations:
            try:
                data = await self.get_current_weather(location)
                if data:
                    weather_data.append(data)
                    
                # Small delay between requests
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to collect weather for {location['name']}: {e}")
        
        logger.info(f"Collected weather data for {len(weather_data)} locations")
        return weather_data
    
    async def get_weather_summary(self, location: Dict[str, Any]) -> Dict[str, Any]:
        """Get comprehensive weather summary for a location."""
        try:
            # Get current weather and forecast
            current = await self.get_current_weather(location)
            forecast = await self.get_weather_forecast(location, days=3)
            air_pollution = await self.get_air_pollution(location)
            
            summary = {
                "location": location["name"],
                "timestamp": datetime.now().isoformat(),
                "current_weather": current.__dict__ if current else None,
                "forecast_count": len(forecast),
                "air_pollution": air_pollution
            }
            
            if forecast:
                # Calculate forecast statistics
                temps = [f.temperature for f in forecast]
                summary["forecast_stats"] = {
                    "min_temp": min(temps),
                    "max_temp": max(temps),
                    "avg_temp": sum(temps) / len(temps),
                    "total_precipitation": sum(f.precipitation for f in forecast)
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get weather summary for {location.get('name', 'Unknown')}: {e}")
            return {}
    
    async def shutdown(self):
        """Shutdown the collector."""
        try:
            if self.session:
                await self.session.close()
            logger.info("OpenWeatherMap collector shutdown completed")
        except Exception as e:
            logger.error(f"Error during collector shutdown: {e}")


# Convenience functions
async def get_weather_for_location(location_name: str, lat: float, lon: float) -> Optional[WeatherData]:
    """Get current weather for a specific location."""
    collector = OpenWeatherMapCollector()
    await collector.initialize()
    
    location = {"name": location_name, "lat": lat, "lon": lon}
    weather = await collector.get_current_weather(location)
    
    await collector.shutdown()
    return weather


async def get_weather_summary_for_location(location_name: str, lat: float, lon: float) -> Dict[str, Any]:
    """Get comprehensive weather summary for a specific location."""
    collector = OpenWeatherMapCollector()
    await collector.initialize()
    
    location = {"name": location_name, "lat": lat, "lon": lon}
    summary = await collector.get_weather_summary(location)
    
    await collector.shutdown()
    return summary
