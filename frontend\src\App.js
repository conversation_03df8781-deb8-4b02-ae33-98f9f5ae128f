import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, CircularProgress, Typography } from '@mui/material';
import { io } from 'socket.io-client';

// Components
import Navbar from './components/Navbar';
import Dashboard from './components/Dashboard';
import MarineConservation from './components/MarineConservation';
import WaterManagement from './components/WaterManagement';
import IntegratedAnalytics from './components/IntegratedAnalytics';
import SystemStatus from './components/SystemStatus';
import Settings from './components/Settings';

// Services
import { apiService } from './services/apiService';
import { websocketService } from './services/websocketService';

// Theme configuration
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#2196f3',
      light: '#64b5f6',
      dark: '#1976d2',
    },
    secondary: {
      main: '#4caf50',
      light: '#81c784',
      dark: '#388e3c',
    },
    background: {
      default: '#0a1929',
      paper: '#132f4c',
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0bec5',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #132f4c 0%, #1e3a5f 100%)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundImage: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
        },
      },
    },
  },
});

function App() {
  const [loading, setLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    initializeApp();
    return () => {
      websocketService.disconnect();
    };
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check system status
      const status = await apiService.getSystemStatus();
      setSystemStatus(status);

      // Get initial dashboard data
      const dashboard = await apiService.getDashboardData();
      setDashboardData(dashboard);

      // Initialize WebSocket connection
      websocketService.connect();
      
      // Set up WebSocket event listeners
      websocketService.on('dashboard_update', (data) => {
        setDashboardData(data.data);
      });

      websocketService.on('connect', () => {
        setConnected(true);
        console.log('🔗 WebSocket connected');
      });

      websocketService.on('disconnect', () => {
        setConnected(false);
        console.log('❌ WebSocket disconnected');
      });

      websocketService.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        setError('WebSocket connection error');
      });

      setLoading(false);

    } catch (error) {
      console.error('❌ Failed to initialize app:', error);
      setError('Failed to initialize application');
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      const dashboard = await apiService.getDashboardData();
      setDashboardData(dashboard);
    } catch (error) {
      console.error('❌ Failed to refresh data:', error);
      setError('Failed to refresh data');
    }
  };

  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
          bgcolor="background.default"
        >
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
            Initializing Unified Environmental Platform...
          </Typography>
          <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
            Loading marine conservation and water management systems
          </Typography>
        </Box>
      </ThemeProvider>
    );
  }

  if (error) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
          bgcolor="background.default"
        >
          <Typography variant="h5" color="error" gutterBottom>
            ❌ Application Error
          </Typography>
          <Typography variant="body1" sx={{ mb: 2, color: 'text.secondary' }}>
            {error}
          </Typography>
          <button 
            onClick={initializeApp}
            style={{
              padding: '10px 20px',
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Retry
          </button>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
          <Navbar 
            connected={connected} 
            systemStatus={systemStatus}
            onRefresh={handleRefresh}
          />
          
          <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
            <Routes>
              <Route 
                path="/" 
                element={
                  <Dashboard 
                    data={dashboardData} 
                    systemStatus={systemStatus}
                    connected={connected}
                  />
                } 
              />
              <Route 
                path="/marine" 
                element={
                  <MarineConservation 
                    data={dashboardData?.marine_conservation}
                    onRefresh={handleRefresh}
                  />
                } 
              />
              <Route 
                path="/water" 
                element={
                  <WaterManagement 
                    data={dashboardData?.water_management}
                    onRefresh={handleRefresh}
                  />
                } 
              />
              <Route 
                path="/analytics" 
                element={
                  <IntegratedAnalytics 
                    data={dashboardData?.integrated_analytics}
                    marineData={dashboardData?.marine_conservation}
                    waterData={dashboardData?.water_management}
                    onRefresh={handleRefresh}
                  />
                } 
              />
              <Route 
                path="/status" 
                element={
                  <SystemStatus 
                    systemStatus={systemStatus}
                    dashboardData={dashboardData}
                    connected={connected}
                  />
                } 
              />
              <Route 
                path="/settings" 
                element={<Settings />} 
              />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Box>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
