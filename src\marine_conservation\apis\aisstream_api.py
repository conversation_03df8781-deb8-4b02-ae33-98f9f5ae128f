#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AISStream.io API Integration for Real-Time Vessel Movement and Debris Correlation
Maritime traffic data for marine conservation and debris tracking
"""

import os
import json
import asyncio
import aiohttp
import websockets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class VesselData:
    """AIS vessel tracking data"""
    mmsi: str  # Maritime Mobile Service Identity
    vessel_name: Optional[str]
    latitude: float
    longitude: float
    timestamp: datetime
    speed: float  # knots
    course: float  # degrees
    heading: Optional[float]  # degrees
    vessel_type: Optional[str]
    length: Optional[float]  # meters
    width: Optional[float]  # meters
    draught: Optional[float]  # meters
    destination: Optional[str]
    eta: Optional[datetime]
    status: Optional[str]


@dataclass
class MaritimeTrafficAnalysis:
    """Maritime traffic analysis for debris correlation"""
    area_bbox: Tuple[float, float, float, float]
    vessel_count: int
    average_speed: float
    traffic_density: float
    potential_debris_sources: List[str]
    high_risk_vessels: List[VesselData]
    timestamp: datetime


class AISStreamAPI:
    """AISStream.io API client for real-time vessel tracking"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("AISSTREAM_API_KEY", "989d91ab25d59efbe59279756d4b355f5b79c4c8")
        self.base_url = "https://stream.aisstream.io"
        self.websocket_url = "wss://stream.aisstream.io/v0/stream"
        self.session = None
        self.websocket = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={'Authorization': f'Bearer {self.api_key}'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.websocket:
            await self.websocket.close()
        if self.session:
            await self.session.close()
    
    async def get_vessels_in_area(
        self,
        bbox: Tuple[float, float, float, float],
        vessel_types: List[str] = None
    ) -> List[VesselData]:
        """Get vessels currently in specified area"""
        try:
            # Simulate AIS data (in production would use actual AIS API)
            vessels = []
            
            # Generate sample vessel data for the area
            import random
            vessel_count = random.randint(5, 20)
            
            vessel_type_options = vessel_types or [
                'cargo', 'tanker', 'fishing', 'passenger', 'tug', 'pleasure'
            ]
            
            for i in range(vessel_count):
                # Random position within bbox
                lat = random.uniform(bbox[1], bbox[3])
                lon = random.uniform(bbox[0], bbox[2])
                
                vessel = VesselData(
                    mmsi=f"12345{i:04d}",
                    vessel_name=f"Vessel_{i+1}",
                    latitude=lat,
                    longitude=lon,
                    timestamp=datetime.now(),
                    speed=random.uniform(0, 25),  # knots
                    course=random.uniform(0, 360),
                    heading=random.uniform(0, 360),
                    vessel_type=random.choice(vessel_type_options),
                    length=random.uniform(20, 300),
                    width=random.uniform(5, 50),
                    draught=random.uniform(2, 15),
                    destination=f"Port_{random.randint(1, 10)}",
                    eta=datetime.now() + timedelta(hours=random.randint(1, 48)),
                    status="under way using engine"
                )
                vessels.append(vessel)
            
            logger.info(f"✅ Retrieved {len(vessels)} vessels in area")
            return vessels
            
        except Exception as e:
            logger.error(f"❌ Error getting vessels in area: {e}")
            return []
    
    async def track_vessel_history(
        self,
        mmsi: str,
        hours_back: int = 24
    ) -> List[VesselData]:
        """Get historical track for specific vessel"""
        try:
            # Simulate vessel track history
            track_points = []
            
            # Generate track points every hour
            for i in range(hours_back):
                timestamp = datetime.now() - timedelta(hours=hours_back - i)
                
                # Simulate vessel movement
                base_lat = 40.0 + i * 0.01  # Moving north
                base_lon = -70.0 + i * 0.02  # Moving east
                
                track_point = VesselData(
                    mmsi=mmsi,
                    vessel_name=f"Tracked_Vessel_{mmsi}",
                    latitude=base_lat,
                    longitude=base_lon,
                    timestamp=timestamp,
                    speed=12.5 + i * 0.5,
                    course=45.0,
                    heading=45.0,
                    vessel_type="cargo",
                    length=200.0,
                    width=30.0,
                    draught=10.0,
                    destination="Port_Destination",
                    eta=datetime.now() + timedelta(hours=24),
                    status="under way using engine"
                )
                track_points.append(track_point)
            
            logger.info(f"✅ Retrieved {len(track_points)} track points for vessel {mmsi}")
            return track_points
            
        except Exception as e:
            logger.error(f"❌ Error tracking vessel history: {e}")
            return []
    
    async def analyze_maritime_traffic(
        self,
        bbox: Tuple[float, float, float, float],
        analysis_period_hours: int = 24
    ) -> MaritimeTrafficAnalysis:
        """Analyze maritime traffic patterns for debris correlation"""
        try:
            # Get current vessels in area
            vessels = await self.get_vessels_in_area(bbox)
            
            if not vessels:
                return MaritimeTrafficAnalysis(
                    area_bbox=bbox,
                    vessel_count=0,
                    average_speed=0.0,
                    traffic_density=0.0,
                    potential_debris_sources=[],
                    high_risk_vessels=[],
                    timestamp=datetime.now()
                )
            
            # Calculate traffic metrics
            total_speed = sum(vessel.speed for vessel in vessels)
            average_speed = total_speed / len(vessels)
            
            # Calculate area in square nautical miles (approximate)
            area_width = abs(bbox[2] - bbox[0]) * 60  # degrees to nautical miles
            area_height = abs(bbox[3] - bbox[1]) * 60
            area_sq_nm = area_width * area_height
            traffic_density = len(vessels) / area_sq_nm if area_sq_nm > 0 else 0
            
            # Identify potential debris sources
            debris_sources = []
            high_risk_vessels = []
            
            for vessel in vessels:
                # High-risk criteria
                if (vessel.vessel_type in ['fishing', 'cargo', 'tanker'] and 
                    vessel.speed < 2.0):  # Slow or stationary vessels
                    high_risk_vessels.append(vessel)
                    
                if vessel.vessel_type == 'fishing':
                    debris_sources.append('fishing_gear')
                elif vessel.vessel_type in ['cargo', 'tanker']:
                    debris_sources.append('container_loss')
                elif vessel.vessel_type == 'pleasure':
                    debris_sources.append('recreational_waste')
            
            analysis = MaritimeTrafficAnalysis(
                area_bbox=bbox,
                vessel_count=len(vessels),
                average_speed=average_speed,
                traffic_density=traffic_density,
                potential_debris_sources=list(set(debris_sources)),
                high_risk_vessels=high_risk_vessels,
                timestamp=datetime.now()
            )
            
            logger.info(f"✅ Maritime traffic analysis complete: {len(vessels)} vessels, "
                       f"{len(high_risk_vessels)} high-risk")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Error analyzing maritime traffic: {e}")
            return MaritimeTrafficAnalysis(
                area_bbox=bbox,
                vessel_count=0,
                average_speed=0.0,
                traffic_density=0.0,
                potential_debris_sources=[],
                high_risk_vessels=[],
                timestamp=datetime.now()
            )
    
    async def start_realtime_tracking(
        self,
        bbox: Tuple[float, float, float, float],
        callback: Callable[[VesselData], None],
        duration_minutes: int = 60
    ):
        """Start real-time vessel tracking via WebSocket"""
        try:
            # Simulate real-time tracking
            logger.info(f"🔄 Starting real-time tracking for {duration_minutes} minutes")
            
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            
            while datetime.now() < end_time:
                # Get current vessels
                vessels = await self.get_vessels_in_area(bbox)
                
                # Call callback for each vessel
                for vessel in vessels:
                    callback(vessel)
                
                # Wait before next update
                await asyncio.sleep(30)  # Update every 30 seconds
                
            logger.info("✅ Real-time tracking completed")
            
        except Exception as e:
            logger.error(f"❌ Error in real-time tracking: {e}")
    
    async def correlate_vessels_with_debris(
        self,
        debris_locations: List[Tuple[float, float]],
        search_radius_km: float = 10.0,
        time_window_hours: int = 48
    ) -> Dict[str, Any]:
        """Correlate vessel movements with debris sightings"""
        try:
            correlations = []
            
            for debris_lat, debris_lon in debris_locations:
                # Create search area around debris
                search_bbox = (
                    debris_lon - search_radius_km/111.0,  # Approximate km to degrees
                    debris_lat - search_radius_km/111.0,
                    debris_lon + search_radius_km/111.0,
                    debris_lat + search_radius_km/111.0
                )
                
                # Get vessels in search area
                vessels = await self.get_vessels_in_area(search_bbox)
                
                # Analyze vessel types and activities
                nearby_vessels = []
                for vessel in vessels:
                    distance_km = self._calculate_distance(
                        debris_lat, debris_lon, vessel.latitude, vessel.longitude
                    )
                    
                    if distance_km <= search_radius_km:
                        nearby_vessels.append({
                            'vessel': vessel,
                            'distance_km': distance_km,
                            'potential_source': self._assess_debris_potential(vessel)
                        })
                
                correlations.append({
                    'debris_location': (debris_lat, debris_lon),
                    'nearby_vessels': nearby_vessels,
                    'correlation_score': len(nearby_vessels) / 10.0  # Simple scoring
                })
            
            return {
                'correlations': correlations,
                'total_debris_locations': len(debris_locations),
                'total_correlations': len([c for c in correlations if c['nearby_vessels']]),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error correlating vessels with debris: {e}")
            return {}
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two points in kilometers"""
        import math
        
        # Haversine formula
        R = 6371  # Earth's radius in kilometers
        
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        
        a = (math.sin(dlat/2) * math.sin(dlat/2) + 
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * 
             math.sin(dlon/2) * math.sin(dlon/2))
        
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c
        
        return distance
    
    def _assess_debris_potential(self, vessel: VesselData) -> str:
        """Assess potential for vessel to be debris source"""
        if vessel.vessel_type == 'fishing':
            return 'high'  # Fishing gear loss
        elif vessel.vessel_type in ['cargo', 'tanker']:
            return 'medium'  # Container/cargo loss
        elif vessel.vessel_type == 'pleasure':
            return 'low'  # Recreational waste
        else:
            return 'unknown'


# Convenience functions
async def get_maritime_traffic_data(
    bbox: Tuple[float, float, float, float],
    api_key: str = None
) -> Dict[str, Any]:
    """Get comprehensive maritime traffic data"""
    async with AISStreamAPI(api_key) as api:
        # Get vessels and traffic analysis
        vessels_task = api.get_vessels_in_area(bbox)
        analysis_task = api.analyze_maritime_traffic(bbox)
        
        vessels, analysis = await asyncio.gather(vessels_task, analysis_task, return_exceptions=True)
        
        return {
            'bbox': bbox,
            'vessels': vessels if not isinstance(vessels, Exception) else [],
            'traffic_analysis': analysis if not isinstance(analysis, Exception) else None,
            'timestamp': datetime.now().isoformat(),
            'data_source': 'AISStream.io'
        }


if __name__ == "__main__":
    async def test_aisstream_api():
        print("🚢 Testing AISStream.io API")
        # Test area: English Channel
        test_bbox = (-1.0, 50.0, 1.0, 51.0)
        
        try:
            data = await get_maritime_traffic_data(test_bbox)
            print(f"✅ AIS data retrieved for bbox {test_bbox}")
            print(f"   Vessels: {len(data['vessels'])}")
            
            if data['traffic_analysis']:
                analysis = data['traffic_analysis']
                print(f"   Average speed: {analysis.average_speed:.1f} knots")
                print(f"   Traffic density: {analysis.traffic_density:.2f} vessels/sq nm")
                print(f"   High-risk vessels: {len(analysis.high_risk_vessels)}")
                print(f"   Debris sources: {analysis.potential_debris_sources}")
            
            # Show sample vessels
            for i, vessel in enumerate(data['vessels'][:3]):
                print(f"   Vessel {i+1}: {vessel.vessel_name} ({vessel.vessel_type})")
                print(f"     Position: {vessel.latitude:.3f}, {vessel.longitude:.3f}")
                print(f"     Speed: {vessel.speed:.1f} knots")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_aisstream_api())
