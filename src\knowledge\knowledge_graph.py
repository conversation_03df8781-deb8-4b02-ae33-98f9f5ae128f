"""
Domain-Specific Knowledge Graphs.

Fast implementation of comprehensive knowledge graphs for
water management with entities, relationships, reasoning,
and intelligent query capabilities.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
import json
import uuid
from dataclasses import dataclass, asdict
from enum import Enum

from src.utils.config import get_settings
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class EntityType(Enum):
    """Knowledge graph entity types."""
    COMPONENT = "component"
    PROCESS = "process"
    PARAMETER = "parameter"
    CHEMICAL = "chemical"
    EQUIPMENT = "equipment"
    REGULATION = "regulation"
    STANDARD = "standard"
    LOCATION = "location"
    ORGANIZATION = "organization"
    PERSON = "person"
    CONCEPT = "concept"


class RelationType(Enum):
    """Knowledge graph relationship types."""
    PART_OF = "part_of"
    USES = "uses"
    PRODUCES = "produces"
    REQUIRES = "requires"
    AFFECTS = "affects"
    REGULATES = "regulates"
    LOCATED_IN = "located_in"
    MANAGED_BY = "managed_by"
    SIMILAR_TO = "similar_to"
    CAUSES = "causes"
    PREVENTS = "prevents"
    OPTIMIZES = "optimizes"


@dataclass
class Entity:
    """Knowledge graph entity."""
    entity_id: str
    name: str
    entity_type: EntityType
    properties: Dict[str, Any]
    description: str
    aliases: List[str]
    confidence: float
    created_at: datetime
    updated_at: datetime


@dataclass
class Relationship:
    """Knowledge graph relationship."""
    relationship_id: str
    source_entity_id: str
    target_entity_id: str
    relationship_type: RelationType
    properties: Dict[str, Any]
    confidence: float
    evidence: List[str]
    created_at: datetime


@dataclass
class KnowledgeQuery:
    """Knowledge graph query."""
    query_id: str
    query_text: str
    query_type: str
    entities: List[str]
    relationships: List[str]
    constraints: Dict[str, Any]
    created_at: datetime


class WaterManagementKnowledgeGraph:
    """
    Domain-specific knowledge graph for water management.
    
    Provides:
    - Entity and relationship management
    - Semantic search and reasoning
    - Domain-specific ontologies
    - Intelligent query processing
    - Knowledge inference and discovery
    - Integration with external knowledge sources
    """
    
    def __init__(self):
        self.settings = get_settings()
        
        # Knowledge storage
        self.entities: Dict[str, Entity] = {}
        self.relationships: Dict[str, Relationship] = {}
        self.entity_index: Dict[EntityType, Set[str]] = {}
        self.relationship_index: Dict[RelationType, Set[str]] = {}
        
        # Domain ontologies
        self.ontologies = self._initialize_ontologies()
        
        # Query history
        self.query_history: List[KnowledgeQuery] = []
        
        # Initialize with domain knowledge
        self._populate_initial_knowledge()
    
    async def add_entity(self, name: str, entity_type: EntityType,
                        properties: Dict[str, Any] = None,
                        description: str = "",
                        aliases: List[str] = None) -> Entity:
        """Add entity to knowledge graph."""
        try:
            if properties is None:
                properties = {}
            if aliases is None:
                aliases = []
            
            entity_id = str(uuid.uuid4())
            
            entity = Entity(
                entity_id=entity_id,
                name=name,
                entity_type=entity_type,
                properties=properties,
                description=description,
                aliases=aliases,
                confidence=1.0,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Store entity
            self.entities[entity_id] = entity
            
            # Update index
            if entity_type not in self.entity_index:
                self.entity_index[entity_type] = set()
            self.entity_index[entity_type].add(entity_id)
            
            logger.info(f"Added entity: {name} ({entity_type.value})")
            return entity
            
        except Exception as e:
            logger.error(f"Failed to add entity: {e}")
            raise
    
    async def add_relationship(self, source_entity_id: str, target_entity_id: str,
                             relationship_type: RelationType,
                             properties: Dict[str, Any] = None,
                             evidence: List[str] = None) -> Relationship:
        """Add relationship to knowledge graph."""
        try:
            if properties is None:
                properties = {}
            if evidence is None:
                evidence = []
            
            # Validate entities exist
            if source_entity_id not in self.entities:
                raise ValueError(f"Source entity {source_entity_id} not found")
            if target_entity_id not in self.entities:
                raise ValueError(f"Target entity {target_entity_id} not found")
            
            relationship_id = str(uuid.uuid4())
            
            relationship = Relationship(
                relationship_id=relationship_id,
                source_entity_id=source_entity_id,
                target_entity_id=target_entity_id,
                relationship_type=relationship_type,
                properties=properties,
                confidence=1.0,
                evidence=evidence,
                created_at=datetime.now()
            )
            
            # Store relationship
            self.relationships[relationship_id] = relationship
            
            # Update index
            if relationship_type not in self.relationship_index:
                self.relationship_index[relationship_type] = set()
            self.relationship_index[relationship_type].add(relationship_id)
            
            logger.info(f"Added relationship: {relationship_type.value}")
            return relationship
            
        except Exception as e:
            logger.error(f"Failed to add relationship: {e}")
            raise
    
    async def query_entities(self, entity_type: EntityType = None,
                           name_pattern: str = None,
                           properties_filter: Dict[str, Any] = None) -> List[Entity]:
        """Query entities with filters."""
        try:
            results = []
            
            # Get candidate entities
            if entity_type:
                candidate_ids = self.entity_index.get(entity_type, set())
                candidates = [self.entities[eid] for eid in candidate_ids]
            else:
                candidates = list(self.entities.values())
            
            # Apply filters
            for entity in candidates:
                # Name pattern filter
                if name_pattern and name_pattern.lower() not in entity.name.lower():
                    continue
                
                # Properties filter
                if properties_filter:
                    match = True
                    for key, value in properties_filter.items():
                        if key not in entity.properties or entity.properties[key] != value:
                            match = False
                            break
                    if not match:
                        continue
                
                results.append(entity)
            
            logger.info(f"Query returned {len(results)} entities")
            return results
            
        except Exception as e:
            logger.error(f"Entity query failed: {e}")
            return []
    
    async def find_relationships(self, source_entity_id: str = None,
                               target_entity_id: str = None,
                               relationship_type: RelationType = None) -> List[Relationship]:
        """Find relationships with filters."""
        try:
            results = []
            
            # Get candidate relationships
            if relationship_type:
                candidate_ids = self.relationship_index.get(relationship_type, set())
                candidates = [self.relationships[rid] for rid in candidate_ids]
            else:
                candidates = list(self.relationships.values())
            
            # Apply filters
            for relationship in candidates:
                if source_entity_id and relationship.source_entity_id != source_entity_id:
                    continue
                if target_entity_id and relationship.target_entity_id != target_entity_id:
                    continue
                
                results.append(relationship)
            
            logger.info(f"Found {len(results)} relationships")
            return results
            
        except Exception as e:
            logger.error(f"Relationship query failed: {e}")
            return []
    
    async def find_connected_entities(self, entity_id: str,
                                    relationship_types: List[RelationType] = None,
                                    max_depth: int = 2) -> Dict[str, Any]:
        """Find entities connected to given entity."""
        try:
            if entity_id not in self.entities:
                raise ValueError(f"Entity {entity_id} not found")
            
            visited = set()
            connected = {}
            queue = [(entity_id, 0)]  # (entity_id, depth)
            
            while queue:
                current_id, depth = queue.pop(0)
                
                if current_id in visited or depth > max_depth:
                    continue
                
                visited.add(current_id)
                
                # Find outgoing relationships
                outgoing = await self.find_relationships(source_entity_id=current_id)
                
                # Find incoming relationships
                incoming = await self.find_relationships(target_entity_id=current_id)
                
                # Process relationships
                for rel in outgoing + incoming:
                    if relationship_types and rel.relationship_type not in relationship_types:
                        continue
                    
                    # Get connected entity
                    if rel.source_entity_id == current_id:
                        connected_id = rel.target_entity_id
                    else:
                        connected_id = rel.source_entity_id
                    
                    if connected_id not in visited:
                        if connected_id not in connected:
                            connected[connected_id] = {
                                'entity': self.entities[connected_id],
                                'relationships': [],
                                'depth': depth + 1
                            }
                        
                        connected[connected_id]['relationships'].append(rel)
                        
                        if depth + 1 <= max_depth:
                            queue.append((connected_id, depth + 1))
            
            return connected
            
        except Exception as e:
            logger.error(f"Connected entities search failed: {e}")
            return {}
    
    async def semantic_search(self, query_text: str, entity_types: List[EntityType] = None,
                            top_k: int = 10) -> List[Dict[str, Any]]:
        """Perform semantic search on knowledge graph."""
        try:
            logger.info(f"Performing semantic search: {query_text}")
            
            results = []
            query_lower = query_text.lower()
            
            # Search entities
            candidates = []
            if entity_types:
                for entity_type in entity_types:
                    entity_ids = self.entity_index.get(entity_type, set())
                    candidates.extend([self.entities[eid] for eid in entity_ids])
            else:
                candidates = list(self.entities.values())
            
            # Calculate relevance scores
            for entity in candidates:
                score = 0.0
                
                # Name match
                if query_lower in entity.name.lower():
                    score += 1.0
                
                # Alias match
                for alias in entity.aliases:
                    if query_lower in alias.lower():
                        score += 0.8
                
                # Description match
                if query_lower in entity.description.lower():
                    score += 0.6
                
                # Properties match
                for key, value in entity.properties.items():
                    if query_lower in str(value).lower():
                        score += 0.4
                
                if score > 0:
                    results.append({
                        'entity': entity,
                        'relevance_score': score,
                        'match_type': self._determine_match_type(query_lower, entity)
                    })
            
            # Sort by relevance
            results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []
    
    async def infer_relationships(self, entity_id: str) -> List[Dict[str, Any]]:
        """Infer potential relationships for an entity."""
        try:
            if entity_id not in self.entities:
                raise ValueError(f"Entity {entity_id} not found")
            
            entity = self.entities[entity_id]
            inferences = []
            
            # Rule-based inference
            if entity.entity_type == EntityType.COMPONENT:
                # Components are part of processes
                process_entities = await self.query_entities(EntityType.PROCESS)
                for process in process_entities:
                    if self._should_infer_part_of(entity, process):
                        inferences.append({
                            'source_entity': entity,
                            'target_entity': process,
                            'relationship_type': RelationType.PART_OF,
                            'confidence': 0.7,
                            'reasoning': 'Component typically part of process'
                        })
            
            elif entity.entity_type == EntityType.CHEMICAL:
                # Chemicals are used by processes
                process_entities = await self.query_entities(EntityType.PROCESS)
                for process in process_entities:
                    if self._should_infer_uses(process, entity):
                        inferences.append({
                            'source_entity': process,
                            'target_entity': entity,
                            'relationship_type': RelationType.USES,
                            'confidence': 0.6,
                            'reasoning': 'Process likely uses chemical'
                        })
            
            return inferences
            
        except Exception as e:
            logger.error(f"Relationship inference failed: {e}")
            return []
    
    async def get_knowledge_summary(self) -> Dict[str, Any]:
        """Get summary of knowledge graph."""
        try:
            # Entity statistics
            entity_stats = {}
            for entity_type in EntityType:
                count = len(self.entity_index.get(entity_type, set()))
                entity_stats[entity_type.value] = count
            
            # Relationship statistics
            relationship_stats = {}
            for rel_type in RelationType:
                count = len(self.relationship_index.get(rel_type, set()))
                relationship_stats[rel_type.value] = count
            
            # Coverage analysis
            coverage = self._analyze_coverage()
            
            return {
                'total_entities': len(self.entities),
                'total_relationships': len(self.relationships),
                'entity_distribution': entity_stats,
                'relationship_distribution': relationship_stats,
                'coverage_analysis': coverage,
                'ontologies': list(self.ontologies.keys()),
                'last_updated': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Knowledge summary failed: {e}")
            return {}
    
    def _initialize_ontologies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize domain ontologies."""
        return {
            'water_treatment': {
                'description': 'Water treatment processes and components',
                'concepts': ['filtration', 'disinfection', 'coagulation', 'sedimentation'],
                'relationships': ['part_of', 'uses', 'produces']
            },
            'water_quality': {
                'description': 'Water quality parameters and standards',
                'concepts': ['turbidity', 'ph', 'bacteria', 'chemicals'],
                'relationships': ['affects', 'regulates', 'measures']
            },
            'sustainability': {
                'description': 'Sustainability and environmental concepts',
                'concepts': ['carbon_footprint', 'energy_efficiency', 'renewable_energy'],
                'relationships': ['reduces', 'improves', 'optimizes']
            },
            'regulations': {
                'description': 'Regulatory frameworks and standards',
                'concepts': ['epa_standards', 'who_guidelines', 'local_regulations'],
                'relationships': ['regulates', 'requires', 'enforces']
            }
        }
    
    def _populate_initial_knowledge(self):
        """Populate knowledge graph with initial domain knowledge."""
        try:
            # Water treatment components
            components = [
                ('Intake System', 'Water intake and preliminary screening'),
                ('Coagulation Tank', 'Chemical coagulation process'),
                ('Flocculation Basin', 'Particle flocculation'),
                ('Sedimentation Tank', 'Gravity settling of particles'),
                ('Sand Filter', 'Granular media filtration'),
                ('Chlorination System', 'Disinfection with chlorine'),
                ('Storage Tank', 'Treated water storage'),
                ('Distribution Pump', 'Water distribution system')
            ]
            
            component_entities = {}
            for name, description in components:
                entity = Entity(
                    entity_id=str(uuid.uuid4()),
                    name=name,
                    entity_type=EntityType.COMPONENT,
                    properties={'category': 'treatment_component'},
                    description=description,
                    aliases=[],
                    confidence=1.0,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.entities[entity.entity_id] = entity
                component_entities[name] = entity.entity_id
                
                # Update index
                if EntityType.COMPONENT not in self.entity_index:
                    self.entity_index[EntityType.COMPONENT] = set()
                self.entity_index[EntityType.COMPONENT].add(entity.entity_id)
            
            # Water treatment processes
            processes = [
                ('Primary Treatment', 'Physical removal of large particles'),
                ('Secondary Treatment', 'Biological and chemical treatment'),
                ('Tertiary Treatment', 'Advanced treatment and polishing'),
                ('Disinfection', 'Pathogen inactivation'),
                ('Distribution', 'Water delivery to consumers')
            ]
            
            process_entities = {}
            for name, description in processes:
                entity = Entity(
                    entity_id=str(uuid.uuid4()),
                    name=name,
                    entity_type=EntityType.PROCESS,
                    properties={'category': 'treatment_process'},
                    description=description,
                    aliases=[],
                    confidence=1.0,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.entities[entity.entity_id] = entity
                process_entities[name] = entity.entity_id
                
                # Update index
                if EntityType.PROCESS not in self.entity_index:
                    self.entity_index[EntityType.PROCESS] = set()
                self.entity_index[EntityType.PROCESS].add(entity.entity_id)
            
            # Water quality parameters
            parameters = [
                ('Turbidity', 'Water clarity measurement'),
                ('pH', 'Acidity/alkalinity level'),
                ('Chlorine Residual', 'Disinfectant concentration'),
                ('Total Dissolved Solids', 'Dissolved mineral content'),
                ('Bacteria Count', 'Microbial contamination level')
            ]
            
            for name, description in parameters:
                entity = Entity(
                    entity_id=str(uuid.uuid4()),
                    name=name,
                    entity_type=EntityType.PARAMETER,
                    properties={'category': 'water_quality'},
                    description=description,
                    aliases=[],
                    confidence=1.0,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.entities[entity.entity_id] = entity
                
                # Update index
                if EntityType.PARAMETER not in self.entity_index:
                    self.entity_index[EntityType.PARAMETER] = set()
                self.entity_index[EntityType.PARAMETER].add(entity.entity_id)
            
            # Chemicals
            chemicals = [
                ('Chlorine', 'Primary disinfectant'),
                ('Aluminum Sulfate', 'Coagulant chemical'),
                ('Polymer', 'Flocculation aid'),
                ('Lime', 'pH adjustment chemical'),
                ('Fluoride', 'Dental health additive')
            ]
            
            for name, description in chemicals:
                entity = Entity(
                    entity_id=str(uuid.uuid4()),
                    name=name,
                    entity_type=EntityType.CHEMICAL,
                    properties={'category': 'treatment_chemical'},
                    description=description,
                    aliases=[],
                    confidence=1.0,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.entities[entity.entity_id] = entity
                
                # Update index
                if EntityType.CHEMICAL not in self.entity_index:
                    self.entity_index[EntityType.CHEMICAL] = set()
                self.entity_index[EntityType.CHEMICAL].add(entity.entity_id)
            
            logger.info("Populated initial knowledge graph")
            
        except Exception as e:
            logger.error(f"Initial knowledge population failed: {e}")
    
    def _determine_match_type(self, query: str, entity: Entity) -> str:
        """Determine type of match for search result."""
        if query in entity.name.lower():
            return 'name_match'
        elif any(query in alias.lower() for alias in entity.aliases):
            return 'alias_match'
        elif query in entity.description.lower():
            return 'description_match'
        else:
            return 'property_match'
    
    def _should_infer_part_of(self, component: Entity, process: Entity) -> bool:
        """Determine if component should be inferred as part of process."""
        # Simple rule-based inference
        component_name = component.name.lower()
        process_name = process.name.lower()
        
        # Mapping rules
        if 'intake' in component_name and 'primary' in process_name:
            return True
        if 'coagulation' in component_name and 'secondary' in process_name:
            return True
        if 'filter' in component_name and ('secondary' in process_name or 'tertiary' in process_name):
            return True
        if 'chlorination' in component_name and 'disinfection' in process_name:
            return True
        
        return False
    
    def _should_infer_uses(self, process: Entity, chemical: Entity) -> bool:
        """Determine if process should be inferred to use chemical."""
        process_name = process.name.lower()
        chemical_name = chemical.name.lower()
        
        # Mapping rules
        if 'chlorine' in chemical_name and 'disinfection' in process_name:
            return True
        if 'aluminum' in chemical_name and 'secondary' in process_name:
            return True
        if 'polymer' in chemical_name and 'secondary' in process_name:
            return True
        
        return False
    
    def _analyze_coverage(self) -> Dict[str, Any]:
        """Analyze knowledge graph coverage."""
        coverage = {
            'completeness': 0.0,
            'connectivity': 0.0,
            'domain_coverage': {},
            'gaps': []
        }
        
        # Calculate completeness (entities vs expected)
        expected_entities = 100  # Expected for comprehensive water management
        actual_entities = len(self.entities)
        coverage['completeness'] = min(1.0, actual_entities / expected_entities)
        
        # Calculate connectivity (relationships vs entities)
        if actual_entities > 0:
            coverage['connectivity'] = len(self.relationships) / actual_entities
        
        # Domain coverage
        for ontology in self.ontologies:
            domain_entities = len([e for e in self.entities.values() 
                                 if ontology in e.properties.get('category', '')])
            coverage['domain_coverage'][ontology] = domain_entities
        
        # Identify gaps
        if coverage['completeness'] < 0.5:
            coverage['gaps'].append('Insufficient entity coverage')
        if coverage['connectivity'] < 1.0:
            coverage['gaps'].append('Low relationship density')
        
        return coverage


# Convenience functions
async def create_knowledge_graph() -> WaterManagementKnowledgeGraph:
    """Create water management knowledge graph."""
    kg = WaterManagementKnowledgeGraph()
    logger.info("Water management knowledge graph created")
    return kg


async def search_knowledge(query: str, entity_types: List[EntityType] = None) -> List[Dict[str, Any]]:
    """Search knowledge graph."""
    kg = await create_knowledge_graph()
    return await kg.semantic_search(query, entity_types)


class SemanticReasoningEngine:
    """Advanced semantic reasoning for water management knowledge."""

    def __init__(self, knowledge_graph: WaterManagementKnowledgeGraph):
        self.kg = knowledge_graph
        self.inference_rules = {}
        self.reasoning_cache = {}

        # Initialize reasoning rules
        self._initialize_reasoning_rules()

    def _initialize_reasoning_rules(self):
        """Initialize semantic reasoning rules."""
        self.inference_rules = {
            'water_quality_inference': {
                'if': ['ph < 6.5 OR ph > 8.5'],
                'then': ['water_quality = poor', 'treatment_required = true']
            },
            'efficiency_optimization': {
                'if': ['efficiency < 85%', 'energy_consumption > threshold'],
                'then': ['optimization_needed = true', 'priority = high']
            },
            'maintenance_prediction': {
                'if': ['operating_hours > 8000', 'performance_degradation > 10%'],
                'then': ['maintenance_required = true', 'urgency = high']
            },
            'climate_impact': {
                'if': ['temperature_increase > 2°C', 'precipitation_change > 20%'],
                'then': ['climate_risk = high', 'adaptation_required = true']
            }
        }

    @log_async_function_call
    async def perform_semantic_reasoning(self, query_context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform semantic reasoning on knowledge graph."""
        try:
            reasoning_results = {
                'inferences': [],
                'recommendations': [],
                'risk_assessments': [],
                'optimization_suggestions': []
            }

            # Apply inference rules
            for rule_name, rule in self.inference_rules.items():
                inference_result = await self._apply_inference_rule(rule, query_context)
                if inference_result['triggered']:
                    reasoning_results['inferences'].append({
                        'rule': rule_name,
                        'conditions_met': inference_result['conditions'],
                        'conclusions': inference_result['conclusions'],
                        'confidence': inference_result['confidence']
                    })

            # Generate recommendations based on inferences
            recommendations = await self._generate_recommendations(reasoning_results['inferences'])
            reasoning_results['recommendations'] = recommendations

            # Assess risks
            risk_assessment = await self._assess_risks(query_context, reasoning_results['inferences'])
            reasoning_results['risk_assessments'] = risk_assessment

            # Suggest optimizations
            optimizations = await self._suggest_optimizations(query_context, reasoning_results['inferences'])
            reasoning_results['optimization_suggestions'] = optimizations

            return {
                'status': 'success',
                'reasoning_results': reasoning_results,
                'query_context': query_context,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Semantic reasoning failed: {e}")
            return {'status': 'error', 'error': str(e)}

    async def _apply_inference_rule(self, rule: Dict[str, Any],
                                  context: Dict[str, Any]) -> Dict[str, Any]:
        """Apply a single inference rule."""
        conditions = rule.get('if', [])
        conclusions = rule.get('then', [])

        conditions_met = []
        confidence = 1.0

        for condition in conditions:
            condition_result = await self._evaluate_condition(condition, context)
            conditions_met.append(condition_result)
            confidence *= condition_result.get('confidence', 0.5)

        triggered = all(c.get('satisfied', False) for c in conditions_met)

        return {
            'triggered': triggered,
            'conditions': conditions_met,
            'conclusions': conclusions if triggered else [],
            'confidence': confidence if triggered else 0.0
        }

    async def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate a single condition."""
        # Simple condition evaluation (can be extended with more sophisticated parsing)
        condition_lower = condition.lower()

        # pH conditions
        if 'ph' in condition_lower:
            ph_value = context.get('ph', 7.0)
            if '<' in condition and '6.5' in condition:
                satisfied = ph_value < 6.5
            elif '>' in condition and '8.5' in condition:
                satisfied = ph_value > 8.5
            else:
                satisfied = False

            return {
                'condition': condition,
                'satisfied': satisfied,
                'confidence': 0.9,
                'value': ph_value
            }

        # Efficiency conditions
        elif 'efficiency' in condition_lower:
            efficiency = context.get('efficiency', 90.0)
            if '<' in condition and '85' in condition:
                satisfied = efficiency < 85.0
            else:
                satisfied = False

            return {
                'condition': condition,
                'satisfied': satisfied,
                'confidence': 0.85,
                'value': efficiency
            }

        # Operating hours conditions
        elif 'operating_hours' in condition_lower:
            hours = context.get('operating_hours', 0)
            if '>' in condition and '8000' in condition:
                satisfied = hours > 8000
            else:
                satisfied = False

            return {
                'condition': condition,
                'satisfied': satisfied,
                'confidence': 0.95,
                'value': hours
            }

        # Default case
        else:
            return {
                'condition': condition,
                'satisfied': False,
                'confidence': 0.5,
                'value': None
            }

    async def _generate_recommendations(self, inferences: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate recommendations based on inferences."""
        recommendations = []

        for inference in inferences:
            rule_name = inference['rule']
            conclusions = inference['conclusions']
            confidence = inference['confidence']

            if rule_name == 'water_quality_inference':
                recommendations.append({
                    'type': 'water_quality',
                    'action': 'Adjust pH levels immediately',
                    'priority': 'high',
                    'confidence': confidence,
                    'details': 'pH levels are outside acceptable range (6.5-8.5)'
                })

            elif rule_name == 'efficiency_optimization':
                recommendations.append({
                    'type': 'efficiency',
                    'action': 'Implement optimization protocols',
                    'priority': 'medium',
                    'confidence': confidence,
                    'details': 'System efficiency below target, optimization needed'
                })

            elif rule_name == 'maintenance_prediction':
                recommendations.append({
                    'type': 'maintenance',
                    'action': 'Schedule immediate maintenance',
                    'priority': 'high',
                    'confidence': confidence,
                    'details': 'Equipment showing signs of wear, maintenance required'
                })

            elif rule_name == 'climate_impact':
                recommendations.append({
                    'type': 'climate_adaptation',
                    'action': 'Implement climate adaptation measures',
                    'priority': 'medium',
                    'confidence': confidence,
                    'details': 'Climate change impacts detected, adaptation required'
                })

        return recommendations
