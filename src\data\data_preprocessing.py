"""Data Preprocessing Module for Water Management System."""

import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json

try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
    from sklearn.impute import SimpleImputer, KNNImputer
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.decomposition import PCA
except ImportError:
    # Fallback classes if scikit-learn not available
    class StandardScaler:
        def fit(self, X): return self
        def transform(self, X): return X
        def fit_transform(self, X): return X
    
    class MinMaxScaler:
        def fit(self, X): return self
        def transform(self, X): return X
        def fit_transform(self, X): return X
    
    class RobustScaler:
        def fit(self, X): return self
        def transform(self, X): return X
        def fit_transform(self, X): return X
    
    class SimpleImputer:
        def __init__(self, *args, **kwargs): pass
        def fit(self, X): return self
        def transform(self, X): return X
        def fit_transform(self, X): return X
    
    class KNNImputer:
        def __init__(self, *args, **kwargs): pass
        def fit(self, X): return self
        def transform(self, X): return X
        def fit_transform(self, X): return X
    
    class SelectKBest:
        def __init__(self, *args, **kwargs): pass
        def fit(self, X, y): return self
        def transform(self, X): return X
        def fit_transform(self, X, y): return X
    
    class PCA:
        def __init__(self, *args, **kwargs): pass
        def fit(self, X): return self
        def transform(self, X): return X
        def fit_transform(self, X): return X
    
    def f_regression(X, y):
        return np.random.random(X.shape[1]), np.random.random(X.shape[1])

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class DataPreprocessor:
    """Comprehensive data preprocessing for water management data."""
    
    def __init__(self):
        self.scalers = {}
        self.imputers = {}
        self.feature_selectors = {}
        self.dimensionality_reducers = {}
        self.preprocessing_history = []
        
        # Data quality thresholds
        self.quality_thresholds = {
            'missing_data_threshold': 0.3,  # 30% missing data threshold
            'outlier_threshold': 3.0,  # 3 standard deviations
            'correlation_threshold': 0.95  # High correlation threshold
        }
    
    @log_async_function_call
    async def preprocess_sensor_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess sensor data for analysis."""
        try:
            # Convert to DataFrame
            df = pd.DataFrame(raw_data)
            
            # Data quality assessment
            quality_report = await self._assess_data_quality(df)
            
            # Handle missing values
            df_imputed = await self._handle_missing_values(df)
            
            # Detect and handle outliers
            df_clean = await self._handle_outliers(df_imputed)
            
            # Feature engineering
            df_engineered = await self._engineer_features(df_clean)
            
            # Normalize/scale features
            df_scaled = await self._scale_features(df_engineered)
            
            # Feature selection
            df_selected = await self._select_features(df_scaled)
            
            # Create preprocessing summary
            preprocessing_summary = {
                'original_shape': df.shape,
                'final_shape': df_selected.shape,
                'quality_report': quality_report,
                'preprocessing_steps': [
                    'missing_value_imputation',
                    'outlier_detection',
                    'feature_engineering',
                    'feature_scaling',
                    'feature_selection'
                ]
            }
            
            return {
                'status': 'success',
                'preprocessed_data': df_selected.to_dict('records'),
                'preprocessing_summary': preprocessing_summary,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Sensor data preprocessing failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _assess_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Assess data quality metrics."""
        quality_report = {
            'total_records': len(df),
            'total_features': len(df.columns),
            'missing_data': {},
            'data_types': {},
            'outliers': {},
            'duplicates': df.duplicated().sum(),
            'overall_quality_score': 0.0
        }
        
        # Missing data analysis
        for column in df.columns:
            missing_count = df[column].isnull().sum()
            missing_percentage = missing_count / len(df)
            quality_report['missing_data'][column] = {
                'count': int(missing_count),
                'percentage': float(missing_percentage)
            }
        
        # Data types
        for column in df.columns:
            quality_report['data_types'][column] = str(df[column].dtype)
        
        # Outlier detection (for numeric columns)
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for column in numeric_columns:
            if df[column].notna().sum() > 0:
                mean_val = df[column].mean()
                std_val = df[column].std()
                outliers = df[
                    (df[column] < mean_val - 3 * std_val) | 
                    (df[column] > mean_val + 3 * std_val)
                ][column].count()
                
                quality_report['outliers'][column] = {
                    'count': int(outliers),
                    'percentage': float(outliers / len(df))
                }
        
        # Calculate overall quality score
        avg_missing = np.mean([v['percentage'] for v in quality_report['missing_data'].values()])
        avg_outliers = np.mean([v['percentage'] for v in quality_report['outliers'].values()]) if quality_report['outliers'] else 0
        duplicate_ratio = quality_report['duplicates'] / len(df)
        
        quality_score = 1.0 - (avg_missing + avg_outliers + duplicate_ratio) / 3
        quality_report['overall_quality_score'] = max(0.0, min(1.0, quality_score))
        
        return quality_report
    
    async def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in the dataset."""
        df_imputed = df.copy()
        
        # Separate numeric and categorical columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        categorical_columns = df.select_dtypes(include=['object']).columns
        
        # Impute numeric columns
        if len(numeric_columns) > 0:
            # Use KNN imputer for numeric data
            numeric_imputer = KNNImputer(n_neighbors=5)
            df_imputed[numeric_columns] = numeric_imputer.fit_transform(df[numeric_columns])
            self.imputers['numeric'] = numeric_imputer
        
        # Impute categorical columns
        if len(categorical_columns) > 0:
            # Use mode imputation for categorical data
            categorical_imputer = SimpleImputer(strategy='most_frequent')
            df_imputed[categorical_columns] = categorical_imputer.fit_transform(df[categorical_columns])
            self.imputers['categorical'] = categorical_imputer
        
        return df_imputed
    
    async def _handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect and handle outliers."""
        df_clean = df.copy()
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        outlier_info = {}
        
        for column in numeric_columns:
            if df[column].notna().sum() > 0:
                # Calculate IQR
                Q1 = df[column].quantile(0.25)
                Q3 = df[column].quantile(0.75)
                IQR = Q3 - Q1
                
                # Define outlier bounds
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # Count outliers
                outliers_mask = (df[column] < lower_bound) | (df[column] > upper_bound)
                outlier_count = outliers_mask.sum()
                
                if outlier_count > 0:
                    # Cap outliers instead of removing them
                    df_clean[column] = df[column].clip(lower=lower_bound, upper=upper_bound)
                    
                    outlier_info[column] = {
                        'count': int(outlier_count),
                        'lower_bound': float(lower_bound),
                        'upper_bound': float(upper_bound),
                        'treatment': 'capped'
                    }
        
        return df_clean
    
    async def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Engineer new features from existing data."""
        df_engineered = df.copy()
        
        # Time-based features if timestamp column exists
        if 'timestamp' in df.columns:
            df_engineered['timestamp'] = pd.to_datetime(df_engineered['timestamp'])
            df_engineered['hour'] = df_engineered['timestamp'].dt.hour
            df_engineered['day_of_week'] = df_engineered['timestamp'].dt.dayofweek
            df_engineered['month'] = df_engineered['timestamp'].dt.month
            df_engineered['is_weekend'] = (df_engineered['day_of_week'] >= 5).astype(int)
        
        # Water quality features
        if 'ph' in df.columns and 'temperature' in df.columns:
            # pH temperature interaction
            df_engineered['ph_temp_interaction'] = df_engineered['ph'] * df_engineered['temperature']
        
        if 'flow_rate' in df.columns and 'pressure' in df.columns:
            # Flow-pressure ratio
            df_engineered['flow_pressure_ratio'] = df_engineered['flow_rate'] / (df_engineered['pressure'] + 1e-6)
        
        # Efficiency ratios
        if 'input_flow' in df.columns and 'output_flow' in df.columns:
            df_engineered['flow_efficiency'] = df_engineered['output_flow'] / (df_engineered['input_flow'] + 1e-6)
        
        if 'energy_consumption' in df.columns and 'output_flow' in df.columns:
            df_engineered['energy_per_unit'] = df_engineered['energy_consumption'] / (df_engineered['output_flow'] + 1e-6)
        
        # Rolling averages for time series data
        numeric_columns = df_engineered.select_dtypes(include=[np.number]).columns
        for column in numeric_columns:
            if column not in ['hour', 'day_of_week', 'month', 'is_weekend']:
                # 3-period rolling average
                df_engineered[f'{column}_rolling_3'] = df_engineered[column].rolling(window=3, min_periods=1).mean()
                
                # Rate of change
                df_engineered[f'{column}_rate_change'] = df_engineered[column].pct_change().fillna(0)
        
        return df_engineered
    
    async def _scale_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Scale numerical features."""
        df_scaled = df.copy()
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) > 0:
            # Use RobustScaler to handle outliers better
            scaler = RobustScaler()
            df_scaled[numeric_columns] = scaler.fit_transform(df[numeric_columns])
            self.scalers['robust'] = scaler
        
        return df_scaled
    
    async def _select_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Select most relevant features."""
        # For now, return all features
        # In a real implementation, would use feature selection techniques
        return df
    
    @log_async_function_call
    async def preprocess_climate_data(self, climate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess climate data specifically."""
        try:
            # Convert to DataFrame
            df = pd.DataFrame(climate_data)
            
            # Climate-specific preprocessing
            df_processed = await self._process_climate_features(df)
            
            # Handle seasonal patterns
            df_seasonal = await self._extract_seasonal_features(df_processed)
            
            # Normalize climate variables
            df_normalized = await self._normalize_climate_variables(df_seasonal)
            
            return {
                'status': 'success',
                'preprocessed_climate_data': df_normalized.to_dict('records'),
                'climate_features': list(df_normalized.columns),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Climate data preprocessing failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _process_climate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process climate-specific features."""
        df_climate = df.copy()
        
        # Temperature features
        if 'temperature' in df.columns:
            df_climate['temp_celsius'] = df_climate['temperature']
            df_climate['temp_fahrenheit'] = df_climate['temperature'] * 9/5 + 32
            df_climate['temp_kelvin'] = df_climate['temperature'] + 273.15
        
        # Precipitation features
        if 'precipitation' in df.columns:
            df_climate['precip_mm'] = df_climate['precipitation']
            df_climate['precip_inches'] = df_climate['precipitation'] / 25.4
            df_climate['is_rainy_day'] = (df_climate['precipitation'] > 1.0).astype(int)
        
        # Humidity features
        if 'humidity' in df.columns:
            df_climate['humidity_percent'] = df_climate['humidity']
            df_climate['humidity_category'] = pd.cut(
                df_climate['humidity'], 
                bins=[0, 30, 60, 80, 100], 
                labels=['low', 'moderate', 'high', 'very_high']
            )
        
        # Wind features
        if 'wind_speed' in df.columns:
            df_climate['wind_speed_ms'] = df_climate['wind_speed']
            df_climate['wind_speed_kmh'] = df_climate['wind_speed'] * 3.6
            df_climate['wind_category'] = pd.cut(
                df_climate['wind_speed'],
                bins=[0, 5, 10, 15, float('inf')],
                labels=['calm', 'light', 'moderate', 'strong']
            )
        
        return df_climate
    
    async def _extract_seasonal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract seasonal patterns from climate data."""
        df_seasonal = df.copy()
        
        # If timestamp exists, extract seasonal information
        if 'timestamp' in df.columns:
            df_seasonal['timestamp'] = pd.to_datetime(df_seasonal['timestamp'])
            df_seasonal['month'] = df_seasonal['timestamp'].dt.month
            df_seasonal['season'] = df_seasonal['month'].map({
                12: 'winter', 1: 'winter', 2: 'winter',
                3: 'spring', 4: 'spring', 5: 'spring',
                6: 'summer', 7: 'summer', 8: 'summer',
                9: 'autumn', 10: 'autumn', 11: 'autumn'
            })
            
            # Cyclical encoding for month
            df_seasonal['month_sin'] = np.sin(2 * np.pi * df_seasonal['month'] / 12)
            df_seasonal['month_cos'] = np.cos(2 * np.pi * df_seasonal['month'] / 12)
        
        return df_seasonal
    
    async def _normalize_climate_variables(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize climate variables to standard ranges."""
        df_normalized = df.copy()
        
        # Climate-specific normalization
        climate_ranges = {
            'temperature': (-50, 50),  # Celsius
            'humidity': (0, 100),      # Percentage
            'pressure': (900, 1100),   # hPa
            'wind_speed': (0, 50)      # m/s
        }
        
        for variable, (min_val, max_val) in climate_ranges.items():
            if variable in df.columns:
                df_normalized[f'{variable}_normalized'] = (
                    (df[variable] - min_val) / (max_val - min_val)
                ).clip(0, 1)
        
        return df_normalized
    
    @log_async_function_call
    async def create_training_dataset(self, processed_data: Dict[str, Any], 
                                    target_variable: str) -> Dict[str, Any]:
        """Create training dataset for machine learning."""
        try:
            df = pd.DataFrame(processed_data)
            
            # Separate features and target
            if target_variable not in df.columns:
                return {'status': 'error', 'error': f'Target variable {target_variable} not found'}
            
            X = df.drop(columns=[target_variable])
            y = df[target_variable]
            
            # Remove non-numeric columns from features
            X_numeric = X.select_dtypes(include=[np.number])
            
            # Handle any remaining missing values
            X_clean = X_numeric.fillna(X_numeric.mean())
            y_clean = y.fillna(y.mean() if y.dtype in ['int64', 'float64'] else y.mode()[0])
            
            # Feature correlation analysis
            correlation_matrix = X_clean.corr()
            high_corr_pairs = []
            
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_value = correlation_matrix.iloc[i, j]
                    if abs(corr_value) > self.quality_thresholds['correlation_threshold']:
                        high_corr_pairs.append({
                            'feature1': correlation_matrix.columns[i],
                            'feature2': correlation_matrix.columns[j],
                            'correlation': float(corr_value)
                        })
            
            # Split data (simple split for demonstration)
            split_index = int(0.8 * len(X_clean))
            
            training_dataset = {
                'X_train': X_clean.iloc[:split_index].values.tolist(),
                'X_test': X_clean.iloc[split_index:].values.tolist(),
                'y_train': y_clean.iloc[:split_index].values.tolist(),
                'y_test': y_clean.iloc[split_index:].values.tolist(),
                'feature_names': X_clean.columns.tolist(),
                'target_name': target_variable,
                'dataset_info': {
                    'total_samples': len(df),
                    'training_samples': split_index,
                    'test_samples': len(X_clean) - split_index,
                    'feature_count': len(X_clean.columns),
                    'high_correlation_pairs': high_corr_pairs
                }
            }
            
            return {
                'status': 'success',
                'training_dataset': training_dataset,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Training dataset creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_preprocessing_summary(self) -> Dict[str, Any]:
        """Get summary of preprocessing operations."""
        return {
            'scalers_fitted': list(self.scalers.keys()),
            'imputers_fitted': list(self.imputers.keys()),
            'feature_selectors_fitted': list(self.feature_selectors.keys()),
            'preprocessing_history_count': len(self.preprocessing_history),
            'quality_thresholds': self.quality_thresholds
        }


# Convenience functions
async def preprocess_sensor_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """Preprocess sensor data."""
    preprocessor = DataPreprocessor()
    return await preprocessor.preprocess_sensor_data(raw_data)


async def preprocess_climate_data(climate_data: Dict[str, Any]) -> Dict[str, Any]:
    """Preprocess climate data."""
    preprocessor = DataPreprocessor()
    return await preprocessor.preprocess_climate_data(climate_data)


async def create_ml_dataset(processed_data: Dict[str, Any], target: str) -> Dict[str, Any]:
    """Create machine learning dataset."""
    preprocessor = DataPreprocessor()
    return await preprocessor.create_training_dataset(processed_data, target)
