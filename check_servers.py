#!/usr/bin/env python3
"""
Check server status and identify any errors
"""

import requests
import traceback
import time

def check_backend():
    """Check backend server status"""
    print("🔍 Checking Backend Server...")
    try:
        # Test health endpoint
        response = requests.get('http://localhost:8000/health', timeout=10)
        print(f"✅ Backend Health: {response.status_code}")
        if response.status_code == 200:
            print(f"   Response: {response.json()}")
        else:
            print(f"   Error Response: {response.text[:200]}")
        
        # Test dashboard endpoint
        response = requests.get('http://localhost:8000/api/dashboard', timeout=10)
        print(f"✅ Dashboard API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Data sections: {list(data.get('data', {}).keys())}")
        else:
            print(f"   Error Response: {response.text[:200]}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Backend server not accessible - Connection refused")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend server timeout")
        return False
    except Exception as e:
        print(f"❌ Backend error: {e}")
        traceback.print_exc()
        return False

def check_frontend():
    """Check frontend server status"""
    print("\n🔍 Checking Frontend Server...")
    try:
        # Test main page
        response = requests.get('http://localhost:3000', timeout=10)
        print(f"✅ Frontend Main: {response.status_code}")
        if response.status_code == 200:
            content = response.text
            if "Feature Control Center" in content:
                print("   ✅ Individual Feature Interfaces loaded")
            if "all_feature_configurations.js" in content:
                print("   ✅ Configuration file linked")
            print(f"   Content size: {len(content):,} bytes")
        else:
            print(f"   Error Response: {response.text[:200]}")
        
        # Test configuration file
        response = requests.get('http://localhost:3000/all_feature_configurations.js', timeout=10)
        print(f"✅ Configuration File: {response.status_code}")
        if response.status_code == 200:
            config_content = response.text
            feature_count = config_content.count('"name":')
            print(f"   Features configured: {feature_count}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Frontend server not accessible - Connection refused")
        return False
    except requests.exceptions.Timeout:
        print("❌ Frontend server timeout")
        return False
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        traceback.print_exc()
        return False

def check_integration():
    """Check frontend-backend integration"""
    print("\n🔍 Checking Integration...")
    try:
        # Test CORS by making request from frontend context
        response = requests.get('http://localhost:8000/api/dashboard', 
                              headers={'Origin': 'http://localhost:3000'}, 
                              timeout=10)
        print(f"✅ CORS Integration: {response.status_code}")
        
        # Check if response has CORS headers
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        if cors_headers:
            print("   ✅ CORS headers present")
            for header, value in cors_headers.items():
                print(f"      {header}: {value}")
        else:
            print("   ⚠️ No CORS headers found")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration error: {e}")
        return False

def main():
    """Main check function"""
    print("🔍 SERVER STATUS CHECK")
    print("=" * 50)
    
    backend_ok = check_backend()
    frontend_ok = check_frontend()
    integration_ok = check_integration()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"Backend Server: {'✅ OK' if backend_ok else '❌ ERROR'}")
    print(f"Frontend Server: {'✅ OK' if frontend_ok else '❌ ERROR'}")
    print(f"Integration: {'✅ OK' if integration_ok else '❌ ERROR'}")
    
    if backend_ok and frontend_ok and integration_ok:
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("🌐 Access your platform:")
        print("   Individual Features: http://localhost:3000")
        print("   Dashboard: http://localhost:3000/dashboard")
        print("   Backend API: http://localhost:8000")
        print("   API Docs: http://localhost:8000/docs")
    else:
        print("\n⚠️ ISSUES DETECTED - See details above")
        
        if not backend_ok:
            print("\n🔧 Backend Issues:")
            print("   • Check if uvicorn is running on port 8000")
            print("   • Check for Python import errors")
            print("   • Verify src.api.unified_api module exists")
            
        if not frontend_ok:
            print("\n🔧 Frontend Issues:")
            print("   • Check if frontend server is running on port 3000")
            print("   • Verify all_feature_configurations.js exists")
            print("   • Check for file serving errors")

if __name__ == "__main__":
    main()
