"""
Climate Projection Integration Module.

This module provides comprehensive climate projection integration capabilities,
including long-term climate projections, scenario analysis, uncertainty quantification,
and water treatment adaptation planning based on climate change projections.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
from scipy import stats
from scipy.stats import linregress
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class ClimateScenario:
    """Climate change scenario information."""
    scenario_id: str
    scenario_name: str  # 'RCP2.6', 'RCP4.5', 'RCP6.0', 'RCP8.5', 'SSP1-2.6', etc.
    description: str
    temperature_change: float  # °C change by 2100
    precipitation_change: float  # % change by 2100
    co2_concentration: float  # ppm by 2100
    confidence_level: str  # 'high', 'medium', 'low'


@dataclass
class ClimateProjection:
    """Climate projection data."""
    projection_id: str
    scenario: ClimateScenario
    target_year: int
    parameter: str  # 'temperature', 'precipitation', etc.
    projected_value: float
    uncertainty_range: Tuple[float, float]  # (lower, upper) bounds
    baseline_value: float
    change_from_baseline: float
    confidence: float


@dataclass
class ProjectionAnalysisResult:
    """Result of climate projection analysis."""
    location: str
    analysis_period: Dict[str, str]
    baseline_period: Dict[str, str]
    climate_scenarios: List[ClimateScenario]
    projections: Dict[str, List[ClimateProjection]]
    impact_assessment: Dict[str, Any]
    adaptation_recommendations: Dict[str, Any]
    uncertainty_analysis: Dict[str, Any]
    water_treatment_implications: Dict[str, Any]
    timestamp: datetime


class ClimateProjectionIntegrator:
    """
    Comprehensive climate projection integration system.
    
    Provides:
    - Climate scenario integration (RCP, SSP scenarios)
    - Long-term climate projections (2030, 2050, 2100)
    - Uncertainty quantification and ensemble analysis
    - Impact assessment for water treatment operations
    - Adaptation planning and recommendations
    - Downscaling of global projections to local scale
    - Multi-model ensemble processing
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Standard climate scenarios
        self.climate_scenarios = {
            'RCP2.6': ClimateScenario(
                scenario_id='RCP2.6',
                scenario_name='RCP2.6 (Paris Agreement)',
                description='Strong mitigation scenario, limiting warming to ~1.5°C',
                temperature_change=1.5,
                precipitation_change=2.0,
                co2_concentration=420,
                confidence_level='high'
            ),
            'RCP4.5': ClimateScenario(
                scenario_id='RCP4.5',
                scenario_name='RCP4.5 (Moderate)',
                description='Moderate mitigation scenario, ~2.5°C warming',
                temperature_change=2.5,
                precipitation_change=4.0,
                co2_concentration=540,
                confidence_level='high'
            ),
            'RCP6.0': ClimateScenario(
                scenario_id='RCP6.0',
                scenario_name='RCP6.0 (High)',
                description='High emissions scenario, ~3.0°C warming',
                temperature_change=3.0,
                precipitation_change=5.0,
                co2_concentration=670,
                confidence_level='medium'
            ),
            'RCP8.5': ClimateScenario(
                scenario_id='RCP8.5',
                scenario_name='RCP8.5 (Very High)',
                description='Very high emissions scenario, ~4.5°C warming',
                temperature_change=4.5,
                precipitation_change=7.0,
                co2_concentration=940,
                confidence_level='medium'
            )
        }
        
        # Projection time horizons
        self.projection_years = [2030, 2050, 2070, 2100]
        
        # Water treatment impact thresholds
        self.treatment_impact_thresholds = {
            'temperature': {
                'minor_impact': 1.0,      # °C change
                'moderate_impact': 2.0,   # °C change
                'major_impact': 3.0,      # °C change
                'critical_impact': 4.0    # °C change
            },
            'precipitation': {
                'minor_impact': 10.0,     # % change
                'moderate_impact': 20.0,  # % change
                'major_impact': 30.0,     # % change
                'critical_impact': 40.0   # % change
            }
        }
        
        # Uncertainty parameters
        self.uncertainty_factors = {
            'model_uncertainty': 0.15,      # 15% model uncertainty
            'scenario_uncertainty': 0.20,   # 20% scenario uncertainty
            'downscaling_uncertainty': 0.10, # 10% downscaling uncertainty
            'natural_variability': 0.25     # 25% natural variability
        }
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the climate projection integrator."""
        try:
            logger.info("Initializing Climate Projection Integrator...")
            self.is_initialized = True
            logger.info("Climate Projection Integrator initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize climate projection integrator: {e}")
            return False
    
    async def analyze_climate_projections(self, data: List[ProcessedClimateData], 
                                        location: str = None,
                                        scenarios: List[str] = None) -> ProjectionAnalysisResult:
        """Analyze climate projections for multiple scenarios."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not data:
                raise ValueError("No data provided for projection analysis")
            
            logger.info(f"Analyzing climate projections for {len(data)} data points")
            
            # Convert to DataFrame for analysis
            df = await self._prepare_projection_dataframe(data)
            
            if df.empty or len(df) < 365:  # Need at least 1 year for baseline
                raise ValueError("Insufficient data for projection analysis")
            
            # Determine location and scenarios
            analysis_location = location or self._extract_location(data)
            analysis_scenarios = scenarios or ['RCP2.6', 'RCP4.5', 'RCP8.5']
            
            # Calculate baseline climate
            baseline_climate = await self._calculate_baseline_climate(df)
            
            # Generate projections for each scenario
            projections = {}
            for scenario_id in analysis_scenarios:
                if scenario_id in self.climate_scenarios:
                    scenario_projections = await self._generate_scenario_projections(
                        baseline_climate, self.climate_scenarios[scenario_id]
                    )
                    projections[scenario_id] = scenario_projections
            
            # Assess climate change impacts
            impact_assessment = await self._assess_climate_impacts(projections, baseline_climate)
            
            # Generate adaptation recommendations
            adaptation_recommendations = await self._generate_adaptation_recommendations(
                projections, impact_assessment
            )
            
            # Quantify uncertainties
            uncertainty_analysis = await self._quantify_uncertainties(projections)
            
            # Assess water treatment implications
            treatment_implications = await self._assess_treatment_implications(
                projections, baseline_climate
            )
            
            # Create result
            result = ProjectionAnalysisResult(
                location=analysis_location,
                analysis_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'duration_years': (df.index.max() - df.index.min()).days / 365.25
                },
                baseline_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'description': 'Historical baseline period'
                },
                climate_scenarios=[self.climate_scenarios[s] for s in analysis_scenarios],
                projections=projections,
                impact_assessment=impact_assessment,
                adaptation_recommendations=adaptation_recommendations,
                uncertainty_analysis=uncertainty_analysis,
                water_treatment_implications=treatment_implications,
                timestamp=datetime.now()
            )
            
            logger.info(f"Climate projection analysis completed for {analysis_location}")
            return result
            
        except Exception as e:
            logger.error(f"Climate projection analysis failed: {e}")
            raise
    
    async def _prepare_projection_dataframe(self, data: List[ProcessedClimateData]) -> pd.DataFrame:
        """Prepare DataFrame from climate data for projection analysis."""
        try:
            records = []
            for item in data:
                record = {
                    'timestamp': item.timestamp,
                    'location': item.location,
                    'source': item.source,
                    'quality_score': getattr(item, 'data_quality_score', 1.0)
                }
                
                # Add available climate parameters
                if item.temperature is not None:
                    record['temperature'] = item.temperature
                if item.temperature_max is not None:
                    record['temperature_max'] = item.temperature_max
                if item.temperature_min is not None:
                    record['temperature_min'] = item.temperature_min
                if item.precipitation is not None:
                    record['precipitation'] = item.precipitation
                
                records.append(record)
            
            if not records:
                return pd.DataFrame()
            
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Handle duplicates
            df = df.groupby(df.index).mean(numeric_only=True)
            
            # Fill missing values
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if col == 'precipitation':
                    df[col] = df[col].fillna(0.0)
                else:
                    df[col] = df[col].interpolate(method='linear')
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to prepare projection DataFrame: {e}")
            return pd.DataFrame()
    
    def _extract_location(self, data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in data if item.location]
        if locations:
            return max(set(locations), key=locations.count)
        return "Unknown Location"
    
    async def _calculate_baseline_climate(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate baseline climate statistics."""
        try:
            baseline = {}
            
            # Temperature baseline
            if 'temperature' in df.columns:
                temp_data = df['temperature'].dropna()
                if len(temp_data) > 0:
                    baseline['temperature_mean'] = float(temp_data.mean())
                    baseline['temperature_std'] = float(temp_data.std())
                    baseline['temperature_min'] = float(temp_data.min())
                    baseline['temperature_max'] = float(temp_data.max())
            
            # Precipitation baseline
            if 'precipitation' in df.columns:
                precip_data = df['precipitation'].dropna()
                if len(precip_data) > 0:
                    baseline['precipitation_mean'] = float(precip_data.mean())
                    baseline['precipitation_std'] = float(precip_data.std())
                    baseline['precipitation_total'] = float(precip_data.sum())
                    baseline['precipitation_max'] = float(precip_data.max())
            
            # Seasonal baselines
            if 'temperature' in df.columns:
                df_temp = df.copy()
                df_temp['month'] = df_temp.index.month
                df_temp['season'] = df_temp['month'].map(self._get_season)
                
                seasonal_temp = df_temp.groupby('season')['temperature'].mean()
                for season, temp in seasonal_temp.items():
                    baseline[f'temperature_{season.lower()}'] = float(temp)
            
            return baseline
            
        except Exception as e:
            logger.error(f"Baseline climate calculation failed: {e}")
            return {}
    
    def _get_season(self, month: int) -> str:
        """Map month to season."""
        if month in [12, 1, 2]:
            return 'Winter'
        elif month in [3, 4, 5]:
            return 'Spring'
        elif month in [6, 7, 8]:
            return 'Summer'
        else:
            return 'Autumn'

    async def _generate_scenario_projections(self, baseline_climate: Dict[str, float],
                                           scenario: ClimateScenario) -> List[ClimateProjection]:
        """Generate climate projections for a specific scenario."""
        try:
            projections = []

            # Generate temperature projections
            if 'temperature_mean' in baseline_climate:
                temp_projections = await self._generate_temperature_projections(
                    baseline_climate, scenario
                )
                projections.extend(temp_projections)

            # Generate precipitation projections
            if 'precipitation_mean' in baseline_climate:
                precip_projections = await self._generate_precipitation_projections(
                    baseline_climate, scenario
                )
                projections.extend(precip_projections)

            return projections

        except Exception as e:
            logger.error(f"Scenario projection generation failed: {e}")
            return []

    async def _generate_temperature_projections(self, baseline_climate: Dict[str, float],
                                              scenario: ClimateScenario) -> List[ClimateProjection]:
        """Generate temperature projections for a scenario."""
        try:
            projections = []
            baseline_temp = baseline_climate['temperature_mean']
            total_change = scenario.temperature_change

            for target_year in self.projection_years:
                # Calculate progressive change (linear interpolation)
                years_from_now = target_year - datetime.now().year
                change_fraction = min(1.0, years_from_now / (2100 - datetime.now().year))
                projected_change = total_change * change_fraction

                projected_temp = baseline_temp + projected_change

                # Calculate uncertainty range
                uncertainty = self._calculate_temperature_uncertainty(projected_change, scenario)
                uncertainty_range = (
                    projected_temp - uncertainty,
                    projected_temp + uncertainty
                )

                # Calculate confidence
                confidence = self._calculate_projection_confidence(target_year, scenario)

                projection = ClimateProjection(
                    projection_id=f"temp_{scenario.scenario_id}_{target_year}",
                    scenario=scenario,
                    target_year=target_year,
                    parameter='temperature',
                    projected_value=float(projected_temp),
                    uncertainty_range=uncertainty_range,
                    baseline_value=float(baseline_temp),
                    change_from_baseline=float(projected_change),
                    confidence=confidence
                )
                projections.append(projection)

            return projections

        except Exception as e:
            logger.error(f"Temperature projection generation failed: {e}")
            return []

    async def _generate_precipitation_projections(self, baseline_climate: Dict[str, float],
                                                scenario: ClimateScenario) -> List[ClimateProjection]:
        """Generate precipitation projections for a scenario."""
        try:
            projections = []
            baseline_precip = baseline_climate['precipitation_mean']
            total_change_percent = scenario.precipitation_change

            for target_year in self.projection_years:
                # Calculate progressive change
                years_from_now = target_year - datetime.now().year
                change_fraction = min(1.0, years_from_now / (2100 - datetime.now().year))
                projected_change_percent = total_change_percent * change_fraction

                projected_precip = baseline_precip * (1 + projected_change_percent / 100)

                # Calculate uncertainty range
                uncertainty = self._calculate_precipitation_uncertainty(projected_change_percent, scenario)
                uncertainty_range = (
                    max(0, projected_precip - uncertainty),
                    projected_precip + uncertainty
                )

                # Calculate confidence
                confidence = self._calculate_projection_confidence(target_year, scenario)

                projection = ClimateProjection(
                    projection_id=f"precip_{scenario.scenario_id}_{target_year}",
                    scenario=scenario,
                    target_year=target_year,
                    parameter='precipitation',
                    projected_value=float(projected_precip),
                    uncertainty_range=uncertainty_range,
                    baseline_value=float(baseline_precip),
                    change_from_baseline=float(projected_precip - baseline_precip),
                    confidence=confidence
                )
                projections.append(projection)

            return projections

        except Exception as e:
            logger.error(f"Precipitation projection generation failed: {e}")
            return []

    def _calculate_temperature_uncertainty(self, projected_change: float, scenario: ClimateScenario) -> float:
        """Calculate temperature projection uncertainty."""
        try:
            # Base uncertainty increases with magnitude of change
            base_uncertainty = abs(projected_change) * 0.3  # 30% of change magnitude

            # Add scenario-specific uncertainty
            scenario_factor = {
                'high': 1.0,
                'medium': 1.2,
                'low': 1.5
            }.get(scenario.confidence_level, 1.3)

            # Combine uncertainty sources
            total_uncertainty = base_uncertainty * scenario_factor

            return float(total_uncertainty)

        except Exception as e:
            logger.warning(f"Temperature uncertainty calculation failed: {e}")
            return 1.0  # Default uncertainty

    def _calculate_precipitation_uncertainty(self, projected_change_percent: float, scenario: ClimateScenario) -> float:
        """Calculate precipitation projection uncertainty."""
        try:
            # Base uncertainty increases with magnitude of change
            base_uncertainty = abs(projected_change_percent) * 0.4  # 40% of change magnitude

            # Add scenario-specific uncertainty
            scenario_factor = {
                'high': 1.0,
                'medium': 1.3,
                'low': 1.6
            }.get(scenario.confidence_level, 1.4)

            # Precipitation has higher uncertainty than temperature
            total_uncertainty = base_uncertainty * scenario_factor * 1.2

            return float(total_uncertainty)

        except Exception as e:
            logger.warning(f"Precipitation uncertainty calculation failed: {e}")
            return 0.5  # Default uncertainty

    def _calculate_projection_confidence(self, target_year: int, scenario: ClimateScenario) -> float:
        """Calculate confidence in projection based on time horizon and scenario."""
        try:
            # Confidence decreases with time
            years_from_now = target_year - datetime.now().year
            time_factor = max(0.3, 1.0 - (years_from_now / 100))  # Decrease over 100 years

            # Scenario confidence
            scenario_confidence = {
                'high': 0.9,
                'medium': 0.7,
                'low': 0.5
            }.get(scenario.confidence_level, 0.6)

            # Combined confidence
            confidence = time_factor * scenario_confidence

            return float(max(0.1, min(1.0, confidence)))

        except Exception as e:
            logger.warning(f"Projection confidence calculation failed: {e}")
            return 0.5

    async def _assess_climate_impacts(self, projections: Dict[str, List[ClimateProjection]],
                                    baseline_climate: Dict[str, float]) -> Dict[str, Any]:
        """Assess climate change impacts based on projections."""
        try:
            impacts = {
                'temperature_impacts': {},
                'precipitation_impacts': {},
                'combined_impacts': {},
                'impact_timeline': {},
                'risk_assessment': {}
            }

            # Assess temperature impacts
            for scenario_id, scenario_projections in projections.items():
                temp_projections = [p for p in scenario_projections if p.parameter == 'temperature']

                if temp_projections:
                    temp_impacts = await self._assess_temperature_impacts(temp_projections)
                    impacts['temperature_impacts'][scenario_id] = temp_impacts

            # Assess precipitation impacts
            for scenario_id, scenario_projections in projections.items():
                precip_projections = [p for p in scenario_projections if p.parameter == 'precipitation']

                if precip_projections:
                    precip_impacts = await self._assess_precipitation_impacts(precip_projections)
                    impacts['precipitation_impacts'][scenario_id] = precip_impacts

            # Assess combined impacts
            impacts['combined_impacts'] = await self._assess_combined_impacts(projections)

            # Create impact timeline
            impacts['impact_timeline'] = await self._create_impact_timeline(projections)

            # Overall risk assessment
            impacts['risk_assessment'] = await self._assess_overall_risk(impacts)

            return impacts

        except Exception as e:
            logger.error(f"Climate impact assessment failed: {e}")
            return {}

    async def _assess_temperature_impacts(self, temp_projections: List[ClimateProjection]) -> Dict[str, Any]:
        """Assess temperature-specific impacts."""
        try:
            impacts = {}

            for projection in temp_projections:
                change = abs(projection.change_from_baseline)

                # Determine impact level
                if change >= self.treatment_impact_thresholds['temperature']['critical_impact']:
                    impact_level = 'critical'
                elif change >= self.treatment_impact_thresholds['temperature']['major_impact']:
                    impact_level = 'major'
                elif change >= self.treatment_impact_thresholds['temperature']['moderate_impact']:
                    impact_level = 'moderate'
                elif change >= self.treatment_impact_thresholds['temperature']['minor_impact']:
                    impact_level = 'minor'
                else:
                    impact_level = 'negligible'

                impacts[projection.target_year] = {
                    'impact_level': impact_level,
                    'temperature_change': projection.change_from_baseline,
                    'projected_temperature': projection.projected_value,
                    'confidence': projection.confidence,
                    'implications': self._get_temperature_implications(impact_level, projection.change_from_baseline)
                }

            return impacts

        except Exception as e:
            logger.error(f"Temperature impact assessment failed: {e}")
            return {}

    async def _assess_precipitation_impacts(self, precip_projections: List[ClimateProjection]) -> Dict[str, Any]:
        """Assess precipitation-specific impacts."""
        try:
            impacts = {}

            for projection in precip_projections:
                baseline = projection.baseline_value
                change_percent = (projection.change_from_baseline / baseline) * 100 if baseline > 0 else 0

                # Determine impact level
                if abs(change_percent) >= self.treatment_impact_thresholds['precipitation']['critical_impact']:
                    impact_level = 'critical'
                elif abs(change_percent) >= self.treatment_impact_thresholds['precipitation']['major_impact']:
                    impact_level = 'major'
                elif abs(change_percent) >= self.treatment_impact_thresholds['precipitation']['moderate_impact']:
                    impact_level = 'moderate'
                elif abs(change_percent) >= self.treatment_impact_thresholds['precipitation']['minor_impact']:
                    impact_level = 'minor'
                else:
                    impact_level = 'negligible'

                impacts[projection.target_year] = {
                    'impact_level': impact_level,
                    'precipitation_change_percent': change_percent,
                    'projected_precipitation': projection.projected_value,
                    'confidence': projection.confidence,
                    'implications': self._get_precipitation_implications(impact_level, change_percent)
                }

            return impacts

        except Exception as e:
            logger.error(f"Precipitation impact assessment failed: {e}")
            return {}

    def _get_temperature_implications(self, impact_level: str, change: float) -> List[str]:
        """Get implications of temperature change."""
        implications = []

        if impact_level in ['major', 'critical']:
            implications.extend([
                'Significant impact on treatment efficiency',
                'Need for enhanced cooling/heating systems',
                'Potential equipment stress and failures'
            ])

        if change > 0:  # Warming
            implications.extend([
                'Increased biological activity in treatment processes',
                'Higher energy costs for cooling',
                'Potential for algae growth in storage'
            ])
        else:  # Cooling
            implications.extend([
                'Reduced biological treatment efficiency',
                'Risk of freezing in pipes and equipment',
                'Higher energy costs for heating'
            ])

        return implications

    def _get_precipitation_implications(self, impact_level: str, change_percent: float) -> List[str]:
        """Get implications of precipitation change."""
        implications = []

        if impact_level in ['major', 'critical']:
            implications.extend([
                'Major changes to treatment capacity requirements',
                'Need for infrastructure modifications',
                'Significant operational adjustments required'
            ])

        if change_percent > 0:  # Increased precipitation
            implications.extend([
                'Higher treatment volumes and capacity needs',
                'Increased flood risk and overflow potential',
                'Need for enhanced drainage systems'
            ])
        else:  # Decreased precipitation
            implications.extend([
                'Reduced water availability for treatment',
                'Need for water conservation measures',
                'Potential for drought conditions'
            ])

        return implications

    async def _assess_combined_impacts(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Assess combined climate impacts across parameters."""
        try:
            combined_impacts = {}

            for scenario_id, scenario_projections in projections.items():
                scenario_impacts = {}

                for year in self.projection_years:
                    temp_proj = next((p for p in scenario_projections
                                    if p.parameter == 'temperature' and p.target_year == year), None)
                    precip_proj = next((p for p in scenario_projections
                                      if p.parameter == 'precipitation' and p.target_year == year), None)

                    if temp_proj and precip_proj:
                        # Calculate combined impact score
                        temp_impact = abs(temp_proj.change_from_baseline) / 4.0  # Normalize to 0-1
                        precip_impact = abs(precip_proj.change_from_baseline / precip_proj.baseline_value) / 0.4  # Normalize to 0-1

                        combined_score = (temp_impact + precip_impact) / 2

                        # Determine combined impact level
                        if combined_score >= 0.8:
                            impact_level = 'critical'
                        elif combined_score >= 0.6:
                            impact_level = 'major'
                        elif combined_score >= 0.4:
                            impact_level = 'moderate'
                        elif combined_score >= 0.2:
                            impact_level = 'minor'
                        else:
                            impact_level = 'negligible'

                        scenario_impacts[year] = {
                            'combined_impact_level': impact_level,
                            'combined_score': float(combined_score),
                            'temperature_component': float(temp_impact),
                            'precipitation_component': float(precip_impact)
                        }

                combined_impacts[scenario_id] = scenario_impacts

            return combined_impacts

        except Exception as e:
            logger.error(f"Combined impact assessment failed: {e}")
            return {}

    async def _create_impact_timeline(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Create timeline of climate impacts."""
        try:
            timeline = {}

            for year in self.projection_years:
                year_impacts = {}

                for scenario_id, scenario_projections in projections.items():
                    year_projections = [p for p in scenario_projections if p.target_year == year]

                    if year_projections:
                        # Calculate average confidence for the year
                        avg_confidence = np.mean([p.confidence for p in year_projections])

                        # Get temperature and precipitation changes
                        temp_change = next((p.change_from_baseline for p in year_projections
                                          if p.parameter == 'temperature'), 0)
                        precip_change = next((p.change_from_baseline for p in year_projections
                                            if p.parameter == 'precipitation'), 0)

                        year_impacts[scenario_id] = {
                            'temperature_change': float(temp_change),
                            'precipitation_change': float(precip_change),
                            'confidence': float(avg_confidence),
                            'years_from_now': year - datetime.now().year
                        }

                timeline[str(year)] = year_impacts

            return timeline

        except Exception as e:
            logger.error(f"Impact timeline creation failed: {e}")
            return {}

    async def _assess_overall_risk(self, impacts: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall climate risk."""
        try:
            risk_assessment = {
                'overall_risk_level': 'low',
                'primary_concerns': [],
                'risk_factors': [],
                'confidence_level': 'medium'
            }

            # Analyze temperature risks
            temp_impacts = impacts.get('temperature_impacts', {})
            high_temp_risk = any(
                any(impact.get('impact_level') in ['major', 'critical']
                    for impact in scenario_impacts.values())
                for scenario_impacts in temp_impacts.values()
            )

            # Analyze precipitation risks
            precip_impacts = impacts.get('precipitation_impacts', {})
            high_precip_risk = any(
                any(impact.get('impact_level') in ['major', 'critical']
                    for impact in scenario_impacts.values())
                for scenario_impacts in precip_impacts.values()
            )

            # Determine overall risk level
            if high_temp_risk and high_precip_risk:
                risk_assessment['overall_risk_level'] = 'critical'
                risk_assessment['primary_concerns'] = ['temperature_extremes', 'precipitation_changes']
            elif high_temp_risk or high_precip_risk:
                risk_assessment['overall_risk_level'] = 'high'
                if high_temp_risk:
                    risk_assessment['primary_concerns'].append('temperature_extremes')
                if high_precip_risk:
                    risk_assessment['primary_concerns'].append('precipitation_changes')
            else:
                # Check for moderate risks
                moderate_risks = any(
                    any(impact.get('impact_level') == 'moderate'
                        for impact in scenario_impacts.values())
                    for impacts_dict in [temp_impacts, precip_impacts]
                    for scenario_impacts in impacts_dict.values()
                )

                if moderate_risks:
                    risk_assessment['overall_risk_level'] = 'medium'

            return risk_assessment

        except Exception as e:
            logger.error(f"Overall risk assessment failed: {e}")
            return {'overall_risk_level': 'unknown', 'primary_concerns': [], 'risk_factors': []}

    async def _generate_adaptation_recommendations(self, projections: Dict[str, List[ClimateProjection]],
                                                 impact_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Generate climate adaptation recommendations."""
        try:
            recommendations = {
                'immediate_actions': [],
                'short_term_planning': [],
                'long_term_strategies': [],
                'infrastructure_adaptations': [],
                'operational_adjustments': []
            }

            # Analyze worst-case scenario
            worst_case_scenario = self._identify_worst_case_scenario(projections)

            # Generate temperature-based recommendations
            temp_recommendations = await self._generate_temperature_adaptations(projections)
            recommendations.update(temp_recommendations)

            # Generate precipitation-based recommendations
            precip_recommendations = await self._generate_precipitation_adaptations(projections)
            self._merge_recommendations(recommendations, precip_recommendations)

            # Add scenario-specific recommendations
            recommendations['scenario_specific'] = {
                'worst_case_scenario': worst_case_scenario,
                'recommended_planning_scenario': self._recommend_planning_scenario(projections),
                'adaptation_priorities': self._prioritize_adaptations(impact_assessment)
            }

            return recommendations

        except Exception as e:
            logger.error(f"Adaptation recommendations generation failed: {e}")
            return {}

    def _identify_worst_case_scenario(self, projections: Dict[str, List[ClimateProjection]]) -> str:
        """Identify the worst-case climate scenario."""
        try:
            scenario_scores = {}

            for scenario_id, scenario_projections in projections.items():
                # Calculate impact score for 2050 (mid-century)
                temp_2050 = next((p for p in scenario_projections
                                if p.parameter == 'temperature' and p.target_year == 2050), None)
                precip_2050 = next((p for p in scenario_projections
                                 if p.parameter == 'precipitation' and p.target_year == 2050), None)

                score = 0
                if temp_2050:
                    score += abs(temp_2050.change_from_baseline)
                if precip_2050:
                    score += abs(precip_2050.change_from_baseline / precip_2050.baseline_value) * 10

                scenario_scores[scenario_id] = score

            return max(scenario_scores, key=scenario_scores.get) if scenario_scores else 'RCP8.5'

        except Exception as e:
            logger.warning(f"Failed to identify worst-case scenario: {e}")
            return 'RCP8.5'

    def _recommend_planning_scenario(self, projections: Dict[str, List[ClimateProjection]]) -> str:
        """Recommend scenario for planning purposes."""
        # For water treatment planning, recommend moderate scenario (RCP4.5)
        # as it balances risk management with cost considerations
        return 'RCP4.5' if 'RCP4.5' in projections else list(projections.keys())[0]

    def _prioritize_adaptations(self, impact_assessment: Dict[str, Any]) -> List[str]:
        """Prioritize adaptation measures based on impact assessment."""
        priorities = []

        # Check for critical impacts
        risk_level = impact_assessment.get('risk_assessment', {}).get('overall_risk_level', 'low')

        if risk_level in ['critical', 'high']:
            priorities.extend([
                'emergency_response_planning',
                'infrastructure_resilience',
                'operational_flexibility'
            ])
        elif risk_level == 'medium':
            priorities.extend([
                'gradual_infrastructure_upgrades',
                'process_optimization',
                'monitoring_enhancement'
            ])
        else:
            priorities.extend([
                'monitoring_and_assessment',
                'planning_and_preparation'
            ])

        return priorities

    async def _generate_temperature_adaptations(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, List[str]]:
        """Generate temperature-specific adaptation recommendations."""
        try:
            adaptations = {
                'immediate_actions': [],
                'short_term_planning': [],
                'long_term_strategies': []
            }

            # Find maximum temperature change across scenarios
            max_temp_change = 0
            for scenario_projections in projections.values():
                for projection in scenario_projections:
                    if projection.parameter == 'temperature':
                        max_temp_change = max(max_temp_change, abs(projection.change_from_baseline))

            if max_temp_change > 3.0:  # Major temperature change
                adaptations['immediate_actions'].extend([
                    'Assess current cooling/heating capacity',
                    'Develop temperature management protocols'
                ])
                adaptations['long_term_strategies'].extend([
                    'Install advanced climate control systems',
                    'Design temperature-resilient infrastructure'
                ])
            elif max_temp_change > 1.5:  # Moderate temperature change
                adaptations['short_term_planning'].extend([
                    'Upgrade temperature monitoring systems',
                    'Optimize energy efficiency for climate control'
                ])

            return adaptations

        except Exception as e:
            logger.error(f"Temperature adaptation generation failed: {e}")
            return {'immediate_actions': [], 'short_term_planning': [], 'long_term_strategies': []}

    async def _generate_precipitation_adaptations(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, List[str]]:
        """Generate precipitation-specific adaptation recommendations."""
        try:
            adaptations = {
                'immediate_actions': [],
                'short_term_planning': [],
                'long_term_strategies': []
            }

            # Find maximum precipitation change across scenarios
            max_precip_change = 0
            for scenario_projections in projections.values():
                for projection in scenario_projections:
                    if projection.parameter == 'precipitation':
                        change_percent = abs(projection.change_from_baseline / projection.baseline_value) * 100
                        max_precip_change = max(max_precip_change, change_percent)

            if max_precip_change > 30:  # Major precipitation change
                adaptations['immediate_actions'].extend([
                    'Assess current treatment capacity limits',
                    'Develop water scarcity/excess protocols'
                ])
                adaptations['long_term_strategies'].extend([
                    'Expand treatment capacity infrastructure',
                    'Implement advanced water storage systems'
                ])
            elif max_precip_change > 15:  # Moderate precipitation change
                adaptations['short_term_planning'].extend([
                    'Optimize treatment process flexibility',
                    'Enhance water storage capabilities'
                ])

            return adaptations

        except Exception as e:
            logger.error(f"Precipitation adaptation generation failed: {e}")
            return {'immediate_actions': [], 'short_term_planning': [], 'long_term_strategies': []}

    def _merge_recommendations(self, base_recommendations: Dict[str, List[str]],
                             new_recommendations: Dict[str, List[str]]):
        """Merge recommendation dictionaries."""
        for key, values in new_recommendations.items():
            if key in base_recommendations:
                base_recommendations[key].extend(values)
            else:
                base_recommendations[key] = values

    async def _quantify_uncertainties(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Quantify uncertainties in climate projections."""
        try:
            uncertainties = {
                'projection_uncertainty': {},
                'scenario_uncertainty': {},
                'confidence_analysis': {},
                'uncertainty_sources': list(self.uncertainty_factors.keys())
            }

            # Calculate projection uncertainties
            for scenario_id, scenario_projections in projections.items():
                scenario_uncertainties = {}

                for projection in scenario_projections:
                    uncertainty_range = projection.uncertainty_range
                    uncertainty_magnitude = (uncertainty_range[1] - uncertainty_range[0]) / 2
                    relative_uncertainty = uncertainty_magnitude / abs(projection.projected_value) if projection.projected_value != 0 else 0

                    scenario_uncertainties[f"{projection.parameter}_{projection.target_year}"] = {
                        'absolute_uncertainty': float(uncertainty_magnitude),
                        'relative_uncertainty': float(relative_uncertainty),
                        'confidence': projection.confidence
                    }

                uncertainties['projection_uncertainty'][scenario_id] = scenario_uncertainties

            # Calculate scenario uncertainty (spread across scenarios)
            uncertainties['scenario_uncertainty'] = await self._calculate_scenario_uncertainty(projections)

            # Confidence analysis
            uncertainties['confidence_analysis'] = await self._analyze_projection_confidence(projections)

            return uncertainties

        except Exception as e:
            logger.error(f"Uncertainty quantification failed: {e}")
            return {}

    async def _calculate_scenario_uncertainty(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Calculate uncertainty across different scenarios."""
        try:
            scenario_uncertainty = {}

            for year in self.projection_years:
                year_uncertainty = {}

                # Temperature scenario spread
                temp_values = []
                for scenario_projections in projections.values():
                    temp_proj = next((p for p in scenario_projections
                                    if p.parameter == 'temperature' and p.target_year == year), None)
                    if temp_proj:
                        temp_values.append(temp_proj.projected_value)

                if len(temp_values) > 1:
                    temp_spread = max(temp_values) - min(temp_values)
                    year_uncertainty['temperature_scenario_spread'] = float(temp_spread)

                # Precipitation scenario spread
                precip_values = []
                for scenario_projections in projections.values():
                    precip_proj = next((p for p in scenario_projections
                                      if p.parameter == 'precipitation' and p.target_year == year), None)
                    if precip_proj:
                        precip_values.append(precip_proj.projected_value)

                if len(precip_values) > 1:
                    precip_spread = max(precip_values) - min(precip_values)
                    year_uncertainty['precipitation_scenario_spread'] = float(precip_spread)

                scenario_uncertainty[str(year)] = year_uncertainty

            return scenario_uncertainty

        except Exception as e:
            logger.error(f"Scenario uncertainty calculation failed: {e}")
            return {}

    async def _analyze_projection_confidence(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Analyze confidence in projections."""
        try:
            confidence_analysis = {}

            # Calculate average confidence by parameter and time horizon
            for parameter in ['temperature', 'precipitation']:
                param_confidence = {}

                for year in self.projection_years:
                    year_confidences = []

                    for scenario_projections in projections.values():
                        proj = next((p for p in scenario_projections
                                   if p.parameter == parameter and p.target_year == year), None)
                        if proj:
                            year_confidences.append(proj.confidence)

                    if year_confidences:
                        param_confidence[str(year)] = {
                            'mean_confidence': float(np.mean(year_confidences)),
                            'min_confidence': float(np.min(year_confidences)),
                            'max_confidence': float(np.max(year_confidences))
                        }

                confidence_analysis[parameter] = param_confidence

            return confidence_analysis

        except Exception as e:
            logger.error(f"Confidence analysis failed: {e}")
            return {}

    async def _assess_treatment_implications(self, projections: Dict[str, List[ClimateProjection]],
                                          baseline_climate: Dict[str, float]) -> Dict[str, Any]:
        """Assess water treatment implications of climate projections."""
        try:
            implications = {
                'capacity_requirements': {},
                'efficiency_impacts': {},
                'infrastructure_needs': {},
                'operational_changes': {},
                'cost_implications': {}
            }

            # Assess capacity requirements
            implications['capacity_requirements'] = await self._assess_capacity_requirements(projections)

            # Assess efficiency impacts
            implications['efficiency_impacts'] = await self._assess_efficiency_impacts(projections, baseline_climate)

            # Assess infrastructure needs
            implications['infrastructure_needs'] = await self._assess_infrastructure_needs(projections)

            # Assess operational changes
            implications['operational_changes'] = await self._assess_operational_changes(projections)

            # Assess cost implications
            implications['cost_implications'] = await self._assess_cost_implications(projections)

            return implications

        except Exception as e:
            logger.error(f"Treatment implications assessment failed: {e}")
            return {}

    async def _assess_capacity_requirements(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Assess treatment capacity requirements under climate change."""
        try:
            capacity_requirements = {}

            for scenario_id, scenario_projections in projections.items():
                scenario_capacity = {}

                for year in self.projection_years:
                    precip_proj = next((p for p in scenario_projections
                                      if p.parameter == 'precipitation' and p.target_year == year), None)

                    if precip_proj:
                        # Calculate capacity adjustment factor
                        change_percent = (precip_proj.change_from_baseline / precip_proj.baseline_value) * 100
                        capacity_factor = 1.0 + (change_percent / 100)  # Linear relationship

                        scenario_capacity[str(year)] = {
                            'capacity_adjustment_factor': float(capacity_factor),
                            'precipitation_change_percent': float(change_percent),
                            'recommended_capacity_buffer': float(max(1.2, capacity_factor * 1.1))  # 10% buffer
                        }

                capacity_requirements[scenario_id] = scenario_capacity

            return capacity_requirements

        except Exception as e:
            logger.error(f"Capacity requirements assessment failed: {e}")
            return {}

    async def _assess_efficiency_impacts(self, projections: Dict[str, List[ClimateProjection]],
                                       baseline_climate: Dict[str, float]) -> Dict[str, Any]:
        """Assess treatment efficiency impacts."""
        try:
            efficiency_impacts = {}

            baseline_temp = baseline_climate.get('temperature_mean', 20.0)
            optimal_temp_range = (15, 25)  # Optimal temperature range for treatment

            for scenario_id, scenario_projections in projections.items():
                scenario_efficiency = {}

                for year in self.projection_years:
                    temp_proj = next((p for p in scenario_projections
                                    if p.parameter == 'temperature' and p.target_year == year), None)

                    if temp_proj:
                        projected_temp = temp_proj.projected_value

                        # Calculate efficiency factor based on temperature
                        if optimal_temp_range[0] <= projected_temp <= optimal_temp_range[1]:
                            efficiency_factor = 1.0  # Optimal efficiency
                        else:
                            # Efficiency decreases with distance from optimal range
                            if projected_temp < optimal_temp_range[0]:
                                efficiency_factor = max(0.6, 1.0 - (optimal_temp_range[0] - projected_temp) * 0.05)
                            else:
                                efficiency_factor = max(0.6, 1.0 - (projected_temp - optimal_temp_range[1]) * 0.03)

                        scenario_efficiency[str(year)] = {
                            'efficiency_factor': float(efficiency_factor),
                            'projected_temperature': float(projected_temp),
                            'temperature_change': float(temp_proj.change_from_baseline),
                            'efficiency_impact': 'positive' if efficiency_factor > 1.0 else 'negative' if efficiency_factor < 1.0 else 'neutral'
                        }

                efficiency_impacts[scenario_id] = scenario_efficiency

            return efficiency_impacts

        except Exception as e:
            logger.error(f"Efficiency impacts assessment failed: {e}")
            return {}

    async def _assess_infrastructure_needs(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Assess infrastructure adaptation needs."""
        try:
            infrastructure_needs = {
                'cooling_systems': False,
                'heating_systems': False,
                'capacity_expansion': False,
                'flood_protection': False,
                'drought_resilience': False,
                'priority_upgrades': []
            }

            # Analyze temperature-related needs
            max_temp_change = 0
            min_temp_change = 0

            for scenario_projections in projections.values():
                for projection in scenario_projections:
                    if projection.parameter == 'temperature':
                        max_temp_change = max(max_temp_change, projection.change_from_baseline)
                        min_temp_change = min(min_temp_change, projection.change_from_baseline)

            if max_temp_change > 2.0:
                infrastructure_needs['cooling_systems'] = True
                infrastructure_needs['priority_upgrades'].append('enhanced_cooling')

            if min_temp_change < -1.0:
                infrastructure_needs['heating_systems'] = True
                infrastructure_needs['priority_upgrades'].append('freeze_protection')

            # Analyze precipitation-related needs
            max_precip_change = 0
            min_precip_change = 0

            for scenario_projections in projections.values():
                for projection in scenario_projections:
                    if projection.parameter == 'precipitation':
                        change_percent = (projection.change_from_baseline / projection.baseline_value) * 100
                        max_precip_change = max(max_precip_change, change_percent)
                        min_precip_change = min(min_precip_change, change_percent)

            if max_precip_change > 20:
                infrastructure_needs['capacity_expansion'] = True
                infrastructure_needs['flood_protection'] = True
                infrastructure_needs['priority_upgrades'].extend(['capacity_expansion', 'flood_protection'])

            if min_precip_change < -15:
                infrastructure_needs['drought_resilience'] = True
                infrastructure_needs['priority_upgrades'].append('water_storage')

            return infrastructure_needs

        except Exception as e:
            logger.error(f"Infrastructure needs assessment failed: {e}")
            return {}

    async def _assess_operational_changes(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Assess required operational changes."""
        try:
            operational_changes = {
                'process_adjustments': [],
                'monitoring_enhancements': [],
                'staff_training_needs': [],
                'emergency_procedures': []
            }

            # Determine changes based on projected impacts
            significant_temp_change = any(
                abs(p.change_from_baseline) > 2.0
                for projections_list in projections.values()
                for p in projections_list
                if p.parameter == 'temperature'
            )

            significant_precip_change = any(
                abs(p.change_from_baseline / p.baseline_value) > 0.2
                for projections_list in projections.values()
                for p in projections_list
                if p.parameter == 'precipitation'
            )

            if significant_temp_change:
                operational_changes['process_adjustments'].extend([
                    'Temperature-dependent chemical dosing',
                    'Seasonal process optimization',
                    'Enhanced temperature monitoring'
                ])
                operational_changes['staff_training_needs'].append('Climate-adaptive operations')

            if significant_precip_change:
                operational_changes['process_adjustments'].extend([
                    'Variable capacity operations',
                    'Water storage management',
                    'Overflow prevention protocols'
                ])
                operational_changes['emergency_procedures'].extend([
                    'Drought response protocols',
                    'Flood management procedures'
                ])

            return operational_changes

        except Exception as e:
            logger.error(f"Operational changes assessment failed: {e}")
            return {}

    async def _assess_cost_implications(self, projections: Dict[str, List[ClimateProjection]]) -> Dict[str, Any]:
        """Assess cost implications of climate adaptation."""
        try:
            cost_implications = {
                'capital_costs': {},
                'operational_costs': {},
                'risk_costs': {},
                'total_cost_estimate': 'medium'
            }

            # Simplified cost assessment based on required adaptations
            infrastructure_costs = 0
            operational_cost_change = 0

            # Temperature-related costs
            max_temp_change = max(
                (abs(p.change_from_baseline) for projections_list in projections.values()
                 for p in projections_list if p.parameter == 'temperature'),
                default=0
            )

            if max_temp_change > 3.0:
                infrastructure_costs += 3  # High cost
                operational_cost_change += 0.2  # 20% increase
            elif max_temp_change > 1.5:
                infrastructure_costs += 2  # Medium cost
                operational_cost_change += 0.1  # 10% increase

            # Precipitation-related costs
            max_precip_change = max(
                (abs(p.change_from_baseline / p.baseline_value) for projections_list in projections.values()
                 for p in projections_list if p.parameter == 'precipitation'),
                default=0
            )

            if max_precip_change > 0.3:
                infrastructure_costs += 3  # High cost
                operational_cost_change += 0.15  # 15% increase
            elif max_precip_change > 0.15:
                infrastructure_costs += 2  # Medium cost
                operational_cost_change += 0.08  # 8% increase

            # Categorize total costs
            if infrastructure_costs >= 5:
                cost_implications['total_cost_estimate'] = 'very_high'
            elif infrastructure_costs >= 3:
                cost_implications['total_cost_estimate'] = 'high'
            elif infrastructure_costs >= 2:
                cost_implications['total_cost_estimate'] = 'medium'
            else:
                cost_implications['total_cost_estimate'] = 'low'

            cost_implications['capital_costs'] = {
                'infrastructure_upgrade_level': infrastructure_costs,
                'estimated_relative_cost': cost_implications['total_cost_estimate']
            }

            cost_implications['operational_costs'] = {
                'annual_cost_change_percent': float(operational_cost_change * 100),
                'primary_cost_drivers': ['energy', 'maintenance', 'monitoring']
            }

            return cost_implications

        except Exception as e:
            logger.error(f"Cost implications assessment failed: {e}")
            return {}


# Convenience functions
async def analyze_climate_projections_for_location(data: List[ProcessedClimateData],
                                                 location: str = None,
                                                 scenarios: List[str] = None) -> ProjectionAnalysisResult:
    """Analyze climate projections for a specific location."""
    integrator = ClimateProjectionIntegrator()
    await integrator.initialize()

    return await integrator.analyze_climate_projections(data, location, scenarios)


async def get_climate_scenario_projections(data: List[ProcessedClimateData],
                                         scenario: str = 'RCP4.5',
                                         location: str = None) -> List[ClimateProjection]:
    """Get climate projections for a specific scenario."""
    integrator = ClimateProjectionIntegrator()
    await integrator.initialize()

    result = await integrator.analyze_climate_projections(data, location, [scenario])
    return result.projections.get(scenario, []) if result else []


async def assess_climate_adaptation_needs(data: List[ProcessedClimateData],
                                        location: str = None) -> Dict[str, Any]:
    """Assess climate adaptation needs for water treatment."""
    integrator = ClimateProjectionIntegrator()
    await integrator.initialize()

    result = await integrator.analyze_climate_projections(data, location)
    return result.adaptation_recommendations if result else {}
