"""Climate Data Collector - Unified interface for all climate data sources."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from src.data.openweathermap_api import OpenWeatherMapAPI
from src.data.nasa_climate_api import NASAClimateAPI
from src.data.world_bank_api import WorldBankAPI
from src.data.noaa_climate_api import NOAAClimateAPI
from src.data.ecmwf_api import ECMWFAPI
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


@dataclass
class ClimateDataPoint:
    """Unified climate data point."""
    timestamp: datetime
    location: Dict[str, float]  # lat, lon
    temperature: Optional[float] = None
    humidity: Optional[float] = None
    precipitation: Optional[float] = None
    wind_speed: Optional[float] = None
    pressure: Optional[float] = None
    source: str = ""
    metadata: Dict[str, Any] = None


class ClimateDataCollector:
    """Unified climate data collector from multiple sources."""
    
    def __init__(self):
        self.apis = {}
        self.data_cache = {}
        self.collection_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'last_collection': None
        }
        
        # Initialize APIs
        self._initialize_apis()
    
    def _initialize_apis(self):
        """Initialize all climate data APIs."""
        try:
            self.apis['openweather'] = OpenWeatherMapAPI()
            self.apis['nasa'] = NASAClimateAPI()
            self.apis['worldbank'] = WorldBankAPI()
            self.apis['noaa'] = NOAAClimateAPI()
            self.apis['ecmwf'] = ECMWFAPI()
            
            logger.info("Climate data APIs initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize climate APIs: {e}")
    
    @log_async_function_call
    async def collect_all_climate_data(self, location: Dict[str, float],
                                     start_date: datetime = None,
                                     end_date: datetime = None) -> Dict[str, Any]:
        """Collect climate data from all available sources."""
        try:
            if not start_date:
                start_date = datetime.now() - timedelta(days=7)
            if not end_date:
                end_date = datetime.now()
            
            results = {}
            
            # Collect from each API
            for source_name, api in self.apis.items():
                try:
                    logger.info(f"Collecting data from {source_name}")
                    
                    if source_name == 'openweather':
                        data = await self._collect_openweather_data(api, location)
                    elif source_name == 'nasa':
                        data = await self._collect_nasa_data(api, location, start_date, end_date)
                    elif source_name == 'worldbank':
                        data = await self._collect_worldbank_data(api, location)
                    elif source_name == 'noaa':
                        data = await self._collect_noaa_data(api, location, start_date, end_date)
                    elif source_name == 'ecmwf':
                        data = await self._collect_ecmwf_data(api, location)
                    else:
                        continue
                    
                    results[source_name] = data
                    self.collection_stats['successful_requests'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to collect data from {source_name}: {e}")
                    results[source_name] = {'error': str(e)}
                    self.collection_stats['failed_requests'] += 1
                
                self.collection_stats['total_requests'] += 1
            
            # Aggregate and normalize data
            aggregated_data = await self._aggregate_climate_data(results, location)
            
            self.collection_stats['last_collection'] = datetime.now()
            
            return {
                'status': 'success',
                'location': location,
                'collection_period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'raw_data': results,
                'aggregated_data': aggregated_data,
                'collection_stats': self.collection_stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Climate data collection failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _collect_openweather_data(self, api, location):
        """Collect data from OpenWeatherMap."""
        current_weather = await api.get_current_weather(location['lat'], location['lon'])
        forecast = await api.get_forecast(location['lat'], location['lon'])
        
        return {
            'current': current_weather,
            'forecast': forecast,
            'source': 'OpenWeatherMap'
        }
    
    async def _collect_nasa_data(self, api, location, start_date, end_date):
        """Collect data from NASA."""
        climate_data = await api.get_climate_data(
            location['lat'], location['lon'], start_date, end_date
        )
        
        return {
            'climate_data': climate_data,
            'source': 'NASA'
        }
    
    async def _collect_worldbank_data(self, api, location):
        """Collect data from World Bank."""
        # World Bank data is typically country-level
        country_data = await api.get_climate_indicators('USA')  # Default to USA
        
        return {
            'country_indicators': country_data,
            'source': 'World Bank'
        }
    
    async def _collect_noaa_data(self, api, location, start_date, end_date):
        """Collect data from NOAA."""
        weather_data = await api.get_weather_data(
            location['lat'], location['lon'], start_date, end_date
        )
        
        return {
            'weather_data': weather_data,
            'source': 'NOAA'
        }
    
    async def _collect_ecmwf_data(self, api, location):
        """Collect data from ECMWF."""
        forecast_data = await api.get_forecast_data(location['lat'], location['lon'])
        
        return {
            'forecast_data': forecast_data,
            'source': 'ECMWF'
        }
    
    async def _aggregate_climate_data(self, raw_data: Dict[str, Any], 
                                    location: Dict[str, float]) -> Dict[str, Any]:
        """Aggregate and normalize climate data from multiple sources."""
        aggregated = {
            'location': location,
            'current_conditions': {},
            'forecasts': [],
            'historical_trends': {},
            'data_quality': {},
            'consensus_metrics': {}
        }
        
        # Extract current conditions
        current_temps = []
        current_humidity = []
        current_pressure = []
        
        for source, data in raw_data.items():
            if 'error' in data:
                continue
            
            try:
                if source == 'openweather' and 'current' in data:
                    current = data['current'].get('data', {})
                    if 'temperature' in current:
                        current_temps.append(current['temperature'])
                    if 'humidity' in current:
                        current_humidity.append(current['humidity'])
                    if 'pressure' in current:
                        current_pressure.append(current['pressure'])
                
                # Add more source-specific extraction logic here
                
            except Exception as e:
                logger.warning(f"Failed to extract data from {source}: {e}")
        
        # Calculate consensus values
        if current_temps:
            aggregated['current_conditions']['temperature'] = {
                'value': sum(current_temps) / len(current_temps),
                'sources': len(current_temps),
                'range': [min(current_temps), max(current_temps)]
            }
        
        if current_humidity:
            aggregated['current_conditions']['humidity'] = {
                'value': sum(current_humidity) / len(current_humidity),
                'sources': len(current_humidity),
                'range': [min(current_humidity), max(current_humidity)]
            }
        
        if current_pressure:
            aggregated['current_conditions']['pressure'] = {
                'value': sum(current_pressure) / len(current_pressure),
                'sources': len(current_pressure),
                'range': [min(current_pressure), max(current_pressure)]
            }
        
        # Data quality assessment
        total_sources = len(self.apis)
        successful_sources = len([d for d in raw_data.values() if 'error' not in d])
        
        aggregated['data_quality'] = {
            'total_sources': total_sources,
            'successful_sources': successful_sources,
            'success_rate': successful_sources / total_sources if total_sources > 0 else 0,
            'reliability_score': self._calculate_reliability_score(raw_data)
        }
        
        return aggregated
    
    def _calculate_reliability_score(self, raw_data: Dict[str, Any]) -> float:
        """Calculate reliability score based on data consistency."""
        # Simple reliability calculation
        successful_sources = len([d for d in raw_data.values() if 'error' not in d])
        total_sources = len(raw_data)
        
        if total_sources == 0:
            return 0.0
        
        base_score = successful_sources / total_sources
        
        # Bonus for multiple successful sources
        if successful_sources >= 3:
            base_score += 0.1
        
        return min(1.0, base_score)
    
    @log_async_function_call
    async def get_real_time_conditions(self, location: Dict[str, float]) -> Dict[str, Any]:
        """Get real-time weather conditions."""
        try:
            # Use OpenWeatherMap for real-time data (fastest response)
            if 'openweather' in self.apis:
                api = self.apis['openweather']
                current_weather = await api.get_current_weather(location['lat'], location['lon'])
                
                return {
                    'status': 'success',
                    'location': location,
                    'current_conditions': current_weather,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {'status': 'error', 'error': 'No real-time API available'}
                
        except Exception as e:
            logger.error(f"Real-time conditions retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_climate_forecast(self, location: Dict[str, float], 
                                 days: int = 7) -> Dict[str, Any]:
        """Get climate forecast from multiple sources."""
        try:
            forecasts = {}
            
            # Collect forecasts from available APIs
            for source_name, api in self.apis.items():
                try:
                    if source_name == 'openweather':
                        forecast = await api.get_forecast(location['lat'], location['lon'])
                        forecasts[source_name] = forecast
                    elif source_name == 'ecmwf':
                        forecast = await api.get_forecast_data(location['lat'], location['lon'])
                        forecasts[source_name] = forecast
                    
                except Exception as e:
                    logger.warning(f"Failed to get forecast from {source_name}: {e}")
            
            # Aggregate forecasts
            aggregated_forecast = await self._aggregate_forecasts(forecasts, days)
            
            return {
                'status': 'success',
                'location': location,
                'forecast_period_days': days,
                'individual_forecasts': forecasts,
                'aggregated_forecast': aggregated_forecast,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Climate forecast retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _aggregate_forecasts(self, forecasts: Dict[str, Any], days: int) -> Dict[str, Any]:
        """Aggregate forecasts from multiple sources."""
        aggregated = {
            'daily_forecasts': [],
            'summary': {},
            'confidence_levels': {}
        }
        
        # Simple aggregation - in practice would be more sophisticated
        for day in range(days):
            day_forecast = {
                'date': (datetime.now() + timedelta(days=day)).date().isoformat(),
                'temperature': {'min': None, 'max': None, 'avg': None},
                'precipitation_probability': None,
                'conditions': 'unknown',
                'confidence': 0.5
            }
            
            aggregated['daily_forecasts'].append(day_forecast)
        
        return aggregated
    
    def get_collection_statistics(self) -> Dict[str, Any]:
        """Get climate data collection statistics."""
        return {
            'collection_stats': self.collection_stats,
            'available_apis': list(self.apis.keys()),
            'api_status': {
                name: 'active' if api else 'inactive'
                for name, api in self.apis.items()
            }
        }


# Convenience functions
async def collect_climate_data(lat: float, lon: float, days_back: int = 7) -> Dict[str, Any]:
    """Collect climate data for location."""
    collector = ClimateDataCollector()
    location = {'lat': lat, 'lon': lon}
    start_date = datetime.now() - timedelta(days=days_back)
    
    return await collector.collect_all_climate_data(location, start_date)


async def get_current_weather(lat: float, lon: float) -> Dict[str, Any]:
    """Get current weather conditions."""
    collector = ClimateDataCollector()
    location = {'lat': lat, 'lon': lon}
    
    return await collector.get_real_time_conditions(location)
