"""
Extreme Weather Event Detection Module.

This module provides comprehensive extreme weather event detection capabilities
for climate data, including severe storms, heat waves, cold snaps, droughts,
floods, and other extreme weather phenomena that could impact water treatment operations.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
from scipy import stats
from scipy.stats import linregress
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class ExtremeWeatherEvent:
    """Extreme weather event information."""
    event_id: str
    event_type: str  # 'heatwave', 'coldsnap', 'storm', 'drought', 'flood', 'wind'
    start_date: datetime
    end_date: datetime
    duration_hours: int
    severity: str  # 'minor', 'moderate', 'severe', 'extreme', 'catastrophic'
    intensity: float
    peak_value: float
    location: str
    confidence: float
    impact_assessment: Dict[str, Any]
    warning_level: str  # 'watch', 'advisory', 'warning', 'emergency'


@dataclass
class ExtremeWeatherAnalysisResult:
    """Result of extreme weather event analysis."""
    location: str
    analysis_period: Dict[str, str]
    detected_events: List[ExtremeWeatherEvent]
    event_statistics: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    early_warning_alerts: List[Dict[str, Any]]
    climate_extremes_summary: Dict[str, Any]
    water_treatment_impacts: Dict[str, Any]
    timestamp: datetime


class ExtremeWeatherDetector:
    """
    Comprehensive extreme weather event detection system.
    
    Provides:
    - Multi-parameter extreme weather detection
    - Heat wave and cold snap identification
    - Severe storm and wind event detection
    - Drought and flood event analysis
    - Early warning system with risk levels
    - Water treatment impact assessment
    - Climate change extreme event trends
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Extreme weather thresholds
        self.temperature_thresholds = {
            'heatwave': {
                'minor': 32.0,      # °C
                'moderate': 35.0,   # °C
                'severe': 38.0,     # °C
                'extreme': 42.0,    # °C
                'catastrophic': 45.0 # °C
            },
            'coldsnap': {
                'minor': -5.0,      # °C
                'moderate': -10.0,  # °C
                'severe': -15.0,    # °C
                'extreme': -20.0,   # °C
                'catastrophic': -25.0 # °C
            }
        }
        
        self.precipitation_thresholds = {
            'heavy_rain': {
                'minor': 25.0,      # mm/day
                'moderate': 50.0,   # mm/day
                'severe': 100.0,    # mm/day
                'extreme': 150.0,   # mm/day
                'catastrophic': 200.0 # mm/day
            },
            'drought': {
                'minor': 0.8,       # ratio of normal
                'moderate': 0.6,    # ratio of normal
                'severe': 0.4,      # ratio of normal
                'extreme': 0.2,     # ratio of normal
                'catastrophic': 0.1 # ratio of normal
            }
        }
        
        self.wind_thresholds = {
            'high_wind': {
                'minor': 15.0,      # m/s (54 km/h)
                'moderate': 20.0,   # m/s (72 km/h)
                'severe': 25.0,     # m/s (90 km/h)
                'extreme': 32.0,    # m/s (115 km/h)
                'catastrophic': 40.0 # m/s (144 km/h)
            }
        }
        
        # Water treatment impact thresholds
        self.treatment_impact_thresholds = {
            'temperature_operational': (-5.0, 45.0),    # °C - operational limits
            'precipitation_capacity': (0.0, 200.0),     # mm/day - treatment capacity
            'wind_infrastructure': 25.0,                # m/s - infrastructure risk
            'combined_stress_index': 0.7                # Combined impact threshold
        }
        
        # Event detection parameters
        self.min_event_duration = 6    # hours
        self.max_gap_hours = 12        # hours to merge events
        self.confidence_threshold = 0.6 # minimum confidence for event detection
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the extreme weather detector."""
        try:
            logger.info("Initializing Extreme Weather Detector...")
            self.is_initialized = True
            logger.info("Extreme Weather Detector initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize extreme weather detector: {e}")
            return False
    
    async def detect_extreme_weather_events(self, data: List[ProcessedClimateData], 
                                          location: str = None) -> ExtremeWeatherAnalysisResult:
        """Detect and analyze extreme weather events."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not data:
                raise ValueError("No data provided for analysis")
            
            logger.info(f"Detecting extreme weather events in {len(data)} data points")
            
            # Convert to DataFrame for analysis
            df = await self._prepare_weather_dataframe(data)
            
            if df.empty or len(df) < 10:
                raise ValueError("Insufficient data for extreme weather detection")
            
            # Determine location
            analysis_location = location or self._extract_location(data)
            
            # Detect different types of extreme events
            detected_events = []
            
            # Temperature extremes (heat waves and cold snaps)
            temp_events = await self._detect_temperature_extremes(df)
            detected_events.extend(temp_events)
            
            # Precipitation extremes (heavy rain and drought)
            precip_events = await self._detect_precipitation_extremes(df)
            detected_events.extend(precip_events)
            
            # Wind extremes
            wind_events = await self._detect_wind_extremes(df)
            detected_events.extend(wind_events)
            
            # Compound events (multiple parameters)
            compound_events = await self._detect_compound_events(df)
            detected_events.extend(compound_events)
            
            # Calculate event statistics
            event_stats = await self._calculate_event_statistics(detected_events)
            
            # Risk assessment
            risk_assessment = await self._assess_extreme_weather_risk(detected_events, df)
            
            # Early warning alerts
            early_warnings = await self._generate_early_warning_alerts(detected_events, df)
            
            # Climate extremes summary
            extremes_summary = await self._summarize_climate_extremes(df, detected_events)
            
            # Water treatment impacts
            treatment_impacts = await self._assess_water_treatment_impacts(detected_events, df)
            
            # Create result
            result = ExtremeWeatherAnalysisResult(
                location=analysis_location,
                analysis_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'duration_days': (df.index.max() - df.index.min()).days
                },
                detected_events=detected_events,
                event_statistics=event_stats,
                risk_assessment=risk_assessment,
                early_warning_alerts=early_warnings,
                climate_extremes_summary=extremes_summary,
                water_treatment_impacts=treatment_impacts,
                timestamp=datetime.now()
            )
            
            logger.info(f"Extreme weather detection completed for {analysis_location}")
            logger.info(f"Detected {len(detected_events)} extreme weather events")
            return result
            
        except Exception as e:
            logger.error(f"Extreme weather detection failed: {e}")
            raise
    
    async def _prepare_weather_dataframe(self, data: List[ProcessedClimateData]) -> pd.DataFrame:
        """Prepare DataFrame from climate data for extreme weather analysis."""
        try:
            records = []
            for item in data:
                record = {
                    'timestamp': item.timestamp,
                    'location': item.location,
                    'source': item.source,
                    'quality_score': getattr(item, 'data_quality_score', 1.0)
                }
                
                # Add available weather parameters
                if item.temperature is not None:
                    record['temperature'] = item.temperature
                if item.temperature_max is not None:
                    record['temperature_max'] = item.temperature_max
                if item.temperature_min is not None:
                    record['temperature_min'] = item.temperature_min
                if item.precipitation is not None:
                    record['precipitation'] = item.precipitation
                if hasattr(item, 'wind_speed') and item.wind_speed is not None:
                    record['wind_speed'] = item.wind_speed
                if hasattr(item, 'humidity') and item.humidity is not None:
                    record['humidity'] = item.humidity
                if hasattr(item, 'pressure') and item.pressure is not None:
                    record['pressure'] = item.pressure
                
                records.append(record)
            
            if not records:
                return pd.DataFrame()
            
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Handle duplicates
            df = df.groupby(df.index).mean(numeric_only=True)
            
            # Fill missing values with appropriate methods
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if col == 'precipitation':
                    df[col] = df[col].fillna(0.0)  # No rain
                else:
                    df[col] = df[col].interpolate(method='linear')
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to prepare weather DataFrame: {e}")
            return pd.DataFrame()
    
    def _extract_location(self, data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in data if item.location]
        if locations:
            return max(set(locations), key=locations.count)
        return "Unknown Location"
    
    async def _detect_temperature_extremes(self, df: pd.DataFrame) -> List[ExtremeWeatherEvent]:
        """Detect temperature extreme events (heat waves and cold snaps)."""
        try:
            events = []
            
            if 'temperature' not in df.columns and 'temperature_max' not in df.columns:
                return events
            
            # Use temperature_max if available, otherwise temperature
            temp_column = 'temperature_max' if 'temperature_max' in df.columns else 'temperature'
            temperatures = df[temp_column].dropna()
            
            if len(temperatures) < 10:
                return events
            
            # Calculate baseline statistics
            temp_mean = temperatures.mean()
            temp_std = temperatures.std()
            temp_95th = temperatures.quantile(0.95)
            temp_5th = temperatures.quantile(0.05)
            
            # Detect heat waves
            heatwave_events = await self._detect_consecutive_extremes(
                temperatures, 
                self.temperature_thresholds['heatwave'],
                'heatwave',
                temp_95th
            )
            events.extend(heatwave_events)
            
            # Detect cold snaps
            coldsnap_events = await self._detect_consecutive_extremes(
                temperatures,
                self.temperature_thresholds['coldsnap'],
                'coldsnap',
                temp_5th,
                reverse=True
            )
            events.extend(coldsnap_events)
            
            return events
            
        except Exception as e:
            logger.error(f"Temperature extreme detection failed: {e}")
            return []
    
    async def _detect_precipitation_extremes(self, df: pd.DataFrame) -> List[ExtremeWeatherEvent]:
        """Detect precipitation extreme events."""
        try:
            events = []
            
            if 'precipitation' not in df.columns:
                return events
            
            precipitation = df['precipitation'].dropna()
            
            if len(precipitation) < 10:
                return events
            
            # Detect heavy rain events
            heavy_rain_events = await self._detect_consecutive_extremes(
                precipitation,
                self.precipitation_thresholds['heavy_rain'],
                'heavy_rain',
                precipitation.quantile(0.95)
            )
            events.extend(heavy_rain_events)
            
            # Detect drought events (using rolling average)
            rolling_30d = precipitation.rolling(window=30, min_periods=15).mean()
            long_term_avg = precipitation.mean()
            
            if long_term_avg > 0:
                drought_ratio = rolling_30d / long_term_avg
                drought_events = await self._detect_drought_periods(drought_ratio, precipitation.index)
                events.extend(drought_events)
            
            return events
            
        except Exception as e:
            logger.error(f"Precipitation extreme detection failed: {e}")
            return []
    
    async def _detect_wind_extremes(self, df: pd.DataFrame) -> List[ExtremeWeatherEvent]:
        """Detect wind extreme events."""
        try:
            events = []
            
            if 'wind_speed' not in df.columns:
                return events
            
            wind_speed = df['wind_speed'].dropna()
            
            if len(wind_speed) < 10:
                return events
            
            # Detect high wind events
            wind_events = await self._detect_consecutive_extremes(
                wind_speed,
                self.wind_thresholds['high_wind'],
                'high_wind',
                wind_speed.quantile(0.95)
            )
            events.extend(wind_events)
            
            return events
            
        except Exception as e:
            logger.error(f"Wind extreme detection failed: {e}")
            return []

    async def _detect_consecutive_extremes(self, data_series: pd.Series, thresholds: Dict[str, float],
                                         event_type: str, baseline_threshold: float,
                                         reverse: bool = False) -> List[ExtremeWeatherEvent]:
        """Detect consecutive extreme values."""
        try:
            events = []
            current_event = None

            for timestamp, value in data_series.items():
                if pd.isna(value):
                    continue

                # Determine if this is an extreme value
                is_extreme = False
                severity = None

                if reverse:  # For cold snaps (lower is more extreme)
                    for sev, threshold in thresholds.items():
                        if value <= threshold:
                            is_extreme = True
                            severity = sev
                            break
                else:  # For heat waves, heavy rain, wind (higher is more extreme)
                    for sev in ['catastrophic', 'extreme', 'severe', 'moderate', 'minor']:
                        if value >= thresholds[sev]:
                            is_extreme = True
                            severity = sev
                            break

                if is_extreme:
                    if current_event is None:
                        # Start new event
                        current_event = {
                            'start_date': timestamp,
                            'end_date': timestamp,
                            'peak_value': value,
                            'severity': severity,
                            'values': [value],
                            'timestamps': [timestamp]
                        }
                    else:
                        # Continue current event
                        current_event['end_date'] = timestamp
                        current_event['values'].append(value)
                        current_event['timestamps'].append(timestamp)

                        # Update peak value and severity
                        if reverse:
                            if value < current_event['peak_value']:
                                current_event['peak_value'] = value
                        else:
                            if value > current_event['peak_value']:
                                current_event['peak_value'] = value

                        # Update severity to most severe
                        current_severity_rank = list(thresholds.keys()).index(current_event['severity'])
                        new_severity_rank = list(thresholds.keys()).index(severity)
                        if new_severity_rank > current_severity_rank:
                            current_event['severity'] = severity
                else:
                    if current_event is not None:
                        # End current event
                        duration_hours = (current_event['end_date'] - current_event['start_date']).total_seconds() / 3600

                        if duration_hours >= self.min_event_duration:
                            # Create event object
                            event = ExtremeWeatherEvent(
                                event_id=f"{event_type}_{current_event['start_date'].strftime('%Y%m%d_%H%M')}",
                                event_type=event_type,
                                start_date=current_event['start_date'],
                                end_date=current_event['end_date'],
                                duration_hours=int(duration_hours),
                                severity=current_event['severity'],
                                intensity=float(np.mean(current_event['values'])),
                                peak_value=float(current_event['peak_value']),
                                location="",  # Will be filled later
                                confidence=self._calculate_event_confidence(current_event, baseline_threshold),
                                impact_assessment={},  # Will be filled later
                                warning_level=self._determine_warning_level(current_event['severity'])
                            )
                            events.append(event)

                        current_event = None

            # Handle ongoing event at end of data
            if current_event is not None:
                duration_hours = (current_event['end_date'] - current_event['start_date']).total_seconds() / 3600

                if duration_hours >= self.min_event_duration:
                    event = ExtremeWeatherEvent(
                        event_id=f"{event_type}_{current_event['start_date'].strftime('%Y%m%d_%H%M')}",
                        event_type=event_type,
                        start_date=current_event['start_date'],
                        end_date=current_event['end_date'],
                        duration_hours=int(duration_hours),
                        severity=current_event['severity'],
                        intensity=float(np.mean(current_event['values'])),
                        peak_value=float(current_event['peak_value']),
                        location="",
                        confidence=self._calculate_event_confidence(current_event, baseline_threshold),
                        impact_assessment={},
                        warning_level=self._determine_warning_level(current_event['severity'])
                    )
                    events.append(event)

            return events

        except Exception as e:
            logger.error(f"Consecutive extreme detection failed: {e}")
            return []

    async def _detect_drought_periods(self, drought_ratio: pd.Series, timestamps: pd.DatetimeIndex) -> List[ExtremeWeatherEvent]:
        """Detect drought periods from precipitation ratio."""
        try:
            events = []
            current_drought = None

            for timestamp, ratio in drought_ratio.items():
                if pd.isna(ratio):
                    continue

                # Determine drought severity
                severity = None
                for sev, threshold in self.precipitation_thresholds['drought'].items():
                    if ratio <= threshold:
                        severity = sev
                        break

                if severity:
                    if current_drought is None:
                        # Start new drought
                        current_drought = {
                            'start_date': timestamp,
                            'end_date': timestamp,
                            'min_ratio': ratio,
                            'severity': severity,
                            'ratios': [ratio]
                        }
                    else:
                        # Continue drought
                        current_drought['end_date'] = timestamp
                        current_drought['ratios'].append(ratio)
                        current_drought['min_ratio'] = min(current_drought['min_ratio'], ratio)

                        # Update severity to most severe
                        drought_thresholds = self.precipitation_thresholds['drought']
                        current_severity_rank = list(drought_thresholds.keys()).index(current_drought['severity'])
                        new_severity_rank = list(drought_thresholds.keys()).index(severity)
                        if new_severity_rank > current_severity_rank:
                            current_drought['severity'] = severity
                else:
                    if current_drought is not None:
                        # End drought
                        duration_hours = (current_drought['end_date'] - current_drought['start_date']).total_seconds() / 3600

                        if duration_hours >= 24 * 7:  # Minimum 7 days for drought
                            event = ExtremeWeatherEvent(
                                event_id=f"drought_{current_drought['start_date'].strftime('%Y%m%d')}",
                                event_type='drought',
                                start_date=current_drought['start_date'],
                                end_date=current_drought['end_date'],
                                duration_hours=int(duration_hours),
                                severity=current_drought['severity'],
                                intensity=float(np.mean(current_drought['ratios'])),
                                peak_value=float(current_drought['min_ratio']),
                                location="",
                                confidence=0.8,  # High confidence for drought detection
                                impact_assessment={},
                                warning_level=self._determine_warning_level(current_drought['severity'])
                            )
                            events.append(event)

                        current_drought = None

            return events

        except Exception as e:
            logger.error(f"Drought period detection failed: {e}")
            return []

    def _calculate_event_confidence(self, event_data: Dict[str, Any], baseline_threshold: float) -> float:
        """Calculate confidence score for detected event."""
        try:
            values = event_data['values']
            peak_value = event_data['peak_value']
            duration_hours = (event_data['end_date'] - event_data['start_date']).total_seconds() / 3600

            # Factors affecting confidence
            intensity_factor = min(1.0, abs(peak_value - baseline_threshold) / baseline_threshold) if baseline_threshold != 0 else 0.5
            duration_factor = min(1.0, duration_hours / 24)  # Normalize to 24 hours
            consistency_factor = 1.0 - (np.std(values) / np.mean(values)) if np.mean(values) != 0 else 0.5

            # Combined confidence score
            confidence = (intensity_factor * 0.4 + duration_factor * 0.3 + consistency_factor * 0.3)

            return max(0.0, min(1.0, confidence))

        except Exception as e:
            logger.warning(f"Failed to calculate event confidence: {e}")
            return 0.5

    def _determine_warning_level(self, severity: str) -> str:
        """Determine warning level based on event severity."""
        warning_map = {
            'minor': 'watch',
            'moderate': 'advisory',
            'severe': 'warning',
            'extreme': 'warning',
            'catastrophic': 'emergency'
        }
        return warning_map.get(severity, 'watch')

    async def _detect_compound_events(self, df: pd.DataFrame) -> List[ExtremeWeatherEvent]:
        """Detect compound extreme weather events (multiple parameters)."""
        try:
            events = []

            # For now, return empty list - compound events are complex
            # and would require more sophisticated analysis
            return events

        except Exception as e:
            logger.error(f"Compound event detection failed: {e}")
            return []

    async def _calculate_event_statistics(self, events: List[ExtremeWeatherEvent]) -> Dict[str, Any]:
        """Calculate statistics for detected extreme weather events."""
        try:
            if not events:
                return {}

            # Count events by type
            event_counts = {}
            for event in events:
                event_type = event.event_type
                if event_type not in event_counts:
                    event_counts[event_type] = 0
                event_counts[event_type] += 1

            # Count events by severity
            severity_counts = {}
            for event in events:
                severity = event.severity
                if severity not in severity_counts:
                    severity_counts[severity] = 0
                severity_counts[severity] += 1

            # Calculate duration statistics
            durations = [event.duration_hours for event in events]
            intensities = [event.intensity for event in events]

            stats = {
                'total_events': len(events),
                'events_by_type': event_counts,
                'events_by_severity': severity_counts,
                'duration_statistics': {
                    'mean_duration_hours': float(np.mean(durations)),
                    'max_duration_hours': float(np.max(durations)),
                    'min_duration_hours': float(np.min(durations))
                } if durations else {},
                'intensity_statistics': {
                    'mean_intensity': float(np.mean(intensities)),
                    'max_intensity': float(np.max(intensities)),
                    'min_intensity': float(np.min(intensities))
                } if intensities else {},
                'most_severe_event': max(events, key=lambda e: ['minor', 'moderate', 'severe', 'extreme', 'catastrophic'].index(e.severity)).event_id if events else None
            }

            return stats

        except Exception as e:
            logger.error(f"Event statistics calculation failed: {e}")
            return {}

    async def _assess_extreme_weather_risk(self, events: List[ExtremeWeatherEvent], df: pd.DataFrame) -> Dict[str, Any]:
        """Assess overall extreme weather risk."""
        try:
            risk_assessment = {
                'overall_risk_level': 'low',
                'risk_factors': [],
                'high_risk_events': [],
                'recommendations': []
            }

            if not events:
                return risk_assessment

            # Count severe events
            severe_events = [e for e in events if e.severity in ['severe', 'extreme', 'catastrophic']]
            recent_events = [e for e in events if (datetime.now() - e.end_date).days <= 30]

            # Assess risk level
            if len(severe_events) > 3:
                risk_assessment['overall_risk_level'] = 'high'
                risk_assessment['risk_factors'].append('multiple_severe_events')
            elif len(severe_events) > 1:
                risk_assessment['overall_risk_level'] = 'medium'
                risk_assessment['risk_factors'].append('some_severe_events')

            if len(recent_events) > 2:
                risk_assessment['risk_factors'].append('recent_extreme_activity')
                risk_assessment['overall_risk_level'] = max(risk_assessment['overall_risk_level'], 'medium')

            # Identify high-risk events
            risk_assessment['high_risk_events'] = [
                {
                    'event_id': event.event_id,
                    'event_type': event.event_type,
                    'severity': event.severity,
                    'warning_level': event.warning_level
                }
                for event in severe_events
            ]

            # Generate recommendations
            if risk_assessment['overall_risk_level'] == 'high':
                risk_assessment['recommendations'].extend([
                    'Activate emergency response protocols',
                    'Monitor weather conditions continuously',
                    'Prepare backup systems and resources',
                    'Review and update emergency procedures'
                ])
            elif risk_assessment['overall_risk_level'] == 'medium':
                risk_assessment['recommendations'].extend([
                    'Increase monitoring frequency',
                    'Prepare contingency plans',
                    'Check emergency equipment readiness'
                ])

            return risk_assessment

        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'overall_risk_level': 'unknown', 'risk_factors': [], 'recommendations': []}

    async def _generate_early_warning_alerts(self, events: List[ExtremeWeatherEvent], df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate early warning alerts for extreme weather events."""
        try:
            alerts = []

            # Generate alerts for ongoing or recent events
            current_time = datetime.now()

            for event in events:
                # Check if event is recent or ongoing
                time_since_end = (current_time - event.end_date).total_seconds() / 3600

                if time_since_end <= 24:  # Within last 24 hours
                    alert_level = 'active' if time_since_end <= 6 else 'recent'

                    alert = {
                        'alert_id': f"alert_{event.event_id}",
                        'event_type': event.event_type,
                        'severity': event.severity,
                        'warning_level': event.warning_level,
                        'alert_level': alert_level,
                        'message': self._generate_alert_message(event),
                        'issued_time': current_time.isoformat(),
                        'event_start': event.start_date.isoformat(),
                        'event_end': event.end_date.isoformat(),
                        'confidence': event.confidence
                    }
                    alerts.append(alert)

            return alerts

        except Exception as e:
            logger.error(f"Early warning alert generation failed: {e}")
            return []

    def _generate_alert_message(self, event: ExtremeWeatherEvent) -> str:
        """Generate alert message for extreme weather event."""
        try:
            event_names = {
                'heatwave': 'Heat Wave',
                'coldsnap': 'Cold Snap',
                'heavy_rain': 'Heavy Rainfall',
                'drought': 'Drought Conditions',
                'high_wind': 'High Wind Event'
            }

            event_name = event_names.get(event.event_type, event.event_type.title())

            message = f"{event.warning_level.upper()}: {event_name} - {event.severity.title()} severity. "

            if event.event_type == 'heatwave':
                message += f"Peak temperature: {event.peak_value:.1f}°C. "
            elif event.event_type == 'coldsnap':
                message += f"Minimum temperature: {event.peak_value:.1f}°C. "
            elif event.event_type == 'heavy_rain':
                message += f"Peak rainfall: {event.peak_value:.1f}mm. "
            elif event.event_type == 'high_wind':
                message += f"Peak wind speed: {event.peak_value:.1f}m/s. "

            message += f"Duration: {event.duration_hours} hours. Take appropriate precautions."

            return message

        except Exception as e:
            logger.warning(f"Failed to generate alert message: {e}")
            return f"Extreme weather event detected: {event.event_type}"

    async def _summarize_climate_extremes(self, df: pd.DataFrame, events: List[ExtremeWeatherEvent]) -> Dict[str, Any]:
        """Summarize climate extremes from the analysis period."""
        try:
            summary = {}

            # Temperature extremes
            if 'temperature' in df.columns:
                temp_data = df['temperature'].dropna()
                if len(temp_data) > 0:
                    summary['temperature_extremes'] = {
                        'absolute_maximum': float(temp_data.max()),
                        'absolute_minimum': float(temp_data.min()),
                        'temperature_range': float(temp_data.max() - temp_data.min()),
                        'days_above_35C': int((temp_data > 35).sum()),
                        'days_below_0C': int((temp_data < 0).sum())
                    }

            # Precipitation extremes
            if 'precipitation' in df.columns:
                precip_data = df['precipitation'].dropna()
                if len(precip_data) > 0:
                    summary['precipitation_extremes'] = {
                        'maximum_daily': float(precip_data.max()),
                        'total_precipitation': float(precip_data.sum()),
                        'days_no_rain': int((precip_data == 0).sum()),
                        'days_heavy_rain': int((precip_data > 25).sum()),
                        'wettest_month': self._find_wettest_month(df)
                    }

            # Event summary
            summary['extreme_events_summary'] = {
                'total_extreme_events': len(events),
                'most_common_event_type': self._find_most_common_event_type(events),
                'highest_severity_reached': self._find_highest_severity(events),
                'longest_event_duration': max([e.duration_hours for e in events], default=0)
            }

            return summary

        except Exception as e:
            logger.error(f"Climate extremes summary failed: {e}")
            return {}

    def _find_wettest_month(self, df: pd.DataFrame) -> str:
        """Find the wettest month in the data."""
        try:
            if 'precipitation' not in df.columns:
                return 'unknown'

            monthly_precip = df['precipitation'].groupby(df.index.month).sum()
            wettest_month_num = monthly_precip.idxmax()

            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            return month_names[wettest_month_num - 1]

        except Exception as e:
            logger.warning(f"Failed to find wettest month: {e}")
            return 'unknown'

    def _find_most_common_event_type(self, events: List[ExtremeWeatherEvent]) -> str:
        """Find the most common extreme event type."""
        try:
            if not events:
                return 'none'

            event_counts = {}
            for event in events:
                event_type = event.event_type
                event_counts[event_type] = event_counts.get(event_type, 0) + 1

            return max(event_counts, key=event_counts.get)

        except Exception as e:
            logger.warning(f"Failed to find most common event type: {e}")
            return 'unknown'

    def _find_highest_severity(self, events: List[ExtremeWeatherEvent]) -> str:
        """Find the highest severity level reached."""
        try:
            if not events:
                return 'none'

            severity_levels = ['minor', 'moderate', 'severe', 'extreme', 'catastrophic']
            max_severity_index = max([severity_levels.index(event.severity) for event in events])

            return severity_levels[max_severity_index]

        except Exception as e:
            logger.warning(f"Failed to find highest severity: {e}")
            return 'unknown'

    async def _assess_water_treatment_impacts(self, events: List[ExtremeWeatherEvent], df: pd.DataFrame) -> Dict[str, Any]:
        """Assess impacts of extreme weather events on water treatment operations."""
        try:
            impacts = {
                'operational_disruptions': [],
                'infrastructure_risks': [],
                'treatment_efficiency_impacts': [],
                'emergency_response_needed': [],
                'overall_impact_level': 'low'
            }

            if not events:
                return impacts

            high_impact_events = 0

            for event in events:
                impact_assessment = {}

                if event.event_type == 'heatwave':
                    if event.peak_value > 40:
                        impact_assessment = {
                            'impact_type': 'temperature_stress',
                            'description': 'High temperatures may reduce treatment efficiency',
                            'severity': 'high' if event.peak_value > 45 else 'medium',
                            'recommendations': ['Implement cooling systems', 'Monitor chemical reaction rates']
                        }
                        high_impact_events += 1

                elif event.event_type == 'coldsnap':
                    if event.peak_value < -10:
                        impact_assessment = {
                            'impact_type': 'freezing_risk',
                            'description': 'Risk of pipe freezing and equipment damage',
                            'severity': 'high' if event.peak_value < -20 else 'medium',
                            'recommendations': ['Implement heating systems', 'Insulate critical infrastructure']
                        }
                        high_impact_events += 1

                elif event.event_type == 'heavy_rain':
                    if event.peak_value > 100:
                        impact_assessment = {
                            'impact_type': 'capacity_overload',
                            'description': 'Treatment capacity may be exceeded',
                            'severity': 'high' if event.peak_value > 150 else 'medium',
                            'recommendations': ['Activate overflow systems', 'Implement emergency protocols']
                        }
                        high_impact_events += 1

                elif event.event_type == 'drought':
                    impact_assessment = {
                        'impact_type': 'water_scarcity',
                        'description': 'Reduced water availability for treatment',
                        'severity': event.severity,
                        'recommendations': ['Implement water conservation', 'Activate backup sources']
                    }
                    if event.severity in ['severe', 'extreme', 'catastrophic']:
                        high_impact_events += 1

                elif event.event_type == 'high_wind':
                    if event.peak_value > 25:
                        impact_assessment = {
                            'impact_type': 'infrastructure_damage',
                            'description': 'Risk of damage to treatment infrastructure',
                            'severity': 'high' if event.peak_value > 35 else 'medium',
                            'recommendations': ['Secure equipment', 'Inspect for damage after event']
                        }
                        high_impact_events += 1

                if impact_assessment:
                    impact_assessment['event_id'] = event.event_id
                    impact_assessment['event_duration'] = event.duration_hours

                    # Categorize impact
                    if impact_assessment['severity'] == 'high':
                        impacts['emergency_response_needed'].append(impact_assessment)
                    elif impact_assessment['impact_type'] in ['capacity_overload', 'water_scarcity']:
                        impacts['operational_disruptions'].append(impact_assessment)
                    elif impact_assessment['impact_type'] in ['infrastructure_damage', 'freezing_risk']:
                        impacts['infrastructure_risks'].append(impact_assessment)
                    else:
                        impacts['treatment_efficiency_impacts'].append(impact_assessment)

            # Determine overall impact level
            if high_impact_events >= 3:
                impacts['overall_impact_level'] = 'critical'
            elif high_impact_events >= 2:
                impacts['overall_impact_level'] = 'high'
            elif high_impact_events >= 1:
                impacts['overall_impact_level'] = 'medium'

            # Add summary recommendations
            impacts['summary_recommendations'] = self._generate_treatment_recommendations(impacts)

            return impacts

        except Exception as e:
            logger.error(f"Water treatment impact assessment failed: {e}")
            return {'overall_impact_level': 'unknown', 'operational_disruptions': [], 'infrastructure_risks': []}

    def _generate_treatment_recommendations(self, impacts: Dict[str, Any]) -> List[str]:
        """Generate treatment-specific recommendations based on impacts."""
        recommendations = []

        if impacts['overall_impact_level'] in ['critical', 'high']:
            recommendations.extend([
                'Activate emergency response protocols',
                'Implement continuous monitoring of all systems',
                'Prepare backup treatment systems',
                'Coordinate with emergency services'
            ])

        if impacts['operational_disruptions']:
            recommendations.extend([
                'Adjust treatment capacity and flow rates',
                'Monitor water quality parameters closely',
                'Implement temporary treatment modifications'
            ])

        if impacts['infrastructure_risks']:
            recommendations.extend([
                'Conduct immediate infrastructure inspections',
                'Implement protective measures for critical equipment',
                'Prepare rapid repair capabilities'
            ])

        if impacts['treatment_efficiency_impacts']:
            recommendations.extend([
                'Adjust chemical dosing and treatment parameters',
                'Monitor treatment efficiency indicators',
                'Implement process optimization measures'
            ])

        return recommendations


# Convenience functions
async def detect_extreme_weather_for_location(data: List[ProcessedClimateData],
                                            location: str = None) -> ExtremeWeatherAnalysisResult:
    """Detect extreme weather events for a specific location."""
    detector = ExtremeWeatherDetector()
    await detector.initialize()

    return await detector.detect_extreme_weather_events(data, location)


async def assess_extreme_weather_risk(data: List[ProcessedClimateData],
                                    location: str = None) -> Dict[str, Any]:
    """Assess extreme weather risk for a specific location."""
    detector = ExtremeWeatherDetector()
    await detector.initialize()

    result = await detector.detect_extreme_weather_events(data, location)
    return result.risk_assessment if result else {}


async def generate_weather_alerts(data: List[ProcessedClimateData],
                                location: str = None) -> List[Dict[str, Any]]:
    """Generate weather alerts for a specific location."""
    detector = ExtremeWeatherDetector()
    await detector.initialize()

    result = await detector.detect_extreme_weather_events(data, location)
    return result.early_warning_alerts if result else []


async def assess_treatment_impacts(data: List[ProcessedClimateData],
                                 location: str = None) -> Dict[str, Any]:
    """Assess water treatment impacts from extreme weather."""
    detector = ExtremeWeatherDetector()
    await detector.initialize()

    result = await detector.detect_extreme_weather_events(data, location)
    return result.water_treatment_impacts if result else {}
