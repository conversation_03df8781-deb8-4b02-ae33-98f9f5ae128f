"""Extreme Weather Analysis Module for Climate Data Processing."""

import numpy as np
import pandas as pd
from typing import Dict, List, Any
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ExtremeWeatherAnalyzer:
    """Advanced extreme weather event analysis."""
    
    def __init__(self):
        self.extreme_thresholds = {
            'temperature': {'heat_wave': 35, 'cold_snap': -10},
            'precipitation': {'heavy_rain': 50, 'extreme_rain': 100},
            'wind': {'strong_wind': 60, 'extreme_wind': 100},
            'drought': {'consecutive_dry_days': 30}
        }
    
    async def analyze_extreme_events(self, weather_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze extreme weather events and their impacts."""
        try:
            # Extract weather data
            temperature = weather_data.get('temperature', [])
            precipitation = weather_data.get('precipitation', [])
            wind_speed = weather_data.get('wind_speed', [])
            timestamps = weather_data.get('timestamps', [])
            
            # Analyze different types of extreme events
            heat_waves = await self._detect_heat_waves(temperature, timestamps)
            heavy_precipitation = await self._detect_heavy_precipitation(precipitation, timestamps)
            drought_periods = await self._detect_drought_periods(precipitation, timestamps)
            wind_events = await self._detect_wind_events(wind_speed, timestamps)
            
            # Calculate overall extreme weather index
            extreme_index = await self._calculate_extreme_weather_index(
                heat_waves, heavy_precipitation, drought_periods, wind_events
            )
            
            # Assess impacts on water management
            water_management_impacts = await self._assess_water_management_impacts(
                heat_waves, heavy_precipitation, drought_periods
            )
            
            return {
                'status': 'success',
                'extreme_events': {
                    'heat_waves': heat_waves,
                    'heavy_precipitation': heavy_precipitation,
                    'drought_periods': drought_periods,
                    'wind_events': wind_events
                },
                'extreme_weather_index': extreme_index,
                'water_management_impacts': water_management_impacts,
                'risk_assessment': await self._assess_extreme_weather_risks(extreme_index),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Extreme weather analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _detect_heat_waves(self, temperature: List[float], timestamps: List[str]) -> Dict[str, Any]:
        """Detect heat wave events."""
        if not temperature:
            return {'count': 0, 'events': [], 'total_days': 0}
        
        heat_wave_threshold = self.extreme_thresholds['temperature']['heat_wave']
        heat_wave_events = []
        current_event = None
        
        for i, temp in enumerate(temperature):
            if temp > heat_wave_threshold:
                if current_event is None:
                    current_event = {
                        'start_index': i,
                        'start_date': timestamps[i] if i < len(timestamps) else None,
                        'max_temperature': temp,
                        'duration': 1
                    }
                else:
                    current_event['duration'] += 1
                    current_event['max_temperature'] = max(current_event['max_temperature'], temp)
            else:
                if current_event is not None and current_event['duration'] >= 3:  # At least 3 consecutive days
                    current_event['end_index'] = i - 1
                    current_event['end_date'] = timestamps[i-1] if i-1 < len(timestamps) else None
                    heat_wave_events.append(current_event)
                current_event = None
        
        # Handle ongoing heat wave at end of data
        if current_event is not None and current_event['duration'] >= 3:
            current_event['end_index'] = len(temperature) - 1
            current_event['end_date'] = timestamps[-1] if timestamps else None
            heat_wave_events.append(current_event)
        
        total_heat_wave_days = sum(event['duration'] for event in heat_wave_events)
        
        return {
            'count': len(heat_wave_events),
            'events': heat_wave_events,
            'total_days': total_heat_wave_days,
            'average_duration': total_heat_wave_days / len(heat_wave_events) if heat_wave_events else 0,
            'max_temperature_recorded': max(event['max_temperature'] for event in heat_wave_events) if heat_wave_events else 0
        }
    
    async def _detect_heavy_precipitation(self, precipitation: List[float], timestamps: List[str]) -> Dict[str, Any]:
        """Detect heavy precipitation events."""
        if not precipitation:
            return {'count': 0, 'events': [], 'total_amount': 0}
        
        heavy_rain_threshold = self.extreme_thresholds['precipitation']['heavy_rain']
        extreme_rain_threshold = self.extreme_thresholds['precipitation']['extreme_rain']
        
        heavy_events = []
        extreme_events = []
        
        for i, precip in enumerate(precipitation):
            event_data = {
                'date': timestamps[i] if i < len(timestamps) else None,
                'amount': precip,
                'index': i
            }
            
            if precip >= extreme_rain_threshold:
                extreme_events.append(event_data)
            elif precip >= heavy_rain_threshold:
                heavy_events.append(event_data)
        
        total_heavy_amount = sum(event['amount'] for event in heavy_events + extreme_events)
        
        return {
            'heavy_rain_events': {
                'count': len(heavy_events),
                'events': heavy_events,
                'total_amount': sum(event['amount'] for event in heavy_events)
            },
            'extreme_rain_events': {
                'count': len(extreme_events),
                'events': extreme_events,
                'total_amount': sum(event['amount'] for event in extreme_events)
            },
            'total_extreme_precipitation': total_heavy_amount,
            'flood_risk_score': min(1.0, len(extreme_events) / 10)  # Normalize to 0-1
        }
    
    async def _detect_drought_periods(self, precipitation: List[float], timestamps: List[str]) -> Dict[str, Any]:
        """Detect drought periods."""
        if not precipitation:
            return {'count': 0, 'periods': [], 'total_days': 0}
        
        dry_day_threshold = 0.1  # mm
        min_drought_days = self.extreme_thresholds['drought']['consecutive_dry_days']
        
        drought_periods = []
        current_drought = None
        
        for i, precip in enumerate(precipitation):
            if precip < dry_day_threshold:
                if current_drought is None:
                    current_drought = {
                        'start_index': i,
                        'start_date': timestamps[i] if i < len(timestamps) else None,
                        'duration': 1,
                        'total_precipitation': precip
                    }
                else:
                    current_drought['duration'] += 1
                    current_drought['total_precipitation'] += precip
            else:
                if current_drought is not None and current_drought['duration'] >= min_drought_days:
                    current_drought['end_index'] = i - 1
                    current_drought['end_date'] = timestamps[i-1] if i-1 < len(timestamps) else None
                    current_drought['severity'] = self._calculate_drought_severity(current_drought)
                    drought_periods.append(current_drought)
                current_drought = None
        
        # Handle ongoing drought at end of data
        if current_drought is not None and current_drought['duration'] >= min_drought_days:
            current_drought['end_index'] = len(precipitation) - 1
            current_drought['end_date'] = timestamps[-1] if timestamps else None
            current_drought['severity'] = self._calculate_drought_severity(current_drought)
            drought_periods.append(current_drought)
        
        total_drought_days = sum(period['duration'] for period in drought_periods)
        
        return {
            'count': len(drought_periods),
            'periods': drought_periods,
            'total_days': total_drought_days,
            'average_duration': total_drought_days / len(drought_periods) if drought_periods else 0,
            'max_severity': max(period['severity'] for period in drought_periods) if drought_periods else 0
        }
    
    def _calculate_drought_severity(self, drought_period: Dict[str, Any]) -> float:
        """Calculate drought severity based on duration and precipitation deficit."""
        duration = drought_period['duration']
        total_precip = drought_period['total_precipitation']
        
        # Expected precipitation (simplified)
        expected_precip = duration * 2.0  # Assume 2mm/day average
        precipitation_deficit = max(0, expected_precip - total_precip)
        
        # Severity score (0-1)
        duration_factor = min(1.0, duration / 90)  # Normalize to 90 days
        deficit_factor = min(1.0, precipitation_deficit / (duration * 5))  # Normalize to 5mm/day deficit
        
        return (duration_factor + deficit_factor) / 2
    
    async def _detect_wind_events(self, wind_speed: List[float], timestamps: List[str]) -> Dict[str, Any]:
        """Detect extreme wind events."""
        if not wind_speed:
            return {'count': 0, 'events': []}
        
        strong_wind_threshold = self.extreme_thresholds['wind']['strong_wind']
        extreme_wind_threshold = self.extreme_thresholds['wind']['extreme_wind']
        
        strong_wind_events = []
        extreme_wind_events = []
        
        for i, wind in enumerate(wind_speed):
            event_data = {
                'date': timestamps[i] if i < len(timestamps) else None,
                'wind_speed': wind,
                'index': i
            }
            
            if wind >= extreme_wind_threshold:
                extreme_wind_events.append(event_data)
            elif wind >= strong_wind_threshold:
                strong_wind_events.append(event_data)
        
        return {
            'strong_wind_events': {
                'count': len(strong_wind_events),
                'events': strong_wind_events,
                'max_speed': max([e['wind_speed'] for e in strong_wind_events]) if strong_wind_events else 0
            },
            'extreme_wind_events': {
                'count': len(extreme_wind_events),
                'events': extreme_wind_events,
                'max_speed': max([e['wind_speed'] for e in extreme_wind_events]) if extreme_wind_events else 0
            }
        }
    
    async def _calculate_extreme_weather_index(self, heat_waves: Dict, heavy_precip: Dict, 
                                             droughts: Dict, wind_events: Dict) -> Dict[str, Any]:
        """Calculate overall extreme weather index."""
        # Normalize each component to 0-1 scale
        heat_wave_score = min(1.0, heat_waves['count'] / 5)  # 5 heat waves = max score
        precipitation_score = min(1.0, heavy_precip['total_extreme_precipitation'] / 500)  # 500mm = max
        drought_score = min(1.0, droughts['total_days'] / 180)  # 180 days = max
        wind_score = min(1.0, (wind_events['strong_wind_events']['count'] + 
                              wind_events['extreme_wind_events']['count'] * 2) / 10)
        
        # Weighted average
        weights = {'heat_waves': 0.3, 'precipitation': 0.3, 'drought': 0.25, 'wind': 0.15}
        
        overall_index = (
            weights['heat_waves'] * heat_wave_score +
            weights['precipitation'] * precipitation_score +
            weights['drought'] * drought_score +
            weights['wind'] * wind_score
        )
        
        # Determine risk level
        if overall_index < 0.3:
            risk_level = 'low'
        elif overall_index < 0.6:
            risk_level = 'moderate'
        elif overall_index < 0.8:
            risk_level = 'high'
        else:
            risk_level = 'extreme'
        
        return {
            'overall_index': overall_index,
            'risk_level': risk_level,
            'component_scores': {
                'heat_waves': heat_wave_score,
                'precipitation': precipitation_score,
                'drought': drought_score,
                'wind': wind_score
            }
        }
    
    async def _assess_water_management_impacts(self, heat_waves: Dict, heavy_precip: Dict, 
                                             droughts: Dict) -> Dict[str, Any]:
        """Assess impacts on water management systems."""
        impacts = {
            'water_demand_increase': 0,
            'infrastructure_stress': 0,
            'water_quality_risk': 0,
            'operational_challenges': []
        }
        
        # Heat wave impacts
        if heat_waves['count'] > 0:
            impacts['water_demand_increase'] += heat_waves['count'] * 0.1  # 10% per heat wave
            impacts['operational_challenges'].append('Increased cooling water demand')
        
        # Heavy precipitation impacts
        if heavy_precip['extreme_rain_events']['count'] > 0:
            impacts['infrastructure_stress'] += heavy_precip['extreme_rain_events']['count'] * 0.2
            impacts['water_quality_risk'] += 0.3
            impacts['operational_challenges'].append('Flood risk and water quality degradation')
        
        # Drought impacts
        if droughts['count'] > 0:
            impacts['water_demand_increase'] += droughts['total_days'] / 365 * 0.5
            impacts['water_quality_risk'] += 0.4
            impacts['operational_challenges'].append('Water scarcity and quality concentration')
        
        # Normalize scores
        impacts['water_demand_increase'] = min(1.0, impacts['water_demand_increase'])
        impacts['infrastructure_stress'] = min(1.0, impacts['infrastructure_stress'])
        impacts['water_quality_risk'] = min(1.0, impacts['water_quality_risk'])
        
        return impacts
    
    async def _assess_extreme_weather_risks(self, extreme_index: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risks associated with extreme weather."""
        overall_index = extreme_index['overall_index']
        risk_level = extreme_index['risk_level']
        
        risk_factors = []
        mitigation_strategies = []
        
        if overall_index > 0.7:
            risk_factors.extend([
                'High probability of infrastructure damage',
                'Significant operational disruptions',
                'Water quality degradation risk'
            ])
            mitigation_strategies.extend([
                'Implement robust early warning systems',
                'Enhance infrastructure resilience',
                'Develop emergency response protocols'
            ])
        elif overall_index > 0.4:
            risk_factors.extend([
                'Moderate infrastructure stress',
                'Potential operational challenges'
            ])
            mitigation_strategies.extend([
                'Monitor weather conditions closely',
                'Prepare contingency plans'
            ])
        
        return {
            'risk_level': risk_level,
            'risk_score': overall_index,
            'risk_factors': risk_factors,
            'mitigation_strategies': mitigation_strategies,
            'monitoring_recommendations': [
                'Continuous weather monitoring',
                'Real-time system performance tracking',
                'Regular infrastructure inspections'
            ]
        }


# Convenience function
async def analyze_extreme_weather(data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze extreme weather events."""
    analyzer = ExtremeWeatherAnalyzer()
    return await analyzer.analyze_extreme_events(data)
