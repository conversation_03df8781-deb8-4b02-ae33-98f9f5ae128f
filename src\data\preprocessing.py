"""Data Preprocessing Module for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class PreprocessingOperation(Enum):
    """Types of preprocessing operations."""
    CLEANING = "cleaning"
    NORMALIZATION = "normalization"
    FEATURE_ENGINEERING = "feature_engineering"
    OUTLIER_DETECTION = "outlier_detection"
    MISSING_VALUE_IMPUTATION = "missing_value_imputation"
    DATA_VALIDATION = "data_validation"
    AGGREGATION = "aggregation"
    TRANSFORMATION = "transformation"


class DataQualityIssue(Enum):
    """Types of data quality issues."""
    MISSING_VALUES = "missing_values"
    OUTLIERS = "outliers"
    DUPLICATES = "duplicates"
    INVALID_FORMAT = "invalid_format"
    OUT_OF_RANGE = "out_of_range"
    INCONSISTENT_UNITS = "inconsistent_units"
    TEMPORAL_GAPS = "temporal_gaps"


@dataclass
class PreprocessingResult:
    """Result of preprocessing operation."""
    operation: PreprocessingOperation
    status: str
    records_processed: int
    records_modified: int
    issues_found: List[Dict[str, Any]]
    processing_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


class DataPreprocessor:
    """Comprehensive data preprocessing system for water management data."""
    
    def __init__(self):
        self.preprocessing_history: List[PreprocessingResult] = []
        
        # Water management specific configurations
        self.water_quality_ranges = {
            'ph': (6.0, 9.0),
            'turbidity': (0.0, 10.0),
            'chlorine_residual': (0.0, 5.0),
            'temperature': (-5.0, 50.0),
            'dissolved_oxygen': (0.0, 20.0),
            'conductivity': (0.0, 3000.0),
            'bacteria_count': (0.0, 1000.0)
        }
        
        self.operational_ranges = {
            'flow_rate': (0.0, 5000.0),
            'pressure': (0.0, 20.0),
            'energy_consumption': (0.0, 10000.0),
            'chemical_dose': (0.0, 100.0),
            'efficiency': (0.0, 100.0)
        }
        
        # Preprocessing configuration
        self.config = {
            'outlier_threshold': 3.0,  # Standard deviations
            'missing_value_threshold': 0.3,  # 30% missing allowed
            'duplicate_tolerance': 0.001,  # Tolerance for duplicate detection
            'temporal_gap_threshold': 3600,  # 1 hour in seconds
            'validation_strict': True
        }
    
    @log_async_function_call
    async def preprocess_water_quality_data(self, data: Union[Dict, List[Dict]]) -> Dict[str, Any]:
        """Preprocess water quality data."""
        try:
            start_time = datetime.now()
            
            # Convert to list if single record
            if isinstance(data, dict):
                data = [data]
            
            logger.info(f"Preprocessing {len(data)} water quality records")
            
            # Convert to DataFrame for easier processing
            df = pd.DataFrame(data)
            original_count = len(df)
            
            results = []
            
            # 1. Data Cleaning
            cleaning_result = await self._clean_data(df, 'water_quality')
            results.append(cleaning_result)
            
            # 2. Missing Value Imputation
            imputation_result = await self._impute_missing_values(df, 'water_quality')
            results.append(imputation_result)
            
            # 3. Outlier Detection and Handling
            outlier_result = await self._detect_and_handle_outliers(df, 'water_quality')
            results.append(outlier_result)
            
            # 4. Data Validation
            validation_result = await self._validate_data(df, 'water_quality')
            results.append(validation_result)
            
            # 5. Feature Engineering
            feature_result = await self._engineer_features(df, 'water_quality')
            results.append(feature_result)
            
            # 6. Normalization
            normalization_result = await self._normalize_data(df, 'water_quality')
            results.append(normalization_result)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Convert back to records
            processed_data = df.to_dict('records')
            
            return {
                'status': 'success',
                'original_records': original_count,
                'processed_records': len(processed_data),
                'processing_time': processing_time,
                'operations_performed': [r.operation.value for r in results],
                'data_quality_issues': self._aggregate_issues(results),
                'processed_data': processed_data,
                'preprocessing_results': [
                    {
                        'operation': r.operation.value,
                        'status': r.status,
                        'records_modified': r.records_modified,
                        'issues_found': len(r.issues_found)
                    }
                    for r in results
                ]
            }
            
        except Exception as e:
            logger.error(f"Water quality data preprocessing failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def preprocess_operational_data(self, data: Union[Dict, List[Dict]]) -> Dict[str, Any]:
        """Preprocess operational data (flow rates, energy, etc.)."""
        try:
            start_time = datetime.now()
            
            if isinstance(data, dict):
                data = [data]
            
            logger.info(f"Preprocessing {len(data)} operational records")
            
            df = pd.DataFrame(data)
            original_count = len(df)
            
            results = []
            
            # 1. Data Cleaning
            cleaning_result = await self._clean_data(df, 'operational')
            results.append(cleaning_result)
            
            # 2. Temporal Processing
            temporal_result = await self._process_temporal_data(df)
            results.append(temporal_result)
            
            # 3. Missing Value Imputation
            imputation_result = await self._impute_missing_values(df, 'operational')
            results.append(imputation_result)
            
            # 4. Outlier Detection
            outlier_result = await self._detect_and_handle_outliers(df, 'operational')
            results.append(outlier_result)
            
            # 5. Data Aggregation
            aggregation_result = await self._aggregate_data(df)
            results.append(aggregation_result)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            processed_data = df.to_dict('records')
            
            return {
                'status': 'success',
                'original_records': original_count,
                'processed_records': len(processed_data),
                'processing_time': processing_time,
                'operations_performed': [r.operation.value for r in results],
                'processed_data': processed_data,
                'preprocessing_results': [
                    {
                        'operation': r.operation.value,
                        'status': r.status,
                        'records_modified': r.records_modified
                    }
                    for r in results
                ]
            }
            
        except Exception as e:
            logger.error(f"Operational data preprocessing failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _clean_data(self, df: pd.DataFrame, data_type: str) -> PreprocessingResult:
        """Clean data by removing duplicates and invalid entries."""
        start_time = datetime.now()
        issues_found = []
        records_modified = 0
        
        try:
            original_count = len(df)
            
            # Remove exact duplicates
            duplicates_before = df.duplicated().sum()
            if duplicates_before > 0:
                df.drop_duplicates(inplace=True)
                records_modified += duplicates_before
                issues_found.append({
                    'type': DataQualityIssue.DUPLICATES.value,
                    'count': duplicates_before,
                    'description': f'Removed {duplicates_before} duplicate records'
                })
            
            # Remove records with invalid timestamps
            if 'timestamp' in df.columns:
                invalid_timestamps = df['timestamp'].isna().sum()
                if invalid_timestamps > 0:
                    df.dropna(subset=['timestamp'], inplace=True)
                    records_modified += invalid_timestamps
                    issues_found.append({
                        'type': DataQualityIssue.INVALID_FORMAT.value,
                        'count': invalid_timestamps,
                        'description': f'Removed {invalid_timestamps} records with invalid timestamps'
                    })
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.CLEANING,
                status='success',
                records_processed=original_count,
                records_modified=records_modified,
                issues_found=issues_found,
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.CLEANING,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _impute_missing_values(self, df: pd.DataFrame, data_type: str) -> PreprocessingResult:
        """Impute missing values using appropriate strategies."""
        start_time = datetime.now()
        issues_found = []
        records_modified = 0
        
        try:
            # Get appropriate ranges for validation
            ranges = self.water_quality_ranges if data_type == 'water_quality' else self.operational_ranges
            
            for column in df.columns:
                if column in ['timestamp', 'id', 'location']:
                    continue
                
                missing_count = df[column].isna().sum()
                if missing_count > 0:
                    missing_ratio = missing_count / len(df)
                    
                    if missing_ratio > self.config['missing_value_threshold']:
                        # Too many missing values - flag for attention
                        issues_found.append({
                            'type': DataQualityIssue.MISSING_VALUES.value,
                            'column': column,
                            'count': missing_count,
                            'ratio': missing_ratio,
                            'description': f'Column {column} has {missing_ratio:.1%} missing values'
                        })
                    else:
                        # Impute missing values
                        if df[column].dtype in ['float64', 'int64']:
                            # Use median for numerical data
                            median_value = df[column].median()
                            df[column].fillna(median_value, inplace=True)
                            records_modified += missing_count
                        else:
                            # Use mode for categorical data
                            mode_value = df[column].mode().iloc[0] if not df[column].mode().empty else 'unknown'
                            df[column].fillna(mode_value, inplace=True)
                            records_modified += missing_count
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.MISSING_VALUE_IMPUTATION,
                status='success',
                records_processed=len(df),
                records_modified=records_modified,
                issues_found=issues_found,
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.MISSING_VALUE_IMPUTATION,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _detect_and_handle_outliers(self, df: pd.DataFrame, data_type: str) -> PreprocessingResult:
        """Detect and handle outliers using statistical methods."""
        start_time = datetime.now()
        issues_found = []
        records_modified = 0
        
        try:
            ranges = self.water_quality_ranges if data_type == 'water_quality' else self.operational_ranges
            
            for column in df.columns:
                if column not in ranges or df[column].dtype not in ['float64', 'int64']:
                    continue
                
                # Statistical outlier detection (Z-score method)
                z_scores = np.abs((df[column] - df[column].mean()) / df[column].std())
                outliers_mask = z_scores > self.config['outlier_threshold']
                outlier_count = outliers_mask.sum()
                
                if outlier_count > 0:
                    # Range-based validation
                    min_val, max_val = ranges[column]
                    range_outliers = (df[column] < min_val) | (df[column] > max_val)
                    range_outlier_count = range_outliers.sum()
                    
                    if range_outlier_count > 0:
                        # Clip values to valid range
                        df[column] = df[column].clip(min_val, max_val)
                        records_modified += range_outlier_count
                        
                        issues_found.append({
                            'type': DataQualityIssue.OUT_OF_RANGE.value,
                            'column': column,
                            'count': range_outlier_count,
                            'description': f'Clipped {range_outlier_count} out-of-range values in {column}'
                        })
                    
                    # Statistical outliers (but within range) - flag for review
                    statistical_outliers = outliers_mask & ~range_outliers
                    if statistical_outliers.sum() > 0:
                        issues_found.append({
                            'type': DataQualityIssue.OUTLIERS.value,
                            'column': column,
                            'count': statistical_outliers.sum(),
                            'description': f'Found {statistical_outliers.sum()} statistical outliers in {column}'
                        })
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.OUTLIER_DETECTION,
                status='success',
                records_processed=len(df),
                records_modified=records_modified,
                issues_found=issues_found,
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.OUTLIER_DETECTION,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _validate_data(self, df: pd.DataFrame, data_type: str) -> PreprocessingResult:
        """Validate data against business rules and constraints."""
        start_time = datetime.now()
        issues_found = []
        
        try:
            # Water quality specific validations
            if data_type == 'water_quality':
                # pH and chlorine relationship validation
                if 'ph' in df.columns and 'chlorine_residual' in df.columns:
                    # High pH reduces chlorine effectiveness
                    problematic_combinations = ((df['ph'] > 8.0) & (df['chlorine_residual'] < 0.5)).sum()
                    if problematic_combinations > 0:
                        issues_found.append({
                            'type': 'validation_warning',
                            'count': problematic_combinations,
                            'description': f'{problematic_combinations} records with high pH and low chlorine'
                        })
                
                # Temperature and dissolved oxygen relationship
                if 'temperature' in df.columns and 'dissolved_oxygen' in df.columns:
                    # Higher temperature should correlate with lower DO
                    anomalies = ((df['temperature'] > 25) & (df['dissolved_oxygen'] > 10)).sum()
                    if anomalies > 0:
                        issues_found.append({
                            'type': 'validation_warning',
                            'count': anomalies,
                            'description': f'{anomalies} records with high temp and high DO (unusual)'
                        })
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.DATA_VALIDATION,
                status='success',
                records_processed=len(df),
                records_modified=0,
                issues_found=issues_found,
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.DATA_VALIDATION,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _engineer_features(self, df: pd.DataFrame, data_type: str) -> PreprocessingResult:
        """Engineer new features from existing data."""
        start_time = datetime.now()
        records_modified = 0
        
        try:
            if data_type == 'water_quality':
                # Create water quality index
                if all(col in df.columns for col in ['ph', 'turbidity', 'chlorine_residual']):
                    df['water_quality_index'] = self._calculate_water_quality_index(
                        df['ph'], df['turbidity'], df['chlorine_residual']
                    )
                    records_modified = len(df)
                
                # Create disinfection efficiency indicator
                if 'chlorine_residual' in df.columns and 'bacteria_count' in df.columns:
                    df['disinfection_efficiency'] = np.where(
                        (df['chlorine_residual'] > 0.2) & (df['bacteria_count'] < 10),
                        'effective', 'needs_attention'
                    )
            
            # Add temporal features if timestamp exists
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df['hour'] = df['timestamp'].dt.hour
                df['day_of_week'] = df['timestamp'].dt.dayofweek
                df['is_weekend'] = df['day_of_week'].isin([5, 6])
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.FEATURE_ENGINEERING,
                status='success',
                records_processed=len(df),
                records_modified=records_modified,
                issues_found=[],
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.FEATURE_ENGINEERING,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _normalize_data(self, df: pd.DataFrame, data_type: str) -> PreprocessingResult:
        """Normalize numerical data for ML processing."""
        start_time = datetime.now()
        records_modified = 0
        
        try:
            numerical_columns = df.select_dtypes(include=[np.number]).columns
            
            for column in numerical_columns:
                if column not in ['id', 'hour', 'day_of_week']:
                    # Min-max normalization
                    min_val = df[column].min()
                    max_val = df[column].max()
                    
                    if max_val > min_val:
                        df[f'{column}_normalized'] = (df[column] - min_val) / (max_val - min_val)
                        records_modified += len(df)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.NORMALIZATION,
                status='success',
                records_processed=len(df),
                records_modified=records_modified,
                issues_found=[],
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.NORMALIZATION,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _process_temporal_data(self, df: pd.DataFrame) -> PreprocessingResult:
        """Process temporal aspects of the data."""
        start_time = datetime.now()
        issues_found = []
        
        try:
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.sort_values('timestamp', inplace=True)
                
                # Detect temporal gaps
                time_diffs = df['timestamp'].diff().dt.total_seconds()
                large_gaps = time_diffs > self.config['temporal_gap_threshold']
                gap_count = large_gaps.sum()
                
                if gap_count > 0:
                    issues_found.append({
                        'type': DataQualityIssue.TEMPORAL_GAPS.value,
                        'count': gap_count,
                        'description': f'Found {gap_count} temporal gaps > {self.config["temporal_gap_threshold"]}s'
                    })
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.TRANSFORMATION,
                status='success',
                records_processed=len(df),
                records_modified=0,
                issues_found=issues_found,
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.TRANSFORMATION,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _aggregate_data(self, df: pd.DataFrame) -> PreprocessingResult:
        """Aggregate data for analysis."""
        start_time = datetime.now()
        
        try:
            # Simple aggregation - could be more sophisticated
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                # Add hourly aggregation markers
                df['hour_bucket'] = df['timestamp'].dt.floor('H')
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return PreprocessingResult(
                operation=PreprocessingOperation.AGGREGATION,
                status='success',
                records_processed=len(df),
                records_modified=len(df),
                issues_found=[],
                processing_time=processing_time
            )
            
        except Exception as e:
            return PreprocessingResult(
                operation=PreprocessingOperation.AGGREGATION,
                status='error',
                records_processed=len(df),
                records_modified=0,
                issues_found=[{'type': 'error', 'description': str(e)}],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _calculate_water_quality_index(self, ph: pd.Series, turbidity: pd.Series, 
                                     chlorine: pd.Series) -> pd.Series:
        """Calculate water quality index from multiple parameters."""
        # Simplified water quality index calculation
        ph_score = np.where((ph >= 6.5) & (ph <= 8.5), 100, 
                           np.where((ph >= 6.0) & (ph <= 9.0), 75, 50))
        
        turbidity_score = np.where(turbidity <= 1.0, 100,
                                 np.where(turbidity <= 4.0, 75, 50))
        
        chlorine_score = np.where((chlorine >= 0.2) & (chlorine <= 4.0), 100, 75)
        
        # Weighted average
        wqi = (ph_score * 0.4 + turbidity_score * 0.4 + chlorine_score * 0.2)
        
        return wqi
    
    def _aggregate_issues(self, results: List[PreprocessingResult]) -> Dict[str, Any]:
        """Aggregate issues found across all preprocessing operations."""
        all_issues = []
        for result in results:
            all_issues.extend(result.issues_found)
        
        # Group by issue type
        issue_summary = {}
        for issue in all_issues:
            issue_type = issue.get('type', 'unknown')
            if issue_type not in issue_summary:
                issue_summary[issue_type] = []
            issue_summary[issue_type].append(issue)
        
        return {
            'total_issues': len(all_issues),
            'issue_types': list(issue_summary.keys()),
            'issue_summary': issue_summary
        }


# Convenience functions
async def preprocess_water_quality(data: Union[Dict, List[Dict]]) -> Dict[str, Any]:
    """Preprocess water quality data."""
    preprocessor = DataPreprocessor()
    return await preprocessor.preprocess_water_quality_data(data)


async def preprocess_operational_data(data: Union[Dict, List[Dict]]) -> Dict[str, Any]:
    """Preprocess operational data."""
    preprocessor = DataPreprocessor()
    return await preprocessor.preprocess_operational_data(data)
