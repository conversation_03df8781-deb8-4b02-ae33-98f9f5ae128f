import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Box,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Divider,
  <PERSON><PERSON>,
  <PERSON>lider,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Save as SaveIcon,
  Restore as RestoreIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Tune as TuneIcon,
} from '@mui/icons-material';

const Settings = () => {
  const [settings, setSettings] = useState({
    // General Settings
    autoRefresh: true,
    refreshInterval: 30,
    theme: 'dark',
    language: 'en',
    
    // Notifications
    enableNotifications: true,
    emailAlerts: true,
    pushNotifications: false,
    alertThreshold: 80,
    
    // Marine Conservation
    marineMonitoring: true,
    debrisAlertLevel: 'medium',
    vesselTrackingRadius: 50,
    
    // Water Management
    waterQualityAlerts: true,
    treatmentEfficiencyThreshold: 85,
    energyOptimization: true,
    
    // Integration
    crossSystemAnalysis: true,
    realTimeSync: true,
    dataRetentionDays: 90,
  });

  const [saved, setSaved] = useState(false);

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    // In a real app, this would save to backend
    console.log('Saving settings:', settings);
    setSaved(true);
    setTimeout(() => setSaved(false), 3000);
  };

  const handleReset = () => {
    // Reset to default values
    setSettings({
      autoRefresh: true,
      refreshInterval: 30,
      theme: 'dark',
      language: 'en',
      enableNotifications: true,
      emailAlerts: true,
      pushNotifications: false,
      alertThreshold: 80,
      marineMonitoring: true,
      debrisAlertLevel: 'medium',
      vesselTrackingRadius: 50,
      waterQualityAlerts: true,
      treatmentEfficiencyThreshold: 85,
      energyOptimization: true,
      crossSystemAnalysis: true,
      realTimeSync: true,
      dataRetentionDays: 90,
    });
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          ⚙️ System Settings
        </Typography>
        <Box display="flex" gap={2}>
          <Button variant="outlined" onClick={handleReset} startIcon={<RestoreIcon />}>
            Reset to Defaults
          </Button>
          <Button variant="contained" onClick={handleSave} startIcon={<SaveIcon />}>
            Save Settings
          </Button>
        </Box>
      </Box>

      {/* Save Confirmation */}
      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Settings saved successfully!
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* General Settings */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <SettingsIcon color="primary" />
                <Typography variant="h6">General Settings</Typography>
              </Box>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoRefresh}
                    onChange={(e) => handleSettingChange('autoRefresh', e.target.checked)}
                  />
                }
                label="Auto Refresh Dashboard"
              />
              
              <Box mt={2}>
                <Typography gutterBottom>Refresh Interval (seconds)</Typography>
                <Slider
                  value={settings.refreshInterval}
                  onChange={(e, value) => handleSettingChange('refreshInterval', value)}
                  min={10}
                  max={300}
                  step={10}
                  marks={[
                    { value: 10, label: '10s' },
                    { value: 60, label: '1m' },
                    { value: 300, label: '5m' },
                  ]}
                  valueLabelDisplay="auto"
                  disabled={!settings.autoRefresh}
                />
              </Box>

              <Box mt={2}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={settings.theme}
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="auto">Auto</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Box mt={2}>
                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={settings.language}
                    onChange={(e) => handleSettingChange('language', e.target.value)}
                  >
                    <MenuItem value="en">English</MenuItem>
                    <MenuItem value="zh">中文</MenuItem>
                    <MenuItem value="es">Español</MenuItem>
                    <MenuItem value="fr">Français</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <NotificationsIcon color="secondary" />
                <Typography variant="h6">Notifications</Typography>
              </Box>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.enableNotifications}
                    onChange={(e) => handleSettingChange('enableNotifications', e.target.checked)}
                  />
                }
                label="Enable Notifications"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emailAlerts}
                    onChange={(e) => handleSettingChange('emailAlerts', e.target.checked)}
                    disabled={!settings.enableNotifications}
                  />
                }
                label="Email Alerts"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.pushNotifications}
                    onChange={(e) => handleSettingChange('pushNotifications', e.target.checked)}
                    disabled={!settings.enableNotifications}
                  />
                }
                label="Push Notifications"
              />

              <Box mt={2}>
                <Typography gutterBottom>Alert Threshold (%)</Typography>
                <Slider
                  value={settings.alertThreshold}
                  onChange={(e, value) => handleSettingChange('alertThreshold', value)}
                  min={50}
                  max={95}
                  step={5}
                  marks={[
                    { value: 50, label: '50%' },
                    { value: 70, label: '70%' },
                    { value: 90, label: '90%' },
                  ]}
                  valueLabelDisplay="auto"
                  disabled={!settings.enableNotifications}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Marine Conservation Settings */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <SecurityIcon color="info" />
                <Typography variant="h6">Marine Conservation</Typography>
              </Box>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.marineMonitoring}
                    onChange={(e) => handleSettingChange('marineMonitoring', e.target.checked)}
                  />
                }
                label="Enable Marine Monitoring"
              />

              <Box mt={2}>
                <FormControl fullWidth>
                  <InputLabel>Debris Alert Level</InputLabel>
                  <Select
                    value={settings.debrisAlertLevel}
                    onChange={(e) => handleSettingChange('debrisAlertLevel', e.target.value)}
                    disabled={!settings.marineMonitoring}
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Box mt={2}>
                <Typography gutterBottom>Vessel Tracking Radius (km)</Typography>
                <Slider
                  value={settings.vesselTrackingRadius}
                  onChange={(e, value) => handleSettingChange('vesselTrackingRadius', value)}
                  min={10}
                  max={200}
                  step={10}
                  marks={[
                    { value: 10, label: '10km' },
                    { value: 100, label: '100km' },
                    { value: 200, label: '200km' },
                  ]}
                  valueLabelDisplay="auto"
                  disabled={!settings.marineMonitoring}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Water Management Settings */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <TuneIcon color="success" />
                <Typography variant="h6">Water Management</Typography>
              </Box>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.waterQualityAlerts}
                    onChange={(e) => handleSettingChange('waterQualityAlerts', e.target.checked)}
                  />
                }
                label="Water Quality Alerts"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.energyOptimization}
                    onChange={(e) => handleSettingChange('energyOptimization', e.target.checked)}
                  />
                }
                label="Energy Optimization"
              />

              <Box mt={2}>
                <Typography gutterBottom>Treatment Efficiency Threshold (%)</Typography>
                <Slider
                  value={settings.treatmentEfficiencyThreshold}
                  onChange={(e, value) => handleSettingChange('treatmentEfficiencyThreshold', value)}
                  min={70}
                  max={98}
                  step={1}
                  marks={[
                    { value: 70, label: '70%' },
                    { value: 85, label: '85%' },
                    { value: 95, label: '95%' },
                  ]}
                  valueLabelDisplay="auto"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Integration Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Integration & Data Settings
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.crossSystemAnalysis}
                        onChange={(e) => handleSettingChange('crossSystemAnalysis', e.target.checked)}
                      />
                    }
                    label="Cross-System Analysis"
                  />
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.realTimeSync}
                        onChange={(e) => handleSettingChange('realTimeSync', e.target.checked)}
                      />
                    }
                    label="Real-time Data Sync"
                  />
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Data Retention (days)"
                    type="number"
                    value={settings.dataRetentionDays}
                    onChange={(e) => handleSettingChange('dataRetentionDays', parseInt(e.target.value))}
                    inputProps={{ min: 30, max: 365 }}
                    fullWidth
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;
