"""Hyperparameter Optimization for Water Management ML Models."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import json
import random

logger = logging.getLogger(__name__)


class OptimizationMethod(Enum):
    """Hyperparameter optimization methods."""
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"
    BAYESIAN_OPTIMIZATION = "bayesian_optimization"
    GENETIC_ALGORITHM = "genetic_algorithm"
    PARTICLE_SWARM = "particle_swarm"


@dataclass
class HyperparameterSpace:
    """Hyperparameter search space definition."""
    parameter_name: str
    parameter_type: str  # 'continuous', 'discrete', 'categorical'
    bounds: Tuple[Any, Any]  # (min, max) for continuous/discrete, list for categorical
    distribution: str = 'uniform'  # 'uniform', 'log_uniform', 'normal'


@dataclass
class OptimizationResult:
    """Result of hyperparameter optimization."""
    best_parameters: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    total_evaluations: int
    optimization_time: float
    convergence_info: Dict[str, Any]


class HyperparameterOptimizer:
    """Hyperparameter optimization system for ML models."""
    
    def __init__(self):
        self.optimization_history: List[Dict[str, Any]] = []
        self.current_optimization = None
        
        # Predefined parameter spaces for common models
        self.model_parameter_spaces = {
            'random_forest': [
                HyperparameterSpace('n_estimators', 'discrete', (10, 500)),
                HyperparameterSpace('max_depth', 'discrete', (3, 50)),
                HyperparameterSpace('min_samples_split', 'discrete', (2, 20)),
                HyperparameterSpace('min_samples_leaf', 'discrete', (1, 10)),
                HyperparameterSpace('max_features', 'categorical', (['auto', 'sqrt', 'log2']))
            ],
            'gradient_boosting': [
                HyperparameterSpace('n_estimators', 'discrete', (50, 300)),
                HyperparameterSpace('learning_rate', 'continuous', (0.01, 0.3)),
                HyperparameterSpace('max_depth', 'discrete', (3, 15)),
                HyperparameterSpace('subsample', 'continuous', (0.6, 1.0))
            ],
            'neural_network': [
                HyperparameterSpace('hidden_layer_sizes', 'categorical', ([(64,), (128,), (64, 32), (128, 64), (256, 128)])),
                HyperparameterSpace('learning_rate', 'continuous', (0.0001, 0.01)),
                HyperparameterSpace('batch_size', 'discrete', (16, 128)),
                HyperparameterSpace('dropout_rate', 'continuous', (0.0, 0.5)),
                HyperparameterSpace('activation', 'categorical', (['relu', 'tanh', 'sigmoid']))
            ],
            'svm': [
                HyperparameterSpace('C', 'continuous', (0.1, 100.0)),
                HyperparameterSpace('gamma', 'continuous', (0.001, 1.0)),
                HyperparameterSpace('kernel', 'categorical', (['rbf', 'linear', 'poly']))
            ]
        }
    
    async def optimize_hyperparameters(self, optimization_config: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize hyperparameters for a given model."""
        try:
            model_type = optimization_config['model_type']
            method = OptimizationMethod(optimization_config.get('optimization_method', 'random_search'))
            max_evaluations = optimization_config.get('max_evaluations', 100)
            
            optimization_id = f"opt_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Get parameter space
            if 'parameter_space' in optimization_config:
                parameter_space = self._parse_parameter_space(optimization_config['parameter_space'])
            else:
                parameter_space = self.model_parameter_spaces.get(model_type, [])
            
            if not parameter_space:
                return {
                    'status': 'error',
                    'error': f'No parameter space defined for model type: {model_type}'
                }
            
            # Start optimization
            start_time = datetime.now()
            
            if method == OptimizationMethod.GRID_SEARCH:
                result = await self._grid_search_optimization(parameter_space, max_evaluations)
            elif method == OptimizationMethod.RANDOM_SEARCH:
                result = await self._random_search_optimization(parameter_space, max_evaluations)
            elif method == OptimizationMethod.BAYESIAN_OPTIMIZATION:
                result = await self._bayesian_optimization(parameter_space, max_evaluations)
            elif method == OptimizationMethod.GENETIC_ALGORITHM:
                result = await self._genetic_algorithm_optimization(parameter_space, max_evaluations)
            else:
                result = await self._random_search_optimization(parameter_space, max_evaluations)
            
            end_time = datetime.now()
            optimization_time = (end_time - start_time).total_seconds()
            
            # Create optimization result
            optimization_result = OptimizationResult(
                best_parameters=result['best_parameters'],
                best_score=result['best_score'],
                optimization_history=result['history'],
                total_evaluations=len(result['history']),
                optimization_time=optimization_time,
                convergence_info=result.get('convergence_info', {})
            )
            
            # Store optimization
            optimization_record = {
                'optimization_id': optimization_id,
                'model_type': model_type,
                'method': method.value,
                'result': optimization_result,
                'timestamp': start_time.isoformat()
            }
            
            self.optimization_history.append(optimization_record)
            
            return {
                'status': 'success',
                'optimization_id': optimization_id,
                'best_parameters': result['best_parameters'],
                'best_score': result['best_score'],
                'total_evaluations': len(result['history']),
                'optimization_time': optimization_time,
                'improvement': result.get('improvement', 0),
                'convergence_achieved': result.get('converged', False)
            }
            
        except Exception as e:
            logger.error(f"Hyperparameter optimization failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _parse_parameter_space(self, parameter_space_config: Dict[str, Any]) -> List[HyperparameterSpace]:
        """Parse parameter space configuration."""
        parameter_spaces = []
        
        for param_name, param_config in parameter_space_config.items():
            if isinstance(param_config, list):
                # Categorical parameter
                space = HyperparameterSpace(
                    parameter_name=param_name,
                    parameter_type='categorical',
                    bounds=param_config
                )
            elif isinstance(param_config, dict):
                # Continuous or discrete parameter
                space = HyperparameterSpace(
                    parameter_name=param_name,
                    parameter_type=param_config.get('type', 'continuous'),
                    bounds=(param_config['min'], param_config['max']),
                    distribution=param_config.get('distribution', 'uniform')
                )
            else:
                continue
            
            parameter_spaces.append(space)
        
        return parameter_spaces
    
    async def _grid_search_optimization(self, parameter_space: List[HyperparameterSpace], 
                                      max_evaluations: int) -> Dict[str, Any]:
        """Perform grid search optimization."""
        # Generate grid points
        grid_points = self._generate_grid_points(parameter_space, max_evaluations)
        
        best_score = -float('inf')
        best_parameters = None
        history = []
        
        for i, parameters in enumerate(grid_points):
            # Evaluate parameters
            score = await self._evaluate_parameters(parameters)
            
            evaluation = {
                'iteration': i + 1,
                'parameters': parameters,
                'score': score,
                'timestamp': datetime.now().isoformat()
            }
            
            history.append(evaluation)
            
            if score > best_score:
                best_score = score
                best_parameters = parameters
            
            logger.debug(f"Grid search iteration {i + 1}: score = {score:.4f}")
        
        return {
            'best_parameters': best_parameters,
            'best_score': best_score,
            'history': history,
            'convergence_info': {
                'method': 'grid_search',
                'total_combinations': len(grid_points)
            }
        }
    
    async def _random_search_optimization(self, parameter_space: List[HyperparameterSpace], 
                                        max_evaluations: int) -> Dict[str, Any]:
        """Perform random search optimization."""
        best_score = -float('inf')
        best_parameters = None
        history = []
        
        for i in range(max_evaluations):
            # Sample random parameters
            parameters = self._sample_random_parameters(parameter_space)
            
            # Evaluate parameters
            score = await self._evaluate_parameters(parameters)
            
            evaluation = {
                'iteration': i + 1,
                'parameters': parameters,
                'score': score,
                'timestamp': datetime.now().isoformat()
            }
            
            history.append(evaluation)
            
            if score > best_score:
                best_score = score
                best_parameters = parameters
            
            logger.debug(f"Random search iteration {i + 1}: score = {score:.4f}")
        
        return {
            'best_parameters': best_parameters,
            'best_score': best_score,
            'history': history,
            'convergence_info': {
                'method': 'random_search',
                'total_evaluations': max_evaluations
            }
        }
    
    async def _bayesian_optimization(self, parameter_space: List[HyperparameterSpace], 
                                   max_evaluations: int) -> Dict[str, Any]:
        """Perform Bayesian optimization (simplified implementation)."""
        # Simplified Bayesian optimization using random sampling with exploitation/exploration
        best_score = -float('inf')
        best_parameters = None
        history = []
        
        # Initial random exploration
        exploration_phase = min(10, max_evaluations // 4)
        
        for i in range(max_evaluations):
            if i < exploration_phase:
                # Exploration: random sampling
                parameters = self._sample_random_parameters(parameter_space)
            else:
                # Exploitation: sample around best parameters with some noise
                if best_parameters:
                    parameters = self._sample_around_best(parameter_space, best_parameters)
                else:
                    parameters = self._sample_random_parameters(parameter_space)
            
            # Evaluate parameters
            score = await self._evaluate_parameters(parameters)
            
            evaluation = {
                'iteration': i + 1,
                'parameters': parameters,
                'score': score,
                'acquisition_function': 'expected_improvement' if i >= exploration_phase else 'random',
                'timestamp': datetime.now().isoformat()
            }
            
            history.append(evaluation)
            
            if score > best_score:
                best_score = score
                best_parameters = parameters
            
            logger.debug(f"Bayesian optimization iteration {i + 1}: score = {score:.4f}")
        
        return {
            'best_parameters': best_parameters,
            'best_score': best_score,
            'history': history,
            'convergence_info': {
                'method': 'bayesian_optimization',
                'exploration_phase': exploration_phase,
                'exploitation_phase': max_evaluations - exploration_phase
            }
        }
    
    async def _genetic_algorithm_optimization(self, parameter_space: List[HyperparameterSpace], 
                                            max_evaluations: int) -> Dict[str, Any]:
        """Perform genetic algorithm optimization."""
        population_size = min(20, max_evaluations // 5)
        generations = max_evaluations // population_size
        
        # Initialize population
        population = [self._sample_random_parameters(parameter_space) for _ in range(population_size)]
        
        best_score = -float('inf')
        best_parameters = None
        history = []
        
        for generation in range(generations):
            # Evaluate population
            population_scores = []
            for individual in population:
                score = await self._evaluate_parameters(individual)
                population_scores.append(score)
                
                evaluation = {
                    'iteration': len(history) + 1,
                    'generation': generation + 1,
                    'parameters': individual,
                    'score': score,
                    'timestamp': datetime.now().isoformat()
                }
                
                history.append(evaluation)
                
                if score > best_score:
                    best_score = score
                    best_parameters = individual
            
            # Selection, crossover, and mutation
            if generation < generations - 1:
                population = self._evolve_population(population, population_scores, parameter_space)
            
            logger.debug(f"GA generation {generation + 1}: best score = {max(population_scores):.4f}")
        
        return {
            'best_parameters': best_parameters,
            'best_score': best_score,
            'history': history,
            'convergence_info': {
                'method': 'genetic_algorithm',
                'population_size': population_size,
                'generations': generations
            }
        }
    
    def _generate_grid_points(self, parameter_space: List[HyperparameterSpace], 
                            max_points: int) -> List[Dict[str, Any]]:
        """Generate grid points for grid search."""
        # Simplified grid generation
        grid_points = []
        
        # Calculate grid size per parameter
        grid_size_per_param = max(2, int(max_points ** (1.0 / len(parameter_space))))
        
        # Generate combinations (simplified)
        for i in range(min(max_points, grid_size_per_param ** len(parameter_space))):
            parameters = {}
            
            for j, param_space in enumerate(parameter_space):
                if param_space.parameter_type == 'categorical':
                    idx = (i // (grid_size_per_param ** j)) % len(param_space.bounds)
                    parameters[param_space.parameter_name] = param_space.bounds[idx]
                elif param_space.parameter_type == 'discrete':
                    min_val, max_val = param_space.bounds
                    step = (max_val - min_val) / (grid_size_per_param - 1)
                    idx = (i // (grid_size_per_param ** j)) % grid_size_per_param
                    parameters[param_space.parameter_name] = int(min_val + idx * step)
                else:  # continuous
                    min_val, max_val = param_space.bounds
                    step = (max_val - min_val) / (grid_size_per_param - 1)
                    idx = (i // (grid_size_per_param ** j)) % grid_size_per_param
                    parameters[param_space.parameter_name] = min_val + idx * step
            
            grid_points.append(parameters)
        
        return grid_points[:max_points]
    
    def _sample_random_parameters(self, parameter_space: List[HyperparameterSpace]) -> Dict[str, Any]:
        """Sample random parameters from parameter space."""
        parameters = {}
        
        for param_space in parameter_space:
            if param_space.parameter_type == 'categorical':
                parameters[param_space.parameter_name] = random.choice(param_space.bounds)
            elif param_space.parameter_type == 'discrete':
                min_val, max_val = param_space.bounds
                parameters[param_space.parameter_name] = random.randint(min_val, max_val)
            else:  # continuous
                min_val, max_val = param_space.bounds
                if param_space.distribution == 'log_uniform':
                    parameters[param_space.parameter_name] = np.exp(random.uniform(np.log(min_val), np.log(max_val)))
                else:  # uniform
                    parameters[param_space.parameter_name] = random.uniform(min_val, max_val)
        
        return parameters
    
    def _sample_around_best(self, parameter_space: List[HyperparameterSpace], 
                          best_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Sample parameters around the best known parameters."""
        parameters = {}
        
        for param_space in parameter_space:
            param_name = param_space.parameter_name
            best_value = best_parameters.get(param_name)
            
            if param_space.parameter_type == 'categorical':
                # Small chance to explore other categories
                if random.random() < 0.8:
                    parameters[param_name] = best_value
                else:
                    parameters[param_name] = random.choice(param_space.bounds)
            elif param_space.parameter_type == 'discrete':
                min_val, max_val = param_space.bounds
                # Add noise around best value
                noise = random.randint(-2, 2)
                new_value = max(min_val, min(max_val, best_value + noise))
                parameters[param_name] = new_value
            else:  # continuous
                min_val, max_val = param_space.bounds
                # Add Gaussian noise around best value
                noise_std = (max_val - min_val) * 0.1
                new_value = random.gauss(best_value, noise_std)
                new_value = max(min_val, min(max_val, new_value))
                parameters[param_name] = new_value
        
        return parameters
    
    def _evolve_population(self, population: List[Dict[str, Any]], 
                         scores: List[float], 
                         parameter_space: List[HyperparameterSpace]) -> List[Dict[str, Any]]:
        """Evolve population for genetic algorithm."""
        # Simple evolution: keep top 50%, generate new 50%
        population_size = len(population)
        elite_size = population_size // 2
        
        # Sort by score
        sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)
        
        # Keep elite
        new_population = [population[i] for i in sorted_indices[:elite_size]]
        
        # Generate offspring
        while len(new_population) < population_size:
            # Select parents
            parent1 = population[random.choice(sorted_indices[:elite_size])]
            parent2 = population[random.choice(sorted_indices[:elite_size])]
            
            # Crossover and mutation
            offspring = self._crossover_and_mutate(parent1, parent2, parameter_space)
            new_population.append(offspring)
        
        return new_population
    
    def _crossover_and_mutate(self, parent1: Dict[str, Any], parent2: Dict[str, Any], 
                            parameter_space: List[HyperparameterSpace]) -> Dict[str, Any]:
        """Perform crossover and mutation."""
        offspring = {}
        
        for param_space in parameter_space:
            param_name = param_space.parameter_name
            
            # Crossover: randomly choose from parents
            if random.random() < 0.5:
                offspring[param_name] = parent1[param_name]
            else:
                offspring[param_name] = parent2[param_name]
            
            # Mutation
            if random.random() < 0.1:  # 10% mutation rate
                if param_space.parameter_type == 'categorical':
                    offspring[param_name] = random.choice(param_space.bounds)
                elif param_space.parameter_type == 'discrete':
                    min_val, max_val = param_space.bounds
                    offspring[param_name] = random.randint(min_val, max_val)
                else:  # continuous
                    min_val, max_val = param_space.bounds
                    offspring[param_name] = random.uniform(min_val, max_val)
        
        return offspring
    
    async def _evaluate_parameters(self, parameters: Dict[str, Any]) -> float:
        """Evaluate parameter configuration (simulate model training and validation)."""
        # Simulate model evaluation with some realistic behavior
        
        # Base score
        score = 0.7
        
        # Add some parameter-dependent effects
        if 'n_estimators' in parameters:
            # More estimators generally better, but with diminishing returns
            n_est = parameters['n_estimators']
            score += min(0.15, n_est / 1000)
        
        if 'learning_rate' in parameters:
            # Optimal learning rate around 0.1
            lr = parameters['learning_rate']
            score += 0.1 * (1 - abs(lr - 0.1) / 0.1)
        
        if 'max_depth' in parameters:
            # Moderate depth is usually best
            depth = parameters['max_depth']
            optimal_depth = 10
            score += 0.1 * (1 - abs(depth - optimal_depth) / optimal_depth)
        
        # Add some noise to simulate real evaluation
        noise = random.gauss(0, 0.05)
        score += noise
        
        # Ensure score is in reasonable range
        score = max(0.0, min(1.0, score))
        
        # Simulate evaluation time
        await asyncio.sleep(0.01)
        
        return score
    
    async def get_optimization_history(self) -> Dict[str, Any]:
        """Get optimization history."""
        return {
            'total_optimizations': len(self.optimization_history),
            'optimizations': [
                {
                    'optimization_id': opt['optimization_id'],
                    'model_type': opt['model_type'],
                    'method': opt['method'],
                    'best_score': opt['result'].best_score,
                    'total_evaluations': opt['result'].total_evaluations,
                    'optimization_time': opt['result'].optimization_time,
                    'timestamp': opt['timestamp']
                }
                for opt in self.optimization_history
            ]
        }
