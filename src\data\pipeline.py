"""Data Pipeline for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class DataPipeline:
    """Data processing pipeline for water management system."""
    
    def __init__(self):
        self.pipeline_stages = [
            'ingestion',
            'validation',
            'transformation',
            'enrichment',
            'storage'
        ]
        self.processed_records = 0
        self.error_count = 0
        self.is_running = False
    
    async def start_pipeline(self):
        """Start the data pipeline."""
        self.is_running = True
        logger.info("Data pipeline started")
        return True
    
    async def stop_pipeline(self):
        """Stop the data pipeline."""
        self.is_running = False
        logger.info("Data pipeline stopped")
        return True
    
    async def process_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process data through the pipeline."""
        try:
            if not self.is_running:
                await self.start_pipeline()
            
            # Stage 1: Ingestion
            ingested_data = await self._ingest_data(data)
            
            # Stage 2: Validation
            validated_data = await self._validate_data(ingested_data)
            
            # Stage 3: Transformation
            transformed_data = await self._transform_data(validated_data)
            
            # Stage 4: Enrichment
            enriched_data = await self._enrich_data(transformed_data)
            
            # Stage 5: Storage
            storage_result = await self._store_data(enriched_data)
            
            self.processed_records += 1
            
            return {
                'status': 'success',
                'processed_data': enriched_data,
                'storage_result': storage_result,
                'pipeline_id': f"pipeline_{self.processed_records}"
            }
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Pipeline processing failed: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'stage': 'unknown'
            }
    
    async def _ingest_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Ingest raw data."""
        ingested = {
            'raw_data': data,
            'ingestion_timestamp': datetime.now().isoformat(),
            'source': data.get('source', 'unknown'),
            'data_type': data.get('type', 'sensor_reading')
        }
        
        logger.debug("Data ingested successfully")
        return ingested
    
    async def _validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data quality and format."""
        raw_data = data['raw_data']
        
        # Basic validation rules
        validation_results = {
            'is_valid': True,
            'validation_errors': [],
            'validation_warnings': []
        }
        
        # Check required fields
        required_fields = ['timestamp', 'sensor_id']
        for field in required_fields:
            if field not in raw_data:
                validation_results['validation_errors'].append(f"Missing required field: {field}")
                validation_results['is_valid'] = False
        
        # Validate data ranges (example for water quality)
        if 'ph' in raw_data:
            ph_value = raw_data['ph']
            if not (0 <= ph_value <= 14):
                validation_results['validation_errors'].append(f"pH value out of range: {ph_value}")
                validation_results['is_valid'] = False
        
        if 'turbidity' in raw_data:
            turbidity = raw_data['turbidity']
            if turbidity < 0:
                validation_results['validation_errors'].append(f"Negative turbidity value: {turbidity}")
                validation_results['is_valid'] = False
        
        data['validation'] = validation_results
        logger.debug(f"Data validation completed: {'PASSED' if validation_results['is_valid'] else 'FAILED'}")
        
        return data
    
    async def _transform_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform data into standardized format."""
        raw_data = data['raw_data']
        
        # Standardize timestamp format
        if 'timestamp' in raw_data:
            try:
                # Ensure ISO format
                if isinstance(raw_data['timestamp'], str):
                    timestamp = datetime.fromisoformat(raw_data['timestamp'].replace('Z', '+00:00'))
                else:
                    timestamp = raw_data['timestamp']
                raw_data['timestamp'] = timestamp.isoformat()
            except Exception as e:
                logger.warning(f"Timestamp transformation failed: {e}")
        
        # Normalize sensor readings
        if 'ph' in raw_data:
            raw_data['ph'] = round(float(raw_data['ph']), 2)
        
        if 'turbidity' in raw_data:
            raw_data['turbidity'] = round(float(raw_data['turbidity']), 2)
        
        if 'temperature' in raw_data:
            raw_data['temperature'] = round(float(raw_data['temperature']), 1)
        
        data['transformed_data'] = raw_data
        logger.debug("Data transformation completed")
        
        return data
    
    async def _enrich_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich data with additional context and metadata."""
        enriched_data = data['transformed_data'].copy()
        
        # Add processing metadata
        enriched_data['processing_metadata'] = {
            'processed_at': datetime.now().isoformat(),
            'pipeline_version': '1.0.0',
            'processing_id': f"proc_{self.processed_records + 1}"
        }
        
        # Add quality scores
        if 'ph' in enriched_data and 'turbidity' in enriched_data:
            ph = enriched_data['ph']
            turbidity = enriched_data['turbidity']
            
            # Simple quality score calculation
            ph_score = 1.0 - abs(ph - 7.0) / 7.0  # Optimal pH around 7
            turbidity_score = max(0, 1.0 - turbidity / 10.0)  # Lower turbidity is better
            
            enriched_data['quality_score'] = round((ph_score + turbidity_score) / 2, 3)
        
        # Add location context if available
        if 'sensor_id' in enriched_data:
            sensor_id = enriched_data['sensor_id']
            # Simulate location lookup
            location_map = {
                'sensor_001': {'plant': 'Plant A', 'zone': 'Intake'},
                'sensor_002': {'plant': 'Plant A', 'zone': 'Treatment'},
                'sensor_003': {'plant': 'Plant B', 'zone': 'Distribution'}
            }
            
            if sensor_id in location_map:
                enriched_data['location'] = location_map[sensor_id]
        
        data['enriched_data'] = enriched_data
        logger.debug("Data enrichment completed")
        
        return data
    
    async def _store_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Store processed data."""
        enriched_data = data['enriched_data']
        
        # Simulate database storage
        storage_result = {
            'storage_id': f"storage_{self.processed_records + 1}",
            'table': 'sensor_readings',
            'stored_at': datetime.now().isoformat(),
            'record_count': 1,
            'storage_status': 'success'
        }
        
        logger.debug(f"Data stored successfully: {storage_result['storage_id']}")
        
        return storage_result
    
    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get pipeline statistics."""
        return {
            'is_running': self.is_running,
            'processed_records': self.processed_records,
            'error_count': self.error_count,
            'success_rate': (self.processed_records / (self.processed_records + self.error_count) * 100) if (self.processed_records + self.error_count) > 0 else 0,
            'pipeline_stages': self.pipeline_stages,
            'uptime': '99.5%'  # Simulated
        }
    
    async def process_batch(self, data_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process batch of data records."""
        results = []
        successful = 0
        failed = 0
        
        for data_item in data_batch:
            result = await self.process_data(data_item)
            results.append(result)
            
            if result['status'] == 'success':
                successful += 1
            else:
                failed += 1
        
        return {
            'batch_results': results,
            'total_records': len(data_batch),
            'successful': successful,
            'failed': failed,
            'batch_id': f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
