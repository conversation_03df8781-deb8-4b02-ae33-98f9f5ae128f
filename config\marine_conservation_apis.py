#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marine Conservation API Configuration
Centralized configuration for all marine conservation and environmental monitoring APIs
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum


class APIAuthType(Enum):
    """API Authentication types"""
    API_KEY = "api_key"
    OAUTH2 = "oauth2"
    USERNAME_PASSWORD = "username_password"
    TOKEN = "token"
    OPEN_ACCESS = "open_access"
    USER_AGENT = "user_agent"


@dataclass
class APIConfig:
    """API Configuration dataclass"""
    name: str
    base_url: str
    auth_type: APIAuthType
    api_key: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    token: Optional[str] = None
    user_agent: Optional[str] = None
    description: str = ""
    documentation_url: str = ""
    rate_limit: Optional[str] = None
    data_format: str = "JSON"
    open_source: bool = False


class MarineConservationAPIs:
    """
    Centralized configuration for Marine Conservation APIs
    
    This class manages all API configurations for the marine conservation
    integration, including authentication details, endpoints, and usage information.
    """
    
    def __init__(self):
        """Initialize API configurations"""
        self.apis = self._initialize_api_configs()
    
    def _initialize_api_configs(self) -> Dict[str, APIConfig]:
        """Initialize all API configurations"""
        
        return {
            # Satellite Imagery APIs
            "sentinel_hub": APIConfig(
                name="Sentinel Hub API",
                base_url="https://services.sentinel-hub.com",
                auth_type=APIAuthType.OAUTH2,
                client_id="91e73709-3e73-4525-9cfc-957258864901",
                client_secret=os.getenv("SENTINEL_HUB_CLIENT_SECRET", ""),
                description="Real-time satellite imagery for marine debris detection",
                documentation_url="https://docs.sentinel-hub.com/api/latest/",
                rate_limit="1000 requests/month (free tier)",
                data_format="GeoTIFF, PNG, JPEG",
                open_source=False
            ),
            
            "planet_labs": APIConfig(
                name="Planet Labs API",
                base_url="https://api.planet.com",
                auth_type=APIAuthType.API_KEY,
                api_key="PLAKf8364d269a8d4764816d44ace4f14977",
                description="High-resolution Earth imagery for marine monitoring",
                documentation_url="https://developers.planet.com/docs/",
                rate_limit="Varies by subscription",
                data_format="GeoTIFF, PNG, JPEG",
                open_source=False
            ),
            
            # NASA APIs
            "nasa_open_data": APIConfig(
                name="NASA Open Data APIs",
                base_url="https://api.nasa.gov",
                auth_type=APIAuthType.API_KEY,
                api_key="AxwO7eGNcFT3TOZ3H4AVEx8OsaTwzxorspqxWlkL",
                description="Large-scale environmental monitoring and satellite data",
                documentation_url="https://api.nasa.gov/",
                rate_limit="1000 requests/hour",
                data_format="JSON, XML, HDF, NetCDF",
                open_source=False
            ),
            
            # Oceanographic APIs
            "noaa_ocean_service": APIConfig(
                name="NOAA Ocean Service API",
                base_url="https://api.weather.gov",
                auth_type=APIAuthType.USER_AGENT,
                user_agent="WaterManagementSystem/1.0 (<EMAIL>)",
                description="Oceanographic and weather data",
                documentation_url="https://www.weather.gov/documentation/services-web-api",
                rate_limit="No official limit (fair use)",
                data_format="JSON, XML",
                open_source=False
            ),
            
            "copernicus_marine": APIConfig(
                name="Copernicus Marine Service API",
                base_url="https://marine.copernicus.eu",
                auth_type=APIAuthType.USERNAME_PASSWORD,
                username=os.getenv("COPERNICUS_USERNAME", ""),
                password=os.getenv("COPERNICUS_PASSWORD", ""),
                description="Comprehensive marine environmental data",
                documentation_url="https://marine.copernicus.eu/services/user-learning-services",
                rate_limit="Varies by subscription",
                data_format="NetCDF, GRIB",
                open_source=False
            ),
            
            # Maritime Traffic APIs
            "aisstream_io": APIConfig(
                name="AISStream.io API",
                base_url="https://stream.aisstream.io",
                auth_type=APIAuthType.API_KEY,
                api_key="989d91ab25d59efbe59279756d4b355f5b79c4c8",
                description="Real-time vessel movement and maritime activity data",
                documentation_url="https://aisstream.io/documentation",
                rate_limit="Varies by subscription",
                data_format="JSON, WebSocket",
                open_source=False
            ),
            
            # Open Access APIs
            "openstreetmap_overpass": APIConfig(
                name="OpenStreetMap Overpass API",
                base_url="https://overpass-api.de/api",
                auth_type=APIAuthType.OPEN_ACCESS,
                description="Coastal infrastructure and geographic data",
                documentation_url="https://wiki.openstreetmap.org/wiki/Overpass_API",
                rate_limit="Fair use policy",
                data_format="XML, JSON",
                open_source=True
            ),
            
            # Taiwan Government APIs
            "taiwan_epa": APIConfig(
                name="Taiwan EPA APIs",
                base_url="https://data.epa.gov.tw",
                auth_type=APIAuthType.OPEN_ACCESS,
                description="Environmental monitoring data from Taiwan EPA",
                documentation_url="https://data.gov.tw/",
                rate_limit="No official limit",
                data_format="JSON, XML, CSV",
                open_source=False  # Data is open, but API service is not open source
            ),
            
            "taiwan_ocean_affairs": APIConfig(
                name="Taiwan Ocean Affairs Council APIs",
                base_url="https://www.oac.gov.tw",
                auth_type=APIAuthType.OPEN_ACCESS,
                description="Marine policy and program data",
                documentation_url="https://www.oac.gov.tw/",
                rate_limit="No official limit",
                data_format="JSON, XML",
                open_source=False
            )
        }
    
    def get_api_config(self, api_name: str) -> Optional[APIConfig]:
        """Get configuration for a specific API"""
        return self.apis.get(api_name)
    
    def get_all_apis(self) -> Dict[str, APIConfig]:
        """Get all API configurations"""
        return self.apis
    
    def get_apis_by_auth_type(self, auth_type: APIAuthType) -> Dict[str, APIConfig]:
        """Get APIs filtered by authentication type"""
        return {
            name: config for name, config in self.apis.items()
            if config.auth_type == auth_type
        }
    
    def get_open_source_apis(self) -> Dict[str, APIConfig]:
        """Get only open source APIs"""
        return {
            name: config for name, config in self.apis.items()
            if config.open_source
        }
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """Validate that all required API keys are present"""
        validation_results = {}
        
        for name, config in self.apis.items():
            if config.auth_type == APIAuthType.API_KEY:
                validation_results[name] = bool(config.api_key)
            elif config.auth_type == APIAuthType.OAUTH2:
                validation_results[name] = bool(config.client_id and config.client_secret)
            elif config.auth_type == APIAuthType.USERNAME_PASSWORD:
                validation_results[name] = bool(config.username and config.password)
            elif config.auth_type == APIAuthType.TOKEN:
                validation_results[name] = bool(config.token)
            elif config.auth_type == APIAuthType.USER_AGENT:
                validation_results[name] = bool(config.user_agent)
            else:  # OPEN_ACCESS
                validation_results[name] = True
        
        return validation_results
    
    def get_api_summary(self) -> Dict[str, Any]:
        """Get summary of all configured APIs"""
        summary = {
            "total_apis": len(self.apis),
            "by_auth_type": {},
            "open_source_count": len(self.get_open_source_apis()),
            "validation_status": self.validate_api_keys()
        }
        
        # Count by authentication type
        for auth_type in APIAuthType:
            count = len(self.get_apis_by_auth_type(auth_type))
            if count > 0:
                summary["by_auth_type"][auth_type.value] = count
        
        return summary


# Global instance for easy access
marine_apis = MarineConservationAPIs()


def get_api_config(api_name: str) -> Optional[APIConfig]:
    """Convenience function to get API configuration"""
    return marine_apis.get_api_config(api_name)


def validate_all_apis() -> bool:
    """Validate all API configurations"""
    validation_results = marine_apis.validate_api_keys()
    return all(validation_results.values())


if __name__ == "__main__":
    # Test the configuration
    print("🌊 Marine Conservation APIs Configuration")
    print("=" * 50)
    
    summary = marine_apis.get_api_summary()
    print(f"Total APIs configured: {summary['total_apis']}")
    print(f"Open source APIs: {summary['open_source_count']}")
    
    print("\nAuthentication types:")
    for auth_type, count in summary['by_auth_type'].items():
        print(f"  {auth_type}: {count} APIs")
    
    print("\nValidation status:")
    for api_name, is_valid in summary['validation_status'].items():
        status = "✅" if is_valid else "❌"
        print(f"  {status} {api_name}")
    
    print(f"\nOverall validation: {'✅ PASS' if validate_all_apis() else '❌ FAIL'}")
