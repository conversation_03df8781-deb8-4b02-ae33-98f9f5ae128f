/**
 * API Routes
 * Handles API endpoints and proxy requests to backend
 */

const express = require('express');
const axios = require('axios');
const router = express.Router();

// Configuration
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8001';

// API client for backend communication
class BackendAPIClient {
  constructor(baseURL) {
    this.client = axios.create({
      baseURL: baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'WaterManagement-Frontend/1.0.0'
      }
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error(`❌ API Response Error: ${error.response?.status} ${error.config?.url}`, error.message);
        return Promise.reject(error);
      }
    );
  }

  async get(endpoint) {
    try {
      const response = await this.client.get(endpoint);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async post(endpoint, data) {
    try {
      const response = await this.client.post(endpoint, data);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  handleError(error) {
    if (error.response) {
      // Server responded with error status
      return {
        status: 'error',
        message: error.response.data?.message || 'Backend API error',
        statusCode: error.response.status,
        data: error.response.data
      };
    } else if (error.request) {
      // Request made but no response received
      return {
        status: 'error',
        message: 'Backend API unavailable',
        statusCode: 503,
        data: null
      };
    } else {
      // Something else happened
      return {
        status: 'error',
        message: error.message || 'Unknown API error',
        statusCode: 500,
        data: null
      };
    }
  }
}

const backendAPI = new BackendAPIClient(BACKEND_API_URL);

// Middleware to handle API errors
const handleAPIResponse = (req, res, next) => {
  res.apiSuccess = (data, message = 'Success') => {
    res.json({
      status: 'success',
      message,
      data,
      timestamp: new Date().toISOString()
    });
  };

  res.apiError = (error, statusCode = 500) => {
    console.error('API Error:', error);
    res.status(statusCode).json({
      status: 'error',
      message: error.message || 'Internal server error',
      timestamp: new Date().toISOString(),
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
  };

  next();
};

router.use(handleAPIResponse);

// Health check
router.get('/health', async (req, res) => {
  try {
    const backendHealth = await backendAPI.get('/api/health');
    res.apiSuccess({
      frontend: {
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage()
      },
      backend: backendHealth
    }, 'System health check');
  } catch (error) {
    res.apiError(error, 503);
  }
});

// Climate data endpoints
router.get('/climate/current', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/climate/current');
    res.apiSuccess(data, 'Current climate data retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Water quality endpoints
router.get('/water-quality', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/water-quality');
    res.apiSuccess(data, 'Water quality data retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Energy grid endpoints
router.get('/energy/grid', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/energy/grid');
    res.apiSuccess(data, 'Energy grid data retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// AI agents endpoints
router.get('/ai/agents', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/ai/agents');
    res.apiSuccess(data, 'AI agents status retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// AI chat endpoint
router.post('/ai/chat', async (req, res) => {
  try {
    const { message } = req.body;
    if (!message) {
      return res.apiError(new Error('Message is required'), 400);
    }

    const data = await backendAPI.post('/api/ai/chat', { message });
    res.apiSuccess(data, 'AI chat response generated');
  } catch (error) {
    res.apiError(error);
  }
});

// ML optimization endpoints
router.get('/ml/optimization', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/ml/optimization');
    res.apiSuccess(data, 'ML optimization results retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Sensor network endpoints
router.get('/sensors/network', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/sensors/network');
    res.apiSuccess(data, 'Sensor network status retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Treatment systems endpoints
router.get('/treatment/systems', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/treatment/systems');
    res.apiSuccess(data, 'Treatment systems status retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Analytics KPIs endpoints
router.get('/analytics/kpis', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/analytics/kpis');
    res.apiSuccess(data, 'Analytics KPIs retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Blockchain endpoints
router.get('/blockchain/status', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/blockchain/status');
    res.apiSuccess(data, 'Blockchain status retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Predictive maintenance endpoints
router.get('/maintenance/predictions', async (req, res) => {
  try {
    const data = await backendAPI.get('/api/maintenance/predictions');
    res.apiSuccess(data, 'Maintenance predictions retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Real-time data aggregation endpoint
router.get('/realtime/dashboard', async (req, res) => {
  try {
    // Fetch multiple data sources in parallel
    const [climate, waterQuality, energy, aiAgents, sensors] = await Promise.all([
      backendAPI.get('/api/climate/current').catch(() => null),
      backendAPI.get('/api/water-quality').catch(() => null),
      backendAPI.get('/api/energy/grid').catch(() => null),
      backendAPI.get('/api/ai/agents').catch(() => null),
      backendAPI.get('/api/sensors/network').catch(() => null)
    ]);

    res.apiSuccess({
      climate,
      waterQuality,
      energy,
      aiAgents,
      sensors,
      lastUpdate: new Date().toISOString()
    }, 'Real-time dashboard data retrieved');
  } catch (error) {
    res.apiError(error);
  }
});

// Generic proxy endpoint for any backend API call
router.all('/proxy/*', async (req, res) => {
  try {
    const endpoint = req.params[0];
    const method = req.method.toLowerCase();

    let data;
    if (method === 'get') {
      data = await backendAPI.get(`/api/${endpoint}`);
    } else if (method === 'post') {
      data = await backendAPI.post(`/api/${endpoint}`, req.body);
    } else {
      return res.apiError(new Error(`Method ${method} not supported`), 405);
    }

    res.apiSuccess(data, `Proxy request to ${endpoint} completed`);
  } catch (error) {
    res.apiError(error);
  }
});

module.exports = router;
