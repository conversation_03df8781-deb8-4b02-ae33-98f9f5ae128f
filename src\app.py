"""
Streamlit Dashboard for Water Management Decarbonisation System

This module provides an interactive web dashboard for visualizing and controlling
the water treatment optimization system.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Configure Streamlit page
st.set_page_config(
    page_title="Water Management Decarbonisation System",
    page_icon="💧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    .status-healthy { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-error { background-color: #dc3545; }
</style>
""", unsafe_allow_html=True)


class WaterManagementDashboard:
    """Main dashboard class for the water management system."""
    
    def __init__(self):
        self.setup_logging()
        self.initialize_session_state()
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables."""
        if 'system_status' not in st.session_state:
            st.session_state.system_status = 'healthy'
        
        if 'last_update' not in st.session_state:
            st.session_state.last_update = datetime.now()
        
        if 'selected_location' not in st.session_state:
            st.session_state.selected_location = 'New York'
    
    def render_header(self):
        """Render the main dashboard header."""
        st.markdown('<h1 class="main-header">💧 Water Management Decarbonisation System</h1>', 
                   unsafe_allow_html=True)
        
        # System status indicator
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            status_color = {
                'healthy': 'status-healthy',
                'warning': 'status-warning',
                'error': 'status-error'
            }.get(st.session_state.system_status, 'status-error')
            
            st.markdown(f"""
                <div style="text-align: center; margin-bottom: 1rem;">
                    <span class="status-indicator {status_color}"></span>
                    System Status: {st.session_state.system_status.title()}
                    <br>
                    <small>Last Updated: {st.session_state.last_update.strftime('%Y-%m-%d %H:%M:%S')}</small>
                </div>
            """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render the sidebar with controls and settings."""
        st.sidebar.title("🎛️ Control Panel")
        
        # Location selection
        locations = ['New York', 'London', 'Tokyo', 'Sydney', 'Mumbai']
        st.session_state.selected_location = st.sidebar.selectbox(
            "Select Location",
            locations,
            index=locations.index(st.session_state.selected_location)
        )
        
        # Time range selection
        time_range = st.sidebar.selectbox(
            "Time Range",
            ["Last 24 Hours", "Last 7 Days", "Last 30 Days", "Custom Range"]
        )
        
        # System controls
        st.sidebar.subheader("🔧 System Controls")
        
        if st.sidebar.button("🔄 Refresh Data"):
            self.refresh_data()
        
        if st.sidebar.button("🚀 Run Optimization"):
            self.run_optimization()
        
        if st.sidebar.button("📊 Generate Report"):
            self.generate_report()
        
        # Agent status
        st.sidebar.subheader("🤖 AI Agents Status")
        self.render_agent_status()
        
        # Settings
        st.sidebar.subheader("⚙️ Settings")
        auto_refresh = st.sidebar.checkbox("Auto Refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 300, 60)
        
        return time_range, auto_refresh, refresh_interval
    
    def render_agent_status(self):
        """Render the status of AI agents."""
        agents = {
            "Climate Analysis": "healthy",
            "Water Treatment": "healthy",
            "Energy Efficiency": "warning",
            "Sustainability": "healthy",
            "Risk Analysis": "healthy"
        }
        
        for agent, status in agents.items():
            status_color = {
                'healthy': '🟢',
                'warning': '🟡',
                'error': '🔴'
            }.get(status, '🔴')
            
            st.sidebar.text(f"{status_color} {agent}")
    
    def render_metrics_overview(self):
        """Render key metrics overview."""
        st.subheader("📊 Key Performance Indicators")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="Energy Efficiency",
                value="87.3%",
                delta="2.1%",
                help="Current energy efficiency of the water treatment system"
            )
        
        with col2:
            st.metric(
                label="Carbon Footprint",
                value="1.2 tons CO₂",
                delta="-0.3 tons",
                delta_color="inverse",
                help="Monthly carbon footprint reduction"
            )
        
        with col3:
            st.metric(
                label="Water Recovery Rate",
                value="94.7%",
                delta="1.8%",
                help="Percentage of water successfully treated and recovered"
            )
        
        with col4:
            st.metric(
                label="System Uptime",
                value="99.2%",
                delta="0.1%",
                help="System availability and operational uptime"
            )
    
    def render_climate_data_visualization(self):
        """Render climate data visualizations."""
        st.subheader("🌡️ Climate Data Analysis")
        
        # Generate sample climate data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='H')
        climate_data = pd.DataFrame({
            'timestamp': dates,
            'temperature': 20 + 10 * pd.Series(range(len(dates))).apply(lambda x: 0.5 * (x % 24) / 12),
            'humidity': 50 + 20 * pd.Series(range(len(dates))).apply(lambda x: 0.3 * ((x + 12) % 24) / 12),
            'precipitation': pd.Series(range(len(dates))).apply(lambda x: max(0, 5 * (x % 168) / 168 - 2))
        })
        
        # Create subplots
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('Temperature (°C)', 'Humidity (%)', 'Precipitation (mm)'),
            vertical_spacing=0.08
        )
        
        # Temperature
        fig.add_trace(
            go.Scatter(x=climate_data['timestamp'], y=climate_data['temperature'],
                      name='Temperature', line=dict(color='red')),
            row=1, col=1
        )
        
        # Humidity
        fig.add_trace(
            go.Scatter(x=climate_data['timestamp'], y=climate_data['humidity'],
                      name='Humidity', line=dict(color='blue')),
            row=2, col=1
        )
        
        # Precipitation
        fig.add_trace(
            go.Bar(x=climate_data['timestamp'], y=climate_data['precipitation'],
                   name='Precipitation', marker_color='lightblue'),
            row=3, col=1
        )
        
        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    def render_optimization_results(self):
        """Render optimization results and recommendations."""
        st.subheader("🎯 Optimization Results")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Current Configuration")
            
            # System configuration
            config_data = {
                'Component': ['Primary Filter', 'Secondary Filter', 'UV Treatment', 'Chemical Dosing'],
                'Status': ['Active', 'Active', 'Standby', 'Active'],
                'Efficiency': [92, 88, 95, 87],
                'Energy Usage (kW)': [15.2, 12.8, 8.5, 6.3]
            }
            
            config_df = pd.DataFrame(config_data)
            st.dataframe(config_df, use_container_width=True)
        
        with col2:
            st.subheader("Optimization Recommendations")
            
            recommendations = [
                "🔧 Adjust primary filter flow rate by 5% to improve efficiency",
                "⚡ Switch UV treatment to active mode during peak hours",
                "🌡️ Optimize chemical dosing based on current temperature",
                "💡 Consider renewable energy integration for 15% carbon reduction"
            ]
            
            for rec in recommendations:
                st.info(rec)
    
    def render_sustainability_metrics(self):
        """Render sustainability and environmental impact metrics."""
        st.subheader("🌱 Sustainability Dashboard")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Carbon footprint over time
            dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
            carbon_data = pd.DataFrame({
                'Date': dates,
                'Carbon Footprint (kg CO₂)': [40 - i * 0.5 + (i % 7) * 2 for i in range(len(dates))]
            })
            
            fig = px.line(carbon_data, x='Date', y='Carbon Footprint (kg CO₂)',
                         title='Daily Carbon Footprint Trend')
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Energy source distribution
            energy_sources = ['Solar', 'Wind', 'Grid (Renewable)', 'Grid (Fossil)']
            energy_values = [35, 25, 30, 10]
            
            fig = px.pie(values=energy_values, names=energy_sources,
                        title='Energy Source Distribution')
            st.plotly_chart(fig, use_container_width=True)
    
    def render_real_time_monitoring(self):
        """Render real-time system monitoring."""
        st.subheader("📡 Real-Time Monitoring")
        
        # Create placeholder for real-time data
        placeholder = st.empty()
        
        # Simulate real-time data
        import time
        import random
        
        with placeholder.container():
            col1, col2, col3 = st.columns(3)
            
            with col1:
                current_temp = 22.5 + random.uniform(-2, 2)
                st.metric("Current Temperature", f"{current_temp:.1f}°C")
            
            with col2:
                flow_rate = 150 + random.uniform(-10, 10)
                st.metric("Flow Rate", f"{flow_rate:.1f} L/min")
            
            with col3:
                pressure = 2.1 + random.uniform(-0.2, 0.2)
                st.metric("System Pressure", f"{pressure:.2f} bar")
    
    def refresh_data(self):
        """Refresh dashboard data."""
        st.session_state.last_update = datetime.now()
        st.success("Data refreshed successfully!")
        st.experimental_rerun()
    
    def run_optimization(self):
        """Run system optimization."""
        with st.spinner("Running optimization..."):
            # Simulate optimization process
            import time
            time.sleep(2)
        
        st.success("Optimization completed! New recommendations available.")
    
    def generate_report(self):
        """Generate system report."""
        with st.spinner("Generating report..."):
            # Simulate report generation
            import time
            time.sleep(1)
        
        st.success("Report generated successfully!")
        
        # Provide download button
        report_data = "Sample report data..."
        st.download_button(
            label="📥 Download Report",
            data=report_data,
            file_name=f"water_management_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain"
        )
    
    def run(self):
        """Run the main dashboard application."""
        try:
            # Render header
            self.render_header()
            
            # Render sidebar and get settings
            time_range, auto_refresh, refresh_interval = self.render_sidebar()
            
            # Main content area
            tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "🌡️ Climate Data", "🎯 Optimization", "📡 Monitoring"])
            
            with tab1:
                self.render_metrics_overview()
                self.render_sustainability_metrics()
            
            with tab2:
                self.render_climate_data_visualization()
            
            with tab3:
                self.render_optimization_results()
            
            with tab4:
                self.render_real_time_monitoring()
            
            # Auto-refresh functionality
            if auto_refresh:
                time.sleep(refresh_interval)
                st.experimental_rerun()
                
        except Exception as e:
            st.error(f"Dashboard error: {str(e)}")
            self.logger.error(f"Dashboard error: {e}")


def main():
    """Main function to run the Streamlit dashboard."""
    dashboard = WaterManagementDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()
