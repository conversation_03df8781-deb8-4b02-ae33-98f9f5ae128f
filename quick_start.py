#!/usr/bin/env python3
"""
Quick Start Script for Unified Environmental Platform
Simple and reliable startup for both backend and frontend
"""

import subprocess
import sys
import os
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_backend():
    """Check if backend is running"""
    try:
        import requests
        response = requests.get('http://localhost:8000/health', timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start backend server"""
    logger.info("🚀 Starting backend server...")
    
    if check_backend():
        logger.info("✅ Backend already running on http://localhost:8000")
        return True
    
    try:
        # Start backend in a new window
        if sys.platform == 'win32':
            subprocess.Popen([
                'cmd', '/c', 'start', 'cmd', '/k',
                f'{sys.executable} -m uvicorn src.api.unified_api:app --host 0.0.0.0 --port 8000 --reload'
            ], shell=True)
        else:
            subprocess.Popen([
                sys.executable, '-m', 'uvicorn',
                'src.api.unified_api:app',
                '--host', '0.0.0.0',
                '--port', '8000',
                '--reload'
            ])
        
        logger.info("✅ Backend server starting...")
        
        # Wait for backend to be ready
        for i in range(20):  # Wait up to 60 seconds
            time.sleep(3)
            if check_backend():
                logger.info("✅ Backend is ready!")
                return True
            logger.info(f"⏳ Waiting for backend... ({i+1}/20)")
        
        logger.error("❌ Backend failed to start")
        return False
        
    except Exception as e:
        logger.error(f"❌ Failed to start backend: {e}")
        return False

def start_frontend():
    """Start frontend server"""
    logger.info("🎨 Starting frontend server...")
    
    frontend_path = "frontend"
    if not os.path.exists(frontend_path):
        logger.error("❌ Frontend directory not found")
        return False
    
    try:
        # Check if node_modules exists
        if not os.path.exists(os.path.join(frontend_path, "node_modules")):
            logger.info("📦 Installing frontend dependencies...")
            result = subprocess.run(['npm', 'install'], cwd=frontend_path, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"❌ npm install failed: {result.stderr}")
                return False
        
        # Start frontend in a new window
        env = os.environ.copy()
        env['REACT_APP_API_URL'] = 'http://localhost:8000'
        env['REACT_APP_WS_URL'] = 'ws://localhost:8000'
        env['BROWSER'] = 'none'  # Don't auto-open browser
        
        if sys.platform == 'win32':
            subprocess.Popen([
                'cmd', '/c', 'start', 'cmd', '/k',
                'npm start'
            ], cwd=frontend_path, env=env, shell=True)
        else:
            subprocess.Popen(['npm', 'start'], cwd=frontend_path, env=env)
        
        logger.info("✅ Frontend server starting...")
        time.sleep(5)  # Give it time to start
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to start frontend: {e}")
        return False

def main():
    """Main startup function"""
    logger.info("🌊💧 Quick Start - Unified Environmental Platform")
    logger.info("=" * 60)
    
    # Start backend
    if not start_backend():
        logger.error("❌ Failed to start backend")
        return 1
    
    # Start frontend
    if not start_frontend():
        logger.error("❌ Failed to start frontend")
        return 1
    
    logger.info("")
    logger.info("🎉 Unified Environmental Platform is starting!")
    logger.info("📊 Frontend: http://localhost:3000")
    logger.info("🔌 Backend API: http://localhost:8000")
    logger.info("📚 API Docs: http://localhost:8000/docs")
    logger.info("")
    logger.info("⏳ Please wait a moment for the frontend to fully load...")
    logger.info("🌐 Then open your browser to: http://localhost:3000")
    logger.info("")
    logger.info("💡 Both servers are running in separate windows.")
    logger.info("💡 Close those windows to stop the servers.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
