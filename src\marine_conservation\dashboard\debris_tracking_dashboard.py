#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real-Time AI Debris Tracking Dashboard
Task 2.26: Interactive dashboard with open-source API integration
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import logging

# Import marine conservation components
from ..apis.sentinel_hub_api import detect_marine_debris_area, BoundingBox
from ..apis.aisstream_api import get_maritime_traffic_data
from ..ai_algorithms.multi_source_intelligence import generate_marine_intelligence
from ..data_validation import validate_marine_conservation_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DashboardMetrics:
    """Real-time dashboard metrics"""
    total_debris_detected: int
    active_cleanup_operations: int
    vessels_monitored: int
    areas_under_surveillance: int
    alert_level: str
    last_update: datetime
    data_quality_score: float


@dataclass
class DebrisAlert:
    """Real-time debris alert"""
    alert_id: str
    alert_type: str  # "high_concentration", "new_hotspot", "vessel_correlation"
    location: Tuple[float, float]
    severity: str  # "low", "medium", "high", "critical"
    description: str
    recommended_actions: List[str]
    timestamp: datetime
    auto_generated: bool


@dataclass
class DashboardData:
    """Complete dashboard data structure"""
    metrics: DashboardMetrics
    debris_detections: List[Dict[str, Any]]
    vessel_data: List[Dict[str, Any]]
    alerts: List[DebrisAlert]
    hotspots: List[Dict[str, Any]]
    intelligence_summary: Dict[str, Any]
    map_layers: Dict[str, Any]
    timestamp: datetime


class DebrisTrackingDashboard:
    """Debris tracking dashboard (alias)"""

    def __init__(self):
        self.dashboard = RealTimeDebrisDashboard()

    async def generate_dashboard_data(self, area_bbox: Tuple[float, float, float, float]) -> Dict[str, Any]:
        """Generate dashboard data for area"""
        try:
            data = await asyncio.wait_for(
                self.dashboard.generate_real_time_dashboard(area_bbox),
                timeout=10.0
            )
            return {
                'dashboard_id': f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'area': area_bbox,
                'data': data,
                'status': 'success'
            }
        except asyncio.TimeoutError:
            return {
                'dashboard_id': f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'area': area_bbox,
                'data': {},
                'status': 'timeout',
                'error': 'Dashboard generation timed out'
            }
        except Exception as e:
            return {
                'dashboard_id': f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'area': area_bbox,
                'data': {},
                'status': 'error',
                'error': str(e)
            }

class RealTimeDebrisDashboard:
    """Real-time AI debris tracking dashboard"""
    
    def __init__(self):
        self.monitoring_areas = []
        self.active_alerts = []
        self.dashboard_config = {
            'update_interval_seconds': 30,
            'alert_thresholds': {
                'debris_concentration': 5,  # debris per km²
                'vessel_correlation': 0.7,
                'data_quality_minimum': 0.6
            },
            'map_settings': {
                'default_zoom': 8,
                'max_zoom': 18,
                'tile_server': 'OpenStreetMap'
            }
        }
        self.data_cache = {}
        self.last_update = None
    
    async def add_monitoring_area(
        self,
        area_id: str,
        bbox: Tuple[float, float, float, float],
        area_name: str,
        priority: str = "medium"
    ):
        """Add area for real-time monitoring"""
        area = {
            'id': area_id,
            'name': area_name,
            'bbox': bbox,
            'priority': priority,
            'added_at': datetime.now(),
            'last_scan': None,
            'debris_count': 0,
            'alert_count': 0
        }
        self.monitoring_areas.append(area)
        logger.info(f"✅ Added monitoring area: {area_name} ({area_id})")
    
    async def collect_real_time_data(self) -> DashboardData:
        """Collect real-time data from all sources"""
        try:
            logger.info("🔄 Collecting real-time dashboard data")
            
            all_debris = []
            all_vessels = []
            all_intelligence = []
            
            # Collect data from all monitoring areas
            for area in self.monitoring_areas:
                area_bbox = area['bbox']
                
                # Collect debris data
                debris_task = self._collect_debris_data(area_bbox, area['id'])
                vessel_task = self._collect_vessel_data(area_bbox, area['id'])
                intelligence_task = self._collect_intelligence_data(area_bbox, area['id'])
                
                debris_data, vessel_data, intelligence_data = await asyncio.gather(
                    debris_task, vessel_task, intelligence_task, return_exceptions=True
                )
                
                if not isinstance(debris_data, Exception):
                    all_debris.extend(debris_data)
                if not isinstance(vessel_data, Exception):
                    all_vessels.extend(vessel_data)
                if not isinstance(intelligence_data, Exception):
                    all_intelligence.append(intelligence_data)
                
                # Update area statistics
                area['last_scan'] = datetime.now()
                area['debris_count'] = len(debris_data) if not isinstance(debris_data, Exception) else 0
            
            # Generate alerts
            alerts = await self._generate_real_time_alerts(all_debris, all_vessels)
            
            # Calculate metrics
            metrics = self._calculate_dashboard_metrics(all_debris, all_vessels, alerts)
            
            # Identify hotspots
            hotspots = await self._identify_debris_hotspots(all_debris)
            
            # Create intelligence summary
            intelligence_summary = self._create_intelligence_summary(all_intelligence)
            
            # Generate map layers
            map_layers = self._generate_map_layers(all_debris, all_vessels, hotspots)
            
            dashboard_data = DashboardData(
                metrics=metrics,
                debris_detections=all_debris,
                vessel_data=all_vessels,
                alerts=alerts,
                hotspots=hotspots,
                intelligence_summary=intelligence_summary,
                map_layers=map_layers,
                timestamp=datetime.now()
            )
            
            # Cache data
            self.data_cache = asdict(dashboard_data)
            self.last_update = datetime.now()
            
            logger.info(f"✅ Dashboard data collected: {len(all_debris)} debris, {len(all_vessels)} vessels")
            return dashboard_data
            
        except Exception as e:
            logger.error(f"❌ Error collecting dashboard data: {e}")
            return self._create_empty_dashboard()
    
    async def _collect_debris_data(self, bbox: Tuple[float, float, float, float], area_id: str) -> List[Dict[str, Any]]:
        """Collect debris detection data for area"""
        try:
            bbox_obj = BoundingBox(bbox[0], bbox[1], bbox[2], bbox[3])
            debris_detections = await detect_marine_debris_area(bbox_obj, days_back=1)
            
            debris_data = []
            for detection in debris_detections:
                debris_data.append({
                    'id': f"debris_{area_id}_{len(debris_data)}",
                    'location': detection.location,
                    'confidence': detection.confidence,
                    'size_estimate': detection.size_estimate,
                    'debris_type': detection.debris_type,
                    'timestamp': detection.timestamp.isoformat(),
                    'area_id': area_id,
                    'source': 'satellite_detection'
                })
            
            return debris_data
            
        except Exception as e:
            logger.error(f"❌ Error collecting debris data for {area_id}: {e}")
            return []
    
    async def _collect_vessel_data(self, bbox: Tuple[float, float, float, float], area_id: str) -> List[Dict[str, Any]]:
        """Collect vessel tracking data for area"""
        try:
            vessel_traffic = await get_maritime_traffic_data(bbox)
            
            vessel_data = []
            if vessel_traffic.get('vessels'):
                for vessel in vessel_traffic['vessels']:
                    vessel_data.append({
                        'id': vessel.mmsi,
                        'name': vessel.vessel_name,
                        'type': vessel.vessel_type,
                        'location': (vessel.latitude, vessel.longitude),
                        'speed': vessel.speed,
                        'course': vessel.course,
                        'timestamp': vessel.timestamp.isoformat(),
                        'area_id': area_id,
                        'risk_score': self._calculate_vessel_risk(vessel)
                    })
            
            return vessel_data
            
        except Exception as e:
            logger.error(f"❌ Error collecting vessel data for {area_id}: {e}")
            return []
    
    async def _collect_intelligence_data(self, bbox: Tuple[float, float, float, float], area_id: str) -> Dict[str, Any]:
        """Collect intelligence data for area"""
        try:
            intelligence = await generate_marine_intelligence(bbox, time_window_hours=6)
            
            return {
                'area_id': area_id,
                'confidence_score': intelligence.confidence_score,
                'debris_count': len(intelligence.debris_detections),
                'vessel_correlations': len(intelligence.vessel_correlations),
                'risk_level': intelligence.risk_assessment.get('risk_level', 'unknown'),
                'recommendations': intelligence.recommendations[:3]  # Top 3 recommendations
            }
            
        except Exception as e:
            logger.error(f"❌ Error collecting intelligence data for {area_id}: {e}")
            return {'area_id': area_id, 'error': str(e)}
    
    def _calculate_vessel_risk(self, vessel) -> float:
        """Calculate risk score for vessel"""
        risk_score = 0.0
        
        # High-risk vessel types
        if vessel.vessel_type in ['fishing', 'cargo', 'tanker']:
            risk_score += 0.4
        
        # Slow or stationary vessels
        if vessel.speed < 2.0:
            risk_score += 0.3
        
        # Large vessels
        if vessel.length and vessel.length > 100:
            risk_score += 0.2
        
        # Random factor for demonstration
        import random
        risk_score += random.uniform(0, 0.1)
        
        return min(1.0, risk_score)
    
    async def _generate_real_time_alerts(
        self,
        debris_data: List[Dict[str, Any]],
        vessel_data: List[Dict[str, Any]]
    ) -> List[DebrisAlert]:
        """Generate real-time alerts based on current data"""
        alerts = []
        
        # High debris concentration alert
        if len(debris_data) > self.dashboard_config['alert_thresholds']['debris_concentration']:
            alert = DebrisAlert(
                alert_id=f"alert_debris_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                alert_type="high_concentration",
                location=self._calculate_center_location(debris_data),
                severity="high",
                description=f"High debris concentration detected: {len(debris_data)} items",
                recommended_actions=[
                    "Deploy cleanup vessels immediately",
                    "Increase monitoring frequency",
                    "Alert nearby vessels"
                ],
                timestamp=datetime.now(),
                auto_generated=True
            )
            alerts.append(alert)
        
        # High-risk vessel alert
        high_risk_vessels = [v for v in vessel_data if v.get('risk_score', 0) > 0.7]
        if high_risk_vessels:
            alert = DebrisAlert(
                alert_id=f"alert_vessel_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                alert_type="vessel_correlation",
                location=high_risk_vessels[0]['location'],
                severity="medium",
                description=f"{len(high_risk_vessels)} high-risk vessels detected",
                recommended_actions=[
                    "Monitor vessel activities",
                    "Check compliance status",
                    "Prepare for potential debris"
                ],
                timestamp=datetime.now(),
                auto_generated=True
            )
            alerts.append(alert)
        
        # New hotspot alert (simplified)
        if len(debris_data) > 3:
            hotspot_location = self._calculate_center_location(debris_data)
            alert = DebrisAlert(
                alert_id=f"alert_hotspot_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                alert_type="new_hotspot",
                location=hotspot_location,
                severity="medium",
                description="New debris hotspot identified",
                recommended_actions=[
                    "Investigate hotspot formation",
                    "Deploy targeted cleanup",
                    "Monitor for expansion"
                ],
                timestamp=datetime.now(),
                auto_generated=True
            )
            alerts.append(alert)
        
        return alerts
    
    def _calculate_center_location(self, data_points: List[Dict[str, Any]]) -> Tuple[float, float]:
        """Calculate center location of data points"""
        if not data_points:
            return (0.0, 0.0)
        
        lats = [point['location'][0] for point in data_points if 'location' in point]
        lons = [point['location'][1] for point in data_points if 'location' in point]
        
        if not lats or not lons:
            return (0.0, 0.0)
        
        return (sum(lats) / len(lats), sum(lons) / len(lons))
    
    def _calculate_dashboard_metrics(
        self,
        debris_data: List[Dict[str, Any]],
        vessel_data: List[Dict[str, Any]],
        alerts: List[DebrisAlert]
    ) -> DashboardMetrics:
        """Calculate dashboard metrics"""
        
        # Determine alert level
        alert_level = "green"
        if any(alert.severity == "critical" for alert in alerts):
            alert_level = "red"
        elif any(alert.severity == "high" for alert in alerts):
            alert_level = "orange"
        elif any(alert.severity == "medium" for alert in alerts):
            alert_level = "yellow"
        
        # Calculate data quality score (simplified)
        data_quality = 0.8  # Base quality
        if len(debris_data) > 0:
            data_quality += 0.1
        if len(vessel_data) > 0:
            data_quality += 0.1
        
        return DashboardMetrics(
            total_debris_detected=len(debris_data),
            active_cleanup_operations=0,  # Would be tracked separately
            vessels_monitored=len(vessel_data),
            areas_under_surveillance=len(self.monitoring_areas),
            alert_level=alert_level,
            last_update=datetime.now(),
            data_quality_score=min(1.0, data_quality)
        )
    
    async def _identify_debris_hotspots(self, debris_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify debris hotspots using clustering"""
        hotspots = []
        
        if len(debris_data) < 3:
            return hotspots
        
        # Simple clustering based on proximity (would use proper clustering in production)
        processed = set()
        
        for i, debris in enumerate(debris_data):
            if i in processed:
                continue
            
            cluster = [debris]
            cluster_indices = {i}
            
            # Find nearby debris (within 0.01 degrees ~ 1km)
            for j, other_debris in enumerate(debris_data):
                if j in processed or j == i:
                    continue
                
                lat_diff = abs(debris['location'][0] - other_debris['location'][0])
                lon_diff = abs(debris['location'][1] - other_debris['location'][1])
                
                if lat_diff < 0.01 and lon_diff < 0.01:
                    cluster.append(other_debris)
                    cluster_indices.add(j)
            
            # If cluster has enough debris, it's a hotspot
            if len(cluster) >= 3:
                center_lat = sum(d['location'][0] for d in cluster) / len(cluster)
                center_lon = sum(d['location'][1] for d in cluster) / len(cluster)
                
                hotspot = {
                    'id': f"hotspot_{len(hotspots)}",
                    'center': (center_lat, center_lon),
                    'debris_count': len(cluster),
                    'density': len(cluster) / 1.0,  # debris per km²
                    'confidence': min(1.0, len(cluster) / 10.0),
                    'timestamp': datetime.now().isoformat()
                }
                hotspots.append(hotspot)
                processed.update(cluster_indices)
        
        return hotspots
    
    def _create_intelligence_summary(self, intelligence_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create intelligence summary from all areas"""
        if not intelligence_data:
            return {'status': 'no_data'}
        
        valid_data = [d for d in intelligence_data if 'error' not in d]
        
        if not valid_data:
            return {'status': 'error', 'message': 'All intelligence collection failed'}
        
        avg_confidence = sum(d.get('confidence_score', 0) for d in valid_data) / len(valid_data)
        total_debris = sum(d.get('debris_count', 0) for d in valid_data)
        total_correlations = sum(d.get('vessel_correlations', 0) for d in valid_data)
        
        # Aggregate risk levels
        risk_levels = [d.get('risk_level', 'unknown') for d in valid_data]
        highest_risk = 'low'
        if 'critical' in risk_levels:
            highest_risk = 'critical'
        elif 'high' in risk_levels:
            highest_risk = 'high'
        elif 'medium' in risk_levels:
            highest_risk = 'medium'
        
        return {
            'status': 'active',
            'areas_analyzed': len(valid_data),
            'average_confidence': avg_confidence,
            'total_debris_detected': total_debris,
            'total_vessel_correlations': total_correlations,
            'highest_risk_level': highest_risk,
            'summary': f"Analyzed {len(valid_data)} areas with {avg_confidence:.1%} confidence"
        }
    
    def _generate_map_layers(
        self,
        debris_data: List[Dict[str, Any]],
        vessel_data: List[Dict[str, Any]],
        hotspots: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate map layers for dashboard visualization"""
        return {
            'debris_layer': {
                'type': 'point',
                'data': [
                    {
                        'lat': d['location'][0],
                        'lon': d['location'][1],
                        'confidence': d['confidence'],
                        'size': d['size_estimate'],
                        'type': d['debris_type']
                    } for d in debris_data
                ],
                'style': {
                    'color': 'red',
                    'size_scale': 'size_estimate',
                    'opacity': 0.7
                }
            },
            'vessel_layer': {
                'type': 'point',
                'data': [
                    {
                        'lat': v['location'][0],
                        'lon': v['location'][1],
                        'name': v['name'],
                        'type': v['type'],
                        'risk_score': v['risk_score']
                    } for v in vessel_data
                ],
                'style': {
                    'color': 'blue',
                    'size': 8,
                    'opacity': 0.8
                }
            },
            'hotspot_layer': {
                'type': 'circle',
                'data': [
                    {
                        'lat': h['center'][0],
                        'lon': h['center'][1],
                        'radius': h['density'] * 1000,  # Convert to meters
                        'debris_count': h['debris_count']
                    } for h in hotspots
                ],
                'style': {
                    'color': 'orange',
                    'fill_opacity': 0.3,
                    'stroke_opacity': 0.8
                }
            }
        }
    
    def _create_empty_dashboard(self) -> DashboardData:
        """Create empty dashboard data structure"""
        return DashboardData(
            metrics=DashboardMetrics(
                total_debris_detected=0,
                active_cleanup_operations=0,
                vessels_monitored=0,
                areas_under_surveillance=0,
                alert_level="gray",
                last_update=datetime.now(),
                data_quality_score=0.0
            ),
            debris_detections=[],
            vessel_data=[],
            alerts=[],
            hotspots=[],
            intelligence_summary={'status': 'error'},
            map_layers={},
            timestamp=datetime.now()
        )
    
    async def start_real_time_monitoring(self, update_interval_seconds: int = 30):
        """Start real-time monitoring loop"""
        logger.info(f"🔄 Starting real-time monitoring (update every {update_interval_seconds}s)")
        
        while True:
            try:
                dashboard_data = await self.collect_real_time_data()
                
                # Log summary
                metrics = dashboard_data.metrics
                logger.info(f"📊 Dashboard update: {metrics.total_debris_detected} debris, "
                           f"{metrics.vessels_monitored} vessels, "
                           f"alert level: {metrics.alert_level}")
                
                # Wait for next update
                await asyncio.sleep(update_interval_seconds)
                
            except Exception as e:
                logger.error(f"❌ Error in monitoring loop: {e}")
                await asyncio.sleep(update_interval_seconds)

    async def generate_real_time_dashboard(self, area_bbox: Tuple[float, float, float, float]) -> Dict[str, Any]:
        """Generate real-time dashboard for specific area"""
        try:
            # Add area for monitoring if not already added
            area_id = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            await self.add_monitoring_area(area_id, area_bbox, "Temporary Area", "high")

            # Collect data with timeout
            dashboard_data = await asyncio.wait_for(
                self.collect_real_time_data(),
                timeout=8.0
            )

            # Convert to dictionary format
            return asdict(dashboard_data)

        except asyncio.TimeoutError:
            logger.warning("Dashboard data collection timed out")
            return asdict(self._create_empty_dashboard())
        except Exception as e:
            logger.error(f"Error generating real-time dashboard: {e}")
            return asdict(self._create_empty_dashboard())
    
    def get_dashboard_json(self) -> str:
        """Get dashboard data as JSON for web interface"""
        if self.data_cache:
            return json.dumps(self.data_cache, indent=2, default=str)
        else:
            return json.dumps(asdict(self._create_empty_dashboard()), indent=2, default=str)


# Convenience functions
async def create_debris_dashboard(monitoring_areas: List[Dict[str, Any]]) -> RealTimeDebrisDashboard:
    """Create and configure debris tracking dashboard"""
    dashboard = RealTimeDebrisDashboard()
    
    for area in monitoring_areas:
        await dashboard.add_monitoring_area(
            area_id=area['id'],
            bbox=area['bbox'],
            area_name=area['name'],
            priority=area.get('priority', 'medium')
        )
    
    return dashboard


if __name__ == "__main__":
    async def test_dashboard():
        print("📊 Testing Real-Time Debris Tracking Dashboard")
        
        # Configure test monitoring areas
        test_areas = [
            {
                'id': 'med_sea_1',
                'name': 'Mediterranean Sea - Barcelona',
                'bbox': (2.0, 41.0, 3.0, 42.0),
                'priority': 'high'
            },
            {
                'id': 'taiwan_strait',
                'name': 'Taiwan Strait',
                'bbox': (119.0, 23.0, 121.0, 25.0),
                'priority': 'critical'
            }
        ]
        
        try:
            dashboard = await create_debris_dashboard(test_areas)
            
            # Collect one round of data
            dashboard_data = await dashboard.collect_real_time_data()
            
            print("✅ Dashboard data collection completed")
            print(f"   Debris detected: {dashboard_data.metrics.total_debris_detected}")
            print(f"   Vessels monitored: {dashboard_data.metrics.vessels_monitored}")
            print(f"   Alert level: {dashboard_data.metrics.alert_level}")
            print(f"   Hotspots identified: {len(dashboard_data.hotspots)}")
            print(f"   Active alerts: {len(dashboard_data.alerts)}")
            
            # Show sample alerts
            for alert in dashboard_data.alerts[:2]:
                print(f"   Alert: {alert.alert_type} - {alert.severity} - {alert.description}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_dashboard())
