/* Water Management Frontend - Main Styles */

:root {
    --primary-color: #1f77b4;
    --secondary-color: #4ade80;
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-color: #ffffff;
    --text-secondary: #94a3b8;
    --border-color: #334155;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.app-container {
    min-height: 100vh;
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
}

.header {
    grid-area: header;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 1rem;
    justify-content: space-between;
}

.header-left .logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-tabs {
    display: flex;
    gap: 1rem;
}

.nav-tab {
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: 0.25rem;
    transition: all 0.2s;
}

.nav-tab:hover,
.nav-tab.active {
    color: var(--text-color);
    background: var(--primary-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicators {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--error-color);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-icon:hover {
    color: var(--text-color);
    background: var(--surface-color);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
}

.sidebar {
    grid-area: sidebar;
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.sidebar-item:hover {
    background: var(--background-color);
}

.sidebar-item.active {
    background: var(--background-color);
    border-left-color: var(--primary-color);
}

.sidebar-item i {
    margin-right: 0.5rem;
    width: 16px;
}

.sidebar-footer {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    text-align: center;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.main-content {
    grid-area: main;
    padding: 1rem;
    overflow-y: auto;
    background: var(--background-color);
}

.system-status {
    background: var(--surface-color);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-color);
}

.status-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-help,
.btn-settings,
.btn-fullscreen {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s;
}

.btn-help:hover,
.btn-settings:hover,
.btn-fullscreen:hover {
    color: var(--text-color);
    border-color: var(--primary-color);
}

.page-container {
    background: var(--surface-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    min-height: 500px;
}

.page-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
}

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-container {
    text-align: center;
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.progress-bar {
    height: 100%;
    background: var(--primary-color);
    width: 0%;
    transition: width 0.3s ease;
}

.error-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.error-container {
    text-align: center;
    max-width: 500px;
    padding: 2rem;
}

.error-icon {
    font-size: 3rem;
    color: var(--error-color);
    margin-bottom: 1rem;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background: #1e6ba8;
}

.btn-secondary {
    background: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.btn-secondary:hover {
    border-color: var(--primary-color);
}

@media (max-width: 768px) {
    .app-container {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }
    
    .sidebar {
        display: none;
    }
    
    .header-center {
        display: none;
    }
    
    .nav-tabs {
        display: none;
    }
}
