#!/usr/bin/env python3
"""
Simple Frontend Server for Unified Environmental Platform
Serves the HTML frontend on port 3000
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import os
import sys

def start_simple_frontend():
    """Start simple HTML frontend server"""
    PORT = 3000
    
    class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            # Add CORS headers
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def do_GET(self):
            if self.path == '/' or self.path == '/index.html':
                self.path = '/individual_feature_interfaces.html'
            elif self.path == '/dashboards':
                self.path = '/individual_feature_dashboards.html'
            return super().do_GET()
    
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"🎨 Simple Frontend Server starting on http://localhost:{PORT}")
            print(f"📊 Dashboard: http://localhost:{PORT}")
            print(f"🔌 Backend API: http://localhost:8000")
            print(f"📚 API Docs: http://localhost:8000/docs")
            print("")
            print("💡 This is a simple HTML frontend that works immediately!")
            print("💡 Press Ctrl+C to stop the server")
            print("")
            
            # Open browser after a short delay
            def open_browser():
                time.sleep(2)
                webbrowser.open(f'http://localhost:{PORT}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Frontend server stopped")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {PORT} is already in use")
            print("💡 Try stopping other servers or use a different port")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    start_simple_frontend()
