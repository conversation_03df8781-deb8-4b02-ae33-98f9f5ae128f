"""
World Bank Climate Data API Collector for global climate indicators.

This module provides integration with World Bank Climate Change Knowledge Portal
and World Bank Open Data APIs for collecting global climate and development indicators.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


@dataclass
class WorldBankClimateData:
    """World Bank climate data structure."""
    timestamp: datetime
    indicator: str
    country_code: str
    country_name: str
    year: int
    value: Optional[float]
    unit: Optional[str]
    source: str = "worldbank"
    metadata: Dict[str, Any] = None


class WorldBankClimateCollector:
    """
    World Bank Climate Data API collector.
    
    Supports:
    - World Bank Open Data API (climate indicators)
    - Climate Change Knowledge Portal
    - Country-specific climate data
    - Global development indicators related to climate
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_initialized = False
        
        # World Bank API endpoints
        self.endpoints = {
            'indicators': "https://api.worldbank.org/v2/indicator",
            'countries': "https://api.worldbank.org/v2/country",
            'climate_data': "https://climateknowledgeportal.worldbank.org/api",
            'open_data': "https://api.worldbank.org/v2"
        }
        
        # Key climate indicators from World Bank
        self.climate_indicators = {
            'EN.ATM.CO2E.KT': 'CO2 emissions (kt)',
            'EN.ATM.CO2E.PC': 'CO2 emissions (metric tons per capita)',
            'EN.ATM.METH.KT.CE': 'Methane emissions (kt of CO2 equivalent)',
            'EN.ATM.NOXE.KT.CE': 'Nitrous oxide emissions (kt of CO2 equivalent)',
            'EN.ATM.GHGT.KT.CE': 'Total greenhouse gas emissions (kt of CO2 equivalent)',
            'AG.LND.FRST.ZS': 'Forest area (% of land area)',
            'EG.USE.COMM.FO.ZS': 'Fossil fuel energy consumption (% of total)',
            'EG.FEC.RNEW.ZS': 'Renewable energy consumption (% of total final energy consumption)',
            'EN.POP.DNST': 'Population density (people per sq. km of land area)',
            'NY.GDP.MKTP.CD': 'GDP (current US$)',
            'SP.POP.TOTL': 'Population, total'
        }
        
        # Rate limiting
        self.requests_per_minute = 120  # World Bank allows generous rate limits
        self.request_timestamps = []
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the World Bank Climate collector."""
        try:
            logger.info("Initializing World Bank Climate Data collector...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={"User-Agent": "WaterManagement-DecarbonisationSystem/1.0"}
            )
            
            # Test API connection
            test_success = await self._test_api_connection()
            
            if test_success:
                self.is_initialized = True
                logger.info("World Bank Climate Data collector initialized successfully")
                return True
            else:
                logger.warning("World Bank API test failed, but collector initialized for offline use")
                self.is_initialized = True
                return True
                
        except Exception as e:
            logger.error(f"Failed to initialize World Bank collector: {e}")
            return False
    
    async def _test_api_connection(self) -> bool:
        """Test API connection with a simple request."""
        try:
            # Test with countries endpoint (always available)
            url = f"{self.endpoints['countries']}/all"
            params = {"format": "json", "per_page": "1"}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if len(data) >= 2 and isinstance(data[1], list):
                        logger.info("World Bank API test successful")
                        return True
                    else:
                        logger.warning("World Bank API returned unexpected format")
                        return False
                else:
                    logger.warning(f"World Bank API test returned status {response.status}")
                    return False
                    
        except Exception as e:
            logger.warning(f"World Bank API connection test failed: {e}")
            return False
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        now = datetime.now()
        
        # Remove timestamps older than 1 minute
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if now - ts < timedelta(minutes=1)
        ]
        
        # Check if we're at the limit
        if len(self.request_timestamps) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.request_timestamps[0]).total_seconds()
            if sleep_time > 0:
                logger.warning(f"World Bank API rate limit reached, sleeping for {sleep_time:.1f} seconds")
                await asyncio.sleep(sleep_time)
        
        # Add current request timestamp
        self.request_timestamps.append(now)
    
    @cache_decorator(ttl=3600, key_prefix="wb_countries")
    async def get_countries(self) -> List[Dict[str, Any]]:
        """Get list of countries from World Bank API."""
        try:
            if not self.is_initialized:
                return []
            
            await self._check_rate_limit()
            
            url = f"{self.endpoints['countries']}/all"
            params = {"format": "json", "per_page": "300"}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if len(data) >= 2 and isinstance(data[1], list):
                        return data[1]  # Second element contains the actual data
                    return []
                else:
                    logger.error(f"World Bank countries API error {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to get countries: {e}")
            return []
    
    @cache_decorator(ttl=7200, key_prefix="wb_indicator")
    async def get_indicator_data(self, indicator_code: str, country_codes: List[str] = None, 
                               start_year: int = None, end_year: int = None) -> List[WorldBankClimateData]:
        """Get indicator data for specified countries and years."""
        try:
            if not self.is_initialized:
                return []
            
            await self._check_rate_limit()
            
            # Default to major economies if no countries specified
            if not country_codes:
                country_codes = ["USA", "CHN", "JPN", "DEU", "IND", "GBR", "FRA", "BRA", "ITA", "CAN"]
            
            # Default to last 5 years if no years specified
            if not end_year:
                end_year = datetime.now().year - 1  # Last complete year
            if not start_year:
                start_year = end_year - 4  # 5 years of data
            
            countries_str = ";".join(country_codes)
            date_range = f"{start_year}:{end_year}"
            
            url = f"{self.endpoints['open_data']}/country/{countries_str}/indicator/{indicator_code}"
            params = {
                "format": "json",
                "date": date_range,
                "per_page": "1000"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if len(data) >= 2 and isinstance(data[1], list):
                        return self._parse_indicator_data(data[1], indicator_code)
                    return []
                else:
                    logger.error(f"World Bank indicator API error {response.status} for {indicator_code}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to get indicator data for {indicator_code}: {e}")
            return []
    
    def _parse_indicator_data(self, data: List[Dict[str, Any]], indicator_code: str) -> List[WorldBankClimateData]:
        """Parse World Bank indicator data response."""
        parsed_data = []
        
        for item in data:
            if item.get('value') is not None:  # Skip null values
                climate_data = WorldBankClimateData(
                    timestamp=datetime.now(),
                    indicator=indicator_code,
                    country_code=item.get('countryiso3code', ''),
                    country_name=item.get('country', {}).get('value', ''),
                    year=int(item.get('date', 0)),
                    value=float(item.get('value', 0)) if item.get('value') else None,
                    unit=self.climate_indicators.get(indicator_code, 'Unknown unit'),
                    metadata={
                        'indicator_name': self.climate_indicators.get(indicator_code, 'Unknown'),
                        'last_updated': item.get('lastupdated', ''),
                        'decimal': item.get('decimal', 0)
                    }
                )
                parsed_data.append(climate_data)
        
        return parsed_data
    
    async def get_climate_indicators_summary(self, country_codes: List[str] = None) -> Dict[str, Any]:
        """Get comprehensive climate indicators summary."""
        try:
            logger.info("Getting World Bank climate indicators summary...")
            
            # Key indicators for climate analysis
            key_indicators = [
                'EN.ATM.CO2E.PC',  # CO2 emissions per capita
                'EN.ATM.GHGT.KT.CE',  # Total GHG emissions
                'EG.FEC.RNEW.ZS',  # Renewable energy consumption
                'AG.LND.FRST.ZS'   # Forest area
            ]
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "indicators": {},
                "countries_analyzed": country_codes or ["USA", "CHN", "JPN", "DEU", "IND"],
                "data_availability": {}
            }
            
            for indicator in key_indicators:
                try:
                    data = await self.get_indicator_data(indicator, country_codes)
                    
                    if data:
                        summary["indicators"][indicator] = {
                            "name": self.climate_indicators.get(indicator, "Unknown"),
                            "data_points": len(data),
                            "latest_year": max(d.year for d in data),
                            "countries_with_data": len(set(d.country_code for d in data)),
                            "sample_data": [
                                {
                                    "country": d.country_name,
                                    "year": d.year,
                                    "value": d.value
                                }
                                for d in data[:5]  # First 5 data points as sample
                            ]
                        }
                        summary["data_availability"][indicator] = True
                    else:
                        summary["data_availability"][indicator] = False
                    
                    # Small delay between requests
                    await asyncio.sleep(0.2)
                    
                except Exception as e:
                    logger.warning(f"Failed to get data for indicator {indicator}: {e}")
                    summary["data_availability"][indicator] = False
            
            # Calculate overall data availability
            available_indicators = sum(1 for available in summary["data_availability"].values() if available)
            summary["overall_availability"] = available_indicators / len(key_indicators)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get climate indicators summary: {e}")
            return {}
    
    async def get_country_climate_profile(self, country_code: str) -> Dict[str, Any]:
        """Get comprehensive climate profile for a specific country."""
        try:
            logger.info(f"Getting climate profile for country: {country_code}")
            
            # Get data for all climate indicators
            profile = {
                "country_code": country_code,
                "timestamp": datetime.now().isoformat(),
                "indicators": {},
                "summary_stats": {}
            }
            
            # Get country name
            countries = await self.get_countries()
            country_info = next((c for c in countries if c.get('iso2Code') == country_code), {})
            profile["country_name"] = country_info.get('name', country_code)
            
            for indicator_code, indicator_name in self.climate_indicators.items():
                try:
                    data = await self.get_indicator_data(indicator_code, [country_code])
                    
                    if data:
                        # Sort by year
                        data.sort(key=lambda x: x.year)
                        
                        profile["indicators"][indicator_code] = {
                            "name": indicator_name,
                            "data": [
                                {
                                    "year": d.year,
                                    "value": d.value,
                                    "unit": d.unit
                                }
                                for d in data
                            ],
                            "latest_value": data[-1].value if data else None,
                            "latest_year": data[-1].year if data else None,
                            "trend": self._calculate_trend(data) if len(data) >= 3 else "insufficient_data"
                        }
                    
                    # Small delay between requests
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"Failed to get {indicator_code} for {country_code}: {e}")
            
            # Calculate summary statistics
            profile["summary_stats"] = self._calculate_country_summary_stats(profile["indicators"])
            
            return profile
            
        except Exception as e:
            logger.error(f"Failed to get country climate profile for {country_code}: {e}")
            return {}
    
    def _calculate_trend(self, data: List[WorldBankClimateData]) -> str:
        """Calculate trend direction for indicator data."""
        if len(data) < 3:
            return "insufficient_data"
        
        # Simple trend calculation using first and last values
        first_value = data[0].value
        last_value = data[-1].value
        
        if first_value is None or last_value is None:
            return "no_data"
        
        change_percent = ((last_value - first_value) / first_value) * 100
        
        if change_percent > 5:
            return "increasing"
        elif change_percent < -5:
            return "decreasing"
        else:
            return "stable"
    
    def _calculate_country_summary_stats(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate summary statistics for a country's climate indicators."""
        stats = {
            "indicators_available": len([i for i in indicators.values() if i.get("latest_value") is not None]),
            "total_indicators": len(indicators),
            "data_completeness": 0,
            "trends": {
                "increasing": 0,
                "decreasing": 0,
                "stable": 0,
                "insufficient_data": 0
            }
        }
        
        # Calculate data completeness
        if stats["total_indicators"] > 0:
            stats["data_completeness"] = stats["indicators_available"] / stats["total_indicators"]
        
        # Count trends
        for indicator in indicators.values():
            trend = indicator.get("trend", "insufficient_data")
            if trend in stats["trends"]:
                stats["trends"][trend] += 1
        
        return stats
    
    async def get_global_climate_comparison(self) -> Dict[str, Any]:
        """Get global climate indicators comparison across major economies."""
        try:
            logger.info("Getting global climate comparison...")
            
            # Major economies for comparison
            major_economies = ["USA", "CHN", "JPN", "DEU", "IND", "GBR", "FRA", "BRA", "ITA", "CAN"]
            
            # Key indicator for comparison
            co2_indicator = "EN.ATM.CO2E.PC"  # CO2 emissions per capita
            
            data = await self.get_indicator_data(co2_indicator, major_economies)
            
            if not data:
                return {}
            
            # Group by country and get latest data
            country_data = {}
            for item in data:
                country = item.country_code
                if country not in country_data or item.year > country_data[country]["year"]:
                    country_data[country] = {
                        "country_name": item.country_name,
                        "year": item.year,
                        "co2_per_capita": item.value
                    }
            
            # Sort by CO2 emissions
            sorted_countries = sorted(
                country_data.values(),
                key=lambda x: x["co2_per_capita"] or 0,
                reverse=True
            )
            
            comparison = {
                "timestamp": datetime.now().isoformat(),
                "indicator": "CO2 Emissions per Capita (metric tons)",
                "countries": sorted_countries,
                "global_stats": {
                    "highest": sorted_countries[0] if sorted_countries else None,
                    "lowest": sorted_countries[-1] if sorted_countries else None,
                    "average": sum(c["co2_per_capita"] or 0 for c in sorted_countries) / len(sorted_countries) if sorted_countries else 0
                }
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to get global climate comparison: {e}")
            return {}
    
    async def shutdown(self):
        """Shutdown the collector."""
        try:
            if self.session:
                await self.session.close()
            logger.info("World Bank Climate Data collector shutdown completed")
        except Exception as e:
            logger.error(f"Error during World Bank collector shutdown: {e}")


# Convenience functions
async def get_country_climate_data(country_code: str) -> Dict[str, Any]:
    """Get climate data for a specific country."""
    collector = WorldBankClimateCollector()
    await collector.initialize()
    
    profile = await collector.get_country_climate_profile(country_code)
    
    await collector.shutdown()
    return profile


async def get_global_climate_indicators() -> Dict[str, Any]:
    """Get global climate indicators summary."""
    collector = WorldBankClimateCollector()
    await collector.initialize()
    
    summary = await collector.get_climate_indicators_summary()
    
    await collector.shutdown()
    return summary


async def get_climate_comparison() -> Dict[str, Any]:
    """Get global climate comparison across major economies."""
    collector = WorldBankClimateCollector()
    await collector.initialize()
    
    comparison = await collector.get_global_climate_comparison()
    
    await collector.shutdown()
    return comparison
