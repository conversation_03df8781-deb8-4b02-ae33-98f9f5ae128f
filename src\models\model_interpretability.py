"""Model Interpretability and Explainability for Water Management ML Models."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ExplanationMethod(Enum):
    """Types of explanation methods."""
    FEATURE_IMPORTANCE = "feature_importance"
    SHAP_VALUES = "shap_values"
    LIME = "lime"
    PERMUTATION_IMPORTANCE = "permutation_importance"
    PARTIAL_DEPENDENCE = "partial_dependence"
    COUNTERFACTUAL = "counterfactual"


class ModelType(Enum):
    """Types of models for interpretation."""
    TREE_BASED = "tree_based"
    LINEAR = "linear"
    NEURAL_NETWORK = "neural_network"
    ENSEMBLE = "ensemble"
    DEEP_LEARNING = "deep_learning"


@dataclass
class FeatureImportance:
    """Feature importance explanation."""
    feature_name: str
    importance_score: float
    rank: int
    confidence_interval: Tuple[float, float]
    description: str


@dataclass
class ModelExplanation:
    """Model explanation result."""
    explanation_id: str
    model_id: str
    explanation_method: ExplanationMethod
    feature_importances: List[FeatureImportance]
    global_explanations: Dict[str, Any]
    local_explanations: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.now)


class ModelInterpretabilitySystem:
    """Advanced model interpretability and explainability system."""

    def __init__(self):
        self.explanations: Dict[str, ModelExplanation] = {}
        self.model_registry: Dict[str, Dict[str, Any]] = {}
        self.feature_metadata: Dict[str, Dict[str, Any]] = {}

        # Initialize system
        self._initialize_feature_metadata()
        self._initialize_model_registry()

    def _initialize_feature_metadata(self):
        """Initialize metadata for water management features."""
        self.feature_metadata = {
            'ph': {
                'description': 'pH level of water',
                'unit': 'pH units',
                'normal_range': (6.5, 8.5),
                'importance_category': 'water_quality'
            },
            'turbidity': {
                'description': 'Water turbidity measurement',
                'unit': 'NTU',
                'normal_range': (0, 4.0),
                'importance_category': 'water_quality'
            },
            'chlorine_residual': {
                'description': 'Chlorine residual concentration',
                'unit': 'mg/L',
                'normal_range': (0.2, 4.0),
                'importance_category': 'disinfection'
            },
            'flow_rate': {
                'description': 'Water flow rate',
                'unit': 'm³/h',
                'normal_range': (500, 1500),
                'importance_category': 'hydraulics'
            },
            'energy_consumption': {
                'description': 'Energy consumption',
                'unit': 'kWh',
                'normal_range': (300, 600),
                'importance_category': 'efficiency'
            },
            'temperature': {
                'description': 'Water temperature',
                'unit': '°C',
                'normal_range': (10, 30),
                'importance_category': 'environmental'
            },
            'pressure': {
                'description': 'System pressure',
                'unit': 'bar',
                'normal_range': (2, 8),
                'importance_category': 'hydraulics'
            },
            'chemical_dose': {
                'description': 'Chemical dosing rate',
                'unit': 'mg/L',
                'normal_range': (1, 5),
                'importance_category': 'treatment'
            }
        }

    def _initialize_model_registry(self):
        """Initialize model registry with sample models."""
        self.model_registry = {
            'water_quality_predictor': {
                'model_type': ModelType.ENSEMBLE,
                'features': ['ph', 'turbidity', 'chlorine_residual', 'temperature'],
                'target': 'water_quality_score',
                'performance': {'accuracy': 0.92, 'r2_score': 0.89}
            },
            'energy_optimizer': {
                'model_type': ModelType.NEURAL_NETWORK,
                'features': ['flow_rate', 'pressure', 'temperature', 'chemical_dose'],
                'target': 'energy_consumption',
                'performance': {'mae': 15.2, 'rmse': 22.1}
            },
            'maintenance_predictor': {
                'model_type': ModelType.TREE_BASED,
                'features': ['operating_hours', 'efficiency', 'vibration', 'temperature'],
                'target': 'maintenance_needed',
                'performance': {'precision': 0.87, 'recall': 0.91}
            }
        }

    @log_async_function_call
    async def explain_model(self, model_id: str,
                          explanation_methods: List[ExplanationMethod] = None,
                          sample_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate comprehensive model explanation."""
        try:
            if model_id not in self.model_registry:
                return {'status': 'error', 'error': 'Model not found in registry'}

            model_info = self.model_registry[model_id]

            if explanation_methods is None:
                explanation_methods = [
                    ExplanationMethod.FEATURE_IMPORTANCE,
                    ExplanationMethod.SHAP_VALUES,
                    ExplanationMethod.PARTIAL_DEPENDENCE
                ]

            explanation_id = f"explanation_{model_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Generate feature importances
            feature_importances = await self._calculate_feature_importance(
                model_id, model_info
            )

            # Generate global explanations
            global_explanations = {}
            for method in explanation_methods:
                global_explanations[method.value] = await self._generate_global_explanation(
                    model_id, model_info, method
                )

            # Generate local explanations if sample data provided
            local_explanations = {}
            if sample_data:
                for method in explanation_methods:
                    local_explanations[method.value] = await self._generate_local_explanation(
                        model_id, model_info, method, sample_data
                    )

            # Create explanation object
            explanation = ModelExplanation(
                explanation_id=explanation_id,
                model_id=model_id,
                explanation_method=explanation_methods[0],  # Primary method
                feature_importances=feature_importances,
                global_explanations=global_explanations,
                local_explanations=local_explanations,
                metadata={
                    'model_type': model_info['model_type'].value,
                    'features': model_info['features'],
                    'target': model_info['target'],
                    'explanation_methods': [m.value for m in explanation_methods]
                }
            )

            self.explanations[explanation_id] = explanation

            return {
                'status': 'success',
                'explanation_id': explanation_id,
                'model_id': model_id,
                'feature_importances': [
                    {
                        'feature_name': fi.feature_name,
                        'importance_score': fi.importance_score,
                        'rank': fi.rank,
                        'description': fi.description
                    }
                    for fi in feature_importances
                ],
                'global_explanations': global_explanations,
                'local_explanations': local_explanations,
                'created_at': explanation.created_at.isoformat()
            }

        except Exception as e:
            logger.error(f"Model explanation failed: {e}")
            return {'status': 'error', 'error': str(e)}

    async def _calculate_feature_importance(self, model_id: str,
                                          model_info: Dict[str, Any]) -> List[FeatureImportance]:
        """Calculate feature importance scores."""
        features = model_info['features']
        model_type = model_info['model_type']

        # Simulate feature importance calculation
        importances = []

        # Generate realistic importance scores based on water management domain knowledge
        domain_importance = {
            'ph': 0.25,
            'turbidity': 0.20,
            'chlorine_residual': 0.18,
            'flow_rate': 0.15,
            'temperature': 0.12,
            'pressure': 0.10,
            'energy_consumption': 0.08,
            'chemical_dose': 0.07
        }

        # Add some randomness
        feature_scores = []
        for feature in features:
            base_score = domain_importance.get(feature, 0.1)
            # Add noise based on model type
            if model_type == ModelType.TREE_BASED:
                noise = np.random.normal(0, 0.02)
            elif model_type == ModelType.NEURAL_NETWORK:
                noise = np.random.normal(0, 0.05)
            else:
                noise = np.random.normal(0, 0.03)

            score = max(0.01, base_score + noise)
            feature_scores.append((feature, score))

        # Normalize scores
        total_score = sum(score for _, score in feature_scores)
        normalized_scores = [(feature, score / total_score) for feature, score in feature_scores]

        # Sort by importance
        normalized_scores.sort(key=lambda x: x[1], reverse=True)

        # Create FeatureImportance objects
        for rank, (feature, score) in enumerate(normalized_scores, 1):
            metadata = self.feature_metadata.get(feature, {})

            # Calculate confidence interval
            ci_width = 0.1 * score  # 10% of the score
            confidence_interval = (score - ci_width, score + ci_width)

            importance = FeatureImportance(
                feature_name=feature,
                importance_score=score,
                rank=rank,
                confidence_interval=confidence_interval,
                description=metadata.get('description', f'Feature: {feature}')
            )

            importances.append(importance)

        return importances

    async def _generate_global_explanation(self, model_id: str, model_info: Dict[str, Any],
                                         method: ExplanationMethod) -> Dict[str, Any]:
        """Generate global model explanation."""
        if method == ExplanationMethod.FEATURE_IMPORTANCE:
            return await self._explain_feature_importance_global(model_info)

        elif method == ExplanationMethod.SHAP_VALUES:
            return await self._explain_shap_global(model_info)

        elif method == ExplanationMethod.PARTIAL_DEPENDENCE:
            return await self._explain_partial_dependence(model_info)

        elif method == ExplanationMethod.PERMUTATION_IMPORTANCE:
            return await self._explain_permutation_importance(model_info)

        else:
            return {'method': method.value, 'explanation': 'Global explanation not implemented'}

    async def _explain_feature_importance_global(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate global feature importance explanation."""
        features = model_info['features']

        # Generate feature interactions
        interactions = []
        for i, feature1 in enumerate(features):
            for feature2 in features[i+1:]:
                interaction_strength = np.random.uniform(0.1, 0.8)
                interactions.append({
                    'feature1': feature1,
                    'feature2': feature2,
                    'interaction_strength': interaction_strength,
                    'description': f'Interaction between {feature1} and {feature2}'
                })

        # Sort by interaction strength
        interactions.sort(key=lambda x: x['interaction_strength'], reverse=True)

        return {
            'method': 'feature_importance',
            'summary': f'Model uses {len(features)} features with varying importance levels',
            'top_interactions': interactions[:3],
            'feature_categories': self._categorize_features(features),
            'interpretation': 'Higher importance scores indicate greater influence on model predictions'
        }

    async def _explain_shap_global(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate SHAP-based global explanation."""
        features = model_info['features']

        # Simulate SHAP summary statistics
        shap_summary = {}
        for feature in features:
            shap_summary[feature] = {
                'mean_abs_shap': np.random.uniform(0.1, 2.0),
                'shap_std': np.random.uniform(0.05, 0.5),
                'positive_contribution_ratio': np.random.uniform(0.3, 0.7)
            }

        return {
            'method': 'shap_values',
            'summary': 'SHAP values explain individual prediction contributions',
            'feature_contributions': shap_summary,
            'interpretation': 'SHAP values show how each feature contributes to moving the prediction away from the baseline'
        }

    async def _explain_partial_dependence(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate partial dependence explanation."""
        features = model_info['features']

        # Generate partial dependence plots data
        pd_plots = {}
        for feature in features:
            metadata = self.feature_metadata.get(feature, {})
            normal_range = metadata.get('normal_range', (0, 100))

            # Generate sample points
            x_values = np.linspace(normal_range[0], normal_range[1], 20)

            # Simulate partial dependence curve
            if feature in ['ph', 'chlorine_residual']:
                # U-shaped curve for optimal ranges
                optimal = (normal_range[0] + normal_range[1]) / 2
                y_values = [1 - 0.5 * ((x - optimal) / (normal_range[1] - normal_range[0])) ** 2 for x in x_values]
            else:
                # Linear or slightly curved relationship
                slope = np.random.uniform(-0.5, 0.5)
                y_values = [0.5 + slope * (x - normal_range[0]) / (normal_range[1] - normal_range[0]) for x in x_values]

            pd_plots[feature] = {
                'x_values': x_values.tolist(),
                'y_values': y_values,
                'interpretation': f'Shows how {feature} affects model predictions when other features are held constant'
            }

        return {
            'method': 'partial_dependence',
            'summary': 'Partial dependence plots show feature effects on predictions',
            'plots': pd_plots,
            'interpretation': 'Curves show the marginal effect of each feature on the model output'
        }

    async def _explain_permutation_importance(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate permutation importance explanation."""
        features = model_info['features']

        # Simulate permutation importance scores
        perm_importance = {}
        for feature in features:
            # Base importance with some variation
            base_importance = np.random.uniform(0.01, 0.15)
            std_dev = base_importance * 0.3

            perm_importance[feature] = {
                'importance': base_importance,
                'std': std_dev,
                'interpretation': f'Performance drops by {base_importance:.3f} when {feature} is shuffled'
            }

        return {
            'method': 'permutation_importance',
            'summary': 'Permutation importance measures feature contribution by shuffling',
            'feature_importance': perm_importance,
            'interpretation': 'Higher values indicate features that are more important for model performance'
        }

    async def _generate_local_explanation(self, model_id: str, model_info: Dict[str, Any],
                                        method: ExplanationMethod,
                                        sample_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate local explanation for specific instance."""
        if method == ExplanationMethod.SHAP_VALUES:
            return await self._explain_shap_local(model_info, sample_data)

        elif method == ExplanationMethod.LIME:
            return await self._explain_lime_local(model_info, sample_data)

        elif method == ExplanationMethod.COUNTERFACTUAL:
            return await self._explain_counterfactual(model_info, sample_data)

        else:
            return {'method': method.value, 'explanation': 'Local explanation not implemented'}

    async def _explain_shap_local(self, model_info: Dict[str, Any],
                                sample_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate local SHAP explanation."""
        features = model_info['features']

        # Simulate SHAP values for this instance
        shap_values = {}
        base_value = 0.5  # Baseline prediction

        for feature in features:
            if feature in sample_data:
                # Generate SHAP value based on feature value and domain knowledge
                feature_value = sample_data[feature]
                metadata = self.feature_metadata.get(feature, {})
                normal_range = metadata.get('normal_range', (0, 100))

                # Normalize feature value
                normalized_value = (feature_value - normal_range[0]) / (normal_range[1] - normal_range[0])

                # Generate SHAP value (positive or negative contribution)
                shap_value = (normalized_value - 0.5) * np.random.uniform(0.1, 0.5)

                shap_values[feature] = {
                    'shap_value': shap_value,
                    'feature_value': feature_value,
                    'contribution': 'positive' if shap_value > 0 else 'negative',
                    'magnitude': abs(shap_value)
                }

        return {
            'method': 'shap_values_local',
            'base_value': base_value,
            'shap_values': shap_values,
            'prediction': base_value + sum(sv['shap_value'] for sv in shap_values.values()),
            'interpretation': 'SHAP values show how each feature contributes to this specific prediction'
        }

    async def _explain_lime_local(self, model_info: Dict[str, Any],
                                sample_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate local LIME explanation."""
        features = model_info['features']

        # Simulate LIME explanation
        lime_explanation = {}

        for feature in features:
            if feature in sample_data:
                # Generate local linear coefficient
                coefficient = np.random.uniform(-1.0, 1.0)

                lime_explanation[feature] = {
                    'coefficient': coefficient,
                    'feature_value': sample_data[feature],
                    'contribution': coefficient * sample_data[feature],
                    'interpretation': f'Local linear effect of {feature} on prediction'
                }

        return {
            'method': 'lime_local',
            'local_model': lime_explanation,
            'r2_score': np.random.uniform(0.7, 0.95),  # Local model fit
            'interpretation': 'LIME approximates the model locally with a linear model'
        }

    async def _explain_counterfactual(self, model_info: Dict[str, Any],
                                    sample_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate counterfactual explanation."""
        features = model_info['features']

        # Generate counterfactual examples
        counterfactuals = []

        for i in range(3):  # Generate 3 counterfactuals
            counterfactual = sample_data.copy()
            changes = []

            # Modify 1-2 features
            features_to_change = np.random.choice(features, size=np.random.randint(1, 3), replace=False)

            for feature in features_to_change:
                metadata = self.feature_metadata.get(feature, {})
                normal_range = metadata.get('normal_range', (0, 100))

                # Generate new value
                new_value = np.random.uniform(normal_range[0], normal_range[1])
                old_value = counterfactual[feature]
                counterfactual[feature] = new_value

                changes.append({
                    'feature': feature,
                    'old_value': old_value,
                    'new_value': new_value,
                    'change': new_value - old_value
                })

            counterfactuals.append({
                'counterfactual_instance': counterfactual,
                'changes': changes,
                'predicted_outcome': np.random.uniform(0, 1),
                'distance': np.random.uniform(0.1, 0.5)
            })

        return {
            'method': 'counterfactual',
            'original_instance': sample_data,
            'counterfactuals': counterfactuals,
            'interpretation': 'Counterfactuals show minimal changes needed to alter the prediction'
        }

    def _categorize_features(self, features: List[str]) -> Dict[str, List[str]]:
        """Categorize features by domain."""
        categories = {}

        for feature in features:
            metadata = self.feature_metadata.get(feature, {})
            category = metadata.get('importance_category', 'other')

            if category not in categories:
                categories[category] = []
            categories[category].append(feature)

        return categories

    @log_async_function_call
    async def generate_explanation_report(self, explanation_id: str) -> Dict[str, Any]:
        """Generate comprehensive explanation report."""
        try:
            if explanation_id not in self.explanations:
                return {'status': 'error', 'error': 'Explanation not found'}

            explanation = self.explanations[explanation_id]
            model_info = self.model_registry.get(explanation.model_id, {})

            # Generate summary insights
            insights = self._generate_explanation_insights(explanation)

            # Generate recommendations
            recommendations = self._generate_model_recommendations(explanation, model_info)

            report = {
                'explanation_summary': {
                    'explanation_id': explanation_id,
                    'model_id': explanation.model_id,
                    'model_type': explanation.metadata.get('model_type'),
                    'target_variable': explanation.metadata.get('target'),
                    'explanation_methods': explanation.metadata.get('explanation_methods'),
                    'created_at': explanation.created_at.isoformat()
                },
                'feature_analysis': {
                    'total_features': len(explanation.feature_importances),
                    'top_features': [
                        {
                            'name': fi.feature_name,
                            'importance': fi.importance_score,
                            'rank': fi.rank,
                            'description': fi.description
                        }
                        for fi in explanation.feature_importances[:5]
                    ],
                    'feature_categories': self._categorize_features([fi.feature_name for fi in explanation.feature_importances])
                },
                'model_insights': insights,
                'recommendations': recommendations,
                'global_explanations': explanation.global_explanations,
                'local_explanations': explanation.local_explanations,
                'generated_at': datetime.now().isoformat()
            }

            return {
                'status': 'success',
                'explanation_report': report
            }

        except Exception as e:
            logger.error(f"Explanation report generation failed: {e}")
            return {'status': 'error', 'error': str(e)}

    def _generate_explanation_insights(self, explanation: ModelExplanation) -> List[str]:
        """Generate insights from model explanation."""
        insights = []

        # Feature importance insights
        top_feature = explanation.feature_importances[0]
        insights.append(f"The most important feature is '{top_feature.feature_name}' with {top_feature.importance_score:.1%} importance")

        # Check for feature dominance
        top_3_importance = sum(fi.importance_score for fi in explanation.feature_importances[:3])
        if top_3_importance > 0.7:
            insights.append("The top 3 features dominate the model (>70% importance), suggesting potential feature concentration")

        # Feature distribution insights
        if len(explanation.feature_importances) > 5:
            bottom_features = explanation.feature_importances[-3:]
            avg_bottom_importance = sum(fi.importance_score for fi in bottom_features) / len(bottom_features)
            if avg_bottom_importance < 0.05:
                insights.append("Several features have very low importance (<5%), consider feature selection")

        return insights

    def _generate_model_recommendations(self, explanation: ModelExplanation,
                                      model_info: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on explanation."""
        recommendations = []

        # Feature engineering recommendations
        top_features = [fi.feature_name for fi in explanation.feature_importances[:3]]
        if 'ph' in top_features and 'chlorine_residual' in top_features:
            recommendations.append("Consider creating interaction features between pH and chlorine residual")

        # Model complexity recommendations
        if len(explanation.feature_importances) > 10:
            low_importance_count = sum(1 for fi in explanation.feature_importances if fi.importance_score < 0.05)
            if low_importance_count > 3:
                recommendations.append(f"Consider removing {low_importance_count} low-importance features to simplify the model")

        # Domain-specific recommendations
        water_quality_features = ['ph', 'turbidity', 'chlorine_residual']
        important_wq_features = [fi.feature_name for fi in explanation.feature_importances[:5] if fi.feature_name in water_quality_features]

        if len(important_wq_features) >= 2:
            recommendations.append("Water quality features are highly important - ensure data quality and sensor calibration")

        return recommendations

    def get_explanation_summary(self, explanation_id: str) -> Dict[str, Any]:
        """Get summary of explanation."""
        if explanation_id not in self.explanations:
            return {'status': 'error', 'error': 'Explanation not found'}

        explanation = self.explanations[explanation_id]

        return {
            'status': 'success',
            'explanation_summary': {
                'explanation_id': explanation_id,
                'model_id': explanation.model_id,
                'top_features': [
                    {
                        'name': fi.feature_name,
                        'importance': fi.importance_score,
                        'rank': fi.rank
                    }
                    for fi in explanation.feature_importances[:5]
                ],
                'explanation_methods': list(explanation.global_explanations.keys()),
                'has_local_explanations': bool(explanation.local_explanations),
                'created_at': explanation.created_at.isoformat()
            }
        }


# Convenience functions
async def explain_water_management_model(model_id: str, sample_data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Explain water management model."""
    system = ModelInterpretabilitySystem()
    return await system.explain_model(model_id, sample_data=sample_data)


async def generate_model_explanation_report(explanation_id: str) -> Dict[str, Any]:
    """Generate model explanation report."""
    system = ModelInterpretabilitySystem()
    return await system.generate_explanation_report(explanation_id)