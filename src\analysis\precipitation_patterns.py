"""
Precipitation Pattern Analysis Module.

This module provides comprehensive precipitation pattern analysis capabilities
for climate data, including rainfall trends, drought detection, flood risk assessment,
and water management optimization insights.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
from scipy import stats
from scipy.stats import linregress
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class PrecipitationAnalysisResult:
    """Result of precipitation pattern analysis."""
    location: str
    analysis_period: Dict[str, str]
    precipitation_trends: Dict[str, Any]
    seasonal_patterns: Dict[str, Any]
    drought_analysis: Dict[str, Any]
    flood_risk_assessment: Dict[str, Any]
    extreme_events: List[Dict[str, Any]]
    statistical_summary: Dict[str, float]
    water_management_insights: Dict[str, Any]
    timestamp: datetime


@dataclass
class DroughtPeriod:
    """Drought period information."""
    start_date: datetime
    end_date: datetime
    duration_days: int
    severity: str  # 'mild', 'moderate', 'severe', 'extreme'
    intensity: float  # Average precipitation deficit
    affected_area: str


@dataclass
class FloodEvent:
    """Flood event information."""
    date: datetime
    precipitation_amount: float
    duration_hours: int
    intensity: str  # 'light', 'moderate', 'heavy', 'extreme'
    risk_level: str  # 'low', 'medium', 'high', 'critical'


class PrecipitationPatternAnalyzer:
    """
    Comprehensive precipitation pattern analysis system.

    Provides:
    - Precipitation trend analysis and forecasting
    - Seasonal rainfall pattern detection
    - Drought identification and severity assessment
    - Flood risk evaluation and early warning
    - Water management optimization insights
    - Climate change impact on precipitation
    """

    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False

        # Analysis parameters
        self.drought_thresholds = {
            'mild': 0.8,      # 80% of normal precipitation
            'moderate': 0.6,  # 60% of normal precipitation
            'severe': 0.4,    # 40% of normal precipitation
            'extreme': 0.2    # 20% of normal precipitation
        }

        self.flood_thresholds = {
            'light': 10.0,    # mm/day
            'moderate': 25.0, # mm/day
            'heavy': 50.0,    # mm/day
            'extreme': 100.0  # mm/day
        }

        # Water management parameters
        self.water_management_thresholds = {
            'optimal_daily': (2.0, 15.0),     # mm/day - optimal for treatment
            'storage_capacity': 500.0,         # mm - typical storage capacity
            'treatment_efficiency': (5.0, 30.0), # mm/day - efficient treatment range
            'drought_alert': 1.0,              # mm/day - drought alert threshold
            'flood_alert': 75.0                # mm/day - flood alert threshold
        }

        # Seasonal analysis parameters
        self.min_data_points = 30  # Minimum points for reliable analysis
        self.rolling_window = 30   # Days for rolling statistics

    @log_async_function_call
    async def initialize(self):
        """Initialize the precipitation pattern analyzer."""
        try:
            logger.info("Initializing Precipitation Pattern Analyzer...")
            self.is_initialized = True
            logger.info("Precipitation Pattern Analyzer initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize precipitation analyzer: {e}")
            return False

    async def analyze_precipitation_patterns(self, data: List[ProcessedClimateData],
                                           location: str = None) -> PrecipitationAnalysisResult:
        """Perform comprehensive precipitation pattern analysis."""
        try:
            if not self.is_initialized:
                await self.initialize()

            if not data:
                raise ValueError("No data provided for analysis")

            logger.info(f"Analyzing precipitation patterns for {len(data)} data points")

            # Convert to DataFrame for analysis
            df = await self._prepare_precipitation_dataframe(data)

            if df.empty or len(df) < self.min_data_points:
                raise ValueError(f"Insufficient data for analysis (need at least {self.min_data_points} points)")

            # Determine location
            analysis_location = location or self._extract_location(data)

            # Perform precipitation trend analysis
            trend_results = await self._analyze_precipitation_trends(df)

            # Analyze seasonal patterns
            seasonal_analysis = await self._analyze_seasonal_precipitation(df)

            # Drought analysis
            drought_analysis = await self._analyze_drought_periods(df)

            # Flood risk assessment
            flood_assessment = await self._assess_flood_risk(df)

            # Detect extreme precipitation events
            extreme_events = await self._detect_extreme_precipitation_events(df)

            # Calculate statistical summary
            stats_summary = await self._calculate_precipitation_statistics(df)

            # Generate water management insights
            management_insights = await self._generate_water_management_insights(df, trend_results, drought_analysis, flood_assessment)

            # Create result
            result = PrecipitationAnalysisResult(
                location=analysis_location,
                analysis_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'duration_days': (df.index.max() - df.index.min()).days
                },
                precipitation_trends=trend_results,
                seasonal_patterns=seasonal_analysis,
                drought_analysis=drought_analysis,
                flood_risk_assessment=flood_assessment,
                extreme_events=extreme_events,
                statistical_summary=stats_summary,
                water_management_insights=management_insights,
                timestamp=datetime.now()
            )

            logger.info(f"Precipitation pattern analysis completed for {analysis_location}")
            return result

        except Exception as e:
            logger.error(f"Precipitation pattern analysis failed: {e}")
            raise

    async def _prepare_precipitation_dataframe(self, data: List[ProcessedClimateData]) -> pd.DataFrame:
        """Prepare DataFrame from climate data for precipitation analysis."""
        try:
            records = []
            for item in data:
                if item.precipitation is not None:
                    records.append({
                        'timestamp': item.timestamp,
                        'precipitation': item.precipitation,
                        'location': item.location,
                        'source': item.source,
                        'quality_score': getattr(item, 'data_quality_score', 1.0)
                    })

            if not records:
                return pd.DataFrame()

            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)

            # Handle duplicates and missing values
            df = df.groupby(df.index).mean(numeric_only=True)

            # Fill missing precipitation with 0 (no rain)
            df['precipitation'] = df['precipitation'].fillna(0.0)

            # Ensure non-negative precipitation
            df['precipitation'] = df['precipitation'].clip(lower=0.0)

            return df

        except Exception as e:
            logger.error(f"Failed to prepare precipitation DataFrame: {e}")
            return pd.DataFrame()

    def _extract_location(self, data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in data if item.location]
        if locations:
            return max(set(locations), key=locations.count)
        return "Unknown Location"

    async def _analyze_precipitation_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze precipitation trends using multiple methods."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return {'trend_direction': 'no_trend', 'trend_magnitude': 0.0, 'confidence': 0.0}

            precipitation = df['precipitation'].dropna()
            if len(precipitation) < 3:
                return {'trend_direction': 'no_trend', 'trend_magnitude': 0.0, 'confidence': 0.0}

            # Convert timestamps to numeric (days since start)
            time_numeric = (df.index - df.index.min()).days

            results = {}

            # Linear regression trend
            if len(precipitation) >= 3:
                slope, intercept, r_value, p_value, std_err = linregress(time_numeric, precipitation)
                results['linear_regression'] = {
                    'slope': slope,
                    'r_squared': r_value ** 2,
                    'p_value': p_value,
                    'significance': p_value < 0.05
                }

            # Mann-Kendall trend test
            try:
                mk_result = self._mann_kendall_test(precipitation.values)
                results['mann_kendall'] = mk_result
            except Exception as e:
                logger.warning(f"Mann-Kendall test failed: {e}")
                results['mann_kendall'] = {'trend': 'no_trend', 'p_value': 1.0}

            # Seasonal trend analysis
            seasonal_trends = await self._analyze_seasonal_trends(df)
            results['seasonal_trends'] = seasonal_trends

            # Determine overall trend
            trend_direction = self._determine_precipitation_trend_direction(results)
            trend_magnitude = self._calculate_precipitation_trend_magnitude(results, len(time_numeric))
            confidence = self._calculate_precipitation_trend_confidence(results)

            return {
                'trend_direction': trend_direction,
                'trend_magnitude': trend_magnitude,  # mm per year
                'confidence': confidence,
                'methods': results
            }

        except Exception as e:
            logger.error(f"Precipitation trend analysis failed: {e}")
            return {'trend_direction': 'no_trend', 'trend_magnitude': 0.0, 'confidence': 0.0}

    def _mann_kendall_test(self, data: np.ndarray) -> Dict[str, Any]:
        """Mann-Kendall trend test for precipitation data."""
        try:
            n = len(data)
            if n < 3:
                return {'trend': 'no_trend', 'p_value': 1.0}

            # Calculate S statistic
            s = 0
            for i in range(n - 1):
                for j in range(i + 1, n):
                    if data[j] > data[i]:
                        s += 1
                    elif data[j] < data[i]:
                        s -= 1

            # Calculate variance
            var_s = n * (n - 1) * (2 * n + 5) / 18

            # Calculate Z statistic
            if s > 0:
                z = (s - 1) / np.sqrt(var_s)
            elif s < 0:
                z = (s + 1) / np.sqrt(var_s)
            else:
                z = 0

            # Calculate p-value (two-tailed)
            p_value = 2 * (1 - stats.norm.cdf(abs(z)))

            # Determine trend
            if p_value < 0.05:
                trend = 'increasing' if s > 0 else 'decreasing'
            else:
                trend = 'no_trend'

            return {
                'trend': trend,
                'p_value': p_value,
                's_statistic': s,
                'z_statistic': z
            }

        except Exception as e:
            logger.warning(f"Mann-Kendall test calculation failed: {e}")
            return {'trend': 'no_trend', 'p_value': 1.0}

    async def _analyze_seasonal_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze seasonal precipitation trends."""
        try:
            if df.empty:
                return {}

            # Add seasonal information
            df_seasonal = df.copy()
            df_seasonal['month'] = df_seasonal.index.month
            df_seasonal['season'] = df_seasonal['month'].map(self._get_season)

            seasonal_trends = {}

            for season in ['Spring', 'Summer', 'Autumn', 'Winter']:
                season_data = df_seasonal[df_seasonal['season'] == season]

                if len(season_data) >= 3:
                    time_numeric = (season_data.index - season_data.index.min()).days
                    precip_values = season_data['precipitation']

                    slope, _, r_value, p_value, _ = linregress(time_numeric, precip_values)

                    seasonal_trends[season] = {
                        'trend_slope': float(slope * 365.25),  # mm per year
                        'trend_confidence': float(r_value ** 2),
                        'trend_significance': p_value < 0.05,
                        'average_precipitation': float(precip_values.mean()),
                        'data_points': len(season_data)
                    }

            return seasonal_trends

        except Exception as e:
            logger.error(f"Seasonal trend analysis failed: {e}")
            return {}

    def _get_season(self, month: int) -> str:
        """Map month to season."""
        if month in [12, 1, 2]:
            return 'Winter'
        elif month in [3, 4, 5]:
            return 'Spring'
        elif month in [6, 7, 8]:
            return 'Summer'
        else:
            return 'Autumn'

    def _determine_precipitation_trend_direction(self, results: Dict[str, Any]) -> str:
        """Determine overall precipitation trend direction."""
        try:
            directions = []

            # Linear regression
            if 'linear_regression' in results:
                lr = results['linear_regression']
                if lr.get('significance', False):
                    if lr['slope'] > 0:
                        directions.append('increasing')
                    elif lr['slope'] < 0:
                        directions.append('decreasing')
                    else:
                        directions.append('stable')

            # Mann-Kendall
            if 'mann_kendall' in results:
                mk_trend = results['mann_kendall'].get('trend', 'no_trend')
                if mk_trend != 'no_trend':
                    directions.append(mk_trend)

            # Return consensus or most common direction
            if not directions:
                return 'no_trend'

            return max(set(directions), key=directions.count)

        except Exception as e:
            logger.warning(f"Failed to determine precipitation trend direction: {e}")
            return 'no_trend'

    def _calculate_precipitation_trend_magnitude(self, results: Dict[str, Any], duration_days: int) -> float:
        """Calculate precipitation trend magnitude in mm per year."""
        try:
            if duration_days == 0:
                return 0.0

            slopes = []

            if 'linear_regression' in results:
                slopes.append(results['linear_regression']['slope'])

            if not slopes:
                return 0.0

            # Average slope (mm per day)
            avg_slope = np.mean(slopes)

            # Convert to mm per year
            mm_per_year = avg_slope * 365.25

            return float(mm_per_year)

        except Exception as e:
            logger.warning(f"Failed to calculate precipitation trend magnitude: {e}")
            return 0.0

    def _calculate_precipitation_trend_confidence(self, results: Dict[str, Any]) -> float:
        """Calculate confidence in precipitation trend analysis."""
        try:
            confidence_scores = []

            # Linear regression confidence
            if 'linear_regression' in results:
                lr = results['linear_regression']
                r_squared = lr.get('r_squared', 0)
                p_value = lr.get('p_value', 1)

                lr_confidence = r_squared * (1 - p_value)
                confidence_scores.append(lr_confidence)

            # Mann-Kendall confidence
            if 'mann_kendall' in results:
                mk = results['mann_kendall']
                p_value = mk.get('p_value', 1)
                mk_confidence = 1 - p_value
                confidence_scores.append(mk_confidence)

            if not confidence_scores:
                return 0.0

            return float(np.mean(confidence_scores))

        except Exception as e:
            logger.warning(f"Failed to calculate precipitation trend confidence: {e}")
            return 0.0

    async def _analyze_seasonal_precipitation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze seasonal precipitation patterns."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return {}

            # Add time-based features
            df_seasonal = df.copy()
            df_seasonal['month'] = df_seasonal.index.month
            df_seasonal['season'] = df_seasonal['month'].map(self._get_season)
            df_seasonal['day_of_year'] = df_seasonal.index.dayofyear

            seasonal_stats = {}

            # Analyze by season
            for season in ['Spring', 'Summer', 'Autumn', 'Winter']:
                season_data = df_seasonal[df_seasonal['season'] == season]['precipitation']

                if len(season_data) > 0:
                    seasonal_stats[season] = {
                        'total_precipitation': float(season_data.sum()),
                        'avg_daily_precipitation': float(season_data.mean()),
                        'max_daily_precipitation': float(season_data.max()),
                        'rainy_days': int((season_data > 0.1).sum()),  # Days with >0.1mm
                        'dry_days': int((season_data <= 0.1).sum()),
                        'precipitation_variability': float(season_data.std()),
                        'data_points': len(season_data)
                    }

            # Calculate seasonal distribution
            if seasonal_stats:
                total_annual = sum(stats['total_precipitation'] for stats in seasonal_stats.values())
                for season, stats in seasonal_stats.items():
                    if total_annual > 0:
                        stats['percentage_of_annual'] = (stats['total_precipitation'] / total_annual) * 100
                    else:
                        stats['percentage_of_annual'] = 0.0

            # Identify wettest and driest seasons
            if seasonal_stats:
                wettest_season = max(seasonal_stats.keys(),
                                   key=lambda s: seasonal_stats[s]['total_precipitation'])
                driest_season = min(seasonal_stats.keys(),
                                  key=lambda s: seasonal_stats[s]['total_precipitation'])
            else:
                wettest_season = driest_season = None

            return {
                'seasonal_statistics': seasonal_stats,
                'wettest_season': wettest_season,
                'driest_season': driest_season,
                'seasonal_variability': self._calculate_seasonal_variability(seasonal_stats),
                'analysis_summary': {
                    'total_seasons_analyzed': len(seasonal_stats),
                    'strong_seasonal_pattern': self._has_strong_seasonal_pattern(seasonal_stats)
                }
            }

        except Exception as e:
            logger.error(f"Seasonal precipitation analysis failed: {e}")
            return {}

    def _calculate_seasonal_variability(self, seasonal_stats: Dict[str, Any]) -> float:
        """Calculate variability across seasons."""
        try:
            if not seasonal_stats:
                return 0.0

            totals = [stats['total_precipitation'] for stats in seasonal_stats.values()]
            if len(totals) < 2:
                return 0.0

            return float(np.std(totals) / np.mean(totals)) if np.mean(totals) > 0 else 0.0

        except Exception as e:
            logger.warning(f"Failed to calculate seasonal variability: {e}")
            return 0.0

    def _has_strong_seasonal_pattern(self, seasonal_stats: Dict[str, Any]) -> bool:
        """Determine if there's a strong seasonal precipitation pattern."""
        try:
            if len(seasonal_stats) < 2:
                return False

            totals = [stats['total_precipitation'] for stats in seasonal_stats.values()]
            max_total = max(totals)
            min_total = min(totals)

            # Strong pattern if max is >3x min
            return max_total > 3 * min_total if min_total > 0 else False

        except Exception as e:
            logger.warning(f"Failed to determine seasonal pattern strength: {e}")
            return False

    async def _analyze_drought_periods(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze drought periods and severity."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return {}

            # Calculate rolling averages for drought detection
            rolling_30d = df['precipitation'].rolling(window=30, min_periods=15).mean()
            rolling_90d = df['precipitation'].rolling(window=90, min_periods=45).mean()

            # Calculate long-term average
            long_term_avg = df['precipitation'].mean()

            # Detect drought periods
            drought_periods = []
            current_drought = None

            for date, precip_30d in rolling_30d.items():
                if pd.isna(precip_30d):
                    continue

                # Determine drought severity
                drought_ratio = precip_30d / long_term_avg if long_term_avg > 0 else 1.0

                severity = None
                if drought_ratio <= self.drought_thresholds['extreme']:
                    severity = 'extreme'
                elif drought_ratio <= self.drought_thresholds['severe']:
                    severity = 'severe'
                elif drought_ratio <= self.drought_thresholds['moderate']:
                    severity = 'moderate'
                elif drought_ratio <= self.drought_thresholds['mild']:
                    severity = 'mild'

                if severity:
                    if current_drought is None:
                        # Start new drought period
                        current_drought = {
                            'start_date': date,
                            'end_date': date,
                            'severity': severity,
                            'min_ratio': drought_ratio,
                            'avg_ratio': drought_ratio,
                            'days': 1
                        }
                    else:
                        # Continue current drought
                        current_drought['end_date'] = date
                        current_drought['days'] += 1
                        current_drought['min_ratio'] = min(current_drought['min_ratio'], drought_ratio)
                        current_drought['severity'] = max(current_drought['severity'], severity,
                                                         key=lambda x: list(self.drought_thresholds.keys()).index(x))
                else:
                    if current_drought is not None:
                        # End current drought period
                        if current_drought['days'] >= 7:  # Minimum 7 days for drought
                            drought_periods.append(current_drought)
                        current_drought = None

            # Close final drought if ongoing
            if current_drought is not None and current_drought['days'] >= 7:
                drought_periods.append(current_drought)

            # Calculate drought statistics
            drought_stats = {
                'total_drought_periods': len(drought_periods),
                'total_drought_days': sum(d['days'] for d in drought_periods),
                'longest_drought_days': max((d['days'] for d in drought_periods), default=0),
                'most_severe_drought': max((d['severity'] for d in drought_periods),
                                         default='none',
                                         key=lambda x: list(self.drought_thresholds.keys()).index(x) if x != 'none' else -1),
                'drought_frequency': len(drought_periods) / (len(df) / 365.25) if len(df) > 365 else 0,
                'current_drought_status': self._assess_current_drought_status(rolling_30d.iloc[-1], long_term_avg)
            }

            return {
                'drought_periods': [
                    {
                        'start_date': d['start_date'].isoformat(),
                        'end_date': d['end_date'].isoformat(),
                        'duration_days': d['days'],
                        'severity': d['severity'],
                        'intensity': d['min_ratio']
                    }
                    for d in drought_periods[-10:]  # Last 10 droughts
                ],
                'drought_statistics': drought_stats,
                'drought_risk_assessment': self._assess_drought_risk(drought_stats, rolling_30d.iloc[-1], long_term_avg)
            }

        except Exception as e:
            logger.error(f"Drought analysis failed: {e}")
            return {}

    def _assess_current_drought_status(self, current_30d_avg: float, long_term_avg: float) -> str:
        """Assess current drought status."""
        try:
            if pd.isna(current_30d_avg) or long_term_avg == 0:
                return 'unknown'

            ratio = current_30d_avg / long_term_avg

            if ratio <= self.drought_thresholds['extreme']:
                return 'extreme_drought'
            elif ratio <= self.drought_thresholds['severe']:
                return 'severe_drought'
            elif ratio <= self.drought_thresholds['moderate']:
                return 'moderate_drought'
            elif ratio <= self.drought_thresholds['mild']:
                return 'mild_drought'
            else:
                return 'normal'

        except Exception as e:
            logger.warning(f"Failed to assess current drought status: {e}")
            return 'unknown'

    def _assess_drought_risk(self, drought_stats: Dict[str, Any], current_30d_avg: float, long_term_avg: float) -> Dict[str, Any]:
        """Assess drought risk based on historical patterns and current conditions."""
        try:
            risk_factors = []
            risk_level = 'low'

            # Historical frequency risk
            freq = drought_stats.get('drought_frequency', 0)
            if freq > 2:  # More than 2 droughts per year
                risk_factors.append('high_historical_frequency')
                risk_level = 'high'
            elif freq > 1:
                risk_factors.append('moderate_historical_frequency')
                risk_level = max(risk_level, 'medium')

            # Current conditions risk
            current_status = drought_stats.get('current_drought_status', 'normal')
            if 'drought' in current_status:
                risk_factors.append('current_drought_conditions')
                risk_level = 'high'

            # Recent drought severity
            most_severe = drought_stats.get('most_severe_drought', 'none')
            if most_severe in ['extreme', 'severe']:
                risk_factors.append('recent_severe_droughts')
                risk_level = max(risk_level, 'medium')

            return {
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'recommendations': self._get_drought_recommendations(risk_level, current_status)
            }

        except Exception as e:
            logger.warning(f"Failed to assess drought risk: {e}")
            return {'risk_level': 'unknown', 'risk_factors': [], 'recommendations': []}

    def _get_drought_recommendations(self, risk_level: str, current_status: str) -> List[str]:
        """Get drought management recommendations."""
        recommendations = []

        if risk_level == 'high':
            recommendations.extend([
                'Implement water conservation measures',
                'Increase water storage capacity',
                'Develop alternative water sources',
                'Monitor soil moisture levels closely'
            ])
        elif risk_level == 'medium':
            recommendations.extend([
                'Prepare drought contingency plans',
                'Monitor precipitation patterns closely',
                'Consider water-efficient technologies'
            ])

        if 'drought' in current_status:
            recommendations.extend([
                'Activate drought response protocols',
                'Reduce non-essential water usage',
                'Implement emergency water rationing if necessary'
            ])

        return recommendations

    async def _assess_flood_risk(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Assess flood risk based on precipitation patterns."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return {}

            # Calculate daily precipitation statistics
            daily_precip = df['precipitation']

            # Identify heavy precipitation events
            flood_events = []
            for date, precip in daily_precip.items():
                if precip >= self.flood_thresholds['light']:
                    intensity = 'light'
                    risk_level = 'low'

                    if precip >= self.flood_thresholds['extreme']:
                        intensity = 'extreme'
                        risk_level = 'critical'
                    elif precip >= self.flood_thresholds['heavy']:
                        intensity = 'heavy'
                        risk_level = 'high'
                    elif precip >= self.flood_thresholds['moderate']:
                        intensity = 'moderate'
                        risk_level = 'medium'

                    flood_events.append({
                        'date': date.isoformat(),
                        'precipitation': float(precip),
                        'intensity': intensity,
                        'risk_level': risk_level
                    })

            # Calculate flood statistics
            flood_stats = {
                'total_flood_events': len(flood_events),
                'extreme_events': len([e for e in flood_events if e['intensity'] == 'extreme']),
                'heavy_events': len([e for e in flood_events if e['intensity'] == 'heavy']),
                'max_daily_precipitation': float(daily_precip.max()),
                'flood_frequency': len(flood_events) / (len(df) / 365.25) if len(df) > 365 else 0,
                'recent_flood_activity': len([e for e in flood_events if
                                            (datetime.now() - datetime.fromisoformat(e['date'])).days <= 30])
            }

            # Assess current flood risk
            recent_precip = daily_precip.tail(7).sum()  # Last 7 days
            current_risk = self._assess_current_flood_risk(recent_precip, flood_stats)

            return {
                'flood_events': flood_events[-20:],  # Last 20 events
                'flood_statistics': flood_stats,
                'current_flood_risk': current_risk,
                'flood_risk_assessment': self._generate_flood_risk_assessment(flood_stats, recent_precip)
            }

        except Exception as e:
            logger.error(f"Flood risk assessment failed: {e}")
            return {}

    def _assess_current_flood_risk(self, recent_precip: float, flood_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Assess current flood risk based on recent precipitation."""
        try:
            risk_level = 'low'
            risk_factors = []

            # Recent precipitation risk
            if recent_precip > 100:  # >100mm in 7 days
                risk_level = 'high'
                risk_factors.append('heavy_recent_precipitation')
            elif recent_precip > 50:
                risk_level = 'medium'
                risk_factors.append('moderate_recent_precipitation')

            # Historical flood frequency
            freq = flood_stats.get('flood_frequency', 0)
            if freq > 5:  # More than 5 flood events per year
                risk_factors.append('high_historical_flood_frequency')
                risk_level = max(risk_level, 'medium')

            return {
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'recent_precipitation_7d': float(recent_precip)
            }

        except Exception as e:
            logger.warning(f"Failed to assess current flood risk: {e}")
            return {'risk_level': 'unknown', 'risk_factors': []}

    def _generate_flood_risk_assessment(self, flood_stats: Dict[str, Any], recent_precip: float) -> Dict[str, Any]:
        """Generate comprehensive flood risk assessment."""
        try:
            assessment = {
                'overall_risk': 'low',
                'risk_factors': [],
                'recommendations': []
            }

            # Analyze historical patterns
            extreme_events = flood_stats.get('extreme_events', 0)
            total_events = flood_stats.get('total_flood_events', 0)

            if extreme_events > 0:
                assessment['risk_factors'].append('history_of_extreme_events')
                assessment['overall_risk'] = 'high'

            if total_events > 10:
                assessment['risk_factors'].append('frequent_flood_events')
                assessment['overall_risk'] = max(assessment['overall_risk'], 'medium')

            # Current conditions
            if recent_precip > 75:
                assessment['risk_factors'].append('high_recent_precipitation')
                assessment['overall_risk'] = 'high'

            # Generate recommendations
            if assessment['overall_risk'] == 'high':
                assessment['recommendations'].extend([
                    'Monitor water levels closely',
                    'Prepare flood response equipment',
                    'Review evacuation procedures',
                    'Increase drainage system capacity'
                ])
            elif assessment['overall_risk'] == 'medium':
                assessment['recommendations'].extend([
                    'Monitor weather forecasts',
                    'Check drainage systems',
                    'Prepare emergency supplies'
                ])

            return assessment

        except Exception as e:
            logger.warning(f"Failed to generate flood risk assessment: {e}")
            return {'overall_risk': 'unknown', 'risk_factors': [], 'recommendations': []}

    async def _detect_extreme_precipitation_events(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect extreme precipitation events."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return []

            # Calculate percentiles for extreme event detection
            precip_95th = df['precipitation'].quantile(0.95)
            precip_99th = df['precipitation'].quantile(0.99)

            extreme_events = []

            for date, precip in df['precipitation'].items():
                if precip >= precip_99th and precip > 20:  # 99th percentile and >20mm
                    severity = 'extreme'
                elif precip >= precip_95th and precip > 10:  # 95th percentile and >10mm
                    severity = 'severe'
                else:
                    continue

                # Calculate return period estimate (simplified)
                return_period = self._estimate_return_period(precip, df['precipitation'])

                extreme_events.append({
                    'date': date.isoformat(),
                    'precipitation': float(precip),
                    'severity': severity,
                    'percentile_rank': float(stats.percentileofscore(df['precipitation'], precip)),
                    'estimated_return_period_years': return_period,
                    'impact_assessment': self._assess_event_impact(precip)
                })

            # Sort by precipitation amount (descending)
            extreme_events.sort(key=lambda x: x['precipitation'], reverse=True)

            return extreme_events[:15]  # Return top 15 events

        except Exception as e:
            logger.error(f"Extreme event detection failed: {e}")
            return []

    def _estimate_return_period(self, event_precip: float, precip_series: pd.Series) -> float:
        """Estimate return period for precipitation event."""
        try:
            # Simple method: rank-based return period
            exceedance_prob = (precip_series >= event_precip).mean()

            if exceedance_prob > 0:
                return 1.0 / exceedance_prob
            else:
                return 100.0  # Default for very rare events

        except Exception as e:
            logger.warning(f"Failed to estimate return period: {e}")
            return 1.0

    def _assess_event_impact(self, precip: float) -> str:
        """Assess impact of precipitation event."""
        if precip >= 100:
            return 'catastrophic'
        elif precip >= 75:
            return 'major'
        elif precip >= 50:
            return 'moderate'
        elif precip >= 25:
            return 'minor'
        else:
            return 'minimal'

    async def _calculate_precipitation_statistics(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate statistical summary of precipitation data."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return {}

            precipitation = df['precipitation'].dropna()
            if len(precipitation) == 0:
                return {}

            # Basic statistics
            stats_dict = {
                'total_precipitation': float(precipitation.sum()),
                'mean_daily_precipitation': float(precipitation.mean()),
                'median_daily_precipitation': float(precipitation.median()),
                'std_precipitation': float(precipitation.std()),
                'min_precipitation': float(precipitation.min()),
                'max_precipitation': float(precipitation.max()),
                'precipitation_range': float(precipitation.max() - precipitation.min()),
                'percentile_25': float(precipitation.quantile(0.25)),
                'percentile_75': float(precipitation.quantile(0.75)),
                'percentile_95': float(precipitation.quantile(0.95)),
                'percentile_99': float(precipitation.quantile(0.99)),
                'data_points': len(precipitation),
                'data_span_days': (df.index.max() - df.index.min()).days
            }

            # Derived statistics
            rainy_days = (precipitation > 0.1).sum()  # Days with >0.1mm
            stats_dict.update({
                'rainy_days': int(rainy_days),
                'dry_days': int(len(precipitation) - rainy_days),
                'rainy_day_percentage': float((rainy_days / len(precipitation)) * 100),
                'average_rainy_day_precipitation': float(precipitation[precipitation > 0.1].mean()) if rainy_days > 0 else 0.0,
                'precipitation_intensity': float(precipitation.sum() / rainy_days) if rainy_days > 0 else 0.0
            })

            return stats_dict

        except Exception as e:
            logger.error(f"Precipitation statistics calculation failed: {e}")
            return {}

    async def _generate_water_management_insights(self, df: pd.DataFrame,
                                                trend_results: Dict[str, Any],
                                                drought_analysis: Dict[str, Any],
                                                flood_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Generate water management optimization insights."""
        try:
            if df.empty or 'precipitation' not in df.columns:
                return {}

            precipitation = df['precipitation'].dropna()
            if len(precipitation) == 0:
                return {}

            insights = {}

            # Water supply reliability analysis
            optimal_min, optimal_max = self.water_management_thresholds['optimal_daily']
            optimal_days = len(precipitation[(precipitation >= optimal_min) & (precipitation <= optimal_max)])
            total_days = len(precipitation)

            insights['water_supply_reliability'] = {
                'optimal_precipitation_days': optimal_days,
                'optimal_percentage': (optimal_days / total_days) * 100,
                'average_daily_precipitation': float(precipitation.mean()),
                'reliability_score': min(100, (optimal_days / total_days) * 100 +
                                       (precipitation.mean() / optimal_max) * 50)
            }

            # Storage and treatment capacity insights
            storage_capacity = self.water_management_thresholds['storage_capacity']
            cumulative_excess = (precipitation - optimal_max).clip(lower=0).sum()
            cumulative_deficit = (optimal_min - precipitation).clip(lower=0).sum()

            insights['storage_requirements'] = {
                'excess_water_volume': float(cumulative_excess),
                'deficit_volume': float(cumulative_deficit),
                'recommended_storage_capacity': float(max(storage_capacity, cumulative_deficit * 1.2)),
                'storage_utilization': float(min(100, (cumulative_excess / storage_capacity) * 100))
            }

            # Treatment system optimization
            treatment_min, treatment_max = self.water_management_thresholds['treatment_efficiency']
            efficient_days = len(precipitation[(precipitation >= treatment_min) & (precipitation <= treatment_max)])

            insights['treatment_optimization'] = {
                'efficient_treatment_days': efficient_days,
                'efficiency_percentage': (efficient_days / total_days) * 100,
                'peak_treatment_load': float(precipitation.max()),
                'average_treatment_load': float(precipitation.mean()),
                'treatment_capacity_recommendation': float(precipitation.quantile(0.95) * 1.1)
            }

            # Risk-based recommendations
            drought_risk = drought_analysis.get('drought_risk_assessment', {}).get('risk_level', 'low')
            flood_risk = flood_assessment.get('current_flood_risk', {}).get('risk_level', 'low')

            insights['risk_management'] = {
                'drought_risk_level': drought_risk,
                'flood_risk_level': flood_risk,
                'primary_concern': self._determine_primary_water_concern(drought_risk, flood_risk, precipitation),
                'adaptive_strategies': self._generate_adaptive_strategies(drought_risk, flood_risk, trend_results)
            }

            # Seasonal management recommendations
            seasonal_patterns = await self._analyze_seasonal_precipitation(df)
            insights['seasonal_management'] = self._generate_seasonal_recommendations(seasonal_patterns)

            # Climate change adaptation
            trend_direction = trend_results.get('trend_direction', 'no_trend')
            trend_magnitude = trend_results.get('trend_magnitude', 0.0)

            insights['climate_adaptation'] = {
                'precipitation_trend': trend_direction,
                'annual_change_rate': trend_magnitude,
                'adaptation_priority': self._assess_adaptation_priority(trend_direction, abs(trend_magnitude)),
                'long_term_recommendations': self._generate_long_term_recommendations(trend_direction, trend_magnitude)
            }

            return insights

        except Exception as e:
            logger.error(f"Water management insights generation failed: {e}")
            return {}

    def _determine_primary_water_concern(self, drought_risk: str, flood_risk: str, precipitation: pd.Series) -> str:
        """Determine primary water management concern."""
        try:
            if drought_risk == 'high':
                return 'water_scarcity'
            elif flood_risk == 'high':
                return 'flood_management'
            elif precipitation.mean() < 2.0:  # Very low average precipitation
                return 'water_conservation'
            elif precipitation.std() > precipitation.mean():  # High variability
                return 'supply_variability'
            else:
                return 'routine_management'

        except Exception as e:
            logger.warning(f"Failed to determine primary water concern: {e}")
            return 'routine_management'

    def _generate_adaptive_strategies(self, drought_risk: str, flood_risk: str, trend_results: Dict[str, Any]) -> List[str]:
        """Generate adaptive water management strategies."""
        strategies = []

        if drought_risk in ['high', 'medium']:
            strategies.extend([
                'Implement rainwater harvesting systems',
                'Develop groundwater backup sources',
                'Install water-efficient treatment technologies',
                'Create drought emergency response protocols'
            ])

        if flood_risk in ['high', 'medium']:
            strategies.extend([
                'Upgrade drainage and overflow systems',
                'Implement flood-resistant infrastructure',
                'Develop rapid response flood management',
                'Create excess water storage and diversion'
            ])

        trend_direction = trend_results.get('trend_direction', 'no_trend')
        if trend_direction == 'decreasing':
            strategies.append('Plan for long-term water scarcity scenarios')
        elif trend_direction == 'increasing':
            strategies.append('Prepare for increased flood management needs')

        return strategies

    def _generate_seasonal_recommendations(self, seasonal_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Generate seasonal water management recommendations."""
        try:
            recommendations = {}

            seasonal_stats = seasonal_patterns.get('seasonal_statistics', {})
            wettest_season = seasonal_patterns.get('wettest_season')
            driest_season = seasonal_patterns.get('driest_season')

            if wettest_season:
                wet_stats = seasonal_stats.get(wettest_season, {})
                recommendations[wettest_season] = {
                    'focus': 'excess_water_management',
                    'strategies': [
                        'Maximize water collection and storage',
                        'Prepare for potential flooding',
                        'Optimize treatment capacity for high volumes'
                    ],
                    'expected_precipitation': wet_stats.get('avg_daily_precipitation', 0)
                }

            if driest_season:
                dry_stats = seasonal_stats.get(driest_season, {})
                recommendations[driest_season] = {
                    'focus': 'water_conservation',
                    'strategies': [
                        'Implement strict water conservation',
                        'Utilize stored water reserves',
                        'Monitor groundwater levels closely'
                    ],
                    'expected_precipitation': dry_stats.get('avg_daily_precipitation', 0)
                }

            return recommendations

        except Exception as e:
            logger.warning(f"Failed to generate seasonal recommendations: {e}")
            return {}

    def _assess_adaptation_priority(self, trend_direction: str, trend_magnitude: float) -> str:
        """Assess climate adaptation priority."""
        if trend_direction == 'no_trend' or trend_magnitude < 10:
            return 'low'
        elif trend_magnitude < 50:
            return 'medium'
        else:
            return 'high'

    def _generate_long_term_recommendations(self, trend_direction: str, trend_magnitude: float) -> List[str]:
        """Generate long-term climate adaptation recommendations."""
        recommendations = []

        if trend_direction == 'increasing' and trend_magnitude > 20:
            recommendations.extend([
                'Invest in flood-resistant infrastructure',
                'Expand water treatment capacity',
                'Develop advanced stormwater management',
                'Plan for increased operational costs'
            ])
        elif trend_direction == 'decreasing' and abs(trend_magnitude) > 20:
            recommendations.extend([
                'Diversify water supply sources',
                'Invest in water recycling technologies',
                'Develop drought-resistant operations',
                'Plan for water scarcity scenarios'
            ])

        if abs(trend_magnitude) > 30:
            recommendations.extend([
                'Conduct regular climate risk assessments',
                'Develop flexible infrastructure designs',
                'Create adaptive management protocols',
                'Invest in climate monitoring systems'
            ])

        return recommendations


# Convenience functions
async def analyze_location_precipitation_patterns(data: List[ProcessedClimateData],
                                                location: str = None) -> PrecipitationAnalysisResult:
    """Analyze precipitation patterns for a specific location."""
    analyzer = PrecipitationPatternAnalyzer()
    await analyzer.initialize()

    return await analyzer.analyze_precipitation_patterns(data, location)


async def compare_precipitation_patterns(data_sets: Dict[str, List[ProcessedClimateData]]) -> Dict[str, PrecipitationAnalysisResult]:
    """Compare precipitation patterns across multiple locations."""
    analyzer = PrecipitationPatternAnalyzer()
    await analyzer.initialize()

    results = {}
    for location, data in data_sets.items():
        try:
            result = await analyzer.analyze_precipitation_patterns(data, location)
            results[location] = result
        except Exception as e:
            logger.error(f"Failed to analyze precipitation patterns for {location}: {e}")

    return results


async def assess_drought_risk_for_location(data: List[ProcessedClimateData],
                                         location: str = None) -> Dict[str, Any]:
    """Assess drought risk for a specific location."""
    analyzer = PrecipitationPatternAnalyzer()
    await analyzer.initialize()

    result = await analyzer.analyze_precipitation_patterns(data, location)
    return result.drought_analysis if result else {}


async def assess_flood_risk_for_location(data: List[ProcessedClimateData],
                                       location: str = None) -> Dict[str, Any]:
    """Assess flood risk for a specific location."""
    analyzer = PrecipitationPatternAnalyzer()
    await analyzer.initialize()

    result = await analyzer.analyze_precipitation_patterns(data, location)
    return result.flood_risk_assessment if result else {}