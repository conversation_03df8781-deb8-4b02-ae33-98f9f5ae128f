"""
Climate Data Collector - Collects and processes climate data from multiple APIs

This module implements a comprehensive climate data collection system that
integrates with multiple climate data sources and APIs.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import aiohttp
import pandas as pd
from dataclasses import dataclass

from src.utils.config import get_settings
from src.utils.database import get_database_connection

logger = logging.getLogger(__name__)


@dataclass
class ClimateDataPoint:
    """Represents a single climate data point."""
    timestamp: datetime
    location: str
    latitude: float
    longitude: float
    temperature: Optional[float] = None
    humidity: Optional[float] = None
    precipitation: Optional[float] = None
    wind_speed: Optional[float] = None
    pressure: Optional[float] = None
    source: str = "unknown"
    metadata: Dict[str, Any] = None


class ClimateDataCollector:
    """
    Comprehensive climate data collector that integrates with multiple APIs.
    
    Supported data sources:
    - OpenWeatherMap API
    - NASA Climate Data
    - NOAA Climate Data
    - World Bank Climate API
    - European Space Agency Climate Data
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_initialized = False
        self.collection_interval = 300  # 5 minutes
        self.is_collecting = False
        
        # API configurations
        self.api_configs = {
            'openweather': {
                'base_url': 'https://api.openweathermap.org/data/2.5',
                'api_key': self.settings.OPENWEATHER_API_KEY,
                'rate_limit': 60  # requests per minute
            },
            'nasa': {
                'base_url': 'https://api.nasa.gov',
                'api_key': self.settings.NASA_API_KEY,
                'rate_limit': 1000  # requests per hour
            },
            'noaa': {
                'base_url': 'https://www.ncdc.noaa.gov/cdo-web/api/v2',
                'api_key': self.settings.NOAA_API_TOKEN,
                'rate_limit': 1000  # requests per day
            },
            'worldbank': {
                'base_url': 'https://climateknowledgeportal.worldbank.org/api',
                'api_key': self.settings.WORLDBANK_API_KEY,
                'rate_limit': 100  # requests per minute
            }
        }
        
        # Default locations for data collection
        self.default_locations = [
            {'name': 'New York', 'lat': 40.7128, 'lon': -74.0060},
            {'name': 'London', 'lat': 51.5074, 'lon': -0.1278},
            {'name': 'Tokyo', 'lat': 35.6762, 'lon': 139.6503},
            {'name': 'Sydney', 'lat': -33.8688, 'lon': 151.2093},
            {'name': 'Mumbai', 'lat': 19.0760, 'lon': 72.8777}
        ]
    
    async def initialize(self):
        """Initialize the climate data collector."""
        try:
            logger.info("Initializing Climate Data Collector...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test API connections
            await self._test_api_connections()
            
            # Initialize database tables
            await self._initialize_database()
            
            self.is_initialized = True
            logger.info("Climate Data Collector initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Climate Data Collector: {e}")
            raise
    
    async def _test_api_connections(self):
        """Test connections to all configured APIs."""
        for api_name, config in self.api_configs.items():
            try:
                if config['api_key']:
                    await self._test_single_api(api_name, config)
                    logger.info(f"API connection test passed: {api_name}")
                else:
                    logger.warning(f"API key not configured for: {api_name}")
            except Exception as e:
                logger.warning(f"API connection test failed for {api_name}: {e}")
    
    async def _test_single_api(self, api_name: str, config: Dict[str, Any]):
        """Test connection to a single API."""
        if api_name == 'openweather':
            url = f"{config['base_url']}/weather"
            params = {
                'q': 'London',
                'appid': config['api_key'],
                'units': 'metric'
            }
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}")
        
        elif api_name == 'nasa':
            url = f"{config['base_url']}/planetary/apod"
            params = {'api_key': config['api_key']}
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}")
        
        # Add more API tests as needed
    
    async def _initialize_database(self):
        """Initialize database tables for climate data storage."""
        try:
            async with get_database_connection() as conn:
                # Create climate_data table
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS climate_data (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP NOT NULL,
                        location VARCHAR(255) NOT NULL,
                        latitude FLOAT NOT NULL,
                        longitude FLOAT NOT NULL,
                        temperature FLOAT,
                        humidity FLOAT,
                        precipitation FLOAT,
                        wind_speed FLOAT,
                        pressure FLOAT,
                        source VARCHAR(100) NOT NULL,
                        metadata JSONB,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes
                await conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_climate_data_timestamp 
                    ON climate_data(timestamp)
                """)
                
                await conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_climate_data_location 
                    ON climate_data(location)
                """)
                
                await conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def collect_current_weather(self, locations: List[Dict[str, Any]] = None) -> List[ClimateDataPoint]:
        """Collect current weather data for specified locations."""
        if not locations:
            locations = self.default_locations
        
        data_points = []
        
        for location in locations:
            try:
                # Collect from OpenWeatherMap
                if self.api_configs['openweather']['api_key']:
                    weather_data = await self._collect_openweather_data(location)
                    if weather_data:
                        data_points.append(weather_data)
                
                # Add delay to respect rate limits
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Failed to collect weather data for {location['name']}: {e}")
        
        return data_points
    
    async def _collect_openweather_data(self, location: Dict[str, Any]) -> Optional[ClimateDataPoint]:
        """Collect data from OpenWeatherMap API."""
        try:
            url = f"{self.api_configs['openweather']['base_url']}/weather"
            params = {
                'lat': location['lat'],
                'lon': location['lon'],
                'appid': self.api_configs['openweather']['api_key'],
                'units': 'metric'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    return ClimateDataPoint(
                        timestamp=datetime.utcnow(),
                        location=location['name'],
                        latitude=location['lat'],
                        longitude=location['lon'],
                        temperature=data['main'].get('temp'),
                        humidity=data['main'].get('humidity'),
                        precipitation=data.get('rain', {}).get('1h', 0),
                        wind_speed=data['wind'].get('speed'),
                        pressure=data['main'].get('pressure'),
                        source='openweather',
                        metadata={
                            'weather_description': data['weather'][0]['description'],
                            'visibility': data.get('visibility'),
                            'clouds': data['clouds'].get('all')
                        }
                    )
                else:
                    logger.error(f"OpenWeatherMap API error: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error collecting OpenWeatherMap data: {e}")
            return None
    
    async def collect_historical_data(self, 
                                    location: Dict[str, Any], 
                                    start_date: datetime, 
                                    end_date: datetime) -> List[ClimateDataPoint]:
        """Collect historical climate data for a location and date range."""
        data_points = []
        
        try:
            # Collect from multiple sources
            if self.api_configs['noaa']['api_key']:
                noaa_data = await self._collect_noaa_historical_data(location, start_date, end_date)
                data_points.extend(noaa_data)
            
            # Add more historical data sources as needed
            
        except Exception as e:
            logger.error(f"Failed to collect historical data: {e}")
        
        return data_points
    
    async def _collect_noaa_historical_data(self, 
                                          location: Dict[str, Any], 
                                          start_date: datetime, 
                                          end_date: datetime) -> List[ClimateDataPoint]:
        """Collect historical data from NOAA API."""
        # Implementation for NOAA historical data collection
        # This would involve finding the nearest weather station and collecting data
        return []
    
    async def store_data_points(self, data_points: List[ClimateDataPoint]):
        """Store climate data points in the database."""
        try:
            async with get_database_connection() as conn:
                for point in data_points:
                    await conn.execute("""
                        INSERT INTO climate_data 
                        (timestamp, location, latitude, longitude, temperature, 
                         humidity, precipitation, wind_speed, pressure, source, metadata)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    """, 
                    point.timestamp, point.location, point.latitude, point.longitude,
                    point.temperature, point.humidity, point.precipitation,
                    point.wind_speed, point.pressure, point.source, point.metadata
                    )
                
                await conn.commit()
                logger.info(f"Stored {len(data_points)} climate data points")
                
        except Exception as e:
            logger.error(f"Failed to store climate data: {e}")
            raise
    
    async def start_continuous_collection(self):
        """Start continuous climate data collection."""
        if not self.is_initialized:
            raise ValueError("Collector not initialized")
        
        self.is_collecting = True
        logger.info("Starting continuous climate data collection...")
        
        while self.is_collecting:
            try:
                # Collect current weather data
                data_points = await self.collect_current_weather()
                
                # Store data points
                if data_points:
                    await self.store_data_points(data_points)
                
                # Wait for next collection cycle
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in continuous collection: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def stop_continuous_collection(self):
        """Stop continuous climate data collection."""
        self.is_collecting = False
        logger.info("Stopped continuous climate data collection")
    
    async def get_recent_data(self, 
                            location: str = None, 
                            hours: int = 24) -> pd.DataFrame:
        """Get recent climate data from the database."""
        try:
            query = """
                SELECT * FROM climate_data 
                WHERE timestamp >= $1
            """
            params = [datetime.utcnow() - timedelta(hours=hours)]
            
            if location:
                query += " AND location = $2"
                params.append(location)
            
            query += " ORDER BY timestamp DESC"
            
            async with get_database_connection() as conn:
                rows = await conn.fetch(query, *params)
                
                # Convert to DataFrame
                data = []
                for row in rows:
                    data.append(dict(row))
                
                return pd.DataFrame(data)
                
        except Exception as e:
            logger.error(f"Failed to get recent data: {e}")
            return pd.DataFrame()
    
    async def shutdown(self):
        """Shutdown the climate data collector."""
        try:
            logger.info("Shutting down Climate Data Collector...")
            
            # Stop continuous collection
            await self.stop_continuous_collection()
            
            # Close HTTP session
            if self.session:
                await self.session.close()
            
            logger.info("Climate Data Collector shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during collector shutdown: {e}")
