"""Federated Learning System for Water Management Networks."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import hashlib

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Types of federated learning nodes."""
    TREATMENT_PLANT = "treatment_plant"
    DISTRIBUTION_CENTER = "distribution_center"
    MONITORING_STATION = "monitoring_station"
    RESEARCH_FACILITY = "research_facility"
    REGULATORY_AGENCY = "regulatory_agency"


class ModelType(Enum):
    """Types of federated models."""
    WATER_QUALITY_PREDICTION = "water_quality_prediction"
    ENERGY_OPTIMIZATION = "energy_optimization"
    MAINTENANCE_PREDICTION = "maintenance_prediction"
    DEMAND_FORECASTING = "demand_forecasting"
    ANOMALY_DETECTION = "anomaly_detection"


@dataclass
class FederatedNode:
    """Federated learning node representation."""
    node_id: str
    node_type: NodeType
    location: str
    data_size: int
    model_version: str
    last_update: datetime
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    privacy_level: str = "high"
    is_active: bool = True


@dataclass
class ModelUpdate:
    """Model update from federated node."""
    update_id: str
    node_id: str
    model_type: ModelType
    parameters: Dict[str, np.ndarray]
    data_size: int
    performance_metrics: Dict[str, float]
    timestamp: datetime
    privacy_preserved: bool = True


@dataclass
class GlobalModel:
    """Global federated model."""
    model_id: str
    model_type: ModelType
    version: str
    parameters: Dict[str, np.ndarray]
    participating_nodes: List[str]
    performance_metrics: Dict[str, float]
    created_at: datetime
    last_updated: datetime


class FederatedLearningCoordinator:
    """Coordinator for federated learning across water management networks."""
    
    def __init__(self):
        self.nodes: Dict[str, FederatedNode] = {}
        self.global_models: Dict[str, GlobalModel] = {}
        self.model_updates: List[ModelUpdate] = []
        self.aggregation_rounds = 0
        
        # Federated learning configuration
        self.config = {
            'min_nodes_for_aggregation': 3,
            'aggregation_frequency': timedelta(hours=6),
            'privacy_budget': 1.0,
            'differential_privacy': True,
            'secure_aggregation': True,
            'model_compression': True
        }
        
        # Initialize system
        self._initialize_nodes()
        self._initialize_global_models()
    
    def _initialize_nodes(self):
        """Initialize federated learning nodes."""
        nodes_config = [
            {
                'node_id': 'plant_001',
                'node_type': NodeType.TREATMENT_PLANT,
                'location': 'Central Treatment Facility',
                'data_size': 10000
            },
            {
                'node_id': 'plant_002',
                'node_type': NodeType.TREATMENT_PLANT,
                'location': 'North Treatment Plant',
                'data_size': 8500
            },
            {
                'node_id': 'dist_001',
                'node_type': NodeType.DISTRIBUTION_CENTER,
                'location': 'Main Distribution Hub',
                'data_size': 15000
            },
            {
                'node_id': 'monitor_001',
                'node_type': NodeType.MONITORING_STATION,
                'location': 'River Monitoring Station',
                'data_size': 5000
            },
            {
                'node_id': 'research_001',
                'node_type': NodeType.RESEARCH_FACILITY,
                'location': 'Water Research Institute',
                'data_size': 20000
            }
        ]
        
        for config in nodes_config:
            node = FederatedNode(
                node_id=config['node_id'],
                node_type=config['node_type'],
                location=config['location'],
                data_size=config['data_size'],
                model_version='1.0.0',
                last_update=datetime.now(),
                performance_metrics={
                    'accuracy': np.random.uniform(0.85, 0.95),
                    'data_quality': np.random.uniform(0.8, 0.95),
                    'update_frequency': np.random.uniform(0.7, 0.9)
                }
            )
            self.nodes[config['node_id']] = node
    
    def _initialize_global_models(self):
        """Initialize global federated models."""
        model_configs = [
            {
                'model_id': 'global_water_quality',
                'model_type': ModelType.WATER_QUALITY_PREDICTION,
                'version': '1.0.0'
            },
            {
                'model_id': 'global_energy_opt',
                'model_type': ModelType.ENERGY_OPTIMIZATION,
                'version': '1.0.0'
            },
            {
                'model_id': 'global_maintenance',
                'model_type': ModelType.MAINTENANCE_PREDICTION,
                'version': '1.0.0'
            },
            {
                'model_id': 'global_demand',
                'model_type': ModelType.DEMAND_FORECASTING,
                'version': '1.0.0'
            },
            {
                'model_id': 'global_anomaly',
                'model_type': ModelType.ANOMALY_DETECTION,
                'version': '1.0.0'
            }
        ]
        
        for config in model_configs:
            # Initialize with random parameters (in practice, would be pre-trained)
            parameters = {
                'weights_layer1': np.random.randn(10, 64) * 0.1,
                'weights_layer2': np.random.randn(64, 32) * 0.1,
                'weights_output': np.random.randn(32, 1) * 0.1,
                'bias_layer1': np.zeros(64),
                'bias_layer2': np.zeros(32),
                'bias_output': np.zeros(1)
            }
            
            model = GlobalModel(
                model_id=config['model_id'],
                model_type=config['model_type'],
                version=config['version'],
                parameters=parameters,
                participating_nodes=list(self.nodes.keys()),
                performance_metrics={
                    'global_accuracy': 0.85,
                    'convergence_rate': 0.02,
                    'privacy_loss': 0.1
                },
                created_at=datetime.now(),
                last_updated=datetime.now()
            )
            
            self.global_models[config['model_id']] = model
    
    @log_async_function_call
    async def register_node(self, node_config: Dict[str, Any]) -> Dict[str, Any]:
        """Register new federated learning node."""
        try:
            node_id = node_config['node_id']
            
            if node_id in self.nodes:
                return {'status': 'error', 'error': 'Node already registered'}
            
            node = FederatedNode(
                node_id=node_id,
                node_type=NodeType(node_config['node_type']),
                location=node_config['location'],
                data_size=node_config['data_size'],
                model_version='1.0.0',
                last_update=datetime.now(),
                performance_metrics=node_config.get('performance_metrics', {}),
                privacy_level=node_config.get('privacy_level', 'high')
            )
            
            self.nodes[node_id] = node
            
            # Add to existing global models
            for model in self.global_models.values():
                if node_id not in model.participating_nodes:
                    model.participating_nodes.append(node_id)
            
            return {
                'status': 'success',
                'node_id': node_id,
                'registered_at': datetime.now().isoformat(),
                'available_models': list(self.global_models.keys())
            }
            
        except Exception as e:
            logger.error(f"Node registration failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def submit_model_update(self, node_id: str, model_type: ModelType,
                                parameters: Dict[str, Any], 
                                performance_metrics: Dict[str, float]) -> Dict[str, Any]:
        """Submit model update from federated node."""
        try:
            if node_id not in self.nodes:
                return {'status': 'error', 'error': 'Node not registered'}
            
            node = self.nodes[node_id]
            
            # Apply differential privacy if enabled
            if self.config['differential_privacy']:
                parameters = self._apply_differential_privacy(parameters)
            
            # Create model update
            update_id = f"update_{node_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Convert parameters to numpy arrays
            np_parameters = {}
            for key, value in parameters.items():
                if isinstance(value, list):
                    np_parameters[key] = np.array(value)
                elif isinstance(value, np.ndarray):
                    np_parameters[key] = value
                else:
                    np_parameters[key] = np.array([value])
            
            update = ModelUpdate(
                update_id=update_id,
                node_id=node_id,
                model_type=model_type,
                parameters=np_parameters,
                data_size=node.data_size,
                performance_metrics=performance_metrics,
                timestamp=datetime.now(),
                privacy_preserved=self.config['differential_privacy']
            )
            
            self.model_updates.append(update)
            
            # Update node information
            node.last_update = datetime.now()
            node.performance_metrics.update(performance_metrics)
            
            # Check if aggregation should be triggered
            aggregation_needed = await self._check_aggregation_trigger(model_type)
            
            result = {
                'status': 'success',
                'update_id': update_id,
                'privacy_preserved': update.privacy_preserved,
                'aggregation_triggered': aggregation_needed
            }
            
            if aggregation_needed:
                aggregation_result = await self._perform_aggregation(model_type)
                result['aggregation_result'] = aggregation_result
            
            return result
            
        except Exception as e:
            logger.error(f"Model update submission failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _apply_differential_privacy(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Apply differential privacy to model parameters."""
        privacy_parameters = {}
        noise_scale = 0.01  # Configurable noise scale
        
        for key, value in parameters.items():
            if isinstance(value, (list, np.ndarray)):
                # Add Gaussian noise for differential privacy
                noise = np.random.normal(0, noise_scale, np.array(value).shape)
                privacy_parameters[key] = np.array(value) + noise
            else:
                # Add noise to scalar values
                noise = np.random.normal(0, noise_scale)
                privacy_parameters[key] = value + noise
        
        return privacy_parameters
    
    async def _check_aggregation_trigger(self, model_type: ModelType) -> bool:
        """Check if model aggregation should be triggered."""
        # Count recent updates for this model type
        recent_updates = [
            update for update in self.model_updates
            if update.model_type == model_type and
            (datetime.now() - update.timestamp) < self.config['aggregation_frequency']
        ]
        
        # Check if minimum number of nodes have submitted updates
        unique_nodes = set(update.node_id for update in recent_updates)
        
        return len(unique_nodes) >= self.config['min_nodes_for_aggregation']
    
    @log_async_function_call
    async def _perform_aggregation(self, model_type: ModelType) -> Dict[str, Any]:
        """Perform federated averaging aggregation."""
        try:
            # Find global model
            global_model = None
            for model in self.global_models.values():
                if model.model_type == model_type:
                    global_model = model
                    break
            
            if not global_model:
                return {'status': 'error', 'error': 'Global model not found'}
            
            # Get recent updates for this model type
            recent_updates = [
                update for update in self.model_updates
                if update.model_type == model_type and
                (datetime.now() - update.timestamp) < self.config['aggregation_frequency']
            ]
            
            if not recent_updates:
                return {'status': 'error', 'error': 'No recent updates available'}
            
            # Perform federated averaging
            aggregated_parameters = self._federated_averaging(recent_updates)
            
            # Update global model
            global_model.parameters = aggregated_parameters
            global_model.last_updated = datetime.now()
            
            # Update version
            version_parts = global_model.version.split('.')
            version_parts[-1] = str(int(version_parts[-1]) + 1)
            global_model.version = '.'.join(version_parts)
            
            # Calculate new performance metrics
            performance_metrics = self._calculate_global_performance(recent_updates)
            global_model.performance_metrics = performance_metrics
            
            self.aggregation_rounds += 1
            
            # Clear processed updates
            self.model_updates = [
                update for update in self.model_updates
                if update not in recent_updates
            ]
            
            return {
                'status': 'success',
                'model_id': global_model.model_id,
                'new_version': global_model.version,
                'participating_nodes': len(set(u.node_id for u in recent_updates)),
                'aggregation_round': self.aggregation_rounds,
                'performance_metrics': performance_metrics
            }
            
        except Exception as e:
            logger.error(f"Model aggregation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _federated_averaging(self, updates: List[ModelUpdate]) -> Dict[str, np.ndarray]:
        """Perform federated averaging of model parameters."""
        if not updates:
            return {}
        
        # Calculate weights based on data size
        total_data_size = sum(update.data_size for update in updates)
        weights = [update.data_size / total_data_size for update in updates]
        
        # Get parameter names from first update
        parameter_names = list(updates[0].parameters.keys())
        
        # Perform weighted averaging
        aggregated_parameters = {}
        
        for param_name in parameter_names:
            # Collect parameters from all updates
            param_values = []
            param_weights = []
            
            for i, update in enumerate(updates):
                if param_name in update.parameters:
                    param_values.append(update.parameters[param_name])
                    param_weights.append(weights[i])
            
            if param_values:
                # Weighted average
                weighted_sum = np.zeros_like(param_values[0])
                total_weight = 0
                
                for value, weight in zip(param_values, param_weights):
                    weighted_sum += value * weight
                    total_weight += weight
                
                if total_weight > 0:
                    aggregated_parameters[param_name] = weighted_sum / total_weight
                else:
                    aggregated_parameters[param_name] = param_values[0]
        
        return aggregated_parameters
    
    def _calculate_global_performance(self, updates: List[ModelUpdate]) -> Dict[str, float]:
        """Calculate global model performance metrics."""
        if not updates:
            return {}
        
        # Aggregate performance metrics
        metrics = {}
        
        # Collect all metric names
        all_metrics = set()
        for update in updates:
            all_metrics.update(update.performance_metrics.keys())
        
        # Calculate weighted averages
        total_data_size = sum(update.data_size for update in updates)
        
        for metric_name in all_metrics:
            weighted_sum = 0
            total_weight = 0
            
            for update in updates:
                if metric_name in update.performance_metrics:
                    weight = update.data_size / total_data_size
                    weighted_sum += update.performance_metrics[metric_name] * weight
                    total_weight += weight
            
            if total_weight > 0:
                metrics[metric_name] = weighted_sum / total_weight
        
        # Add federated-specific metrics
        metrics['convergence_rate'] = self._calculate_convergence_rate(updates)
        metrics['privacy_loss'] = self._calculate_privacy_loss(updates)
        metrics['communication_efficiency'] = self._calculate_communication_efficiency(updates)
        
        return metrics
    
    def _calculate_convergence_rate(self, updates: List[ModelUpdate]) -> float:
        """Calculate model convergence rate."""
        if len(updates) < 2:
            return 0.0
        
        # Simple convergence measure based on parameter variance
        parameter_variances = []
        
        for param_name in updates[0].parameters.keys():
            param_values = [update.parameters[param_name] for update in updates 
                          if param_name in update.parameters]
            
            if len(param_values) > 1:
                # Calculate variance across nodes
                param_array = np.array(param_values)
                variance = np.var(param_array)
                parameter_variances.append(variance)
        
        if parameter_variances:
            avg_variance = np.mean(parameter_variances)
            # Convert to convergence rate (lower variance = higher convergence)
            convergence_rate = 1.0 / (1.0 + avg_variance)
            return float(convergence_rate)
        
        return 0.5  # Default moderate convergence
    
    def _calculate_privacy_loss(self, updates: List[ModelUpdate]) -> float:
        """Calculate privacy loss for differential privacy."""
        if not self.config['differential_privacy']:
            return 0.0
        
        # Simple privacy loss calculation
        privacy_preserved_updates = sum(1 for update in updates if update.privacy_preserved)
        privacy_loss = 1.0 - (privacy_preserved_updates / len(updates))
        
        return privacy_loss
    
    def _calculate_communication_efficiency(self, updates: List[ModelUpdate]) -> float:
        """Calculate communication efficiency."""
        if not updates:
            return 0.0
        
        # Measure based on update frequency and data size
        total_data_transferred = sum(
            sum(param.nbytes for param in update.parameters.values())
            for update in updates
        )
        
        # Normalize by number of nodes and time period
        num_nodes = len(set(update.node_id for update in updates))
        efficiency = 1.0 / (1.0 + total_data_transferred / (num_nodes * 1e6))  # MB normalization
        
        return efficiency
    
    @log_async_function_call
    async def get_global_model(self, model_type: ModelType) -> Dict[str, Any]:
        """Get current global model."""
        try:
            global_model = None
            for model in self.global_models.values():
                if model.model_type == model_type:
                    global_model = model
                    break
            
            if not global_model:
                return {'status': 'error', 'error': 'Global model not found'}
            
            # Convert numpy arrays to lists for JSON serialization
            serializable_parameters = {}
            for key, value in global_model.parameters.items():
                if isinstance(value, np.ndarray):
                    serializable_parameters[key] = value.tolist()
                else:
                    serializable_parameters[key] = value
            
            return {
                'status': 'success',
                'model_info': {
                    'model_id': global_model.model_id,
                    'model_type': global_model.model_type.value,
                    'version': global_model.version,
                    'participating_nodes': global_model.participating_nodes,
                    'performance_metrics': global_model.performance_metrics,
                    'last_updated': global_model.last_updated.isoformat(),
                    'parameters': serializable_parameters
                }
            }
            
        except Exception as e:
            logger.error(f"Global model retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_federation_status(self) -> Dict[str, Any]:
        """Get comprehensive federation status."""
        try:
            # Node statistics
            active_nodes = sum(1 for node in self.nodes.values() if node.is_active)
            total_data_size = sum(node.data_size for node in self.nodes.values())
            
            # Model statistics
            model_stats = {}
            for model in self.global_models.values():
                model_stats[model.model_id] = {
                    'version': model.version,
                    'participating_nodes': len(model.participating_nodes),
                    'performance': model.performance_metrics,
                    'last_updated': model.last_updated.isoformat()
                }
            
            # Recent activity
            recent_updates = [
                update for update in self.model_updates
                if (datetime.now() - update.timestamp) < timedelta(hours=24)
            ]
            
            return {
                'status': 'success',
                'federation_status': {
                    'nodes': {
                        'total_nodes': len(self.nodes),
                        'active_nodes': active_nodes,
                        'total_data_size': total_data_size,
                        'node_types': {
                            node_type.value: sum(1 for node in self.nodes.values() 
                                               if node.node_type == node_type)
                            for node_type in NodeType
                        }
                    },
                    'models': {
                        'total_models': len(self.global_models),
                        'model_statistics': model_stats
                    },
                    'activity': {
                        'aggregation_rounds': self.aggregation_rounds,
                        'recent_updates': len(recent_updates),
                        'last_aggregation': max(
                            (model.last_updated for model in self.global_models.values()),
                            default=datetime.now()
                        ).isoformat()
                    },
                    'configuration': self.config
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Federation status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_node_performance(self, node_id: str) -> Dict[str, Any]:
        """Get performance metrics for specific node."""
        if node_id not in self.nodes:
            return {'status': 'error', 'error': 'Node not found'}
        
        node = self.nodes[node_id]
        
        # Get node's recent updates
        node_updates = [
            update for update in self.model_updates
            if update.node_id == node_id and
            (datetime.now() - update.timestamp) < timedelta(days=7)
        ]
        
        return {
            'status': 'success',
            'node_performance': {
                'node_id': node_id,
                'node_type': node.node_type.value,
                'location': node.location,
                'data_size': node.data_size,
                'performance_metrics': node.performance_metrics,
                'last_update': node.last_update.isoformat(),
                'recent_updates': len(node_updates),
                'privacy_level': node.privacy_level,
                'is_active': node.is_active
            }
        }


# Convenience functions
async def setup_federated_learning() -> Dict[str, Any]:
    """Set up federated learning system."""
    coordinator = FederatedLearningCoordinator()
    return await coordinator.get_federation_status()


async def submit_federated_update(node_id: str, model_type: str, 
                                parameters: Dict[str, Any],
                                performance_metrics: Dict[str, float]) -> Dict[str, Any]:
    """Submit federated learning update."""
    coordinator = FederatedLearningCoordinator()
    return await coordinator.submit_model_update(
        node_id, ModelType(model_type), parameters, performance_metrics
    )
