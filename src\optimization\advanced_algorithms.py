"""
Advanced Optimization Algorithms Suite.

Comprehensive optimization algorithms including Particle Swarm Optimization,
Bayesian Optimization, Multi-objective Optimization, and Evolutionary Strategies.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
import logging
import asyncio
from abc import ABC, abstractmethod
import scipy.optimize as opt
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern
import matplotlib.pyplot as plt

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class OptimizationObjective(Enum):
    """Optimization objectives."""
    MINIMIZE = "minimize"
    MAXIMIZE = "maximize"


@dataclass
class OptimizationProblem:
    """Optimization problem definition."""
    objective_function: Callable
    variables: Dict[str, Tuple[float, float]]  # Variable bounds
    constraints: List[Dict[str, Any]]
    objectives: List[str]
    objective_types: List[OptimizationObjective]
    problem_type: str


@dataclass
class OptimizationResult:
    """Optimization result structure."""
    best_solution: Dict[str, float]
    best_objective_values: List[float]
    optimization_history: List[Dict[str, Any]]
    convergence_data: Dict[str, Any]
    algorithm_used: str
    computation_time: float
    success: bool


class ParticleSwarmOptimizer:
    """Particle Swarm Optimization algorithm."""

    def __init__(self, swarm_size: int = 50, max_iterations: int = 1000,
                 w: float = 0.7, c1: float = 1.5, c2: float = 1.5):
        self.swarm_size = swarm_size
        self.max_iterations = max_iterations
        self.w = w  # Inertia weight
        self.c1 = c1  # Cognitive parameter
        self.c2 = c2  # Social parameter

    @log_async_function_call
    async def optimize(self, problem: OptimizationProblem) -> OptimizationResult:
        """Optimize using Particle Swarm Optimization."""
        try:
            logger.info("Starting Particle Swarm Optimization")

            # Initialize swarm
            swarm = await self._initialize_swarm(problem)

            # Track best solutions
            global_best_position = None
            global_best_fitness = float('inf')
            optimization_history = []

            for iteration in range(self.max_iterations):
                # Evaluate fitness for all particles
                for particle in swarm:
                    fitness = await self._evaluate_fitness(particle['position'], problem)
                    particle['fitness'] = fitness

                    # Update personal best
                    if fitness < particle['best_fitness']:
                        particle['best_position'] = particle['position'].copy()
                        particle['best_fitness'] = fitness

                    # Update global best
                    if fitness < global_best_fitness:
                        global_best_position = particle['position'].copy()
                        global_best_fitness = fitness

                # Update velocities and positions
                for particle in swarm:
                    await self._update_particle(particle, global_best_position, problem)

                # Record iteration data
                iteration_data = {
                    'iteration': iteration,
                    'best_fitness': global_best_fitness,
                    'average_fitness': np.mean([p['fitness'] for p in swarm]),
                    'diversity': self._calculate_diversity(swarm)
                }
                optimization_history.append(iteration_data)

                # Check convergence
                if await self._check_convergence(optimization_history):
                    logger.info(f"PSO converged at iteration {iteration}")
                    break

            # Convert position to solution format
            best_solution = {}
            var_names = list(problem.variables.keys())
            for i, var_name in enumerate(var_names):
                best_solution[var_name] = global_best_position[i]

            return OptimizationResult(
                best_solution=best_solution,
                best_objective_values=[global_best_fitness],
                optimization_history=optimization_history,
                convergence_data={'converged': True, 'iterations': len(optimization_history)},
                algorithm_used='ParticleSwarmOptimization',
                computation_time=0.0,  # Would be measured in real implementation
                success=True
            )

        except Exception as e:
            logger.error(f"PSO optimization failed: {e}")
            return OptimizationResult(
                best_solution={},
                best_objective_values=[],
                optimization_history=[],
                convergence_data={'converged': False},
                algorithm_used='ParticleSwarmOptimization',
                computation_time=0.0,
                success=False
            )

    async def _initialize_swarm(self, problem: OptimizationProblem) -> List[Dict[str, Any]]:
        """Initialize particle swarm."""
        swarm = []
        var_names = list(problem.variables.keys())
        n_vars = len(var_names)

        for _ in range(self.swarm_size):
            # Random position within bounds
            position = []
            for var_name in var_names:
                bounds = problem.variables[var_name]
                pos = np.random.uniform(bounds[0], bounds[1])
                position.append(pos)

            # Random velocity
            velocity = np.random.uniform(-1, 1, n_vars)

            particle = {
                'position': np.array(position),
                'velocity': np.array(velocity),
                'best_position': np.array(position),
                'best_fitness': float('inf'),
                'fitness': float('inf')
            }
            swarm.append(particle)

        return swarm

    async def _evaluate_fitness(self, position: np.ndarray, problem: OptimizationProblem) -> float:
        """Evaluate fitness of a position."""
        # Convert position to variable dictionary
        var_names = list(problem.variables.keys())
        variables = {}
        for i, var_name in enumerate(var_names):
            variables[var_name] = position[i]

        # Evaluate objective function
        try:
            fitness = problem.objective_function(variables)

            # Apply constraints (penalty method)
            penalty = 0
            for constraint in problem.constraints:
                if constraint['type'] == 'inequality':
                    violation = max(0, constraint['function'](variables))
                    penalty += violation * 1000  # Penalty factor
                elif constraint['type'] == 'equality':
                    violation = abs(constraint['function'](variables))
                    penalty += violation * 1000

            return fitness + penalty

        except Exception:
            return float('inf')  # Invalid solution

    async def _update_particle(self, particle: Dict[str, Any],
                             global_best: np.ndarray,
                             problem: OptimizationProblem):
        """Update particle velocity and position."""
        # Update velocity
        r1, r2 = np.random.random(2)

        cognitive_component = self.c1 * r1 * (particle['best_position'] - particle['position'])
        social_component = self.c2 * r2 * (global_best - particle['position'])

        particle['velocity'] = (self.w * particle['velocity'] +
                              cognitive_component + social_component)

        # Update position
        particle['position'] += particle['velocity']

        # Apply bounds
        var_names = list(problem.variables.keys())
        for i, var_name in enumerate(var_names):
            bounds = problem.variables[var_name]
            particle['position'][i] = np.clip(particle['position'][i], bounds[0], bounds[1])

    def _calculate_diversity(self, swarm: List[Dict[str, Any]]) -> float:
        """Calculate swarm diversity."""
        positions = np.array([p['position'] for p in swarm])
        center = np.mean(positions, axis=0)
        distances = np.linalg.norm(positions - center, axis=1)
        return np.mean(distances)

    async def _check_convergence(self, history: List[Dict[str, Any]]) -> bool:
        """Check convergence criteria."""
        if len(history) < 50:
            return False

        # Check if best fitness hasn't improved significantly
        recent_best = [h['best_fitness'] for h in history[-50:]]
        improvement = (recent_best[0] - recent_best[-1]) / abs(recent_best[0])

        return improvement < 0.001  # 0.1% improvement threshold


class BayesianOptimizer:
    """Bayesian Optimization with Gaussian Process."""

    def __init__(self, acquisition_function: str = 'expected_improvement',
                 n_initial_points: int = 10, max_iterations: int = 100):
        self.acquisition_function = acquisition_function
        self.n_initial_points = n_initial_points
        self.max_iterations = max_iterations
        self.gp = None

    @log_async_function_call
    async def optimize(self, problem: OptimizationProblem) -> OptimizationResult:
        """Optimize using Bayesian Optimization."""
        try:
            logger.info("Starting Bayesian Optimization")

            # Initialize Gaussian Process
            kernel = Matern(length_scale=1.0, nu=2.5)
            self.gp = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, normalize_y=True)

            # Generate initial points
            X_observed, y_observed = await self._generate_initial_points(problem)

            optimization_history = []
            best_solution = None
            best_objective = float('inf')

            for iteration in range(self.max_iterations):
                # Fit Gaussian Process
                self.gp.fit(X_observed, y_observed)

                # Find next point to evaluate
                next_point = await self._acquire_next_point(problem, X_observed, y_observed)

                # Evaluate objective at next point
                next_objective = await self._evaluate_objective(next_point, problem)

                # Update observations
                X_observed = np.vstack([X_observed, next_point])
                y_observed = np.append(y_observed, next_objective)

                # Update best solution
                if next_objective < best_objective:
                    best_objective = next_objective
                    var_names = list(problem.variables.keys())
                    best_solution = {}
                    for i, var_name in enumerate(var_names):
                        best_solution[var_name] = next_point[i]

                # Record iteration data
                iteration_data = {
                    'iteration': iteration,
                    'best_objective': best_objective,
                    'current_objective': next_objective,
                    'acquisition_value': 0.0  # Would calculate actual acquisition value
                }
                optimization_history.append(iteration_data)

                # Check convergence
                if await self._check_bo_convergence(optimization_history):
                    logger.info(f"Bayesian optimization converged at iteration {iteration}")
                    break

            return OptimizationResult(
                best_solution=best_solution or {},
                best_objective_values=[best_objective],
                optimization_history=optimization_history,
                convergence_data={'converged': True, 'iterations': len(optimization_history)},
                algorithm_used='BayesianOptimization',
                computation_time=0.0,
                success=True
            )

        except Exception as e:
            logger.error(f"Bayesian optimization failed: {e}")
            return OptimizationResult(
                best_solution={},
                best_objective_values=[],
                optimization_history=[],
                convergence_data={'converged': False},
                algorithm_used='BayesianOptimization',
                computation_time=0.0,
                success=False
            )

    async def _generate_initial_points(self, problem: OptimizationProblem) -> Tuple[np.ndarray, np.ndarray]:
        """Generate initial observation points."""
        var_names = list(problem.variables.keys())
        n_vars = len(var_names)

        X_initial = []
        y_initial = []

        for _ in range(self.n_initial_points):
            # Random point within bounds
            point = []
            for var_name in var_names:
                bounds = problem.variables[var_name]
                value = np.random.uniform(bounds[0], bounds[1])
                point.append(value)

            X_initial.append(point)

            # Evaluate objective
            objective_value = await self._evaluate_objective(np.array(point), problem)
            y_initial.append(objective_value)

        return np.array(X_initial), np.array(y_initial)

    async def _evaluate_objective(self, point: np.ndarray, problem: OptimizationProblem) -> float:
        """Evaluate objective function at a point."""
        var_names = list(problem.variables.keys())
        variables = {}
        for i, var_name in enumerate(var_names):
            variables[var_name] = point[i]

        try:
            return problem.objective_function(variables)
        except Exception:
            return float('inf')

    async def _acquire_next_point(self, problem: OptimizationProblem,
                                X_observed: np.ndarray, y_observed: np.ndarray) -> np.ndarray:
        """Acquire next point using acquisition function."""
        var_names = list(problem.variables.keys())
        bounds = [problem.variables[var_name] for var_name in var_names]

        def acquisition(x):
            x = x.reshape(1, -1)
            mu, sigma = self.gp.predict(x, return_std=True)

            if self.acquisition_function == 'expected_improvement':
                # Expected Improvement
                best_f = np.min(y_observed)
                z = (best_f - mu) / (sigma + 1e-9)
                ei = (best_f - mu) * norm.cdf(z) + sigma * norm.pdf(z)
                return -ei[0]  # Minimize negative EI
            elif self.acquisition_function == 'upper_confidence_bound':
                # Upper Confidence Bound
                kappa = 2.576  # 99% confidence
                return -(mu[0] - kappa * sigma[0])  # Minimize negative UCB
            else:
                return mu[0]  # Just use mean prediction

        # Optimize acquisition function
        from scipy.stats import norm
        result = opt.minimize(acquisition,
                            x0=np.mean(bounds, axis=1),
                            bounds=bounds,
                            method='L-BFGS-B')

        return result.x

    async def _check_bo_convergence(self, history: List[Dict[str, Any]]) -> bool:
        """Check Bayesian optimization convergence."""
        if len(history) < 20:
            return False

        # Check if best objective hasn't improved
        recent_best = [h['best_objective'] for h in history[-20:]]
        improvement = (recent_best[0] - recent_best[-1]) / abs(recent_best[0])

        return improvement < 0.001


class MultiObjectiveOptimizer:
    """Multi-objective optimization using NSGA-II algorithm."""

    def __init__(self, population_size: int = 100, max_generations: int = 500,
                 crossover_prob: float = 0.9, mutation_prob: float = 0.1):
        self.population_size = population_size
        self.max_generations = max_generations
        self.crossover_prob = crossover_prob
        self.mutation_prob = mutation_prob

    @log_async_function_call
    async def optimize(self, problem: OptimizationProblem) -> OptimizationResult:
        """Optimize using NSGA-II multi-objective algorithm."""
        try:
            logger.info("Starting Multi-objective Optimization (NSGA-II)")

            # Initialize population
            population = await self._initialize_population(problem)

            # Evaluate initial population
            for individual in population:
                individual['objectives'] = await self._evaluate_objectives(individual, problem)

            optimization_history = []
            pareto_front_history = []

            for generation in range(self.max_generations):
                # Non-dominated sorting
                fronts = self._non_dominated_sort(population)

                # Calculate crowding distance
                for front in fronts:
                    self._calculate_crowding_distance(front)

                # Create new population
                new_population = []
                front_index = 0

                while len(new_population) + len(fronts[front_index]) <= self.population_size:
                    new_population.extend(fronts[front_index])
                    front_index += 1

                # Fill remaining slots with best individuals from next front
                if len(new_population) < self.population_size:
                    remaining_front = fronts[front_index]
                    remaining_front.sort(key=lambda x: x['crowding_distance'], reverse=True)
                    remaining_slots = self.population_size - len(new_population)
                    new_population.extend(remaining_front[:remaining_slots])

                # Generate offspring
                offspring = await self._generate_offspring(new_population, problem)

                # Evaluate offspring
                for individual in offspring:
                    individual['objectives'] = await self._evaluate_objectives(individual, problem)

                # Combine parent and offspring populations
                population = new_population + offspring

                # Record generation data
                pareto_front = fronts[0] if fronts else []
                generation_data = {
                    'generation': generation,
                    'pareto_front_size': len(pareto_front),
                    'population_diversity': self._calculate_population_diversity(population),
                    'hypervolume': self._calculate_hypervolume(pareto_front)
                }
                optimization_history.append(generation_data)
                pareto_front_history.append([ind['objectives'] for ind in pareto_front])

                # Check convergence
                if await self._check_mo_convergence(optimization_history):
                    logger.info(f"Multi-objective optimization converged at generation {generation}")
                    break

            # Extract final Pareto front
            final_fronts = self._non_dominated_sort(population)
            final_pareto_front = final_fronts[0] if final_fronts else []

            # Convert to solution format
            pareto_solutions = []
            for individual in final_pareto_front:
                solution = {}
                var_names = list(problem.variables.keys())
                for i, var_name in enumerate(var_names):
                    solution[var_name] = individual['variables'][i]
                pareto_solutions.append({
                    'solution': solution,
                    'objectives': individual['objectives']
                })

            # Select representative solution (closest to ideal point)
            if pareto_solutions:
                ideal_point = [min(sol['objectives'][i] for sol in pareto_solutions)
                             for i in range(len(problem.objectives))]

                best_solution_idx = min(range(len(pareto_solutions)),
                                      key=lambda i: np.linalg.norm(
                                          np.array(pareto_solutions[i]['objectives']) - np.array(ideal_point)
                                      ))
                best_solution = pareto_solutions[best_solution_idx]['solution']
                best_objectives = pareto_solutions[best_solution_idx]['objectives']
            else:
                best_solution = {}
                best_objectives = []

            return OptimizationResult(
                best_solution=best_solution,
                best_objective_values=best_objectives,
                optimization_history=optimization_history,
                convergence_data={
                    'converged': True,
                    'generations': len(optimization_history),
                    'pareto_front_size': len(final_pareto_front),
                    'pareto_solutions': pareto_solutions
                },
                algorithm_used='NSGA-II',
                computation_time=0.0,
                success=True
            )

        except Exception as e:
            logger.error(f"Multi-objective optimization failed: {e}")
            return OptimizationResult(
                best_solution={},
                best_objective_values=[],
                optimization_history=[],
                convergence_data={'converged': False},
                algorithm_used='NSGA-II',
                computation_time=0.0,
                success=False
            )

    async def _initialize_population(self, problem: OptimizationProblem) -> List[Dict[str, Any]]:
        """Initialize population for multi-objective optimization."""
        population = []
        var_names = list(problem.variables.keys())

        for _ in range(self.population_size):
            variables = []
            for var_name in var_names:
                bounds = problem.variables[var_name]
                value = np.random.uniform(bounds[0], bounds[1])
                variables.append(value)

            individual = {
                'variables': np.array(variables),
                'objectives': [],
                'rank': 0,
                'crowding_distance': 0.0
            }
            population.append(individual)

        return population

    async def _evaluate_objectives(self, individual: Dict[str, Any],
                                 problem: OptimizationProblem) -> List[float]:
        """Evaluate all objectives for an individual."""
        var_names = list(problem.variables.keys())
        variables = {}
        for i, var_name in enumerate(var_names):
            variables[var_name] = individual['variables'][i]

        try:
            # For now, assume single objective function returns multiple values
            # In practice, you'd have separate objective functions
            objective_value = problem.objective_function(variables)

            # If single objective, create multiple objectives
            if isinstance(objective_value, (int, float)):
                # Create synthetic multi-objectives for demonstration
                obj1 = objective_value
                obj2 = sum(individual['variables']) / len(individual['variables'])  # Average variable value
                return [obj1, obj2]
            else:
                return list(objective_value)

        except Exception:
            return [float('inf')] * len(problem.objectives)

    def _non_dominated_sort(self, population: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Perform non-dominated sorting."""
        fronts = [[]]

        for individual in population:
            individual['domination_count'] = 0
            individual['dominated_solutions'] = []

            for other in population:
                if self._dominates(individual, other):
                    individual['dominated_solutions'].append(other)
                elif self._dominates(other, individual):
                    individual['domination_count'] += 1

            if individual['domination_count'] == 0:
                individual['rank'] = 0
                fronts[0].append(individual)

        front_index = 0
        while len(fronts[front_index]) > 0:
            next_front = []
            for individual in fronts[front_index]:
                for dominated in individual['dominated_solutions']:
                    dominated['domination_count'] -= 1
                    if dominated['domination_count'] == 0:
                        dominated['rank'] = front_index + 1
                        next_front.append(dominated)

            front_index += 1
            fronts.append(next_front)

        return fronts[:-1]  # Remove empty last front

    def _dominates(self, individual1: Dict[str, Any], individual2: Dict[str, Any]) -> bool:
        """Check if individual1 dominates individual2."""
        obj1 = individual1['objectives']
        obj2 = individual2['objectives']

        # Assuming minimization for all objectives
        better_in_any = any(obj1[i] < obj2[i] for i in range(len(obj1)))
        not_worse_in_any = all(obj1[i] <= obj2[i] for i in range(len(obj1)))

        return better_in_any and not_worse_in_any