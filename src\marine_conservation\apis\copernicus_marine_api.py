#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Copernicus Marine Service API Integration for Marine Conservation
Comprehensive oceanographic data for marine debris tracking and analysis
"""

import os
import json
import asyncio
import aiohttp
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import numpy as np
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class OceanographicData:
    """Comprehensive oceanographic measurement data"""
    latitude: float
    longitude: float
    timestamp: datetime
    sea_surface_temperature: Optional[float] = None  # °C
    sea_surface_height: Optional[float] = None  # meters
    salinity: Optional[float] = None  # PSU (Practical Salinity Units)
    chlorophyll_concentration: Optional[float] = None  # mg/m³
    current_velocity_u: Optional[float] = None  # m/s (eastward)
    current_velocity_v: Optional[float] = None  # m/s (northward)
    current_speed: Optional[float] = None  # m/s
    current_direction: Optional[float] = None  # degrees
    wave_height: Optional[float] = None  # meters
    wave_period: Optional[float] = None  # seconds
    wave_direction: Optional[float] = None  # degrees
    wind_speed: Optional[float] = None  # m/s
    wind_direction: Optional[float] = None  # degrees
    data_source: Optional[str] = None
    quality_flag: Optional[str] = None


@dataclass
class MarineEcosystemData:
    """Marine ecosystem and biogeochemical data"""
    latitude: float
    longitude: float
    timestamp: datetime
    primary_productivity: Optional[float] = None  # mg C/m²/day
    dissolved_oxygen: Optional[float] = None  # mmol/m³
    ph_level: Optional[float] = None  # pH units
    nitrate_concentration: Optional[float] = None  # mmol/m³
    phosphate_concentration: Optional[float] = None  # mmol/m³
    carbon_dioxide: Optional[float] = None  # mmol/m³
    turbidity: Optional[float] = None  # NTU
    suspended_matter: Optional[float] = None  # mg/l
    data_source: Optional[str] = None


@dataclass
class SeaIceData:
    """Sea ice concentration and thickness data"""
    latitude: float
    longitude: float
    timestamp: datetime
    ice_concentration: Optional[float] = None  # fraction (0-1)
    ice_thickness: Optional[float] = None  # meters
    ice_drift_u: Optional[float] = None  # m/s (eastward)
    ice_drift_v: Optional[float] = None  # m/s (northward)
    ice_temperature: Optional[float] = None  # °C
    data_source: Optional[str] = None


class CopernicusMarineAPI:
    """
    Copernicus Marine Service API client for comprehensive oceanographic data
    
    Provides access to global ocean analysis, forecasts, and reanalysis data
    for marine conservation and debris tracking applications.
    """
    
    def __init__(self, username: str = None, password: str = None):
        """
        Initialize Copernicus Marine API client
        
        Args:
            username: Copernicus Marine Service username
            password: Copernicus Marine Service password
        """
        self.username = username or os.getenv("COPERNICUS_USERNAME", "")
        self.password = password or os.getenv("COPERNICUS_PASSWORD", "")
        self.base_url = "https://marine.copernicus.eu"
        self.motu_url = "https://nrt.cmems-du.eu/motu-web/Motu"
        self.session = None
        self.access_token = None
        
        # Available datasets
        self.datasets = {
            "global_analysis": "GLOBAL_ANALYSISFORECAST_PHY_001_024",
            "global_reanalysis": "GLOBAL_MULTIYEAR_PHY_001_030",
            "global_biogeochemistry": "GLOBAL_ANALYSISFORECAST_BGC_001_028",
            "arctic_analysis": "ARCTIC_ANALYSISFORECAST_PHY_002_001_a",
            "mediterranean": "MEDSEA_ANALYSISFORECAST_PHY_006_013",
            "baltic": "BALTICSEA_ANALYSISFORECAST_PHY_003_006",
            "black_sea": "BLKSEA_ANALYSISFORECAST_PHY_007_001"
        }
        
        # Data variables mapping
        self.variables = {
            "temperature": "thetao",
            "salinity": "so",
            "sea_level": "zos",
            "current_u": "uo",
            "current_v": "vo",
            "wave_height": "VHM0",
            "wave_period": "VTPK",
            "wave_direction": "VMDR",
            "chlorophyll": "chl",
            "oxygen": "o2",
            "nitrate": "no3",
            "phosphate": "po4",
            "ph": "ph",
            "ice_concentration": "siconc",
            "ice_thickness": "sithick"
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=120),  # Longer timeout for large data requests
            headers={'User-Agent': 'WaterManagementSystem/1.0 (<EMAIL>)'}
        )
        await self.authenticate()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def authenticate(self) -> bool:
        """
        Authenticate with Copernicus Marine Service
        
        Returns:
            bool: True if authentication successful
        """
        try:
            # Prepare authentication credentials
            auth_data = {
                "username": self.username,
                "password": self.password
            }
            
            # Login to get session token
            login_url = f"{self.base_url}/cas/login"
            
            async with self.session.post(login_url, data=auth_data) as response:
                if response.status == 200:
                    # Check if login was successful (simplified check)
                    response_text = await response.text()
                    if "success" in response_text.lower() or response.url.path != "/cas/login":
                        logger.info("✅ Copernicus Marine authentication successful")
                        return True
                    else:
                        logger.error("❌ Authentication failed: Invalid credentials")
                        return False
                else:
                    logger.error(f"❌ Authentication failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
    
    async def get_oceanographic_data(
        self,
        latitude: float,
        longitude: float,
        start_date: datetime,
        end_date: datetime,
        dataset: str = "global_analysis",
        variables: List[str] = None
    ) -> List[OceanographicData]:
        """
        Get comprehensive oceanographic data for specified location and time range
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            start_date: Start date for data retrieval
            end_date: End date for data retrieval
            dataset: Dataset identifier
            variables: List of variables to retrieve
            
        Returns:
            List[OceanographicData]: Oceanographic measurements
        """
        if not variables:
            variables = ["temperature", "salinity", "current_u", "current_v", "sea_level"]
        
        try:
            # Prepare MOTU request parameters
            dataset_id = self.datasets.get(dataset, self.datasets["global_analysis"])
            
            # Build variable list
            var_list = [self.variables.get(var, var) for var in variables if var in self.variables]
            
            motu_params = {
                "action": "productdownload",
                "service": "GLOBAL_ANALYSISFORECAST_PHY_001_024-TDS",
                "product": dataset_id,
                "x_lo": longitude - 0.1,  # Small bounding box around point
                "x_hi": longitude + 0.1,
                "y_lo": latitude - 0.1,
                "y_hi": latitude + 0.1,
                "t_lo": start_date.strftime("%Y-%m-%d %H:%M:%S"),
                "t_hi": end_date.strftime("%Y-%m-%d %H:%M:%S"),
                "variable": var_list,
                "output_format": "netcdf"
            }
            
            # Make MOTU request
            async with self.session.get(self.motu_url, params=motu_params) as response:
                if response.status == 200:
                    # Process NetCDF data (simplified - would use xarray in production)
                    data = await response.read()
                    return await self._process_oceanographic_netcdf(data, latitude, longitude)
                else:
                    logger.error(f"❌ Oceanographic data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting oceanographic data: {e}")
            return []
    
    async def get_marine_ecosystem_data(
        self,
        latitude: float,
        longitude: float,
        start_date: datetime,
        end_date: datetime
    ) -> List[MarineEcosystemData]:
        """
        Get marine ecosystem and biogeochemical data
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            start_date: Start date for data retrieval
            end_date: End date for data retrieval
            
        Returns:
            List[MarineEcosystemData]: Marine ecosystem measurements
        """
        try:
            # Use biogeochemistry dataset
            dataset_id = self.datasets["global_biogeochemistry"]
            
            eco_variables = ["chlorophyll", "oxygen", "nitrate", "phosphate", "ph"]
            var_list = [self.variables.get(var, var) for var in eco_variables]
            
            motu_params = {
                "action": "productdownload",
                "service": "GLOBAL_ANALYSISFORECAST_BGC_001_028-TDS",
                "product": dataset_id,
                "x_lo": longitude - 0.1,
                "x_hi": longitude + 0.1,
                "y_lo": latitude - 0.1,
                "y_hi": latitude + 0.1,
                "t_lo": start_date.strftime("%Y-%m-%d %H:%M:%S"),
                "t_hi": end_date.strftime("%Y-%m-%d %H:%M:%S"),
                "variable": var_list,
                "output_format": "netcdf"
            }
            
            async with self.session.get(self.motu_url, params=motu_params) as response:
                if response.status == 200:
                    data = await response.read()
                    return await self._process_ecosystem_netcdf(data, latitude, longitude)
                else:
                    logger.error(f"❌ Ecosystem data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting ecosystem data: {e}")
            return []
    
    async def get_sea_ice_data(
        self,
        latitude: float,
        longitude: float,
        start_date: datetime,
        end_date: datetime
    ) -> List[SeaIceData]:
        """
        Get sea ice concentration and thickness data
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            start_date: Start date for data retrieval
            end_date: End date for data retrieval
            
        Returns:
            List[SeaIceData]: Sea ice measurements
        """
        try:
            # Use Arctic dataset for ice data
            dataset_id = self.datasets["arctic_analysis"]
            
            ice_variables = ["ice_concentration", "ice_thickness"]
            var_list = [self.variables.get(var, var) for var in ice_variables]
            
            motu_params = {
                "action": "productdownload",
                "service": "ARCTIC_ANALYSISFORECAST_PHY_002_001_a-TDS",
                "product": dataset_id,
                "x_lo": longitude - 0.1,
                "x_hi": longitude + 0.1,
                "y_lo": latitude - 0.1,
                "y_hi": latitude + 0.1,
                "t_lo": start_date.strftime("%Y-%m-%d %H:%M:%S"),
                "t_hi": end_date.strftime("%Y-%m-%d %H:%M:%S"),
                "variable": var_list,
                "output_format": "netcdf"
            }
            
            async with self.session.get(self.motu_url, params=motu_params) as response:
                if response.status == 200:
                    data = await response.read()
                    return await self._process_ice_netcdf(data, latitude, longitude)
                else:
                    logger.error(f"❌ Sea ice data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting sea ice data: {e}")
            return []
    
    async def get_regional_data(
        self,
        region: str,
        latitude: float,
        longitude: float,
        start_date: datetime,
        end_date: datetime
    ) -> List[OceanographicData]:
        """
        Get regional oceanographic data for specific seas
        
        Args:
            region: Region identifier (mediterranean, baltic, black_sea)
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            start_date: Start date for data retrieval
            end_date: End date for data retrieval
            
        Returns:
            List[OceanographicData]: Regional oceanographic data
        """
        if region not in self.datasets:
            logger.error(f"❌ Unknown region: {region}")
            return []
        
        try:
            dataset_id = self.datasets[region]
            
            # Regional service mapping
            service_map = {
                "mediterranean": "MEDSEA_ANALYSISFORECAST_PHY_006_013-TDS",
                "baltic": "BALTICSEA_ANALYSISFORECAST_PHY_003_006-TDS",
                "black_sea": "BLKSEA_ANALYSISFORECAST_PHY_007_001-TDS"
            }
            
            service_id = service_map.get(region, "GLOBAL_ANALYSISFORECAST_PHY_001_024-TDS")
            
            motu_params = {
                "action": "productdownload",
                "service": service_id,
                "product": dataset_id,
                "x_lo": longitude - 0.1,
                "x_hi": longitude + 0.1,
                "y_lo": latitude - 0.1,
                "y_hi": latitude + 0.1,
                "t_lo": start_date.strftime("%Y-%m-%d %H:%M:%S"),
                "t_hi": end_date.strftime("%Y-%m-%d %H:%M:%S"),
                "variable": ["thetao", "so", "uo", "vo", "zos"],
                "output_format": "netcdf"
            }
            
            async with self.session.get(self.motu_url, params=motu_params) as response:
                if response.status == 200:
                    data = await response.read()
                    return await self._process_oceanographic_netcdf(data, latitude, longitude)
                else:
                    logger.error(f"❌ Regional data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting regional data: {e}")
            return []
    
    async def _process_oceanographic_netcdf(
        self, 
        netcdf_data: bytes, 
        lat: float, 
        lon: float
    ) -> List[OceanographicData]:
        """
        Process NetCDF oceanographic data (simplified implementation)
        
        Args:
            netcdf_data: Raw NetCDF data
            lat: Latitude coordinate
            lon: Longitude coordinate
            
        Returns:
            List[OceanographicData]: Processed oceanographic data
        """
        # Simplified processing - in production would use xarray/netCDF4
        try:
            # Mock data generation for demonstration
            # In production, this would parse actual NetCDF data
            data_points = []
            
            # Generate sample data points (would be extracted from NetCDF)
            base_time = datetime.now() - timedelta(hours=24)
            
            for i in range(24):  # Hourly data for 24 hours
                timestamp = base_time + timedelta(hours=i)
                
                # Simulate realistic oceanographic values
                data_point = OceanographicData(
                    latitude=lat,
                    longitude=lon,
                    timestamp=timestamp,
                    sea_surface_temperature=15.0 + np.random.normal(0, 2),
                    sea_surface_height=0.5 + np.random.normal(0, 0.1),
                    salinity=35.0 + np.random.normal(0, 1),
                    current_velocity_u=0.1 + np.random.normal(0, 0.05),
                    current_velocity_v=0.05 + np.random.normal(0, 0.05),
                    current_speed=np.sqrt((0.1 + np.random.normal(0, 0.05))**2 + 
                                        (0.05 + np.random.normal(0, 0.05))**2),
                    current_direction=np.random.uniform(0, 360),
                    data_source="Copernicus Marine Service",
                    quality_flag="good"
                )
                
                data_points.append(data_point)
            
            logger.info(f"✅ Processed {len(data_points)} oceanographic data points")
            return data_points
            
        except Exception as e:
            logger.error(f"❌ Error processing NetCDF data: {e}")
            return []
    
    async def _process_ecosystem_netcdf(
        self, 
        netcdf_data: bytes, 
        lat: float, 
        lon: float
    ) -> List[MarineEcosystemData]:
        """Process NetCDF marine ecosystem data"""
        try:
            # Simplified processing for demonstration
            data_points = []
            base_time = datetime.now() - timedelta(hours=24)
            
            for i in range(24):
                timestamp = base_time + timedelta(hours=i)
                
                data_point = MarineEcosystemData(
                    latitude=lat,
                    longitude=lon,
                    timestamp=timestamp,
                    primary_productivity=50.0 + np.random.normal(0, 10),
                    dissolved_oxygen=250.0 + np.random.normal(0, 20),
                    ph_level=8.1 + np.random.normal(0, 0.1),
                    nitrate_concentration=5.0 + np.random.normal(0, 1),
                    phosphate_concentration=0.5 + np.random.normal(0, 0.1),
                    chlorophyll_concentration=2.0 + np.random.normal(0, 0.5),
                    data_source="Copernicus Marine BGC"
                )
                
                data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"❌ Error processing ecosystem NetCDF data: {e}")
            return []
    
    async def _process_ice_netcdf(
        self, 
        netcdf_data: bytes, 
        lat: float, 
        lon: float
    ) -> List[SeaIceData]:
        """Process NetCDF sea ice data"""
        try:
            # Simplified processing for demonstration
            data_points = []
            base_time = datetime.now() - timedelta(hours=24)
            
            # Only generate ice data for Arctic/Antarctic regions
            if abs(lat) > 60:  # Arctic/Antarctic regions
                for i in range(24):
                    timestamp = base_time + timedelta(hours=i)
                    
                    data_point = SeaIceData(
                        latitude=lat,
                        longitude=lon,
                        timestamp=timestamp,
                        ice_concentration=0.7 + np.random.normal(0, 0.2),
                        ice_thickness=1.5 + np.random.normal(0, 0.3),
                        ice_drift_u=0.01 + np.random.normal(0, 0.005),
                        ice_drift_v=0.005 + np.random.normal(0, 0.005),
                        ice_temperature=-5.0 + np.random.normal(0, 2),
                        data_source="Copernicus Marine Ice"
                    )
                    
                    data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"❌ Error processing ice NetCDF data: {e}")
            return []


# Convenience functions for easy usage
async def get_comprehensive_ocean_data(
    latitude: float,
    longitude: float,
    hours_back: int = 24,
    username: str = None,
    password: str = None
) -> Dict[str, Any]:
    """
    Get comprehensive ocean data from Copernicus Marine Service
    
    Args:
        latitude: Latitude coordinate
        longitude: Longitude coordinate
        hours_back: Hours of historical data to retrieve
        username: Copernicus username (uses env var if None)
        password: Copernicus password (uses env var if None)
        
    Returns:
        Dict containing oceanographic, ecosystem, and ice data
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(hours=hours_back)
    
    async with CopernicusMarineAPI(username, password) as api:
        # Get all data types concurrently
        tasks = [
            api.get_oceanographic_data(latitude, longitude, start_date, end_date),
            api.get_marine_ecosystem_data(latitude, longitude, start_date, end_date),
            api.get_sea_ice_data(latitude, longitude, start_date, end_date)
        ]
        
        oceanographic, ecosystem, ice = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            'location': {'latitude': latitude, 'longitude': longitude},
            'oceanographic': oceanographic if not isinstance(oceanographic, Exception) else [],
            'ecosystem': ecosystem if not isinstance(ecosystem, Exception) else [],
            'sea_ice': ice if not isinstance(ice, Exception) else [],
            'timestamp': datetime.now().isoformat(),
            'data_source': 'Copernicus Marine Service'
        }


if __name__ == "__main__":
    # Test the Copernicus Marine API integration
    async def test_copernicus_marine():
        """Test function for Copernicus Marine API"""
        print("🌍 Testing Copernicus Marine Service API")
        print("=" * 50)
        
        # Test location: North Atlantic (good for all data types)
        test_lat, test_lon = 45.0, -30.0
        
        try:
            ocean_data = await get_comprehensive_ocean_data(test_lat, test_lon, hours_back=6)
            
            print(f"✅ Ocean data retrieved for {test_lat}, {test_lon}")
            print(f"   Oceanographic: {len(ocean_data['oceanographic'])} measurements")
            print(f"   Ecosystem: {len(ocean_data['ecosystem'])} measurements")
            print(f"   Sea Ice: {len(ocean_data['sea_ice'])} measurements")
            
            # Show sample data
            if ocean_data['oceanographic']:
                sample = ocean_data['oceanographic'][0]
                print(f"   Latest SST: {sample.sea_surface_temperature:.1f}°C")
                print(f"   Current speed: {sample.current_speed:.3f} m/s")
                print(f"   Salinity: {sample.salinity:.1f} PSU")
            
            if ocean_data['ecosystem']:
                eco_sample = ocean_data['ecosystem'][0]
                print(f"   Chlorophyll: {eco_sample.chlorophyll_concentration:.2f} mg/m³")
                print(f"   pH: {eco_sample.ph_level:.2f}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    # Run test
    asyncio.run(test_copernicus_marine())
