#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NOAA Ocean Service API Integration for Marine Conservation
Real-time oceanographic and weather data for marine debris tracking
"""

import os
import json
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class OceanCurrentData:
    """Ocean current measurement data"""
    latitude: float
    longitude: float
    u_velocity: float  # East-west velocity component (m/s)
    v_velocity: float  # North-south velocity component (m/s)
    speed: float  # Current speed (m/s)
    direction: float  # Current direction (degrees)
    timestamp: datetime
    depth: Optional[float] = None  # Depth in meters
    quality_flag: Optional[str] = None


@dataclass
class WaterTemperatureData:
    """Water temperature measurement data"""
    latitude: float
    longitude: float
    temperature: float  # Temperature in Celsius
    depth: float  # Depth in meters
    timestamp: datetime
    station_id: Optional[str] = None
    quality_flag: Optional[str] = None


@dataclass
class TidalData:
    """Tidal information data"""
    station_id: str
    station_name: str
    latitude: float
    longitude: float
    water_level: float  # Water level in meters
    prediction: float  # Predicted water level
    timestamp: datetime
    datum: str = "MLLW"  # Mean Lower Low Water


@dataclass
class WeatherData:
    """Marine weather data"""
    latitude: float
    longitude: float
    air_temperature: float  # Air temperature in Celsius
    wind_speed: float  # Wind speed in m/s
    wind_direction: float  # Wind direction in degrees
    wave_height: Optional[float] = None  # Significant wave height in meters
    atmospheric_pressure: Optional[float] = None  # Pressure in hPa
    visibility: Optional[float] = None  # Visibility in km
    timestamp: Optional[datetime] = None


class NOAAOceanAPI:
    """
    NOAA Ocean Service API client for marine conservation data
    
    Provides access to oceanographic data including currents, temperature,
    tides, and weather information for marine debris tracking and analysis.
    """
    
    def __init__(self, user_agent: str = None):
        """
        Initialize NOAA Ocean API client
        
        Args:
            user_agent: User-Agent string for API requests
        """
        self.user_agent = user_agent or "WaterManagementSystem/1.0 (<EMAIL>)"
        self.base_url = "https://api.weather.gov"
        self.co_ops_url = "https://api.tidesandcurrents.noaa.gov/api/prod/datagetter"
        self.session = None
        
        # API endpoints
        self.endpoints = {
            "weather": f"{self.base_url}/points",
            "forecast": f"{self.base_url}/gridpoints",
            "stations": f"{self.base_url}/stations",
            "tides": self.co_ops_url,
            "currents": self.co_ops_url,
            "water_temp": self.co_ops_url,
            "water_level": self.co_ops_url
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': self.user_agent,
                'Accept': 'application/json'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_ocean_currents(
        self, 
        latitude: float, 
        longitude: float, 
        hours_back: int = 24
    ) -> List[OceanCurrentData]:
        """
        Get ocean current data for specified location
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            hours_back: Hours of historical data to retrieve
            
        Returns:
            List[OceanCurrentData]: Ocean current measurements
        """
        try:
            # Find nearest current station
            station_id = await self._find_nearest_current_station(latitude, longitude)
            if not station_id:
                logger.warning(f"No current station found near {latitude}, {longitude}")
                return []
            
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            # Prepare API request parameters
            params = {
                'product': 'currents',
                'application': 'WaterManagementSystem',
                'begin_date': start_time.strftime('%Y%m%d %H:%M'),
                'end_date': end_time.strftime('%Y%m%d %H:%M'),
                'station': station_id,
                'time_zone': 'gmt',
                'units': 'metric',
                'format': 'json'
            }
            
            async with self.session.get(self.endpoints["currents"], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_current_data(data, latitude, longitude)
                else:
                    logger.error(f"❌ Current data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting ocean currents: {e}")
            return []
    
    async def get_water_temperature(
        self, 
        latitude: float, 
        longitude: float, 
        hours_back: int = 24
    ) -> List[WaterTemperatureData]:
        """
        Get water temperature data for specified location
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            hours_back: Hours of historical data to retrieve
            
        Returns:
            List[WaterTemperatureData]: Water temperature measurements
        """
        try:
            # Find nearest water temperature station
            station_id = await self._find_nearest_temp_station(latitude, longitude)
            if not station_id:
                logger.warning(f"No temperature station found near {latitude}, {longitude}")
                return []
            
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            # Prepare API request parameters
            params = {
                'product': 'water_temperature',
                'application': 'WaterManagementSystem',
                'begin_date': start_time.strftime('%Y%m%d %H:%M'),
                'end_date': end_time.strftime('%Y%m%d %H:%M'),
                'station': station_id,
                'time_zone': 'gmt',
                'units': 'metric',
                'format': 'json'
            }
            
            async with self.session.get(self.endpoints["water_temp"], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_temperature_data(data, latitude, longitude)
                else:
                    logger.error(f"❌ Temperature data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting water temperature: {e}")
            return []
    
    async def get_tidal_data(
        self, 
        latitude: float, 
        longitude: float, 
        hours_back: int = 24
    ) -> List[TidalData]:
        """
        Get tidal data for specified location
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            hours_back: Hours of historical data to retrieve
            
        Returns:
            List[TidalData]: Tidal measurements
        """
        try:
            # Find nearest tidal station
            station_id = await self._find_nearest_tidal_station(latitude, longitude)
            if not station_id:
                logger.warning(f"No tidal station found near {latitude}, {longitude}")
                return []
            
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)
            
            # Prepare API request parameters
            params = {
                'product': 'water_level',
                'application': 'WaterManagementSystem',
                'begin_date': start_time.strftime('%Y%m%d %H:%M'),
                'end_date': end_time.strftime('%Y%m%d %H:%M'),
                'station': station_id,
                'time_zone': 'gmt',
                'units': 'metric',
                'format': 'json'
            }
            
            async with self.session.get(self.endpoints["water_level"], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_tidal_data(data, latitude, longitude)
                else:
                    logger.error(f"❌ Tidal data request failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting tidal data: {e}")
            return []
    
    async def get_marine_weather(
        self, 
        latitude: float, 
        longitude: float
    ) -> Optional[WeatherData]:
        """
        Get marine weather data for specified location
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            
        Returns:
            Optional[WeatherData]: Current weather conditions
        """
        try:
            # Get weather station information
            point_url = f"{self.endpoints['weather']}/{latitude:.4f},{longitude:.4f}"
            
            async with self.session.get(point_url) as response:
                if response.status == 200:
                    point_data = await response.json()
                    
                    # Get current conditions from the forecast office
                    forecast_url = point_data['properties']['forecast']
                    
                    async with self.session.get(forecast_url) as forecast_response:
                        if forecast_response.status == 200:
                            forecast_data = await forecast_response.json()
                            return self._parse_weather_data(forecast_data, latitude, longitude)
                        else:
                            logger.error(f"❌ Weather forecast request failed: {forecast_response.status}")
                            return None
                else:
                    logger.error(f"❌ Weather point request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting marine weather: {e}")
            return None
    
    async def _find_nearest_current_station(self, lat: float, lon: float) -> Optional[str]:
        """Find nearest ocean current monitoring station"""
        # Known current stations (simplified - in production, would query stations API)
        current_stations = {
            "8518750": {"lat": 41.8043, "lon": -71.4006, "name": "Newport, RI"},
            "8447930": {"lat": 42.3581, "lon": -71.0636, "name": "Boston, MA"},
            "8516945": {"lat": 41.3611, "lon": -71.9644, "name": "New London, CT"},
            "8534720": {"lat": 39.3569, "lon": -74.4147, "name": "Atlantic City, NJ"},
            "8557380": {"lat": 36.9467, "lon": -76.3300, "name": "Lewisetta, VA"}
        }
        
        # Find closest station (simplified distance calculation)
        min_distance = float('inf')
        closest_station = None
        
        for station_id, station_info in current_stations.items():
            distance = ((lat - station_info["lat"]) ** 2 + (lon - station_info["lon"]) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_station = station_id
        
        # Return station if within reasonable distance (about 2 degrees)
        return closest_station if min_distance < 2.0 else None
    
    async def _find_nearest_temp_station(self, lat: float, lon: float) -> Optional[str]:
        """Find nearest water temperature monitoring station"""
        # Use same stations as current monitoring (many stations provide both)
        return await self._find_nearest_current_station(lat, lon)
    
    async def _find_nearest_tidal_station(self, lat: float, lon: float) -> Optional[str]:
        """Find nearest tidal monitoring station"""
        # Use same stations as current monitoring (many stations provide tidal data)
        return await self._find_nearest_current_station(lat, lon)
    
    def _parse_current_data(self, data: Dict, lat: float, lon: float) -> List[OceanCurrentData]:
        """Parse ocean current data from NOAA API response"""
        current_data = []
        
        try:
            if 'data' in data:
                for record in data['data']:
                    # Parse current data (format: time, speed, direction, bin)
                    timestamp = datetime.strptime(record['t'], '%Y-%m-%d %H:%M')
                    speed = float(record['s']) if record['s'] else 0.0
                    direction = float(record['d']) if record['d'] else 0.0
                    
                    # Convert speed and direction to velocity components
                    import math
                    direction_rad = math.radians(direction)
                    u_velocity = speed * math.sin(direction_rad)  # East component
                    v_velocity = speed * math.cos(direction_rad)  # North component
                    
                    current_data.append(OceanCurrentData(
                        latitude=lat,
                        longitude=lon,
                        u_velocity=u_velocity,
                        v_velocity=v_velocity,
                        speed=speed,
                        direction=direction,
                        timestamp=timestamp,
                        depth=float(record.get('bin', 0)) if record.get('bin') else None,
                        quality_flag=record.get('f')
                    ))
        except Exception as e:
            logger.error(f"❌ Error parsing current data: {e}")
        
        return current_data
    
    def _parse_temperature_data(self, data: Dict, lat: float, lon: float) -> List[WaterTemperatureData]:
        """Parse water temperature data from NOAA API response"""
        temp_data = []
        
        try:
            if 'data' in data:
                for record in data['data']:
                    timestamp = datetime.strptime(record['t'], '%Y-%m-%d %H:%M')
                    temperature = float(record['v']) if record['v'] else None
                    
                    if temperature is not None:
                        temp_data.append(WaterTemperatureData(
                            latitude=lat,
                            longitude=lon,
                            temperature=temperature,
                            depth=0.0,  # Surface temperature
                            timestamp=timestamp,
                            station_id=data.get('metadata', {}).get('id'),
                            quality_flag=record.get('f')
                        ))
        except Exception as e:
            logger.error(f"❌ Error parsing temperature data: {e}")
        
        return temp_data
    
    def _parse_tidal_data(self, data: Dict, lat: float, lon: float) -> List[TidalData]:
        """Parse tidal data from NOAA API response"""
        tidal_data = []
        
        try:
            if 'data' in data:
                metadata = data.get('metadata', {})
                station_id = metadata.get('id', 'unknown')
                station_name = metadata.get('name', 'Unknown Station')
                
                for record in data['data']:
                    timestamp = datetime.strptime(record['t'], '%Y-%m-%d %H:%M')
                    water_level = float(record['v']) if record['v'] else 0.0
                    
                    tidal_data.append(TidalData(
                        station_id=station_id,
                        station_name=station_name,
                        latitude=lat,
                        longitude=lon,
                        water_level=water_level,
                        prediction=water_level,  # Simplified - would need separate prediction call
                        timestamp=timestamp
                    ))
        except Exception as e:
            logger.error(f"❌ Error parsing tidal data: {e}")
        
        return tidal_data
    
    def _parse_weather_data(self, data: Dict, lat: float, lon: float) -> Optional[WeatherData]:
        """Parse weather data from NOAA API response"""
        try:
            if 'properties' in data and 'periods' in data['properties']:
                current_period = data['properties']['periods'][0]
                
                # Extract weather information
                temperature = current_period.get('temperature', 0)
                wind_speed = self._parse_wind_speed(current_period.get('windSpeed', '0 mph'))
                wind_direction = self._parse_wind_direction(current_period.get('windDirection', 'N'))
                
                return WeatherData(
                    latitude=lat,
                    longitude=lon,
                    air_temperature=self._fahrenheit_to_celsius(temperature),
                    wind_speed=wind_speed,
                    wind_direction=wind_direction,
                    timestamp=datetime.now()
                )
        except Exception as e:
            logger.error(f"❌ Error parsing weather data: {e}")
        
        return None
    
    def _parse_wind_speed(self, wind_speed_str: str) -> float:
        """Parse wind speed from string format"""
        try:
            # Extract number from strings like "10 mph" or "5 to 10 mph"
            import re
            numbers = re.findall(r'\d+', wind_speed_str)
            if numbers:
                speed_mph = float(numbers[0])
                return speed_mph * 0.44704  # Convert mph to m/s
        except:
            pass
        return 0.0
    
    def _parse_wind_direction(self, direction_str: str) -> float:
        """Parse wind direction from string format"""
        direction_map = {
            'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
            'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
            'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
            'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
        }
        return direction_map.get(direction_str.upper(), 0.0)
    
    def _fahrenheit_to_celsius(self, fahrenheit: float) -> float:
        """Convert Fahrenheit to Celsius"""
        return (fahrenheit - 32) * 5.0 / 9.0


# Convenience functions for easy usage
async def get_marine_conditions(
    latitude: float,
    longitude: float,
    hours_back: int = 24,
    user_agent: str = None
) -> Dict[str, Any]:
    """
    Get comprehensive marine conditions for a location
    
    Args:
        latitude: Latitude coordinate
        longitude: Longitude coordinate
        hours_back: Hours of historical data to retrieve
        user_agent: User-Agent string for API requests
        
    Returns:
        Dict containing currents, temperature, tides, and weather data
    """
    async with NOAAOceanAPI(user_agent) as api:
        # Get all marine data concurrently
        currents_task = api.get_ocean_currents(latitude, longitude, hours_back)
        temperature_task = api.get_water_temperature(latitude, longitude, hours_back)
        tidal_task = api.get_tidal_data(latitude, longitude, hours_back)
        weather_task = api.get_marine_weather(latitude, longitude)
        
        currents, temperature, tides, weather = await asyncio.gather(
            currents_task, temperature_task, tidal_task, weather_task,
            return_exceptions=True
        )
        
        return {
            'currents': currents if not isinstance(currents, Exception) else [],
            'temperature': temperature if not isinstance(temperature, Exception) else [],
            'tides': tides if not isinstance(tides, Exception) else [],
            'weather': weather if not isinstance(weather, Exception) else None,
            'timestamp': datetime.now().isoformat()
        }


if __name__ == "__main__":
    # Test the NOAA Ocean API integration
    async def test_noaa_ocean():
        """Test function for NOAA Ocean API"""
        print("🌊 Testing NOAA Ocean Service API")
        print("=" * 50)
        
        # Test location: Near Boston Harbor
        test_lat, test_lon = 42.3581, -71.0636
        
        try:
            conditions = await get_marine_conditions(test_lat, test_lon, hours_back=6)
            
            print(f"✅ Marine conditions retrieved for {test_lat}, {test_lon}")
            print(f"   Currents: {len(conditions['currents'])} measurements")
            print(f"   Temperature: {len(conditions['temperature'])} measurements")
            print(f"   Tides: {len(conditions['tides'])} measurements")
            print(f"   Weather: {'Available' if conditions['weather'] else 'Not available'}")
            
            # Show sample data
            if conditions['currents']:
                current = conditions['currents'][0]
                print(f"   Latest current: {current.speed:.2f} m/s at {current.direction:.1f}°")
            
            if conditions['temperature']:
                temp = conditions['temperature'][0]
                print(f"   Latest temperature: {temp.temperature:.1f}°C")
            
            if conditions['weather']:
                weather = conditions['weather']
                print(f"   Current weather: {weather.air_temperature:.1f}°C, wind {weather.wind_speed:.1f} m/s")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    # Run test
    asyncio.run(test_noaa_ocean())
