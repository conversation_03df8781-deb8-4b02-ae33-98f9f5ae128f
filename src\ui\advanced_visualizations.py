"""
Advanced Visualization Components for Water Management Dashboard.

Comprehensive Plotly-based visualization suite with interactive dashboards,
real-time monitoring displays, and geospatial mapping interfaces.
"""

import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import folium
from folium import plugins
import streamlit as st
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class AdvancedVisualizationSuite:
    """Advanced visualization components for water management system."""
    
    def __init__(self):
        self.color_palette = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e',
            'success': '#2ca02c',
            'warning': '#d62728',
            'info': '#9467bd',
            'light': '#8c564b',
            'dark': '#e377c2'
        }
    
    def create_real_time_monitoring_dashboard(self, data: Dict[str, Any]) -> go.Figure:
        """Create comprehensive real-time monitoring dashboard."""
        try:
            # Create subplots with secondary y-axis
            fig = make_subplots(
                rows=3, cols=2,
                subplot_titles=(
                    'Water Flow Rate', 'Energy Consumption',
                    'Water Quality Metrics', 'System Efficiency',
                    'Temperature Trends', 'Pressure Monitoring'
                ),
                specs=[[{"secondary_y": True}, {"secondary_y": True}],
                       [{"secondary_y": True}, {"secondary_y": True}],
                       [{"secondary_y": True}, {"secondary_y": True}]],
                vertical_spacing=0.08,
                horizontal_spacing=0.1
            )
            
            # Generate sample real-time data
            timestamps = pd.date_range(
                start=datetime.now() - timedelta(hours=24),
                end=datetime.now(),
                freq='H'
            )
            
            # Flow rate data
            flow_rates = 2000 + 200 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 24) + np.random.normal(0, 50, len(timestamps))
            fig.add_trace(
                go.Scatter(x=timestamps, y=flow_rates, name='Flow Rate (L/min)',
                          line=dict(color=self.color_palette['primary'], width=2)),
                row=1, col=1
            )
            
            # Energy consumption
            energy = 45 + 10 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 24) + np.random.normal(0, 3, len(timestamps))
            fig.add_trace(
                go.Scatter(x=timestamps, y=energy, name='Energy (kWh)',
                          line=dict(color=self.color_palette['warning'], width=2)),
                row=1, col=2
            )
            
            # Water quality metrics
            ph_values = 7.2 + 0.3 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 48) + np.random.normal(0, 0.1, len(timestamps))
            turbidity = 0.5 + 0.2 * np.random.random(len(timestamps))
            
            fig.add_trace(
                go.Scatter(x=timestamps, y=ph_values, name='pH Level',
                          line=dict(color=self.color_palette['success'], width=2)),
                row=2, col=1
            )
            fig.add_trace(
                go.Scatter(x=timestamps, y=turbidity, name='Turbidity (NTU)',
                          line=dict(color=self.color_palette['info'], width=2),
                          yaxis='y2'),
                row=2, col=1, secondary_y=True
            )
            
            # System efficiency
            efficiency = 88 + 5 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 72) + np.random.normal(0, 2, len(timestamps))
            fig.add_trace(
                go.Scatter(x=timestamps, y=efficiency, name='Efficiency (%)',
                          line=dict(color=self.color_palette['secondary'], width=2),
                          fill='tonexty'),
                row=2, col=2
            )
            
            # Temperature trends
            temp_inlet = 22 + 3 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 24) + np.random.normal(0, 1, len(timestamps))
            temp_outlet = temp_inlet + np.random.normal(2, 0.5, len(timestamps))
            
            fig.add_trace(
                go.Scatter(x=timestamps, y=temp_inlet, name='Inlet Temp (°C)',
                          line=dict(color=self.color_palette['light'], width=2)),
                row=3, col=1
            )
            fig.add_trace(
                go.Scatter(x=timestamps, y=temp_outlet, name='Outlet Temp (°C)',
                          line=dict(color=self.color_palette['dark'], width=2)),
                row=3, col=1
            )
            
            # Pressure monitoring
            pressure = 2.1 + 0.3 * np.sin(np.arange(len(timestamps)) * 2 * np.pi / 12) + np.random.normal(0, 0.1, len(timestamps))
            fig.add_trace(
                go.Scatter(x=timestamps, y=pressure, name='System Pressure (bar)',
                          line=dict(color=self.color_palette['primary'], width=2),
                          mode='lines+markers'),
                row=3, col=2
            )
            
            # Update layout
            fig.update_layout(
                height=800,
                title_text="Real-Time Water Treatment Monitoring Dashboard",
                title_x=0.5,
                showlegend=True,
                template='plotly_white'
            )
            
            # Update axes labels
            fig.update_xaxes(title_text="Time", row=3, col=1)
            fig.update_xaxes(title_text="Time", row=3, col=2)
            fig.update_yaxes(title_text="Flow Rate (L/min)", row=1, col=1)
            fig.update_yaxes(title_text="Energy (kWh)", row=1, col=2)
            fig.update_yaxes(title_text="pH Level", row=2, col=1)
            fig.update_yaxes(title_text="Efficiency (%)", row=2, col=2)
            fig.update_yaxes(title_text="Temperature (°C)", row=3, col=1)
            fig.update_yaxes(title_text="Pressure (bar)", row=3, col=2)
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating real-time monitoring dashboard: {e}")
            return go.Figure()
    
    def create_geospatial_facility_map(self, facilities_data: List[Dict]) -> folium.Map:
        """Create interactive geospatial map of water treatment facilities."""
        try:
            # Create base map centered on average coordinates
            if facilities_data:
                avg_lat = np.mean([f['lat'] for f in facilities_data])
                avg_lon = np.mean([f['lon'] for f in facilities_data])
            else:
                avg_lat, avg_lon = 40.7128, -74.0060  # Default to NYC
            
            m = folium.Map(
                location=[avg_lat, avg_lon],
                zoom_start=10,
                tiles='OpenStreetMap'
            )
            
            # Add facility markers
            for facility in facilities_data:
                # Determine marker color based on efficiency
                efficiency = facility.get('efficiency', 0.85)
                if efficiency >= 0.9:
                    color = 'green'
                elif efficiency >= 0.8:
                    color = 'orange'
                else:
                    color = 'red'
                
                # Create popup content
                popup_content = f"""
                <div style="width: 200px;">
                    <h4>{facility['name']}</h4>
                    <p><strong>Capacity:</strong> {facility.get('capacity', 'N/A')} L/day</p>
                    <p><strong>Efficiency:</strong> {efficiency:.1%}</p>
                    <p><strong>Status:</strong> {facility.get('status', 'Active')}</p>
                    <p><strong>Energy:</strong> {facility.get('energy_consumption', 'N/A')} kWh</p>
                </div>
                """
                
                folium.Marker(
                    location=[facility['lat'], facility['lon']],
                    popup=folium.Popup(popup_content, max_width=250),
                    tooltip=facility['name'],
                    icon=folium.Icon(color=color, icon='tint', prefix='fa')
                ).add_to(m)
            
            # Add heat map layer for efficiency
            if facilities_data:
                heat_data = [[f['lat'], f['lon'], f.get('efficiency', 0.85)] for f in facilities_data]
                plugins.HeatMap(heat_data, name='Efficiency Heatmap', overlay=True).add_to(m)
            
            # Add layer control
            folium.LayerControl().add_to(m)
            
            return m
            
        except Exception as e:
            logger.error(f"Error creating geospatial map: {e}")
            return folium.Map(location=[40.7128, -74.0060], zoom_start=10)
    
    def create_optimization_results_visualization(self, optimization_data: Dict[str, Any]) -> go.Figure:
        """Create comprehensive optimization results visualization."""
        try:
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    'Parameter Optimization Progress',
                    'Multi-Objective Trade-offs',
                    'Performance Improvements',
                    'Cost-Benefit Analysis'
                ),
                specs=[[{"type": "scatter"}, {"type": "scatter"}],
                       [{"type": "bar"}, {"type": "scatter"}]]
            )
            
            # Optimization progress
            generations = list(range(1, 101))
            best_fitness = [0.7 + 0.2 * (1 - np.exp(-g/20)) + np.random.normal(0, 0.01) for g in generations]
            avg_fitness = [f - 0.1 - np.random.normal(0, 0.02) for f in best_fitness]
            
            fig.add_trace(
                go.Scatter(x=generations, y=best_fitness, name='Best Fitness',
                          line=dict(color=self.color_palette['success'], width=3)),
                row=1, col=1
            )
            fig.add_trace(
                go.Scatter(x=generations, y=avg_fitness, name='Average Fitness',
                          line=dict(color=self.color_palette['info'], width=2)),
                row=1, col=1
            )
            
            # Multi-objective trade-offs (Pareto front)
            efficiency = np.random.uniform(0.85, 0.95, 50)
            cost = 1000 - 800 * efficiency + np.random.normal(0, 20, 50)
            
            fig.add_trace(
                go.Scatter(x=efficiency, y=cost, mode='markers',
                          name='Pareto Solutions',
                          marker=dict(size=8, color=self.color_palette['primary'],
                                    opacity=0.7)),
                row=1, col=2
            )
            
            # Performance improvements
            metrics = ['Efficiency', 'Energy Savings', 'Cost Reduction', 'Carbon Reduction']
            before = [85, 0, 0, 0]
            after = [92, 15, 12, 18]
            improvements = [after[i] - before[i] for i in range(len(metrics))]
            
            fig.add_trace(
                go.Bar(x=metrics, y=improvements, name='Improvements',
                      marker_color=[self.color_palette['success'], 
                                  self.color_palette['warning'],
                                  self.color_palette['info'],
                                  self.color_palette['secondary']]),
                row=2, col=1
            )
            
            # Cost-benefit analysis
            months = list(range(1, 25))
            cumulative_savings = [month * 1200 - 15000 for month in months]
            investment_cost = [-15000] * len(months)
            
            fig.add_trace(
                go.Scatter(x=months, y=cumulative_savings, name='Cumulative Savings',
                          line=dict(color=self.color_palette['success'], width=3)),
                row=2, col=2
            )
            fig.add_trace(
                go.Scatter(x=months, y=investment_cost, name='Investment Cost',
                          line=dict(color=self.color_palette['warning'], width=2, dash='dash')),
                row=2, col=2
            )
            
            # Update layout
            fig.update_layout(
                height=700,
                title_text="Optimization Results Analysis",
                title_x=0.5,
                showlegend=True,
                template='plotly_white'
            )
            
            # Update axes
            fig.update_xaxes(title_text="Generation", row=1, col=1)
            fig.update_yaxes(title_text="Fitness Score", row=1, col=1)
            fig.update_xaxes(title_text="Efficiency (%)", row=1, col=2)
            fig.update_yaxes(title_text="Cost ($)", row=1, col=2)
            fig.update_xaxes(title_text="Metrics", row=2, col=1)
            fig.update_yaxes(title_text="Improvement (%)", row=2, col=1)
            fig.update_xaxes(title_text="Months", row=2, col=2)
            fig.update_yaxes(title_text="Amount ($)", row=2, col=2)
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating optimization visualization: {e}")
            return go.Figure()
    
    def create_sustainability_dashboard(self, sustainability_data: Dict[str, Any]) -> go.Figure:
        """Create comprehensive sustainability metrics dashboard."""
        try:
            fig = make_subplots(
                rows=2, cols=3,
                subplot_titles=(
                    'ESG Score Breakdown',
                    'Carbon Footprint Trend',
                    'Energy Source Distribution',
                    'Water Recovery Rate',
                    'Waste Reduction Progress',
                    'Renewable Energy Growth'
                ),
                specs=[[{"type": "bar"}, {"type": "scatter"}, {"type": "pie"}],
                       [{"type": "scatter"}, {"type": "bar"}, {"type": "scatter"}]]
            )
            
            # ESG Score breakdown
            esg_categories = ['Environmental', 'Social', 'Governance']
            esg_scores = [8.2, 7.5, 7.7]
            
            fig.add_trace(
                go.Bar(x=esg_categories, y=esg_scores, name='ESG Scores',
                      marker_color=[self.color_palette['success'],
                                  self.color_palette['info'],
                                  self.color_palette['primary']]),
                row=1, col=1
            )
            
            # Carbon footprint trend
            months = pd.date_range(start='2023-01-01', end='2024-01-01', freq='M')
            carbon_footprint = [450 - i*5 + np.random.normal(0, 10) for i in range(len(months))]
            
            fig.add_trace(
                go.Scatter(x=months, y=carbon_footprint, name='Carbon Footprint',
                          line=dict(color=self.color_palette['warning'], width=3),
                          fill='tonexty'),
                row=1, col=2
            )
            
            # Energy source distribution
            energy_sources = ['Solar', 'Wind', 'Grid (Renewable)', 'Grid (Fossil)']
            energy_values = [35, 25, 30, 10]
            
            fig.add_trace(
                go.Pie(labels=energy_sources, values=energy_values, name='Energy Sources'),
                row=1, col=3
            )
            
            # Water recovery rate
            days = list(range(1, 31))
            recovery_rate = [94 + 2*np.sin(d/5) + np.random.normal(0, 0.5) for d in days]
            
            fig.add_trace(
                go.Scatter(x=days, y=recovery_rate, name='Recovery Rate',
                          line=dict(color=self.color_palette['primary'], width=2),
                          mode='lines+markers'),
                row=2, col=1
            )
            
            # Waste reduction progress
            quarters = ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024']
            waste_reduction = [5, 12, 18, 25, 32]
            
            fig.add_trace(
                go.Bar(x=quarters, y=waste_reduction, name='Waste Reduction',
                      marker_color=self.color_palette['success']),
                row=2, col=2
            )
            
            # Renewable energy growth
            years = list(range(2020, 2025))
            renewable_percentage = [15, 22, 28, 35, 45]
            
            fig.add_trace(
                go.Scatter(x=years, y=renewable_percentage, name='Renewable Energy',
                          line=dict(color=self.color_palette['secondary'], width=3),
                          mode='lines+markers'),
                row=2, col=3
            )
            
            # Update layout
            fig.update_layout(
                height=700,
                title_text="Sustainability Metrics Dashboard",
                title_x=0.5,
                showlegend=True,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating sustainability dashboard: {e}")
            return go.Figure()
    
    def create_comparative_analysis_tool(self, comparison_data: Dict[str, Any]) -> go.Figure:
        """Create comparative analysis visualization tool."""
        try:
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    'Facility Performance Comparison',
                    'Technology Efficiency Comparison',
                    'Cost-Effectiveness Analysis',
                    'Environmental Impact Comparison'
                ),
                specs=[[{"type": "bar"}, {"type": "scatter"}],
                       [{"type": "scatter"}, {"type": "bar"}]]
            )
            
            # Facility performance comparison
            facilities = ['Plant A', 'Plant B', 'Plant C', 'Plant D']
            efficiency = [88, 92, 85, 90]
            capacity = [2000, 1500, 2500, 1800]
            
            fig.add_trace(
                go.Bar(x=facilities, y=efficiency, name='Efficiency (%)',
                      marker_color=self.color_palette['primary']),
                row=1, col=1
            )
            
            # Technology efficiency comparison
            technologies = ['Membrane', 'UV', 'Chemical', 'Biological']
            efficiency_scores = [95, 88, 82, 90]
            maintenance_cost = [150, 80, 60, 120]
            
            fig.add_trace(
                go.Scatter(x=efficiency_scores, y=maintenance_cost, 
                          text=technologies, mode='markers+text',
                          textposition='top center',
                          marker=dict(size=15, color=self.color_palette['success']),
                          name='Technologies'),
                row=1, col=2
            )
            
            # Cost-effectiveness analysis
            investment = [50000, 75000, 40000, 60000]
            annual_savings = [12000, 18000, 8000, 15000]
            
            fig.add_trace(
                go.Scatter(x=investment, y=annual_savings,
                          text=facilities, mode='markers+text',
                          textposition='top center',
                          marker=dict(size=12, color=self.color_palette['warning']),
                          name='Cost-Effectiveness'),
                row=2, col=1
            )
            
            # Environmental impact comparison
            impact_categories = ['Water Quality', 'Energy Efficiency', 'Waste Reduction', 'Carbon Footprint']
            plant_a_scores = [85, 88, 75, 82]
            plant_b_scores = [92, 85, 88, 90]
            
            fig.add_trace(
                go.Bar(x=impact_categories, y=plant_a_scores, name='Plant A',
                      marker_color=self.color_palette['info']),
                row=2, col=2
            )
            fig.add_trace(
                go.Bar(x=impact_categories, y=plant_b_scores, name='Plant B',
                      marker_color=self.color_palette['secondary']),
                row=2, col=2
            )
            
            # Update layout
            fig.update_layout(
                height=700,
                title_text="Comparative Analysis Dashboard",
                title_x=0.5,
                showlegend=True,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating comparative analysis: {e}")
            return go.Figure()


# Convenience functions for Streamlit integration
def render_advanced_monitoring_dashboard():
    """Render advanced monitoring dashboard in Streamlit."""
    viz_suite = AdvancedVisualizationSuite()
    
    st.subheader("🔍 Advanced Real-Time Monitoring")
    
    # Create monitoring dashboard
    monitoring_fig = viz_suite.create_real_time_monitoring_dashboard({})
    st.plotly_chart(monitoring_fig, use_container_width=True)
    
    # Add controls
    col1, col2, col3 = st.columns(3)
    with col1:
        auto_refresh = st.checkbox("Auto Refresh", value=True)
    with col2:
        refresh_interval = st.selectbox("Refresh Rate", [5, 10, 30, 60], index=1)
    with col3:
        data_range = st.selectbox("Data Range", ["1 Hour", "6 Hours", "24 Hours", "7 Days"], index=2)


def render_geospatial_interface():
    """Render geospatial mapping interface in Streamlit."""
    viz_suite = AdvancedVisualizationSuite()
    
    st.subheader("🗺️ Facility Geospatial Mapping")
    
    # Sample facility data
    facilities_data = [
        {"name": "NYC Treatment Plant", "lat": 40.7128, "lon": -74.0060, "efficiency": 0.92, "capacity": 50000, "status": "Active", "energy_consumption": 45},
        {"name": "Brooklyn Facility", "lat": 40.6782, "lon": -73.9442, "efficiency": 0.88, "capacity": 35000, "status": "Active", "energy_consumption": 38},
        {"name": "Queens Processing Center", "lat": 40.7282, "lon": -73.7949, "efficiency": 0.85, "capacity": 28000, "status": "Maintenance", "energy_consumption": 42}
    ]
    
    # Create map
    facility_map = viz_suite.create_geospatial_facility_map(facilities_data)
    
    # Display map in Streamlit
    st.components.v1.html(facility_map._repr_html_(), height=500)


def render_optimization_visualization():
    """Render optimization results visualization in Streamlit."""
    viz_suite = AdvancedVisualizationSuite()
    
    st.subheader("🎯 Optimization Results Analysis")
    
    # Create optimization visualization
    opt_fig = viz_suite.create_optimization_results_visualization({})
    st.plotly_chart(opt_fig, use_container_width=True)


def render_sustainability_dashboard():
    """Render sustainability metrics dashboard in Streamlit."""
    viz_suite = AdvancedVisualizationSuite()
    
    st.subheader("🌱 Sustainability Metrics Dashboard")
    
    # Create sustainability dashboard
    sustainability_fig = viz_suite.create_sustainability_dashboard({})
    st.plotly_chart(sustainability_fig, use_container_width=True)


def render_comparative_analysis():
    """Render comparative analysis tool in Streamlit."""
    viz_suite = AdvancedVisualizationSuite()
    
    st.subheader("📊 Comparative Analysis Tool")
    
    # Create comparative analysis
    comparison_fig = viz_suite.create_comparative_analysis_tool({})
    st.plotly_chart(comparison_fig, use_container_width=True)
