"""Advanced Reasoning System for LLM Integration."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import re

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ReasoningType(Enum):
    """Types of reasoning approaches."""
    DEDUCTIVE = "deductive"
    INDUCTIVE = "inductive"
    ABDUCTIVE = "abductive"
    CAUSAL = "causal"
    ANALOGICAL = "analogical"
    PROBABILISTIC = "probabilistic"
    TEMPORAL = "temporal"
    SPATIAL = "spatial"


class ReasoningStrategy(Enum):
    """Reasoning strategies."""
    CHAIN_OF_THOUGHT = "chain_of_thought"
    TREE_OF_THOUGHTS = "tree_of_thoughts"
    STEP_BY_STEP = "step_by_step"
    MULTI_PERSPECTIVE = "multi_perspective"
    EVIDENCE_BASED = "evidence_based"
    HYPOTHESIS_TESTING = "hypothesis_testing"


@dataclass
class ReasoningStep:
    """Individual reasoning step."""
    step_id: str
    reasoning_type: ReasoningType
    premise: str
    inference: str
    conclusion: str
    confidence: float
    evidence: List[str] = field(default_factory=list)
    assumptions: List[str] = field(default_factory=list)


@dataclass
class ReasoningChain:
    """Chain of reasoning steps."""
    chain_id: str
    problem_statement: str
    strategy: ReasoningStrategy
    steps: List[ReasoningStep]
    final_conclusion: str
    overall_confidence: float
    created_at: datetime = field(default_factory=datetime.now)


class AdvancedReasoningSystem:
    """Advanced reasoning system for complex problem solving."""
    
    def __init__(self):
        self.reasoning_chains: Dict[str, ReasoningChain] = {}
        self.reasoning_templates: Dict[str, Dict[str, Any]] = {}
        
        # Initialize reasoning templates
        self._initialize_reasoning_templates()
    
    def _initialize_reasoning_templates(self):
        """Initialize reasoning templates for different domains."""
        self.reasoning_templates = {
            'water_quality_analysis': {
                'strategy': ReasoningStrategy.EVIDENCE_BASED,
                'steps': [
                    {
                        'type': ReasoningType.DEDUCTIVE,
                        'template': 'Given water quality parameters {parameters}, and standards {standards}, we can deduce {conclusion}'
                    },
                    {
                        'type': ReasoningType.CAUSAL,
                        'template': 'If {condition} occurs, then {effect} will likely result because {mechanism}'
                    },
                    {
                        'type': ReasoningType.PROBABILISTIC,
                        'template': 'Based on historical data, there is a {probability}% chance that {outcome} will occur'
                    }
                ]
            },
            'treatment_optimization': {
                'strategy': ReasoningStrategy.CHAIN_OF_THOUGHT,
                'steps': [
                    {
                        'type': ReasoningType.ANALOGICAL,
                        'template': 'Similar to {reference_case}, we can apply {solution} to achieve {goal}'
                    },
                    {
                        'type': ReasoningType.CAUSAL,
                        'template': 'Adjusting {parameter} will cause {change} in {outcome} due to {mechanism}'
                    },
                    {
                        'type': ReasoningType.TEMPORAL,
                        'template': 'Over time period {duration}, we expect {progression} to occur'
                    }
                ]
            },
            'risk_assessment': {
                'strategy': ReasoningStrategy.HYPOTHESIS_TESTING,
                'steps': [
                    {
                        'type': ReasoningType.ABDUCTIVE,
                        'template': 'The best explanation for {observation} is {hypothesis} because {reasoning}'
                    },
                    {
                        'type': ReasoningType.PROBABILISTIC,
                        'template': 'Given {evidence}, the probability of {risk} is {probability} with confidence {confidence}'
                    },
                    {
                        'type': ReasoningType.CAUSAL,
                        'template': 'Risk factor {factor} contributes to {outcome} through pathway {pathway}'
                    }
                ]
            },
            'system_diagnosis': {
                'strategy': ReasoningStrategy.TREE_OF_THOUGHTS,
                'steps': [
                    {
                        'type': ReasoningType.DEDUCTIVE,
                        'template': 'From symptoms {symptoms}, we can rule out {excluded_causes}'
                    },
                    {
                        'type': ReasoningType.INDUCTIVE,
                        'template': 'Pattern {pattern} in data suggests {general_principle}'
                    },
                    {
                        'type': ReasoningType.ABDUCTIVE,
                        'template': 'Most likely cause of {problem} is {cause} given {evidence}'
                    }
                ]
            }
        }
    
    @log_async_function_call
    async def create_reasoning_chain(self, problem_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a reasoning chain for a given problem."""
        try:
            problem_statement = problem_config['problem_statement']
            domain = problem_config.get('domain', 'general')
            strategy = ReasoningStrategy(problem_config.get('strategy', 'chain_of_thought'))
            evidence = problem_config.get('evidence', [])
            
            chain_id = f"reasoning_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Select appropriate template
            template = self.reasoning_templates.get(domain, self.reasoning_templates['water_quality_analysis'])
            
            # Generate reasoning steps
            reasoning_steps = await self._generate_reasoning_steps(
                problem_statement, template, evidence, strategy
            )
            
            # Calculate overall confidence
            overall_confidence = self._calculate_overall_confidence(reasoning_steps)
            
            # Generate final conclusion
            final_conclusion = await self._synthesize_conclusion(reasoning_steps, problem_statement)
            
            # Create reasoning chain
            reasoning_chain = ReasoningChain(
                chain_id=chain_id,
                problem_statement=problem_statement,
                strategy=strategy,
                steps=reasoning_steps,
                final_conclusion=final_conclusion,
                overall_confidence=overall_confidence
            )
            
            self.reasoning_chains[chain_id] = reasoning_chain
            
            return {
                'status': 'success',
                'chain_id': chain_id,
                'reasoning_summary': {
                    'problem_statement': problem_statement,
                    'strategy': strategy.value,
                    'total_steps': len(reasoning_steps),
                    'overall_confidence': overall_confidence,
                    'final_conclusion': final_conclusion
                },
                'reasoning_steps': [
                    {
                        'step_id': step.step_id,
                        'reasoning_type': step.reasoning_type.value,
                        'premise': step.premise,
                        'inference': step.inference,
                        'conclusion': step.conclusion,
                        'confidence': step.confidence
                    }
                    for step in reasoning_steps
                ],
                'created_at': reasoning_chain.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Reasoning chain creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_reasoning_steps(self, problem_statement: str, 
                                      template: Dict[str, Any],
                                      evidence: List[str],
                                      strategy: ReasoningStrategy) -> List[ReasoningStep]:
        """Generate reasoning steps based on template and strategy."""
        steps = []
        
        # Extract key information from problem statement
        problem_context = await self._extract_problem_context(problem_statement)
        
        for i, step_template in enumerate(template['steps']):
            step_id = f"step_{i+1}"
            reasoning_type = ReasoningType(step_template['type'])
            
            # Generate step based on reasoning type
            if reasoning_type == ReasoningType.DEDUCTIVE:
                step = await self._generate_deductive_step(
                    step_id, problem_context, evidence, step_template
                )
            elif reasoning_type == ReasoningType.INDUCTIVE:
                step = await self._generate_inductive_step(
                    step_id, problem_context, evidence, step_template
                )
            elif reasoning_type == ReasoningType.ABDUCTIVE:
                step = await self._generate_abductive_step(
                    step_id, problem_context, evidence, step_template
                )
            elif reasoning_type == ReasoningType.CAUSAL:
                step = await self._generate_causal_step(
                    step_id, problem_context, evidence, step_template
                )
            elif reasoning_type == ReasoningType.PROBABILISTIC:
                step = await self._generate_probabilistic_step(
                    step_id, problem_context, evidence, step_template
                )
            elif reasoning_type == ReasoningType.ANALOGICAL:
                step = await self._generate_analogical_step(
                    step_id, problem_context, evidence, step_template
                )
            elif reasoning_type == ReasoningType.TEMPORAL:
                step = await self._generate_temporal_step(
                    step_id, problem_context, evidence, step_template
                )
            else:
                # Default to deductive reasoning
                step = await self._generate_deductive_step(
                    step_id, problem_context, evidence, step_template
                )
            
            steps.append(step)
        
        return steps
    
    async def _extract_problem_context(self, problem_statement: str) -> Dict[str, Any]:
        """Extract context and key elements from problem statement."""
        context = {
            'domain': 'water_management',
            'key_terms': [],
            'parameters': [],
            'objectives': [],
            'constraints': []
        }
        
        # Simple keyword extraction (in practice, would use NLP)
        water_terms = ['ph', 'turbidity', 'chlorine', 'flow', 'pressure', 'temperature', 'quality']
        for term in water_terms:
            if term.lower() in problem_statement.lower():
                context['key_terms'].append(term)
        
        # Extract numerical values as parameters
        numbers = re.findall(r'\d+\.?\d*', problem_statement)
        context['parameters'] = numbers[:5]  # Limit to first 5 numbers
        
        # Identify objectives (words like optimize, minimize, maximize, improve)
        objective_words = ['optimize', 'minimize', 'maximize', 'improve', 'reduce', 'increase']
        for word in objective_words:
            if word.lower() in problem_statement.lower():
                context['objectives'].append(word)
        
        return context
    
    async def _generate_deductive_step(self, step_id: str, context: Dict[str, Any],
                                     evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate deductive reasoning step."""
        # Deductive: General rule + specific case → specific conclusion
        premise = f"Given the general principle that {context.get('key_terms', ['water quality'])[0] if context.get('key_terms') else 'water quality'} standards must be maintained"
        inference = f"And observing that current conditions show {evidence[0] if evidence else 'parameter variations'}"
        conclusion = f"We can deduce that {context.get('objectives', ['optimization'])[0] if context.get('objectives') else 'corrective action'} is required"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.DEDUCTIVE,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.85,
            evidence=evidence[:2],
            assumptions=["Standards are correctly defined", "Measurements are accurate"]
        )
    
    async def _generate_inductive_step(self, step_id: str, context: Dict[str, Any],
                                     evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate inductive reasoning step."""
        # Inductive: Specific observations → general pattern
        premise = f"Observing multiple instances of {context.get('key_terms', ['system behavior'])[0] if context.get('key_terms') else 'system behavior'}"
        inference = f"Pattern analysis shows consistent relationship between variables"
        conclusion = f"We can generalize that this pattern will likely continue under similar conditions"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.INDUCTIVE,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.75,
            evidence=evidence[:3],
            assumptions=["Sample is representative", "Conditions remain stable"]
        )
    
    async def _generate_abductive_step(self, step_id: str, context: Dict[str, Any],
                                     evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate abductive reasoning step."""
        # Abductive: Observation → best explanation
        premise = f"Observing unexpected {context.get('key_terms', ['behavior'])[0] if context.get('key_terms') else 'system behavior'}"
        inference = f"Considering possible explanations, the most likely cause is system parameter drift"
        conclusion = f"Therefore, the best explanation is that recalibration is needed"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.ABDUCTIVE,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.70,
            evidence=evidence[:2],
            assumptions=["No external interference", "System components functioning"]
        )
    
    async def _generate_causal_step(self, step_id: str, context: Dict[str, Any],
                                  evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate causal reasoning step."""
        # Causal: Cause → effect relationship
        premise = f"When {context.get('key_terms', ['parameter'])[0] if context.get('key_terms') else 'input parameter'} changes"
        inference = f"This triggers a cascade of effects through the system"
        conclusion = f"Resulting in predictable changes to {context.get('objectives', ['output'])[0] if context.get('objectives') else 'system output'}"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.CAUSAL,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.80,
            evidence=evidence[:2],
            assumptions=["Causal mechanism is understood", "No confounding factors"]
        )
    
    async def _generate_probabilistic_step(self, step_id: str, context: Dict[str, Any],
                                         evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate probabilistic reasoning step."""
        # Probabilistic: Evidence → probability estimate
        premise = f"Based on historical data and current evidence"
        inference = f"Statistical analysis indicates probability distributions for outcomes"
        conclusion = f"There is approximately 75% confidence in the predicted outcome"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.PROBABILISTIC,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.75,
            evidence=evidence[:3],
            assumptions=["Data is representative", "Statistical model is valid"]
        )
    
    async def _generate_analogical_step(self, step_id: str, context: Dict[str, Any],
                                      evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate analogical reasoning step."""
        # Analogical: Similar case → similar solution
        premise = f"This situation is analogous to previous cases in similar systems"
        inference = f"The structural similarity suggests similar solutions will work"
        conclusion = f"Therefore, we can adapt the successful approach from the reference case"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.ANALOGICAL,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.70,
            evidence=evidence[:2],
            assumptions=["Cases are truly analogous", "Context differences are minimal"]
        )
    
    async def _generate_temporal_step(self, step_id: str, context: Dict[str, Any],
                                    evidence: List[str], template: Dict[str, Any]) -> ReasoningStep:
        """Generate temporal reasoning step."""
        # Temporal: Time-based progression
        premise = f"Considering the temporal sequence of events"
        inference = f"Time-series analysis reveals progression patterns"
        conclusion = f"Future states can be predicted based on temporal trends"
        
        return ReasoningStep(
            step_id=step_id,
            reasoning_type=ReasoningType.TEMPORAL,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=0.65,
            evidence=evidence[:2],
            assumptions=["Trends continue", "No sudden disruptions"]
        )
    
    def _calculate_overall_confidence(self, steps: List[ReasoningStep]) -> float:
        """Calculate overall confidence from individual step confidences."""
        if not steps:
            return 0.0
        
        # Use geometric mean for conservative confidence estimate
        confidence_product = 1.0
        for step in steps:
            confidence_product *= step.confidence
        
        return confidence_product ** (1.0 / len(steps))
    
    async def _synthesize_conclusion(self, steps: List[ReasoningStep], 
                                   problem_statement: str) -> str:
        """Synthesize final conclusion from reasoning steps."""
        if not steps:
            return "No conclusion could be reached due to insufficient reasoning steps."
        
        # Combine conclusions from all steps
        step_conclusions = [step.conclusion for step in steps]
        
        # Create synthesized conclusion
        conclusion = f"Based on {len(steps)} reasoning steps using multiple approaches, "
        conclusion += f"the analysis concludes that: "
        
        # Identify the most confident step
        most_confident_step = max(steps, key=lambda s: s.confidence)
        conclusion += most_confident_step.conclusion
        
        # Add confidence qualifier
        overall_confidence = self._calculate_overall_confidence(steps)
        if overall_confidence > 0.8:
            conclusion += " This conclusion is supported with high confidence."
        elif overall_confidence > 0.6:
            conclusion += " This conclusion is supported with moderate confidence."
        else:
            conclusion += " This conclusion should be considered tentative."
        
        return conclusion
    
    @log_async_function_call
    async def evaluate_reasoning_quality(self, chain_id: str) -> Dict[str, Any]:
        """Evaluate the quality of a reasoning chain."""
        try:
            if chain_id not in self.reasoning_chains:
                return {'status': 'error', 'error': 'Reasoning chain not found'}
            
            chain = self.reasoning_chains[chain_id]
            
            # Evaluate different aspects
            coherence_score = await self._evaluate_coherence(chain)
            completeness_score = await self._evaluate_completeness(chain)
            validity_score = await self._evaluate_validity(chain)
            clarity_score = await self._evaluate_clarity(chain)
            
            # Calculate overall quality score
            overall_score = (coherence_score + completeness_score + 
                           validity_score + clarity_score) / 4
            
            return {
                'status': 'success',
                'chain_id': chain_id,
                'quality_metrics': {
                    'coherence_score': coherence_score,
                    'completeness_score': completeness_score,
                    'validity_score': validity_score,
                    'clarity_score': clarity_score,
                    'overall_score': overall_score
                },
                'quality_assessment': self._get_quality_assessment(overall_score),
                'improvement_suggestions': await self._generate_improvement_suggestions(chain)
            }
            
        except Exception as e:
            logger.error(f"Reasoning quality evaluation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _evaluate_coherence(self, chain: ReasoningChain) -> float:
        """Evaluate logical coherence of reasoning chain."""
        # Check if steps follow logically
        coherence_score = 0.8  # Base score
        
        # Penalize for inconsistencies
        for i in range(len(chain.steps) - 1):
            current_step = chain.steps[i]
            next_step = chain.steps[i + 1]
            
            # Simple coherence check (in practice, would use more sophisticated analysis)
            if current_step.confidence > 0.9 and next_step.confidence < 0.5:
                coherence_score -= 0.1  # Inconsistent confidence levels
        
        return max(0.0, min(1.0, coherence_score))
    
    async def _evaluate_completeness(self, chain: ReasoningChain) -> float:
        """Evaluate completeness of reasoning chain."""
        # Check if all necessary reasoning types are covered
        reasoning_types_used = set(step.reasoning_type for step in chain.steps)
        
        # For water management, expect at least causal and probabilistic reasoning
        expected_types = {ReasoningType.CAUSAL, ReasoningType.PROBABILISTIC}
        coverage = len(reasoning_types_used.intersection(expected_types)) / len(expected_types)
        
        # Bonus for using diverse reasoning types
        diversity_bonus = min(0.2, len(reasoning_types_used) * 0.05)
        
        return min(1.0, coverage + diversity_bonus)
    
    async def _evaluate_validity(self, chain: ReasoningChain) -> float:
        """Evaluate logical validity of reasoning steps."""
        # Check if premises support conclusions
        validity_score = 0.0
        
        for step in chain.steps:
            # Simple validity check based on reasoning type
            if step.reasoning_type == ReasoningType.DEDUCTIVE:
                validity_score += 0.9  # Deductive reasoning is typically valid if premises are true
            elif step.reasoning_type == ReasoningType.INDUCTIVE:
                validity_score += 0.7  # Inductive reasoning is probabilistic
            elif step.reasoning_type == ReasoningType.ABDUCTIVE:
                validity_score += 0.6  # Abductive reasoning provides best explanation
            else:
                validity_score += 0.8  # Other types
        
        return min(1.0, validity_score / len(chain.steps) if chain.steps else 0)
    
    async def _evaluate_clarity(self, chain: ReasoningChain) -> float:
        """Evaluate clarity and understandability of reasoning."""
        # Simple clarity metrics
        clarity_score = 0.8  # Base score
        
        for step in chain.steps:
            # Check for clear structure
            if len(step.premise) < 10 or len(step.conclusion) < 10:
                clarity_score -= 0.1  # Too brief
            elif len(step.premise) > 200 or len(step.conclusion) > 200:
                clarity_score -= 0.1  # Too verbose
        
        return max(0.0, min(1.0, clarity_score))
    
    def _get_quality_assessment(self, score: float) -> str:
        """Get qualitative assessment of reasoning quality."""
        if score >= 0.9:
            return "Excellent reasoning quality"
        elif score >= 0.8:
            return "Good reasoning quality"
        elif score >= 0.7:
            return "Acceptable reasoning quality"
        elif score >= 0.6:
            return "Poor reasoning quality"
        else:
            return "Inadequate reasoning quality"
    
    async def _generate_improvement_suggestions(self, chain: ReasoningChain) -> List[str]:
        """Generate suggestions for improving reasoning quality."""
        suggestions = []
        
        # Check reasoning diversity
        reasoning_types_used = set(step.reasoning_type for step in chain.steps)
        if len(reasoning_types_used) < 3:
            suggestions.append("Consider using more diverse reasoning types for comprehensive analysis")
        
        # Check confidence levels
        low_confidence_steps = [s for s in chain.steps if s.confidence < 0.6]
        if len(low_confidence_steps) > len(chain.steps) / 2:
            suggestions.append("Many steps have low confidence - consider gathering more evidence")
        
        # Check evidence usage
        steps_without_evidence = [s for s in chain.steps if not s.evidence]
        if len(steps_without_evidence) > 0:
            suggestions.append("Some steps lack supporting evidence - add relevant data or observations")
        
        # Check assumptions
        steps_with_many_assumptions = [s for s in chain.steps if len(s.assumptions) > 3]
        if len(steps_with_many_assumptions) > 0:
            suggestions.append("Some steps rely on many assumptions - try to validate key assumptions")
        
        return suggestions


# Convenience functions
async def create_water_management_reasoning(problem_statement: str, 
                                          evidence: List[str] = None) -> Dict[str, Any]:
    """Create reasoning chain for water management problem."""
    system = AdvancedReasoningSystem()
    
    config = {
        'problem_statement': problem_statement,
        'domain': 'water_quality_analysis',
        'strategy': 'evidence_based',
        'evidence': evidence or []
    }
    
    return await system.create_reasoning_chain(config)


async def evaluate_reasoning_chain(chain_id: str) -> Dict[str, Any]:
    """Evaluate quality of reasoning chain."""
    system = AdvancedReasoningSystem()
    return await system.evaluate_reasoning_quality(chain_id)
