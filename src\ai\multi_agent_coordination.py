"""Multi-Agent Coordination System for Water Management."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class AgentStatus(Enum):
    """Agent status states."""
    IDLE = "idle"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


@dataclass
class AgentCapability:
    """Agent capability definition."""
    name: str
    description: str
    input_types: List[str]
    output_types: List[str]
    processing_time: float  # seconds
    reliability: float  # 0-1


@dataclass
class Task:
    """Task definition for agent execution."""
    task_id: str
    task_type: str
    priority: TaskPriority
    data: Dict[str, Any]
    requirements: List[str]
    deadline: Optional[datetime] = None
    assigned_agent: Optional[str] = None
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


@dataclass
class Agent:
    """Agent definition."""
    agent_id: str
    name: str
    agent_type: str
    capabilities: List[AgentCapability]
    status: AgentStatus
    current_task: Optional[str] = None
    load_factor: float = 0.0  # 0-1
    last_heartbeat: datetime = field(default_factory=datetime.now)
    performance_metrics: Dict[str, float] = field(default_factory=dict)


class MultiAgentCoordinator:
    """Advanced multi-agent coordination system."""
    
    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[str] = []
        self.completed_tasks: List[str] = []
        self.coordination_rules = {}
        self.performance_history = []
        
        # Initialize coordination system
        self._initialize_agents()
        self._initialize_coordination_rules()
    
    def _initialize_agents(self):
        """Initialize water management agents."""
        # Climate Analysis Agent
        climate_agent = Agent(
            agent_id="climate_agent",
            name="Climate Analysis Agent",
            agent_type="analysis",
            capabilities=[
                AgentCapability(
                    name="climate_data_analysis",
                    description="Analyze climate data and trends",
                    input_types=["climate_data", "weather_data"],
                    output_types=["climate_analysis", "projections"],
                    processing_time=30.0,
                    reliability=0.95
                ),
                AgentCapability(
                    name="extreme_weather_prediction",
                    description="Predict extreme weather events",
                    input_types=["weather_patterns", "historical_data"],
                    output_types=["weather_predictions", "risk_assessment"],
                    processing_time=45.0,
                    reliability=0.88
                )
            ],
            status=AgentStatus.IDLE,
            performance_metrics={"accuracy": 0.92, "response_time": 28.5}
        )
        
        # Treatment Optimization Agent
        treatment_agent = Agent(
            agent_id="treatment_agent",
            name="Treatment Optimization Agent",
            agent_type="optimization",
            capabilities=[
                AgentCapability(
                    name="process_optimization",
                    description="Optimize treatment processes",
                    input_types=["process_data", "quality_targets"],
                    output_types=["optimization_plan", "parameter_settings"],
                    processing_time=60.0,
                    reliability=0.93
                ),
                AgentCapability(
                    name="chemical_dosing_optimization",
                    description="Optimize chemical dosing",
                    input_types=["water_quality", "chemical_inventory"],
                    output_types=["dosing_schedule", "cost_analysis"],
                    processing_time=25.0,
                    reliability=0.90
                )
            ],
            status=AgentStatus.IDLE,
            performance_metrics={"efficiency_improvement": 0.15, "cost_reduction": 0.12}
        )
        
        # Predictive Maintenance Agent
        maintenance_agent = Agent(
            agent_id="maintenance_agent",
            name="Predictive Maintenance Agent",
            agent_type="prediction",
            capabilities=[
                AgentCapability(
                    name="equipment_health_monitoring",
                    description="Monitor equipment health",
                    input_types=["sensor_data", "operational_data"],
                    output_types=["health_assessment", "maintenance_schedule"],
                    processing_time=20.0,
                    reliability=0.91
                ),
                AgentCapability(
                    name="failure_prediction",
                    description="Predict equipment failures",
                    input_types=["performance_data", "maintenance_history"],
                    output_types=["failure_predictions", "risk_scores"],
                    processing_time=35.0,
                    reliability=0.87
                )
            ],
            status=AgentStatus.IDLE,
            performance_metrics={"prediction_accuracy": 0.89, "false_positive_rate": 0.08}
        )
        
        # Energy Efficiency Agent
        energy_agent = Agent(
            agent_id="energy_agent",
            name="Energy Efficiency Agent",
            agent_type="optimization",
            capabilities=[
                AgentCapability(
                    name="energy_optimization",
                    description="Optimize energy consumption",
                    input_types=["energy_data", "operational_schedule"],
                    output_types=["energy_plan", "savings_projection"],
                    processing_time=40.0,
                    reliability=0.92
                ),
                AgentCapability(
                    name="renewable_integration",
                    description="Integrate renewable energy sources",
                    input_types=["renewable_data", "grid_data"],
                    output_types=["integration_plan", "stability_analysis"],
                    processing_time=50.0,
                    reliability=0.85
                )
            ],
            status=AgentStatus.IDLE,
            performance_metrics={"energy_savings": 0.18, "renewable_integration": 0.65}
        )
        
        # Water Quality Agent
        quality_agent = Agent(
            agent_id="quality_agent",
            name="Water Quality Agent",
            agent_type="monitoring",
            capabilities=[
                AgentCapability(
                    name="quality_assessment",
                    description="Assess water quality",
                    input_types=["quality_data", "standards"],
                    output_types=["quality_report", "compliance_status"],
                    processing_time=15.0,
                    reliability=0.96
                ),
                AgentCapability(
                    name="contamination_detection",
                    description="Detect water contamination",
                    input_types=["sensor_readings", "baseline_data"],
                    output_types=["contamination_alerts", "source_identification"],
                    processing_time=10.0,
                    reliability=0.94
                )
            ],
            status=AgentStatus.IDLE,
            performance_metrics={"detection_accuracy": 0.96, "response_time": 8.2}
        )
        
        # Store agents
        self.agents = {
            agent.agent_id: agent for agent in [
                climate_agent, treatment_agent, maintenance_agent, 
                energy_agent, quality_agent
            ]
        }
    
    def _initialize_coordination_rules(self):
        """Initialize coordination rules between agents."""
        self.coordination_rules = {
            'priority_escalation': {
                'emergency_tasks': 'immediate_assignment',
                'critical_tasks': 'priority_queue',
                'high_priority': 'fast_track'
            },
            'agent_collaboration': {
                'climate_treatment': ['climate_agent', 'treatment_agent'],
                'quality_maintenance': ['quality_agent', 'maintenance_agent'],
                'energy_optimization': ['energy_agent', 'treatment_agent']
            },
            'load_balancing': {
                'max_load_factor': 0.8,
                'redistribution_threshold': 0.9,
                'idle_preference': True
            },
            'failure_handling': {
                'agent_timeout': 300,  # seconds
                'retry_attempts': 3,
                'fallback_agents': {
                    'climate_agent': ['treatment_agent'],
                    'treatment_agent': ['energy_agent'],
                    'maintenance_agent': ['quality_agent']
                }
            }
        }
    
    @log_async_function_call
    async def submit_task(self, task_type: str, data: Dict[str, Any], 
                         priority: TaskPriority = TaskPriority.MEDIUM,
                         deadline: Optional[datetime] = None) -> Dict[str, Any]:
        """Submit task for agent execution."""
        try:
            task_id = str(uuid.uuid4())
            
            # Determine task requirements
            requirements = self._determine_task_requirements(task_type, data)
            
            # Create task
            task = Task(
                task_id=task_id,
                task_type=task_type,
                priority=priority,
                data=data,
                requirements=requirements,
                deadline=deadline
            )
            
            self.tasks[task_id] = task
            
            # Add to queue based on priority
            self._add_to_queue(task_id)
            
            # Attempt immediate assignment if possible
            assignment_result = await self._assign_task(task_id)
            
            return {
                'status': 'success',
                'task_id': task_id,
                'assignment_status': assignment_result['status'],
                'estimated_completion': self._estimate_completion_time(task_id),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Task submission failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _determine_task_requirements(self, task_type: str, data: Dict[str, Any]) -> List[str]:
        """Determine task requirements based on type and data."""
        requirements_map = {
            'climate_analysis': ['climate_data_analysis'],
            'treatment_optimization': ['process_optimization'],
            'maintenance_prediction': ['equipment_health_monitoring', 'failure_prediction'],
            'energy_optimization': ['energy_optimization'],
            'quality_assessment': ['quality_assessment'],
            'contamination_detection': ['contamination_detection'],
            'weather_prediction': ['extreme_weather_prediction'],
            'chemical_optimization': ['chemical_dosing_optimization'],
            'renewable_integration': ['renewable_integration']
        }
        
        return requirements_map.get(task_type, [task_type])
    
    def _add_to_queue(self, task_id: str):
        """Add task to queue based on priority."""
        task = self.tasks[task_id]
        
        # Insert based on priority
        inserted = False
        for i, queued_task_id in enumerate(self.task_queue):
            queued_task = self.tasks[queued_task_id]
            if task.priority.value > queued_task.priority.value:
                self.task_queue.insert(i, task_id)
                inserted = True
                break
        
        if not inserted:
            self.task_queue.append(task_id)
    
    @log_async_function_call
    async def _assign_task(self, task_id: str) -> Dict[str, Any]:
        """Assign task to appropriate agent."""
        try:
            task = self.tasks[task_id]
            
            # Find capable agents
            capable_agents = self._find_capable_agents(task.requirements)
            
            if not capable_agents:
                return {
                    'status': 'no_capable_agents',
                    'message': f'No agents capable of handling {task.task_type}'
                }
            
            # Select best agent based on load and performance
            selected_agent = self._select_best_agent(capable_agents, task)
            
            if not selected_agent:
                return {
                    'status': 'all_agents_busy',
                    'message': 'All capable agents are currently busy'
                }
            
            # Assign task
            task.assigned_agent = selected_agent.agent_id
            task.status = "assigned"
            task.started_at = datetime.now()
            
            selected_agent.current_task = task_id
            selected_agent.status = AgentStatus.BUSY
            selected_agent.load_factor = min(1.0, selected_agent.load_factor + 0.3)
            
            # Remove from queue
            if task_id in self.task_queue:
                self.task_queue.remove(task_id)
            
            # Start task execution
            asyncio.create_task(self._execute_task(task_id))
            
            return {
                'status': 'assigned',
                'agent_id': selected_agent.agent_id,
                'agent_name': selected_agent.name
            }
            
        except Exception as e:
            logger.error(f"Task assignment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _find_capable_agents(self, requirements: List[str]) -> List[Agent]:
        """Find agents capable of handling task requirements."""
        capable_agents = []
        
        for agent in self.agents.values():
            if agent.status == AgentStatus.OFFLINE:
                continue
            
            agent_capabilities = [cap.name for cap in agent.capabilities]
            
            # Check if agent has all required capabilities
            if all(req in agent_capabilities for req in requirements):
                capable_agents.append(agent)
        
        return capable_agents
    
    def _select_best_agent(self, capable_agents: List[Agent], task: Task) -> Optional[Agent]:
        """Select best agent for task based on load and performance."""
        available_agents = [
            agent for agent in capable_agents 
            if agent.status in [AgentStatus.IDLE, AgentStatus.ACTIVE] and 
               agent.load_factor < self.coordination_rules['load_balancing']['max_load_factor']
        ]
        
        if not available_agents:
            return None
        
        # Score agents based on multiple factors
        best_agent = None
        best_score = -1
        
        for agent in available_agents:
            score = self._calculate_agent_score(agent, task)
            if score > best_score:
                best_score = score
                best_agent = agent
        
        return best_agent
    
    def _calculate_agent_score(self, agent: Agent, task: Task) -> float:
        """Calculate agent suitability score for task."""
        # Base score from load factor (lower load = higher score)
        load_score = 1.0 - agent.load_factor
        
        # Performance score
        performance_score = agent.performance_metrics.get('accuracy', 0.8)
        
        # Capability match score
        capability_score = 1.0  # All capable agents have perfect match
        
        # Priority bonus for idle agents
        status_bonus = 0.2 if agent.status == AgentStatus.IDLE else 0.0
        
        # Deadline urgency factor
        urgency_factor = 1.0
        if task.deadline:
            time_to_deadline = (task.deadline - datetime.now()).total_seconds()
            if time_to_deadline < 3600:  # Less than 1 hour
                urgency_factor = 1.5
        
        total_score = (
            load_score * 0.4 + 
            performance_score * 0.3 + 
            capability_score * 0.2 + 
            status_bonus
        ) * urgency_factor
        
        return total_score
    
    async def _execute_task(self, task_id: str):
        """Execute task with assigned agent."""
        try:
            task = self.tasks[task_id]
            agent = self.agents[task.assigned_agent]
            
            logger.info(f"Executing task {task_id} with agent {agent.name}")
            
            # Simulate task execution
            execution_time = self._estimate_execution_time(task, agent)
            await asyncio.sleep(min(execution_time, 5.0))  # Cap simulation time
            
            # Generate task result
            result = await self._generate_task_result(task, agent)
            
            # Complete task
            task.result = result
            task.status = "completed"
            task.completed_at = datetime.now()
            
            # Update agent status
            agent.current_task = None
            agent.status = AgentStatus.IDLE
            agent.load_factor = max(0.0, agent.load_factor - 0.3)
            agent.last_heartbeat = datetime.now()
            
            # Add to completed tasks
            self.completed_tasks.append(task_id)
            
            # Update performance metrics
            self._update_performance_metrics(agent, task)
            
            logger.info(f"Task {task_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Task execution failed for {task_id}: {e}")
            
            # Handle task failure
            task = self.tasks[task_id]
            task.status = "failed"
            task.result = {'error': str(e)}
            
            # Reset agent
            if task.assigned_agent:
                agent = self.agents[task.assigned_agent]
                agent.current_task = None
                agent.status = AgentStatus.ERROR
                agent.load_factor = max(0.0, agent.load_factor - 0.3)
    
    def _estimate_execution_time(self, task: Task, agent: Agent) -> float:
        """Estimate task execution time."""
        base_time = 30.0  # Default 30 seconds
        
        # Find matching capability
        for capability in agent.capabilities:
            if capability.name in task.requirements:
                base_time = capability.processing_time
                break
        
        # Adjust for agent load
        load_factor = 1.0 + agent.load_factor * 0.5
        
        # Adjust for task priority
        priority_factor = {
            TaskPriority.LOW: 1.2,
            TaskPriority.MEDIUM: 1.0,
            TaskPriority.HIGH: 0.8,
            TaskPriority.CRITICAL: 0.6,
            TaskPriority.EMERGENCY: 0.4
        }[task.priority]
        
        return base_time * load_factor * priority_factor
    
    async def _generate_task_result(self, task: Task, agent: Agent) -> Dict[str, Any]:
        """Generate task result based on task type and agent capabilities."""
        base_result = {
            'task_id': task.task_id,
            'agent_id': agent.agent_id,
            'execution_time': (datetime.now() - task.started_at).total_seconds(),
            'status': 'completed'
        }
        
        # Task-specific results
        if task.task_type == 'climate_analysis':
            base_result.update({
                'climate_trends': ['temperature_increase', 'precipitation_change'],
                'risk_assessment': 'medium',
                'adaptation_recommendations': ['infrastructure_upgrade', 'process_optimization']
            })
        
        elif task.task_type == 'treatment_optimization':
            base_result.update({
                'optimized_parameters': {
                    'flow_rate': 950,
                    'chemical_dose': 2.3,
                    'energy_consumption': 420
                },
                'efficiency_improvement': 8.5,
                'cost_savings': 12.3
            })
        
        elif task.task_type == 'maintenance_prediction':
            base_result.update({
                'maintenance_schedule': [
                    {'equipment': 'pump_1', 'date': '2024-01-15', 'type': 'preventive'},
                    {'equipment': 'filter_2', 'date': '2024-01-20', 'type': 'replacement'}
                ],
                'risk_scores': {'pump_1': 0.3, 'filter_2': 0.7},
                'cost_estimate': 15000
            })
        
        elif task.task_type == 'quality_assessment':
            base_result.update({
                'quality_status': 'compliant',
                'parameters': {
                    'ph': 7.2,
                    'turbidity': 1.5,
                    'chlorine_residual': 0.8
                },
                'recommendations': ['monitor_turbidity', 'adjust_ph']
            })
        
        else:
            base_result.update({
                'message': f'Task {task.task_type} completed',
                'data_processed': len(str(task.data))
            })
        
        return base_result
    
    def _estimate_completion_time(self, task_id: str) -> str:
        """Estimate task completion time."""
        task = self.tasks[task_id]
        
        if task.status == "completed":
            return "Already completed"
        
        if task.assigned_agent:
            agent = self.agents[task.assigned_agent]
            execution_time = self._estimate_execution_time(task, agent)
            completion_time = datetime.now() + timedelta(seconds=execution_time)
            return completion_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # Estimate based on queue position
        queue_position = self.task_queue.index(task_id) if task_id in self.task_queue else 0
        estimated_wait = queue_position * 60  # 1 minute per task in queue
        completion_time = datetime.now() + timedelta(seconds=estimated_wait + 120)
        
        return completion_time.strftime('%Y-%m-%d %H:%M:%S')
    
    def _update_performance_metrics(self, agent: Agent, task: Task):
        """Update agent performance metrics."""
        execution_time = (task.completed_at - task.started_at).total_seconds()
        
        # Update response time
        if 'response_time' in agent.performance_metrics:
            current_time = agent.performance_metrics['response_time']
            agent.performance_metrics['response_time'] = (current_time + execution_time) / 2
        else:
            agent.performance_metrics['response_time'] = execution_time
        
        # Update task completion count
        if 'tasks_completed' in agent.performance_metrics:
            agent.performance_metrics['tasks_completed'] += 1
        else:
            agent.performance_metrics['tasks_completed'] = 1
        
        # Update success rate
        if task.status == "completed":
            if 'success_rate' in agent.performance_metrics:
                current_rate = agent.performance_metrics['success_rate']
                total_tasks = agent.performance_metrics['tasks_completed']
                agent.performance_metrics['success_rate'] = (current_rate * (total_tasks - 1) + 1.0) / total_tasks
            else:
                agent.performance_metrics['success_rate'] = 1.0
    
    @log_async_function_call
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        try:
            agent_statuses = {}
            for agent_id, agent in self.agents.items():
                agent_statuses[agent_id] = {
                    'name': agent.name,
                    'status': agent.status.value,
                    'load_factor': agent.load_factor,
                    'current_task': agent.current_task,
                    'performance_metrics': agent.performance_metrics
                }
            
            task_summary = {
                'total_tasks': len(self.tasks),
                'pending_tasks': len(self.task_queue),
                'completed_tasks': len(self.completed_tasks),
                'active_tasks': len([t for t in self.tasks.values() if t.status == "assigned"])
            }
            
            return {
                'status': 'success',
                'system_status': {
                    'agents': agent_statuses,
                    'tasks': task_summary,
                    'queue_length': len(self.task_queue),
                    'system_load': sum(a.load_factor for a in self.agents.values()) / len(self.agents)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"System status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get detailed task status."""
        try:
            if task_id not in self.tasks:
                return {'status': 'error', 'error': 'Task not found'}
            
            task = self.tasks[task_id]
            
            task_info = {
                'task_id': task_id,
                'task_type': task.task_type,
                'priority': task.priority.value,
                'status': task.status,
                'assigned_agent': task.assigned_agent,
                'created_at': task.created_at.isoformat(),
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                'result': task.result
            }
            
            if task.deadline:
                task_info['deadline'] = task.deadline.isoformat()
                task_info['time_remaining'] = (task.deadline - datetime.now()).total_seconds()
            
            return {
                'status': 'success',
                'task_info': task_info,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Task status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience functions
async def submit_coordination_task(task_type: str, data: Dict[str, Any], 
                                 priority: str = 'medium') -> Dict[str, Any]:
    """Submit task to multi-agent coordinator."""
    coordinator = MultiAgentCoordinator()
    priority_map = {
        'low': TaskPriority.LOW,
        'medium': TaskPriority.MEDIUM,
        'high': TaskPriority.HIGH,
        'critical': TaskPriority.CRITICAL,
        'emergency': TaskPriority.EMERGENCY
    }
    return await coordinator.submit_task(task_type, data, priority_map.get(priority, TaskPriority.MEDIUM))


async def get_coordination_status() -> Dict[str, Any]:
    """Get multi-agent coordination system status."""
    coordinator = MultiAgentCoordinator()
    return await coordinator.get_system_status()
