"""Reinforcement Learning Models for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ActionType(Enum):
    """Types of actions the RL agent can take."""
    ADJUST_FLOW_RATE = "adjust_flow_rate"
    MODIFY_CHEMICAL_DOSE = "modify_chemical_dose"
    CHANGE_PRESSURE = "change_pressure"
    ACTIVATE_BACKUP = "activate_backup"
    SCHEDULE_MAINTENANCE = "schedule_maintenance"
    OPTIMIZE_ENERGY = "optimize_energy"


@dataclass
class State:
    """Environment state representation."""
    water_quality: Dict[str, float]
    flow_rates: Dict[str, float]
    energy_consumption: float
    equipment_status: Dict[str, str]
    timestamp: datetime
    
    def to_vector(self) -> np.ndarray:
        """Convert state to numerical vector."""
        vector = []
        
        # Water quality features
        vector.extend([
            self.water_quality.get('ph', 7.0),
            self.water_quality.get('turbidity', 1.0),
            self.water_quality.get('chlorine', 0.5),
            self.water_quality.get('bacteria', 0.0)
        ])
        
        # Flow rate features
        vector.extend([
            self.flow_rates.get('input', 1000.0),
            self.flow_rates.get('output', 950.0),
            self.flow_rates.get('recycle', 50.0)
        ])
        
        # Energy and efficiency
        vector.append(self.energy_consumption)
        
        # Equipment status (encoded as numbers)
        status_encoding = {'normal': 1.0, 'warning': 0.5, 'error': 0.0, 'offline': -1.0}
        for equipment in ['pump_1', 'pump_2', 'filter_1', 'filter_2']:
            status = self.equipment_status.get(equipment, 'normal')
            vector.append(status_encoding.get(status, 0.0))
        
        return np.array(vector, dtype=np.float32)


@dataclass
class Action:
    """Action representation."""
    action_type: ActionType
    parameters: Dict[str, float]
    magnitude: float  # -1.0 to 1.0
    
    def to_vector(self) -> np.ndarray:
        """Convert action to numerical vector."""
        # One-hot encoding for action type
        action_encoding = np.zeros(len(ActionType))
        action_encoding[list(ActionType).index(self.action_type)] = 1.0
        
        # Add magnitude
        vector = np.concatenate([action_encoding, [self.magnitude]])
        
        return vector.astype(np.float32)


class WaterTreatmentEnvironment:
    """Water treatment environment for RL training."""
    
    def __init__(self):
        self.state = None
        self.episode_length = 100
        self.current_step = 0
        self.total_reward = 0.0
        
        # Environment parameters
        self.target_quality = {
            'ph': 7.2,
            'turbidity': 1.0,
            'chlorine': 0.8,
            'bacteria': 0.0
        }
        
        self.quality_bounds = {
            'ph': (6.5, 8.5),
            'turbidity': (0.0, 4.0),
            'chlorine': (0.2, 4.0),
            'bacteria': (0.0, 0.0)
        }
        
        self.reset()
    
    def reset(self) -> np.ndarray:
        """Reset environment to initial state."""
        self.current_step = 0
        self.total_reward = 0.0
        
        # Initialize random state
        self.state = State(
            water_quality={
                'ph': np.random.uniform(6.8, 7.6),
                'turbidity': np.random.uniform(0.5, 2.0),
                'chlorine': np.random.uniform(0.4, 1.2),
                'bacteria': 0.0
            },
            flow_rates={
                'input': np.random.uniform(900, 1100),
                'output': np.random.uniform(850, 1050),
                'recycle': np.random.uniform(30, 70)
            },
            energy_consumption=np.random.uniform(400, 600),
            equipment_status={
                'pump_1': 'normal',
                'pump_2': 'normal',
                'filter_1': 'normal',
                'filter_2': 'normal'
            },
            timestamp=datetime.now()
        )
        
        return self.state.to_vector()
    
    def step(self, action: Action) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """Execute action and return new state, reward, done, info."""
        self.current_step += 1
        
        # Apply action to environment
        self._apply_action(action)
        
        # Calculate reward
        reward = self._calculate_reward(action)
        self.total_reward += reward
        
        # Check if episode is done
        done = (self.current_step >= self.episode_length or 
                self._is_unsafe_state() or 
                self._is_target_achieved())
        
        # Additional info
        info = {
            'step': self.current_step,
            'total_reward': self.total_reward,
            'quality_compliance': self._check_quality_compliance(),
            'energy_efficiency': self._calculate_energy_efficiency(),
            'safety_status': 'safe' if not self._is_unsafe_state() else 'unsafe'
        }
        
        return self.state.to_vector(), reward, done, info
    
    def _apply_action(self, action: Action):
        """Apply action to modify environment state."""
        if action.action_type == ActionType.ADJUST_FLOW_RATE:
            # Adjust flow rates
            adjustment = action.magnitude * 50  # Max 50 units adjustment
            self.state.flow_rates['input'] += adjustment
            self.state.flow_rates['output'] += adjustment * 0.95  # Some loss
            
            # Update energy consumption
            self.state.energy_consumption += abs(adjustment) * 0.1
        
        elif action.action_type == ActionType.MODIFY_CHEMICAL_DOSE:
            # Adjust chemical dosing affects water quality
            dose_change = action.magnitude * 0.2  # Max 0.2 mg/L change
            
            # Chlorine adjustment
            self.state.water_quality['chlorine'] += dose_change
            self.state.water_quality['chlorine'] = np.clip(
                self.state.water_quality['chlorine'], 0.0, 5.0
            )
            
            # pH adjustment (secondary effect)
            self.state.water_quality['ph'] += dose_change * 0.1
            self.state.water_quality['ph'] = np.clip(
                self.state.water_quality['ph'], 5.0, 9.0
            )
        
        elif action.action_type == ActionType.CHANGE_PRESSURE:
            # Pressure changes affect filtration efficiency
            pressure_change = action.magnitude * 10  # Max 10 psi change
            
            # Affects turbidity
            turbidity_change = -pressure_change * 0.02  # Higher pressure = lower turbidity
            self.state.water_quality['turbidity'] += turbidity_change
            self.state.water_quality['turbidity'] = np.clip(
                self.state.water_quality['turbidity'], 0.0, 10.0
            )
            
            # Energy impact
            self.state.energy_consumption += abs(pressure_change) * 0.5
        
        elif action.action_type == ActionType.OPTIMIZE_ENERGY:
            # Energy optimization
            energy_reduction = action.magnitude * 50  # Max 50 kWh reduction
            self.state.energy_consumption -= energy_reduction
            self.state.energy_consumption = max(200, self.state.energy_consumption)
            
            # Slight impact on performance
            if energy_reduction > 0:
                self.state.water_quality['turbidity'] += energy_reduction * 0.001
        
        # Add some random environmental changes
        self._apply_environmental_changes()
    
    def _apply_environmental_changes(self):
        """Apply random environmental changes."""
        # Random fluctuations
        self.state.water_quality['ph'] += np.random.normal(0, 0.05)
        self.state.water_quality['turbidity'] += np.random.normal(0, 0.1)
        self.state.water_quality['chlorine'] += np.random.normal(0, 0.02)
        
        # Clip to reasonable bounds
        self.state.water_quality['ph'] = np.clip(self.state.water_quality['ph'], 5.0, 9.0)
        self.state.water_quality['turbidity'] = np.clip(self.state.water_quality['turbidity'], 0.0, 10.0)
        self.state.water_quality['chlorine'] = np.clip(self.state.water_quality['chlorine'], 0.0, 5.0)
        
        # Equipment degradation
        if np.random.random() < 0.01:  # 1% chance per step
            equipment = np.random.choice(list(self.state.equipment_status.keys()))
            if self.state.equipment_status[equipment] == 'normal':
                self.state.equipment_status[equipment] = 'warning'
    
    def _calculate_reward(self, action: Action) -> float:
        """Calculate reward for current state and action."""
        reward = 0.0
        
        # Quality compliance reward
        quality_reward = 0.0
        for param, target in self.target_quality.items():
            current = self.state.water_quality[param]
            bounds = self.quality_bounds[param]
            
            if bounds[0] <= current <= bounds[1]:
                # Within bounds - reward based on proximity to target
                distance = abs(current - target)
                max_distance = max(target - bounds[0], bounds[1] - target)
                quality_reward += (1.0 - distance / max_distance) * 10
            else:
                # Out of bounds - penalty
                if current < bounds[0]:
                    quality_reward -= (bounds[0] - current) * 20
                else:
                    quality_reward -= (current - bounds[1]) * 20
        
        reward += quality_reward
        
        # Energy efficiency reward
        target_energy = 450  # kWh
        energy_efficiency = max(0, 1.0 - abs(self.state.energy_consumption - target_energy) / target_energy)
        reward += energy_efficiency * 5
        
        # Flow efficiency reward
        input_flow = self.state.flow_rates['input']
        output_flow = self.state.flow_rates['output']
        flow_efficiency = output_flow / input_flow if input_flow > 0 else 0
        reward += flow_efficiency * 3
        
        # Equipment health reward
        healthy_equipment = sum(1 for status in self.state.equipment_status.values() if status == 'normal')
        equipment_reward = (healthy_equipment / len(self.state.equipment_status)) * 2
        reward += equipment_reward
        
        # Action cost penalty
        action_cost = abs(action.magnitude) * 0.1
        reward -= action_cost
        
        return reward
    
    def _check_quality_compliance(self) -> bool:
        """Check if water quality is within compliance bounds."""
        for param, bounds in self.quality_bounds.items():
            current = self.state.water_quality[param]
            if not (bounds[0] <= current <= bounds[1]):
                return False
        return True
    
    def _calculate_energy_efficiency(self) -> float:
        """Calculate energy efficiency score."""
        target_energy = 450
        return max(0, 1.0 - abs(self.state.energy_consumption - target_energy) / target_energy)
    
    def _is_unsafe_state(self) -> bool:
        """Check if current state is unsafe."""
        # Critical safety bounds
        ph = self.state.water_quality['ph']
        bacteria = self.state.water_quality['bacteria']
        
        if ph < 6.0 or ph > 9.0:
            return True
        
        if bacteria > 0.1:
            return True
        
        # Equipment failures
        failed_equipment = sum(1 for status in self.state.equipment_status.values() if status == 'error')
        if failed_equipment > 1:
            return True
        
        return False
    
    def _is_target_achieved(self) -> bool:
        """Check if target performance is achieved."""
        # Check if all quality parameters are close to target
        for param, target in self.target_quality.items():
            current = self.state.water_quality[param]
            tolerance = 0.1 * target if target > 0 else 0.05
            
            if abs(current - target) > tolerance:
                return False
        
        # Check energy efficiency
        if self._calculate_energy_efficiency() < 0.9:
            return False
        
        return True


class DQNAgent:
    """Deep Q-Network agent for water treatment optimization."""
    
    def __init__(self, state_size: int, action_size: int):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = []
        self.memory_size = 10000
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.gamma = 0.95  # Discount factor
        self.batch_size = 32
        
        # Q-network (simplified implementation)
        self.q_network = self._build_network()
        self.target_network = self._build_network()
        self.update_target_network()
        
        # Training statistics
        self.training_stats = {
            'episodes': 0,
            'total_reward': 0.0,
            'average_reward': 0.0,
            'epsilon': self.epsilon,
            'loss': 0.0
        }
    
    def _build_network(self) -> Dict[str, Any]:
        """Build neural network (simplified representation)."""
        # In a real implementation, this would use TensorFlow/PyTorch
        return {
            'weights': {
                'layer1': np.random.randn(self.state_size, 64) * 0.1,
                'layer2': np.random.randn(64, 32) * 0.1,
                'output': np.random.randn(32, self.action_size) * 0.1
            },
            'biases': {
                'layer1': np.zeros(64),
                'layer2': np.zeros(32),
                'output': np.zeros(self.action_size)
            }
        }
    
    def update_target_network(self):
        """Update target network with current network weights."""
        # Copy weights from main network to target network
        self.target_network = {
            'weights': {k: v.copy() for k, v in self.q_network['weights'].items()},
            'biases': {k: v.copy() for k, v in self.q_network['biases'].items()}
        }
    
    def remember(self, state: np.ndarray, action: int, reward: float, 
                next_state: np.ndarray, done: bool):
        """Store experience in replay memory."""
        experience = (state, action, reward, next_state, done)
        
        if len(self.memory) >= self.memory_size:
            self.memory.pop(0)
        
        self.memory.append(experience)
    
    def act(self, state: np.ndarray) -> int:
        """Choose action using epsilon-greedy policy."""
        if np.random.random() <= self.epsilon:
            return np.random.randint(self.action_size)
        
        q_values = self._predict(state, self.q_network)
        return np.argmax(q_values)
    
    def _predict(self, state: np.ndarray, network: Dict[str, Any]) -> np.ndarray:
        """Predict Q-values using network (simplified)."""
        # Forward pass through network
        x = state.reshape(1, -1)
        
        # Layer 1
        x = np.dot(x, network['weights']['layer1']) + network['biases']['layer1']
        x = np.maximum(0, x)  # ReLU activation
        
        # Layer 2
        x = np.dot(x, network['weights']['layer2']) + network['biases']['layer2']
        x = np.maximum(0, x)  # ReLU activation
        
        # Output layer
        q_values = np.dot(x, network['weights']['output']) + network['biases']['output']
        
        return q_values.flatten()
    
    def replay(self) -> float:
        """Train the model on a batch of experiences."""
        if len(self.memory) < self.batch_size:
            return 0.0
        
        # Sample batch from memory
        batch_indices = np.random.choice(len(self.memory), self.batch_size, replace=False)
        batch = [self.memory[i] for i in batch_indices]
        
        # Prepare training data
        states = np.array([e[0] for e in batch])
        actions = np.array([e[1] for e in batch])
        rewards = np.array([e[2] for e in batch])
        next_states = np.array([e[3] for e in batch])
        dones = np.array([e[4] for e in batch])
        
        # Calculate target Q-values
        current_q_values = np.array([self._predict(s, self.q_network) for s in states])
        next_q_values = np.array([self._predict(s, self.target_network) for s in next_states])
        
        target_q_values = current_q_values.copy()
        
        for i in range(self.batch_size):
            if dones[i]:
                target_q_values[i][actions[i]] = rewards[i]
            else:
                target_q_values[i][actions[i]] = rewards[i] + self.gamma * np.max(next_q_values[i])
        
        # Simplified training (in practice, would use gradient descent)
        loss = np.mean((current_q_values - target_q_values) ** 2)
        
        # Update epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        return loss
    
    def get_action_from_index(self, action_index: int) -> Action:
        """Convert action index to Action object."""
        action_types = list(ActionType)
        action_type = action_types[action_index % len(action_types)]
        
        # Generate magnitude based on action index
        magnitude = (action_index // len(action_types)) / 10.0 - 0.5  # -0.5 to 0.5
        magnitude = np.clip(magnitude, -1.0, 1.0)
        
        return Action(
            action_type=action_type,
            parameters={},
            magnitude=magnitude
        )


class ReinforcementLearningTrainer:
    """Trainer for RL agents in water management."""
    
    def __init__(self):
        self.environment = WaterTreatmentEnvironment()
        self.state_size = len(self.environment.reset())
        self.action_size = len(ActionType) * 10  # 10 magnitude levels per action type
        self.agent = DQNAgent(self.state_size, self.action_size)
        
        self.training_history = []
        self.best_reward = float('-inf')
        self.best_policy = None
    
    @log_async_function_call
    async def train(self, episodes: int = 1000, target_update_freq: int = 100) -> Dict[str, Any]:
        """Train the RL agent."""
        try:
            logger.info(f"Starting RL training for {episodes} episodes")
            
            episode_rewards = []
            episode_lengths = []
            
            for episode in range(episodes):
                state = self.environment.reset()
                total_reward = 0.0
                steps = 0
                
                while True:
                    # Choose action
                    action_index = self.agent.act(state)
                    action = self.agent.get_action_from_index(action_index)
                    
                    # Execute action
                    next_state, reward, done, info = self.environment.step(action)
                    
                    # Store experience
                    self.agent.remember(state, action_index, reward, next_state, done)
                    
                    # Update state
                    state = next_state
                    total_reward += reward
                    steps += 1
                    
                    if done:
                        break
                
                # Train agent
                loss = self.agent.replay()
                
                # Update target network
                if episode % target_update_freq == 0:
                    self.agent.update_target_network()
                
                # Record episode statistics
                episode_rewards.append(total_reward)
                episode_lengths.append(steps)
                
                # Update best policy
                if total_reward > self.best_reward:
                    self.best_reward = total_reward
                    self.best_policy = {
                        'episode': episode,
                        'reward': total_reward,
                        'epsilon': self.agent.epsilon,
                        'network_weights': {
                            k: v.copy() for k, v in self.agent.q_network['weights'].items()
                        }
                    }
                
                # Update training statistics
                self.agent.training_stats.update({
                    'episodes': episode + 1,
                    'total_reward': total_reward,
                    'average_reward': np.mean(episode_rewards[-100:]),  # Last 100 episodes
                    'epsilon': self.agent.epsilon,
                    'loss': loss
                })
                
                # Log progress
                if episode % 100 == 0:
                    avg_reward = np.mean(episode_rewards[-100:])
                    logger.info(f"Episode {episode}, Avg Reward: {avg_reward:.2f}, Epsilon: {self.agent.epsilon:.3f}")
            
            # Record training history
            training_record = {
                'episodes': episodes,
                'final_average_reward': np.mean(episode_rewards[-100:]),
                'best_reward': self.best_reward,
                'final_epsilon': self.agent.epsilon,
                'episode_rewards': episode_rewards,
                'episode_lengths': episode_lengths,
                'timestamp': datetime.now().isoformat()
            }
            
            self.training_history.append(training_record)
            
            return {
                'status': 'success',
                'training_results': training_record,
                'agent_stats': self.agent.training_stats
            }
            
        except Exception as e:
            logger.error(f"RL training failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def evaluate_policy(self, episodes: int = 10) -> Dict[str, Any]:
        """Evaluate trained policy."""
        try:
            evaluation_rewards = []
            evaluation_info = []
            
            # Temporarily disable exploration
            original_epsilon = self.agent.epsilon
            self.agent.epsilon = 0.0
            
            for episode in range(episodes):
                state = self.environment.reset()
                total_reward = 0.0
                episode_info = []
                
                while True:
                    action_index = self.agent.act(state)
                    action = self.agent.get_action_from_index(action_index)
                    
                    next_state, reward, done, info = self.environment.step(action)
                    
                    episode_info.append({
                        'step': info['step'],
                        'action': action.action_type.value,
                        'reward': reward,
                        'quality_compliance': info['quality_compliance'],
                        'energy_efficiency': info['energy_efficiency']
                    })
                    
                    state = next_state
                    total_reward += reward
                    
                    if done:
                        break
                
                evaluation_rewards.append(total_reward)
                evaluation_info.append(episode_info)
            
            # Restore original epsilon
            self.agent.epsilon = original_epsilon
            
            evaluation_results = {
                'average_reward': np.mean(evaluation_rewards),
                'std_reward': np.std(evaluation_rewards),
                'min_reward': np.min(evaluation_rewards),
                'max_reward': np.max(evaluation_rewards),
                'episodes_evaluated': episodes,
                'detailed_episodes': evaluation_info[:3]  # First 3 episodes for detail
            }
            
            return {
                'status': 'success',
                'evaluation_results': evaluation_results
            }
            
        except Exception as e:
            logger.error(f"Policy evaluation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """Get comprehensive training statistics."""
        return {
            'agent_stats': self.agent.training_stats,
            'training_history': self.training_history,
            'best_policy': self.best_policy,
            'environment_info': {
                'state_size': self.state_size,
                'action_size': self.action_size,
                'episode_length': self.environment.episode_length
            }
        }


# Convenience functions
async def train_rl_agent(episodes: int = 1000) -> Dict[str, Any]:
    """Train reinforcement learning agent for water management."""
    trainer = ReinforcementLearningTrainer()
    return await trainer.train(episodes)


async def evaluate_rl_policy(episodes: int = 10) -> Dict[str, Any]:
    """Evaluate trained RL policy."""
    trainer = ReinforcementLearningTrainer()
    return await trainer.evaluate_policy(episodes)
