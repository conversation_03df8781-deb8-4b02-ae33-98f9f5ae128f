body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0a1929 0%, #132f4c 100%);
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Loading animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 2s infinite;
}

/* Chart container styling */
.recharts-wrapper {
  font-family: 'Roboto', sans-serif !important;
}

/* Custom card hover effects */
.MuiCard-root {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.MuiCard-root:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-operational {
  background-color: #4caf50;
}

.status-warning {
  background-color: #ff9800;
}

.status-error {
  background-color: #f44336;
}

/* Responsive text */
@media (max-width: 600px) {
  .MuiTypography-h4 {
    font-size: 1.5rem;
  }
  
  .MuiTypography-h5 {
    font-size: 1.25rem;
  }
}

/* Custom button styles */
.gradient-button {
  background: linear-gradient(45deg, #2196f3 30%, #21cbf3 90%);
  border: 0;
  border-radius: 3px;
  box-shadow: 0 3px 5px 2px rgba(33, 203, 243, 0.3);
  color: white;
  height: 48px;
  padding: 0 30px;
}

/* Map container */
.map-container {
  height: 400px;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

/* Alert animations */
@keyframes slideIn {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.alert-enter {
  animation: slideIn 0.3s ease-out;
}

/* Data grid customization */
.MuiDataGrid-root {
  border: none;
  background: transparent;
}

.MuiDataGrid-cell {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.MuiDataGrid-columnHeaders {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Chart tooltips */
.recharts-tooltip-wrapper {
  background: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px !important;
}

/* Navigation active state */
.nav-active {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .MuiAppBar-root {
    display: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}
