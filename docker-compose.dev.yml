# Development-specific Docker Compose configuration

version: '3.8'

services:
  # Override backend for development
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
      - ./data:/app/data
      - ./notebooks:/app/notebooks
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - API_RELOAD=true
    ports:
      - "8000:8000"
      - "8888:8888"  # Jupyter
    command: uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

  # Override streamlit for development
  streamlit:
    build:
      context: .
      dockerfile: Dockerfile.streamlit
    volumes:
      - ./src:/app/src
      - ./data:/app/data
    environment:
      - DEBUG=true
      - STREAMLIT_SERVER_RUNONCAVE=true
    ports:
      - "8501:8501"
    command: streamlit run src/app.py --server.port=8501 --server.address=0.0.0.0 --server.runOnSave=true

  # Development tools
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    ports:
      - "8888:8888"
    volumes:
      - ./notebooks:/app/notebooks
      - ./src:/app/src
      - ./data:/app/data
    environment:
      - JUPYTER_ENABLE_LAB=yes
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''

  # Database with development settings
  postgres:
    environment:
      - POSTGRES_DB=watermanagement_dev
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d

  # Redis with development settings
  redis:
    command: redis-server --appendonly yes --requirepass devpassword

volumes:
  postgres_dev_data:
