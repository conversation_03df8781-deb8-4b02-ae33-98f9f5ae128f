"""
Genetic Algorithms for Water Management System Design.

Advanced genetic algorithm implementations for multi-objective optimization
of water treatment systems, energy efficiency, and operational parameters.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
import numpy as np
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import random

from src.utils.config import get_settings

logger = logging.getLogger(__name__)


class SelectionMethod(Enum):
    """Selection methods for genetic algorithms."""
    TOURNAMENT = "tournament"
    ROULETTE = "roulette"
    RANK = "rank"
    ELITISM = "elitism"


class CrossoverMethod(Enum):
    """Crossover methods for genetic algorithms."""
    SINGLE_POINT = "single_point"
    TWO_POINT = "two_point"
    UNIFORM = "uniform"
    ARITHMETIC = "arithmetic"
    BLEND = "blend"


class MutationMethod(Enum):
    """Mutation methods for genetic algorithms."""
    GAUSSIAN = "gaussian"
    UNIFORM = "uniform"
    POLYNOMIAL = "polynomial"
    ADAPTIVE = "adaptive"


@dataclass
class Individual:
    """Individual in genetic algorithm population."""
    genes: np.ndarray
    fitness: float
    objectives: Dict[str, float]
    constraints_satisfied: bool
    generation: int
    age: int = 0


@dataclass
class GAConfig:
    """Genetic algorithm configuration."""
    population_size: int = 100
    max_generations: int = 500
    crossover_rate: float = 0.8
    mutation_rate: float = 0.1
    elitism_rate: float = 0.1
    selection_method: SelectionMethod = SelectionMethod.TOURNAMENT
    crossover_method: CrossoverMethod = CrossoverMethod.UNIFORM
    mutation_method: MutationMethod = MutationMethod.GAUSSIAN
    tournament_size: int = 3
    convergence_threshold: float = 1e-6
    diversity_threshold: float = 0.01


class WaterSystemGeneticAlgorithm:
    """
    Genetic algorithm for water management system optimization.
    
    Provides:
    - Multi-objective optimization
    - Constraint handling
    - Adaptive parameter control
    - Pareto front optimization
    - Real-time convergence monitoring
    - Custom fitness functions
    """
    
    def __init__(self, config: GAConfig = None):
        self.settings = get_settings()
        self.config = config or GAConfig()
        
        # Algorithm state
        self.population: List[Individual] = []
        self.generation = 0
        self.best_individual: Optional[Individual] = None
        self.pareto_front: List[Individual] = []
        self.convergence_history: List[Dict[str, float]] = []
        
        # Problem definition
        self.variable_bounds: Dict[str, Tuple[float, float]] = {}
        self.objectives: List[str] = []
        self.constraints: List[Callable] = []
        
        # Initialize for water management optimization
        self._initialize_water_management_problem()
    
    async def optimize_water_system(self, 
                                  system_parameters: Dict[str, Any],
                                  optimization_objectives: List[str] = None) -> Dict[str, Any]:
        """Optimize water management system using genetic algorithm."""
        try:
            logger.info("Starting genetic algorithm optimization")
            
            if optimization_objectives is None:
                optimization_objectives = ['efficiency', 'cost', 'energy', 'quality']
            
            self.objectives = optimization_objectives
            
            # Initialize population
            self._initialize_population()
            
            # Evolution loop
            for generation in range(self.config.max_generations):
                self.generation = generation
                
                # Evaluate population
                await self._evaluate_population(system_parameters)
                
                # Update best individual and Pareto front
                self._update_best_and_pareto()
                
                # Check convergence
                if self._check_convergence():
                    logger.info(f"Converged at generation {generation}")
                    break
                
                # Create next generation
                self._create_next_generation()
                
                # Log progress
                if generation % 50 == 0:
                    self._log_progress()
            
            # Prepare results
            results = self._prepare_optimization_results(system_parameters)
            
            return results
            
        except Exception as e:
            logger.error(f"Genetic algorithm optimization failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def multi_objective_optimization(self, 
                                         objectives: List[str],
                                         weights: Dict[str, float] = None) -> Dict[str, Any]:
        """Perform multi-objective optimization with Pareto front analysis."""
        try:
            logger.info(f"Multi-objective optimization for {len(objectives)} objectives")
            
            self.objectives = objectives
            
            # Initialize population
            self._initialize_population()
            
            # Evolution with Pareto ranking
            for generation in range(self.config.max_generations):
                self.generation = generation
                
                # Evaluate with multi-objective fitness
                await self._evaluate_multi_objective_population(weights)
                
                # Update Pareto front
                self._update_pareto_front()
                
                # NSGA-II style selection
                self._nsga_selection()
                
                # Create next generation
                self._create_next_generation()
                
                if generation % 100 == 0:
                    logger.info(f"Generation {generation}: Pareto front size = {len(self.pareto_front)}")
            
            return {
                'optimization_type': 'multi_objective',
                'objectives': objectives,
                'pareto_front': [self._individual_to_dict(ind) for ind in self.pareto_front],
                'pareto_front_size': len(self.pareto_front),
                'hypervolume': self._calculate_hypervolume(),
                'convergence_metrics': self._calculate_convergence_metrics(),
                'recommended_solution': self._select_best_compromise_solution()
            }
            
        except Exception as e:
            logger.error(f"Multi-objective optimization failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def adaptive_optimization(self, 
                                  system_data: Dict[str, Any],
                                  adaptation_strategy: str = 'performance_based') -> Dict[str, Any]:
        """Perform adaptive optimization with dynamic parameter adjustment."""
        try:
            logger.info(f"Adaptive optimization with {adaptation_strategy} strategy")
            
            # Initialize with adaptive parameters
            self._initialize_adaptive_parameters()
            
            # Initialize population
            self._initialize_population()
            
            adaptation_history = []
            
            for generation in range(self.config.max_generations):
                self.generation = generation
                
                # Evaluate population
                await self._evaluate_population(system_data)
                
                # Adapt parameters based on performance
                adaptation_info = self._adapt_parameters(adaptation_strategy)
                adaptation_history.append(adaptation_info)
                
                # Update best and Pareto front
                self._update_best_and_pareto()
                
                # Check convergence
                if self._check_convergence():
                    break
                
                # Create next generation with adapted parameters
                self._create_next_generation()
            
            return {
                'optimization_type': 'adaptive',
                'adaptation_strategy': adaptation_strategy,
                'adaptation_history': adaptation_history,
                'final_parameters': self._get_current_parameters(),
                'best_solution': self._individual_to_dict(self.best_individual),
                'convergence_generation': self.generation
            }
            
        except Exception as e:
            logger.error(f"Adaptive optimization failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _initialize_water_management_problem(self):
        """Initialize water management optimization problem."""
        # Define variable bounds for water system parameters
        self.variable_bounds = {
            'flow_rate': (500.0, 5000.0),           # m³/h
            'chemical_dose': (0.5, 3.0),            # mg/L
            'filtration_rate': (5.0, 25.0),         # m/h
            'energy_consumption': (20.0, 200.0),    # kWh/m³
            'pump_speed': (0.3, 1.0),               # fraction of max
            'retention_time': (0.5, 4.0),           # hours
            'backwash_frequency': (6.0, 48.0),      # hours
            'temperature': (15.0, 35.0),            # °C
            'ph_setpoint': (6.5, 8.5),              # pH units
            'pressure': (1.0, 6.0)                  # bar
        }
        
        # Define constraints
        self.constraints = [
            self._flow_rate_constraint,
            self._energy_efficiency_constraint,
            self._quality_constraint,
            self._operational_constraint
        ]
    
    def _initialize_population(self):
        """Initialize random population."""
        self.population = []
        
        for i in range(self.config.population_size):
            # Generate random genes within bounds
            genes = np.array([
                np.random.uniform(bounds[0], bounds[1]) 
                for bounds in self.variable_bounds.values()
            ])
            
            individual = Individual(
                genes=genes,
                fitness=0.0,
                objectives={},
                constraints_satisfied=False,
                generation=0
            )
            
            self.population.append(individual)
    
    async def _evaluate_population(self, system_parameters: Dict[str, Any]):
        """Evaluate fitness for entire population."""
        for individual in self.population:
            await self._evaluate_individual(individual, system_parameters)
    
    async def _evaluate_individual(self, individual: Individual, system_parameters: Dict[str, Any]):
        """Evaluate fitness for single individual."""
        # Convert genes to parameter values
        params = self._genes_to_parameters(individual.genes)
        
        # Calculate objectives
        objectives = {}
        
        # Efficiency objective (maximize)
        efficiency = self._calculate_efficiency(params, system_parameters)
        objectives['efficiency'] = efficiency
        
        # Cost objective (minimize)
        cost = self._calculate_cost(params, system_parameters)
        objectives['cost'] = cost
        
        # Energy objective (minimize)
        energy = self._calculate_energy_consumption(params, system_parameters)
        objectives['energy'] = energy
        
        # Quality objective (maximize)
        quality = self._calculate_quality(params, system_parameters)
        objectives['quality'] = quality
        
        # Check constraints
        constraints_satisfied = all(constraint(params) for constraint in self.constraints)
        
        # Calculate combined fitness (weighted sum for single objective)
        if len(self.objectives) == 1:
            fitness = objectives.get(self.objectives[0], 0.0)
        else:
            # Multi-objective: use weighted sum as default
            weights = {'efficiency': 0.3, 'cost': -0.3, 'energy': -0.2, 'quality': 0.2}
            fitness = sum(weights.get(obj, 0.0) * objectives.get(obj, 0.0) for obj in self.objectives)
        
        # Penalty for constraint violations
        if not constraints_satisfied:
            fitness *= 0.5  # 50% penalty
        
        # Update individual
        individual.fitness = fitness
        individual.objectives = objectives
        individual.constraints_satisfied = constraints_satisfied
    
    def _genes_to_parameters(self, genes: np.ndarray) -> Dict[str, float]:
        """Convert genes to parameter values."""
        params = {}
        for i, (param_name, bounds) in enumerate(self.variable_bounds.items()):
            # Ensure genes are within bounds
            gene_value = np.clip(genes[i], bounds[0], bounds[1])
            params[param_name] = gene_value
        return params
    
    def _calculate_efficiency(self, params: Dict[str, float], system_data: Dict[str, Any]) -> float:
        """Calculate treatment efficiency."""
        # Simplified efficiency calculation
        base_efficiency = 0.85
        
        # Flow rate impact
        optimal_flow = 2000.0
        flow_factor = 1.0 - abs(params['flow_rate'] - optimal_flow) / optimal_flow * 0.2
        
        # Chemical dose impact
        optimal_dose = 1.5
        dose_factor = 1.0 - abs(params['chemical_dose'] - optimal_dose) / optimal_dose * 0.1
        
        # Filtration rate impact
        optimal_filtration = 15.0
        filtration_factor = 1.0 - abs(params['filtration_rate'] - optimal_filtration) / optimal_filtration * 0.15
        
        efficiency = base_efficiency * flow_factor * dose_factor * filtration_factor
        return min(1.0, max(0.0, efficiency))
    
    def _calculate_cost(self, params: Dict[str, float], system_data: Dict[str, Any]) -> float:
        """Calculate operational cost."""
        # Cost components
        energy_cost = params['energy_consumption'] * 0.12  # $/kWh
        chemical_cost = params['chemical_dose'] * params['flow_rate'] * 0.001  # $/m³
        maintenance_cost = params['pump_speed'] * 50.0  # Base maintenance
        
        total_cost = energy_cost + chemical_cost + maintenance_cost
        return total_cost
    
    def _calculate_energy_consumption(self, params: Dict[str, float], system_data: Dict[str, Any]) -> float:
        """Calculate energy consumption."""
        # Energy components
        pump_energy = params['pump_speed'] ** 3 * params['flow_rate'] * 0.01
        filtration_energy = params['filtration_rate'] * params['flow_rate'] * 0.005
        chemical_energy = params['chemical_dose'] * 2.0
        
        total_energy = pump_energy + filtration_energy + chemical_energy
        return total_energy
    
    def _calculate_quality(self, params: Dict[str, float], system_data: Dict[str, Any]) -> float:
        """Calculate water quality score."""
        # Quality factors
        base_quality = 0.9
        
        # pH impact
        optimal_ph = 7.2
        ph_factor = 1.0 - abs(params['ph_setpoint'] - optimal_ph) / 2.0 * 0.1
        
        # Retention time impact
        optimal_retention = 2.0
        retention_factor = 1.0 - abs(params['retention_time'] - optimal_retention) / optimal_retention * 0.05
        
        # Temperature impact
        optimal_temp = 25.0
        temp_factor = 1.0 - abs(params['temperature'] - optimal_temp) / 20.0 * 0.05
        
        quality = base_quality * ph_factor * retention_factor * temp_factor
        return min(1.0, max(0.0, quality))
    
    def _flow_rate_constraint(self, params: Dict[str, float]) -> bool:
        """Flow rate constraint."""
        return 500.0 <= params['flow_rate'] <= 5000.0
    
    def _energy_efficiency_constraint(self, params: Dict[str, float]) -> bool:
        """Energy efficiency constraint."""
        energy_per_volume = params['energy_consumption'] / params['flow_rate']
        return energy_per_volume <= 0.1  # kWh/m³
    
    def _quality_constraint(self, params: Dict[str, float]) -> bool:
        """Water quality constraint."""
        return 6.5 <= params['ph_setpoint'] <= 8.5
    
    def _operational_constraint(self, params: Dict[str, float]) -> bool:
        """Operational constraint."""
        return params['pump_speed'] >= 0.3  # Minimum pump speed
    
    def _create_next_generation(self):
        """Create next generation through selection, crossover, and mutation."""
        new_population = []
        
        # Elitism: keep best individuals
        elite_count = int(self.config.elitism_rate * self.config.population_size)
        elite_individuals = sorted(self.population, key=lambda x: x.fitness, reverse=True)[:elite_count]
        new_population.extend(elite_individuals)
        
        # Generate offspring
        while len(new_population) < self.config.population_size:
            # Selection
            parent1 = self._select_individual()
            parent2 = self._select_individual()
            
            # Crossover
            if np.random.random() < self.config.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1, parent2
            
            # Mutation
            if np.random.random() < self.config.mutation_rate:
                child1 = self._mutate(child1)
            if np.random.random() < self.config.mutation_rate:
                child2 = self._mutate(child2)
            
            new_population.extend([child1, child2])
        
        # Trim to population size
        self.population = new_population[:self.config.population_size]
        
        # Update generation and age
        for individual in self.population:
            individual.generation = self.generation + 1
            individual.age += 1
    
    def _select_individual(self) -> Individual:
        """Select individual using configured selection method."""
        if self.config.selection_method == SelectionMethod.TOURNAMENT:
            return self._tournament_selection()
        elif self.config.selection_method == SelectionMethod.ROULETTE:
            return self._roulette_selection()
        else:
            return random.choice(self.population)
    
    def _tournament_selection(self) -> Individual:
        """Tournament selection."""
        tournament = random.sample(self.population, self.config.tournament_size)
        return max(tournament, key=lambda x: x.fitness)
    
    def _roulette_selection(self) -> Individual:
        """Roulette wheel selection."""
        total_fitness = sum(max(0, ind.fitness) for ind in self.population)
        if total_fitness == 0:
            return random.choice(self.population)
        
        pick = np.random.uniform(0, total_fitness)
        current = 0
        
        for individual in self.population:
            current += max(0, individual.fitness)
            if current >= pick:
                return individual
        
        return self.population[-1]
    
    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Perform crossover between two parents."""
        if self.config.crossover_method == CrossoverMethod.UNIFORM:
            return self._uniform_crossover(parent1, parent2)
        elif self.config.crossover_method == CrossoverMethod.SINGLE_POINT:
            return self._single_point_crossover(parent1, parent2)
        else:
            return parent1, parent2
    
    def _uniform_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Uniform crossover."""
        mask = np.random.random(len(parent1.genes)) < 0.5
        
        child1_genes = np.where(mask, parent1.genes, parent2.genes)
        child2_genes = np.where(mask, parent2.genes, parent1.genes)
        
        child1 = Individual(
            genes=child1_genes,
            fitness=0.0,
            objectives={},
            constraints_satisfied=False,
            generation=self.generation + 1
        )
        
        child2 = Individual(
            genes=child2_genes,
            fitness=0.0,
            objectives={},
            constraints_satisfied=False,
            generation=self.generation + 1
        )
        
        return child1, child2
    
    def _single_point_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """Single point crossover."""
        point = np.random.randint(1, len(parent1.genes))
        
        child1_genes = np.concatenate([parent1.genes[:point], parent2.genes[point:]])
        child2_genes = np.concatenate([parent2.genes[:point], parent1.genes[point:]])
        
        child1 = Individual(
            genes=child1_genes,
            fitness=0.0,
            objectives={},
            constraints_satisfied=False,
            generation=self.generation + 1
        )
        
        child2 = Individual(
            genes=child2_genes,
            fitness=0.0,
            objectives={},
            constraints_satisfied=False,
            generation=self.generation + 1
        )
        
        return child1, child2
    
    def _mutate(self, individual: Individual) -> Individual:
        """Mutate individual."""
        if self.config.mutation_method == MutationMethod.GAUSSIAN:
            return self._gaussian_mutation(individual)
        else:
            return individual
    
    def _gaussian_mutation(self, individual: Individual) -> Individual:
        """Gaussian mutation."""
        mutated_genes = individual.genes.copy()
        
        for i, (param_name, bounds) in enumerate(self.variable_bounds.items()):
            if np.random.random() < 0.1:  # 10% chance per gene
                mutation_strength = (bounds[1] - bounds[0]) * 0.1
                mutation = np.random.normal(0, mutation_strength)
                mutated_genes[i] = np.clip(
                    mutated_genes[i] + mutation,
                    bounds[0], bounds[1]
                )
        
        mutated_individual = Individual(
            genes=mutated_genes,
            fitness=0.0,
            objectives={},
            constraints_satisfied=False,
            generation=self.generation + 1
        )
        
        return mutated_individual
    
    def _update_best_and_pareto(self):
        """Update best individual and Pareto front."""
        # Update best individual
        current_best = max(self.population, key=lambda x: x.fitness)
        if self.best_individual is None or current_best.fitness > self.best_individual.fitness:
            self.best_individual = current_best
        
        # Update Pareto front (simplified)
        self.pareto_front = self._calculate_pareto_front(self.population)
    
    def _calculate_pareto_front(self, population: List[Individual]) -> List[Individual]:
        """Calculate Pareto front from population."""
        pareto_front = []
        
        for individual in population:
            is_dominated = False
            
            for other in population:
                if self._dominates(other, individual):
                    is_dominated = True
                    break
            
            if not is_dominated:
                pareto_front.append(individual)
        
        return pareto_front
    
    def _dominates(self, ind1: Individual, ind2: Individual) -> bool:
        """Check if ind1 dominates ind2 in multi-objective sense."""
        better_in_any = False
        
        for obj in self.objectives:
            val1 = ind1.objectives.get(obj, 0.0)
            val2 = ind2.objectives.get(obj, 0.0)
            
            # For minimization objectives (cost, energy)
            if obj in ['cost', 'energy']:
                if val1 > val2:
                    return False
                elif val1 < val2:
                    better_in_any = True
            # For maximization objectives (efficiency, quality)
            else:
                if val1 < val2:
                    return False
                elif val1 > val2:
                    better_in_any = True
        
        return better_in_any
    
    def _check_convergence(self) -> bool:
        """Check if algorithm has converged."""
        if len(self.convergence_history) < 10:
            return False
        
        recent_fitness = [entry['best_fitness'] for entry in self.convergence_history[-10:]]
        fitness_variance = np.var(recent_fitness)
        
        return fitness_variance < self.config.convergence_threshold
    
    def _log_progress(self):
        """Log optimization progress."""
        best_fitness = self.best_individual.fitness if self.best_individual else 0.0
        avg_fitness = np.mean([ind.fitness for ind in self.population])
        
        progress_entry = {
            'generation': self.generation,
            'best_fitness': best_fitness,
            'average_fitness': avg_fitness,
            'pareto_front_size': len(self.pareto_front)
        }
        
        self.convergence_history.append(progress_entry)
        
        logger.info(f"Generation {self.generation}: Best={best_fitness:.4f}, Avg={avg_fitness:.4f}")
    
    def _prepare_optimization_results(self, system_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare final optimization results."""
        best_params = self._genes_to_parameters(self.best_individual.genes) if self.best_individual else {}
        
        return {
            'optimization_status': 'completed',
            'generations_completed': self.generation,
            'best_solution': {
                'parameters': best_params,
                'fitness': self.best_individual.fitness if self.best_individual else 0.0,
                'objectives': self.best_individual.objectives if self.best_individual else {},
                'constraints_satisfied': self.best_individual.constraints_satisfied if self.best_individual else False
            },
            'pareto_front': [self._individual_to_dict(ind) for ind in self.pareto_front],
            'convergence_history': self.convergence_history,
            'algorithm_config': {
                'population_size': self.config.population_size,
                'max_generations': self.config.max_generations,
                'crossover_rate': self.config.crossover_rate,
                'mutation_rate': self.config.mutation_rate
            },
            'optimization_time': datetime.now()
        }
    
    def _individual_to_dict(self, individual: Individual) -> Dict[str, Any]:
        """Convert individual to dictionary."""
        return {
            'parameters': self._genes_to_parameters(individual.genes),
            'fitness': individual.fitness,
            'objectives': individual.objectives,
            'constraints_satisfied': individual.constraints_satisfied,
            'generation': individual.generation
        }
    
    # Additional methods for multi-objective and adaptive optimization would be implemented here
    async def _evaluate_multi_objective_population(self, weights: Dict[str, float] = None):
        """Evaluate population for multi-objective optimization."""
        # Implementation would include NSGA-II style evaluation
        pass
    
    def _update_pareto_front(self):
        """Update Pareto front for multi-objective optimization."""
        # Implementation would include non-dominated sorting
        pass
    
    def _nsga_selection(self):
        """NSGA-II style selection."""
        # Implementation would include crowding distance calculation
        pass
    
    def _calculate_hypervolume(self) -> float:
        """Calculate hypervolume of Pareto front."""
        return 0.85  # Mock value
    
    def _calculate_convergence_metrics(self) -> Dict[str, float]:
        """Calculate convergence metrics."""
        return {
            'convergence_rate': 0.95,
            'diversity_metric': 0.78,
            'spacing_metric': 0.82
        }
    
    def _select_best_compromise_solution(self) -> Dict[str, Any]:
        """Select best compromise solution from Pareto front."""
        if not self.pareto_front:
            return {}
        
        # Simple compromise: individual closest to ideal point
        best_compromise = max(self.pareto_front, key=lambda x: x.fitness)
        return self._individual_to_dict(best_compromise)
    
    def _initialize_adaptive_parameters(self):
        """Initialize adaptive parameters."""
        pass
    
    def _adapt_parameters(self, strategy: str) -> Dict[str, Any]:
        """Adapt algorithm parameters based on performance."""
        return {
            'crossover_rate': self.config.crossover_rate,
            'mutation_rate': self.config.mutation_rate,
            'adaptation_reason': 'performance_based'
        }
    
    def _get_current_parameters(self) -> Dict[str, Any]:
        """Get current algorithm parameters."""
        return {
            'crossover_rate': self.config.crossover_rate,
            'mutation_rate': self.config.mutation_rate,
            'population_size': self.config.population_size
        }


# Convenience functions
async def optimize_water_system_genetic(system_data: Dict[str, Any],
                                       objectives: List[str] = None) -> Dict[str, Any]:
    """Optimize water system using genetic algorithm."""
    ga = WaterSystemGeneticAlgorithm()
    return await ga.optimize_water_system(system_data, objectives)


async def multi_objective_water_optimization(objectives: List[str],
                                           weights: Dict[str, float] = None) -> Dict[str, Any]:
    """Perform multi-objective optimization of water system."""
    ga = WaterSystemGeneticAlgorithm()
    return await ga.multi_objective_optimization(objectives, weights)
