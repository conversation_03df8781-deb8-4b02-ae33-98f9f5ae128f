"""Risk Assessment Agent for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class RiskCategory(Enum):
    """Risk categories."""
    OPERATIONAL = "operational"
    ENVIRONMENTAL = "environmental"
    REGULATORY = "regulatory"
    FINANCIAL = "financial"
    TECHNICAL = "technical"
    SAFETY = "safety"
    CYBERSECURITY = "cybersecurity"
    SUPPLY_CHAIN = "supply_chain"


class RiskSeverity(Enum):
    """Risk severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NEGLIGIBLE = "negligible"


class RiskLikelihood(Enum):
    """Risk likelihood levels."""
    VERY_LIKELY = "very_likely"
    LIKELY = "likely"
    POSSIBLE = "possible"
    UNLIKELY = "unlikely"
    VERY_UNLIKELY = "very_unlikely"


@dataclass
class RiskFactor:
    """Individual risk factor."""
    factor_id: str
    category: RiskCategory
    description: str
    current_level: float  # 0-1
    severity: RiskSeverity
    likelihood: RiskLikelihood
    impact_score: float  # 0-100
    mitigation_measures: List[str]
    last_assessed: datetime = field(default_factory=datetime.now)


@dataclass
class RiskAssessment:
    """Comprehensive risk assessment."""
    assessment_id: str
    overall_risk_score: float  # 0-100
    risk_factors: List[RiskFactor]
    risk_matrix: Dict[str, Dict[str, int]]
    critical_risks: List[str]
    recommendations: List[str]
    assessment_date: datetime = field(default_factory=datetime.now)


class RiskAssessmentAgent:
    """AI agent for comprehensive risk assessment and management."""
    
    def __init__(self):
        self.risk_assessments: List[RiskAssessment] = []
        self.risk_models: Dict[str, Dict[str, Any]] = {}
        self.mitigation_strategies: Dict[str, List[str]] = {}
        
        # Initialize risk models and strategies
        self._initialize_risk_models()
        self._initialize_mitigation_strategies()
    
    def _initialize_risk_models(self):
        """Initialize risk assessment models."""
        self.risk_models = {
            'operational_risks': {
                'equipment_failure': {
                    'base_probability': 0.15,
                    'impact_factors': ['age', 'maintenance_quality', 'operating_conditions'],
                    'severity_multiplier': 0.8
                },
                'process_disruption': {
                    'base_probability': 0.10,
                    'impact_factors': ['system_complexity', 'operator_training', 'automation_level'],
                    'severity_multiplier': 0.6
                },
                'capacity_shortage': {
                    'base_probability': 0.08,
                    'impact_factors': ['demand_growth', 'infrastructure_age', 'maintenance_backlog'],
                    'severity_multiplier': 0.9
                }
            },
            'environmental_risks': {
                'climate_change': {
                    'base_probability': 0.25,
                    'impact_factors': ['temperature_trends', 'precipitation_changes', 'extreme_events'],
                    'severity_multiplier': 1.0
                },
                'water_scarcity': {
                    'base_probability': 0.20,
                    'impact_factors': ['drought_frequency', 'demand_growth', 'source_reliability'],
                    'severity_multiplier': 0.95
                },
                'contamination': {
                    'base_probability': 0.12,
                    'impact_factors': ['source_protection', 'treatment_effectiveness', 'distribution_integrity'],
                    'severity_multiplier': 1.0
                }
            },
            'regulatory_risks': {
                'compliance_violation': {
                    'base_probability': 0.05,
                    'impact_factors': ['regulatory_complexity', 'monitoring_frequency', 'staff_training'],
                    'severity_multiplier': 0.7
                },
                'regulatory_changes': {
                    'base_probability': 0.30,
                    'impact_factors': ['political_climate', 'environmental_pressure', 'technology_advancement'],
                    'severity_multiplier': 0.5
                }
            },
            'financial_risks': {
                'cost_overrun': {
                    'base_probability': 0.18,
                    'impact_factors': ['project_complexity', 'market_volatility', 'planning_quality'],
                    'severity_multiplier': 0.6
                },
                'revenue_loss': {
                    'base_probability': 0.12,
                    'impact_factors': ['customer_satisfaction', 'competition', 'service_reliability'],
                    'severity_multiplier': 0.8
                }
            },
            'technical_risks': {
                'technology_obsolescence': {
                    'base_probability': 0.15,
                    'impact_factors': ['technology_age', 'innovation_rate', 'upgrade_planning'],
                    'severity_multiplier': 0.4
                },
                'system_integration': {
                    'base_probability': 0.20,
                    'impact_factors': ['system_complexity', 'vendor_diversity', 'standardization'],
                    'severity_multiplier': 0.5
                }
            },
            'safety_risks': {
                'worker_injury': {
                    'base_probability': 0.08,
                    'impact_factors': ['safety_training', 'equipment_condition', 'procedure_compliance'],
                    'severity_multiplier': 0.9
                },
                'public_health': {
                    'base_probability': 0.03,
                    'impact_factors': ['water_quality', 'distribution_integrity', 'emergency_response'],
                    'severity_multiplier': 1.0
                }
            },
            'cybersecurity_risks': {
                'cyber_attack': {
                    'base_probability': 0.25,
                    'impact_factors': ['security_measures', 'system_exposure', 'threat_landscape'],
                    'severity_multiplier': 0.8
                },
                'data_breach': {
                    'base_probability': 0.15,
                    'impact_factors': ['data_protection', 'access_controls', 'employee_training'],
                    'severity_multiplier': 0.6
                }
            }
        }
    
    def _initialize_mitigation_strategies(self):
        """Initialize risk mitigation strategies."""
        self.mitigation_strategies = {
            'equipment_failure': [
                'Implement predictive maintenance programs',
                'Maintain equipment redundancy',
                'Regular equipment inspections and testing',
                'Upgrade aging equipment proactively',
                'Establish emergency repair procedures'
            ],
            'process_disruption': [
                'Develop comprehensive operating procedures',
                'Implement process automation and controls',
                'Provide regular operator training',
                'Establish backup processes and procedures',
                'Monitor process performance continuously'
            ],
            'climate_change': [
                'Develop climate adaptation strategies',
                'Diversify water sources',
                'Implement water conservation measures',
                'Upgrade infrastructure for resilience',
                'Monitor climate indicators continuously'
            ],
            'water_scarcity': [
                'Develop alternative water sources',
                'Implement demand management programs',
                'Increase water recycling and reuse',
                'Improve system efficiency',
                'Establish emergency water supplies'
            ],
            'compliance_violation': [
                'Implement comprehensive compliance monitoring',
                'Provide regular regulatory training',
                'Establish compliance management systems',
                'Conduct regular compliance audits',
                'Maintain regulatory relationships'
            ],
            'cyber_attack': [
                'Implement robust cybersecurity measures',
                'Conduct regular security assessments',
                'Provide cybersecurity training',
                'Establish incident response procedures',
                'Maintain security monitoring systems'
            ],
            'contamination': [
                'Implement multiple barrier treatment',
                'Monitor water quality continuously',
                'Protect water sources',
                'Maintain distribution system integrity',
                'Establish contamination response procedures'
            ]
        }
    
    @log_async_function_call
    async def conduct_risk_assessment(self, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Conduct comprehensive risk assessment."""
        try:
            assessment_id = f"risk_assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Assess risks by category
            risk_factors = []
            
            # Operational risks
            operational_risks = await self._assess_operational_risks(system_data)
            risk_factors.extend(operational_risks)
            
            # Environmental risks
            environmental_risks = await self._assess_environmental_risks(system_data)
            risk_factors.extend(environmental_risks)
            
            # Regulatory risks
            regulatory_risks = await self._assess_regulatory_risks(system_data)
            risk_factors.extend(regulatory_risks)
            
            # Financial risks
            financial_risks = await self._assess_financial_risks(system_data)
            risk_factors.extend(financial_risks)
            
            # Technical risks
            technical_risks = await self._assess_technical_risks(system_data)
            risk_factors.extend(technical_risks)
            
            # Safety risks
            safety_risks = await self._assess_safety_risks(system_data)
            risk_factors.extend(safety_risks)
            
            # Cybersecurity risks
            cyber_risks = await self._assess_cybersecurity_risks(system_data)
            risk_factors.extend(cyber_risks)
            
            # Calculate overall risk score
            overall_risk_score = await self._calculate_overall_risk_score(risk_factors)
            
            # Create risk matrix
            risk_matrix = await self._create_risk_matrix(risk_factors)
            
            # Identify critical risks
            critical_risks = await self._identify_critical_risks(risk_factors)
            
            # Generate recommendations
            recommendations = await self._generate_risk_recommendations(risk_factors, critical_risks)
            
            # Create assessment
            assessment = RiskAssessment(
                assessment_id=assessment_id,
                overall_risk_score=overall_risk_score,
                risk_factors=risk_factors,
                risk_matrix=risk_matrix,
                critical_risks=critical_risks,
                recommendations=recommendations
            )
            
            self.risk_assessments.append(assessment)
            
            return {
                'status': 'success',
                'assessment_id': assessment_id,
                'risk_summary': {
                    'overall_risk_score': overall_risk_score,
                    'risk_level': self._get_risk_level(overall_risk_score),
                    'total_risks_identified': len(risk_factors),
                    'critical_risks_count': len(critical_risks)
                },
                'risk_factors': [
                    {
                        'factor_id': rf.factor_id,
                        'category': rf.category.value,
                        'description': rf.description,
                        'severity': rf.severity.value,
                        'likelihood': rf.likelihood.value,
                        'impact_score': rf.impact_score
                    }
                    for rf in risk_factors
                ],
                'risk_matrix': risk_matrix,
                'critical_risks': critical_risks,
                'recommendations': recommendations,
                'assessment_date': assessment.assessment_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _assess_operational_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess operational risks."""
        risks = []
        operational_models = self.risk_models['operational_risks']
        
        # Equipment failure risk
        equipment_age = data.get('average_equipment_age', 5)  # years
        maintenance_quality = data.get('maintenance_quality_score', 0.8)  # 0-1
        
        equipment_risk_level = min(1.0, (equipment_age / 15) * (2 - maintenance_quality))
        equipment_severity = RiskSeverity.HIGH if equipment_risk_level > 0.7 else RiskSeverity.MEDIUM
        equipment_likelihood = RiskLikelihood.LIKELY if equipment_risk_level > 0.6 else RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='equipment_failure',
            category=RiskCategory.OPERATIONAL,
            description='Risk of critical equipment failure leading to service disruption',
            current_level=equipment_risk_level,
            severity=equipment_severity,
            likelihood=equipment_likelihood,
            impact_score=equipment_risk_level * 80,
            mitigation_measures=self.mitigation_strategies.get('equipment_failure', [])
        ))
        
        # Process disruption risk
        system_complexity = data.get('system_complexity_score', 0.6)  # 0-1
        automation_level = data.get('automation_level', 0.7)  # 0-1
        
        process_risk_level = system_complexity * (1 - automation_level * 0.5)
        process_severity = RiskSeverity.MEDIUM if process_risk_level > 0.5 else RiskSeverity.LOW
        process_likelihood = RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='process_disruption',
            category=RiskCategory.OPERATIONAL,
            description='Risk of process disruption due to operational issues',
            current_level=process_risk_level,
            severity=process_severity,
            likelihood=process_likelihood,
            impact_score=process_risk_level * 60,
            mitigation_measures=self.mitigation_strategies.get('process_disruption', [])
        ))
        
        return risks
    
    async def _assess_environmental_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess environmental risks."""
        risks = []
        
        # Climate change risk
        climate_vulnerability = data.get('climate_vulnerability_score', 0.5)  # 0-1
        extreme_weather_frequency = data.get('extreme_weather_events_per_year', 2)
        
        climate_risk_level = min(1.0, climate_vulnerability + (extreme_weather_frequency / 10))
        climate_severity = RiskSeverity.HIGH if climate_risk_level > 0.7 else RiskSeverity.MEDIUM
        climate_likelihood = RiskLikelihood.LIKELY
        
        risks.append(RiskFactor(
            factor_id='climate_change',
            category=RiskCategory.ENVIRONMENTAL,
            description='Risk from climate change impacts on water resources and infrastructure',
            current_level=climate_risk_level,
            severity=climate_severity,
            likelihood=climate_likelihood,
            impact_score=climate_risk_level * 90,
            mitigation_measures=self.mitigation_strategies.get('climate_change', [])
        ))
        
        # Water scarcity risk
        drought_frequency = data.get('drought_events_per_decade', 3)
        source_diversity = data.get('water_source_diversity_index', 0.6)  # 0-1
        
        scarcity_risk_level = min(1.0, (drought_frequency / 5) * (1 - source_diversity))
        scarcity_severity = RiskSeverity.HIGH if scarcity_risk_level > 0.6 else RiskSeverity.MEDIUM
        scarcity_likelihood = RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='water_scarcity',
            category=RiskCategory.ENVIRONMENTAL,
            description='Risk of water scarcity affecting supply reliability',
            current_level=scarcity_risk_level,
            severity=scarcity_severity,
            likelihood=scarcity_likelihood,
            impact_score=scarcity_risk_level * 85,
            mitigation_measures=self.mitigation_strategies.get('water_scarcity', [])
        ))
        
        return risks
    
    async def _assess_regulatory_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess regulatory risks."""
        risks = []
        
        # Compliance violation risk
        compliance_history = data.get('compliance_violations_last_year', 0)
        regulatory_complexity = data.get('regulatory_complexity_score', 0.6)  # 0-1
        
        compliance_risk_level = min(1.0, (compliance_history / 5) + regulatory_complexity * 0.3)
        compliance_severity = RiskSeverity.HIGH if compliance_risk_level > 0.5 else RiskSeverity.MEDIUM
        compliance_likelihood = RiskLikelihood.UNLIKELY if compliance_history == 0 else RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='compliance_violation',
            category=RiskCategory.REGULATORY,
            description='Risk of regulatory compliance violations and penalties',
            current_level=compliance_risk_level,
            severity=compliance_severity,
            likelihood=compliance_likelihood,
            impact_score=compliance_risk_level * 70,
            mitigation_measures=self.mitigation_strategies.get('compliance_violation', [])
        ))
        
        return risks
    
    async def _assess_financial_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess financial risks."""
        risks = []
        
        # Cost overrun risk
        budget_variance_history = data.get('average_budget_variance', 0.1)  # 10% overrun
        project_complexity = data.get('current_project_complexity', 0.5)  # 0-1
        
        cost_risk_level = min(1.0, budget_variance_history + project_complexity * 0.3)
        cost_severity = RiskSeverity.MEDIUM
        cost_likelihood = RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='cost_overrun',
            category=RiskCategory.FINANCIAL,
            description='Risk of project cost overruns and budget constraints',
            current_level=cost_risk_level,
            severity=cost_severity,
            likelihood=cost_likelihood,
            impact_score=cost_risk_level * 60,
            mitigation_measures=self.mitigation_strategies.get('cost_overrun', [])
        ))
        
        return risks
    
    async def _assess_technical_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess technical risks."""
        risks = []
        
        # Technology obsolescence risk
        technology_age = data.get('average_technology_age', 8)  # years
        upgrade_planning = data.get('technology_upgrade_planning_score', 0.6)  # 0-1
        
        tech_risk_level = min(1.0, (technology_age / 15) * (1 - upgrade_planning * 0.5))
        tech_severity = RiskSeverity.MEDIUM
        tech_likelihood = RiskLikelihood.LIKELY if technology_age > 10 else RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='technology_obsolescence',
            category=RiskCategory.TECHNICAL,
            description='Risk of technology becoming obsolete and requiring major upgrades',
            current_level=tech_risk_level,
            severity=tech_severity,
            likelihood=tech_likelihood,
            impact_score=tech_risk_level * 50,
            mitigation_measures=self.mitigation_strategies.get('technology_obsolescence', [])
        ))
        
        return risks
    
    async def _assess_safety_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess safety risks."""
        risks = []
        
        # Worker injury risk
        safety_incidents_last_year = data.get('safety_incidents_last_year', 0)
        safety_training_score = data.get('safety_training_score', 0.8)  # 0-1
        
        safety_risk_level = min(1.0, (safety_incidents_last_year / 10) + (1 - safety_training_score) * 0.3)
        safety_severity = RiskSeverity.HIGH if safety_risk_level > 0.6 else RiskSeverity.MEDIUM
        safety_likelihood = RiskLikelihood.UNLIKELY if safety_incidents_last_year == 0 else RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='worker_injury',
            category=RiskCategory.SAFETY,
            description='Risk of worker injuries and safety incidents',
            current_level=safety_risk_level,
            severity=safety_severity,
            likelihood=safety_likelihood,
            impact_score=safety_risk_level * 75,
            mitigation_measures=self.mitigation_strategies.get('worker_injury', [])
        ))
        
        return risks
    
    async def _assess_cybersecurity_risks(self, data: Dict[str, Any]) -> List[RiskFactor]:
        """Assess cybersecurity risks."""
        risks = []
        
        # Cyber attack risk
        security_measures_score = data.get('cybersecurity_measures_score', 0.7)  # 0-1
        system_connectivity = data.get('system_connectivity_level', 0.8)  # 0-1
        
        cyber_risk_level = min(1.0, system_connectivity * (1 - security_measures_score))
        cyber_severity = RiskSeverity.HIGH if cyber_risk_level > 0.5 else RiskSeverity.MEDIUM
        cyber_likelihood = RiskLikelihood.POSSIBLE
        
        risks.append(RiskFactor(
            factor_id='cyber_attack',
            category=RiskCategory.CYBERSECURITY,
            description='Risk of cyber attacks on control systems and data',
            current_level=cyber_risk_level,
            severity=cyber_severity,
            likelihood=cyber_likelihood,
            impact_score=cyber_risk_level * 80,
            mitigation_measures=self.mitigation_strategies.get('cyber_attack', [])
        ))
        
        return risks
    
    async def _calculate_overall_risk_score(self, risk_factors: List[RiskFactor]) -> float:
        """Calculate overall risk score."""
        if not risk_factors:
            return 0.0
        
        # Weight risks by category
        category_weights = {
            RiskCategory.SAFETY: 0.25,
            RiskCategory.ENVIRONMENTAL: 0.20,
            RiskCategory.OPERATIONAL: 0.20,
            RiskCategory.CYBERSECURITY: 0.15,
            RiskCategory.REGULATORY: 0.10,
            RiskCategory.FINANCIAL: 0.05,
            RiskCategory.TECHNICAL: 0.05
        }
        
        weighted_scores = []
        for risk in risk_factors:
            weight = category_weights.get(risk.category, 0.1)
            weighted_score = risk.impact_score * weight
            weighted_scores.append(weighted_score)
        
        overall_score = sum(weighted_scores)
        return min(100.0, overall_score)
    
    async def _create_risk_matrix(self, risk_factors: List[RiskFactor]) -> Dict[str, Dict[str, int]]:
        """Create risk matrix showing likelihood vs severity."""
        matrix = {
            'very_likely': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'negligible': 0},
            'likely': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'negligible': 0},
            'possible': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'negligible': 0},
            'unlikely': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'negligible': 0},
            'very_unlikely': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'negligible': 0}
        }
        
        for risk in risk_factors:
            likelihood = risk.likelihood.value
            severity = risk.severity.value
            matrix[likelihood][severity] += 1
        
        return matrix
    
    async def _identify_critical_risks(self, risk_factors: List[RiskFactor]) -> List[str]:
        """Identify critical risks requiring immediate attention."""
        critical_risks = []
        
        for risk in risk_factors:
            # Critical if high/critical severity and likely/very likely
            if (risk.severity in [RiskSeverity.CRITICAL, RiskSeverity.HIGH] and
                risk.likelihood in [RiskLikelihood.LIKELY, RiskLikelihood.VERY_LIKELY]):
                critical_risks.append(risk.factor_id)
            
            # Also critical if impact score is very high
            elif risk.impact_score > 75:
                critical_risks.append(risk.factor_id)
        
        return critical_risks
    
    async def _generate_risk_recommendations(self, risk_factors: List[RiskFactor],
                                           critical_risks: List[str]) -> List[str]:
        """Generate risk management recommendations."""
        recommendations = []
        
        # Address critical risks first
        for risk_id in critical_risks:
            risk = next((r for r in risk_factors if r.factor_id == risk_id), None)
            if risk:
                recommendations.append(f"URGENT: Address {risk.description}")
                recommendations.extend(risk.mitigation_measures[:2])  # Top 2 measures
        
        # General risk management recommendations
        high_risk_categories = {}
        for risk in risk_factors:
            category = risk.category.value
            if category not in high_risk_categories:
                high_risk_categories[category] = 0
            high_risk_categories[category] += risk.impact_score
        
        # Sort categories by total risk
        sorted_categories = sorted(high_risk_categories.items(), key=lambda x: x[1], reverse=True)
        
        for category, total_risk in sorted_categories[:3]:  # Top 3 categories
            recommendations.append(f"Develop comprehensive {category} risk management plan")
        
        # Add general recommendations
        recommendations.extend([
            "Conduct regular risk assessments and updates",
            "Implement risk monitoring and early warning systems",
            "Develop and test emergency response procedures",
            "Provide regular risk management training to staff"
        ])
        
        return recommendations
    
    def _get_risk_level(self, risk_score: float) -> str:
        """Get risk level description."""
        if risk_score >= 80:
            return 'Very High'
        elif risk_score >= 60:
            return 'High'
        elif risk_score >= 40:
            return 'Medium'
        elif risk_score >= 20:
            return 'Low'
        else:
            return 'Very Low'


# Convenience functions
async def assess_system_risks(system_data: Dict[str, Any]) -> Dict[str, Any]:
    """Assess risks for water management system."""
    agent = RiskAssessmentAgent()
    return await agent.conduct_risk_assessment(system_data)


async def get_critical_risks(system_data: Dict[str, Any]) -> List[str]:
    """Get critical risks requiring immediate attention."""
    agent = RiskAssessmentAgent()
    result = await agent.conduct_risk_assessment(system_data)
    
    if result['status'] == 'success':
        return result['critical_risks']
    else:
        return []
