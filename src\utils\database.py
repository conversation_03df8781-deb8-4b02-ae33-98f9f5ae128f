"""
Database utilities and connection management for the Water Management System.

This module provides database connection management, initialization,
and utility functions for PostgreSQL operations.
"""

import asyncio
import logging
from typing import Optional, Dict, Any
import asyncpg
from contextlib import asynccontextmanager

from src.utils.config import get_settings

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection manager for PostgreSQL."""
    
    def __init__(self):
        self.settings = get_settings()
        self.pool: Optional[asyncpg.Pool] = None
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize database connection pool."""
        try:
            logger.info("Initializing database connection pool...")
            
            self.pool = await asyncpg.create_pool(
                self.settings.get_database_url(),
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.execute('SELECT 1')
            
            self.is_initialized = True
            logger.info("Database connection pool initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def close(self):
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("Database connection pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool."""
        if not self.is_initialized:
            await self.initialize()
        
        async with self.pool.acquire() as conn:
            yield conn


# Global database manager instance
_db_manager = DatabaseManager()


async def init_database():
    """Initialize database and create tables."""
    try:
        logger.info("Initializing database schema...")
        
        await _db_manager.initialize()
        
        async with _db_manager.get_connection() as conn:
            # Create climate_data table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS climate_data (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    location VARCHAR(255) NOT NULL,
                    latitude FLOAT NOT NULL,
                    longitude FLOAT NOT NULL,
                    temperature FLOAT,
                    humidity FLOAT,
                    precipitation FLOAT,
                    wind_speed FLOAT,
                    pressure FLOAT,
                    source VARCHAR(100) NOT NULL,
                    metadata JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create water_treatment_systems table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS water_treatment_systems (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    location VARCHAR(255) NOT NULL,
                    capacity_liters_per_day INTEGER,
                    energy_consumption_kwh FLOAT,
                    efficiency_percentage FLOAT,
                    configuration JSONB,
                    status VARCHAR(50) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create optimization_results table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS optimization_results (
                    id SERIAL PRIMARY KEY,
                    system_id INTEGER REFERENCES water_treatment_systems(id),
                    optimization_type VARCHAR(100) NOT NULL,
                    input_parameters JSONB,
                    results JSONB,
                    performance_metrics JSONB,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create agent_logs table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS agent_logs (
                    id SERIAL PRIMARY KEY,
                    agent_type VARCHAR(100) NOT NULL,
                    task_id VARCHAR(255),
                    input_data JSONB,
                    output_data JSONB,
                    execution_time_seconds FLOAT,
                    success BOOLEAN,
                    error_message TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_climate_data_timestamp ON climate_data(timestamp)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_climate_data_location ON climate_data(location)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_optimization_results_system_id ON optimization_results(system_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_agent_logs_agent_type ON agent_logs(agent_type)")
            
        logger.info("Database schema initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database schema: {e}")
        raise


@asynccontextmanager
async def get_database_connection():
    """Get database connection context manager."""
    async with _db_manager.get_connection() as conn:
        yield conn


async def close_database():
    """Close database connections."""
    await _db_manager.close()


# Database utility functions
async def execute_query(query: str, *args) -> list:
    """Execute a query and return results."""
    async with get_database_connection() as conn:
        return await conn.fetch(query, *args)


async def execute_command(query: str, *args) -> str:
    """Execute a command and return status."""
    async with get_database_connection() as conn:
        return await conn.execute(query, *args)


async def insert_climate_data(data_points: list) -> None:
    """Insert climate data points into database."""
    async with get_database_connection() as conn:
        await conn.executemany("""
            INSERT INTO climate_data 
            (timestamp, location, latitude, longitude, temperature, 
             humidity, precipitation, wind_speed, pressure, source, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        """, data_points)


async def get_recent_climate_data(location: str = None, hours: int = 24) -> list:
    """Get recent climate data from database."""
    query = """
        SELECT * FROM climate_data 
        WHERE timestamp >= NOW() - INTERVAL '%s hours'
    """ % hours
    
    if location:
        query += " AND location = $1 ORDER BY timestamp DESC"
        return await execute_query(query, location)
    else:
        query += " ORDER BY timestamp DESC"
        return await execute_query(query)


async def health_check() -> Dict[str, Any]:
    """Check database health."""
    try:
        async with get_database_connection() as conn:
            result = await conn.fetchval('SELECT 1')
            
            # Get table counts
            climate_count = await conn.fetchval('SELECT COUNT(*) FROM climate_data')
            systems_count = await conn.fetchval('SELECT COUNT(*) FROM water_treatment_systems')
            
            return {
                'status': 'healthy',
                'connection': 'ok' if result == 1 else 'error',
                'tables': {
                    'climate_data': climate_count,
                    'water_treatment_systems': systems_count
                }
            }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e)
        }
