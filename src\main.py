"""
Water Management Decarbonisation System - Main Application Entry Point

This module serves as the main entry point for the water management decarbonisation
system, integrating climate data, LLM agents, and optimization algorithms.
"""

import asyncio
import logging
from typing import Dict, Any
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from src.api.routes import router
from src.utils.config import get_settings
from src.utils.database import init_database
from src.utils.logging_config import setup_logging
from src.agents.orchestrator import AgentOrchestrator
from src.data.collectors.climate_collector import ClimateDataCollector
from src.models.manager import ModelManager

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global instances
agent_orchestrator: AgentOrchestrator = None
climate_collector: ClimateDataCollector = None
model_manager: ModelManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting Water Management Decarbonisation System...")
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized successfully")
        
        # Initialize global components
        global agent_orchestrator, climate_collector, model_manager
        
        # Initialize model manager
        model_manager = ModelManager()
        await model_manager.initialize()
        logger.info("Model manager initialized")
        
        # Initialize climate data collector
        climate_collector = ClimateDataCollector()
        await climate_collector.initialize()
        logger.info("Climate data collector initialized")
        
        # Initialize agent orchestrator
        agent_orchestrator = AgentOrchestrator()
        await agent_orchestrator.initialize()
        logger.info("Agent orchestrator initialized")
        
        # Start background tasks
        asyncio.create_task(start_background_tasks())
        
        logger.info("System startup completed successfully")
        
    except Exception as e:
        logger.error(f"Failed to start system: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Water Management Decarbonisation System...")
    
    try:
        if agent_orchestrator:
            await agent_orchestrator.shutdown()
        if climate_collector:
            await climate_collector.shutdown()
        if model_manager:
            await model_manager.shutdown()
        
        logger.info("System shutdown completed successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


async def start_background_tasks():
    """Start background tasks for data collection and processing."""
    try:
        # Start climate data collection
        if climate_collector:
            asyncio.create_task(climate_collector.start_continuous_collection())
        
        # Start model training scheduler
        if model_manager:
            asyncio.create_task(model_manager.start_training_scheduler())
        
        logger.info("Background tasks started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start background tasks: {e}")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="Water Management Decarbonisation System",
        description="AI-powered system for optimizing water treatment processes with climate integration",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(router, prefix="/api/v1")
    
    @app.get("/")
    async def root():
        """Root endpoint providing system information."""
        return {
            "message": "Water Management Decarbonisation System",
            "version": "1.0.0",
            "status": "operational",
            "documentation": "/docs"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint for monitoring."""
        try:
            # Check system components
            health_status = {
                "status": "healthy",
                "timestamp": asyncio.get_event_loop().time(),
                "components": {
                    "database": "healthy",
                    "agent_orchestrator": "healthy" if agent_orchestrator else "not_initialized",
                    "climate_collector": "healthy" if climate_collector else "not_initialized",
                    "model_manager": "healthy" if model_manager else "not_initialized"
                }
            }
            
            return health_status
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            raise HTTPException(status_code=503, detail="Service unhealthy")
    
    return app


# Create the FastAPI app instance
app = create_app()


def main():
    """Main function to run the application."""
    settings = get_settings()
    
    logger.info(f"Starting server on {settings.API_HOST}:{settings.API_PORT}")
    
    uvicorn.run(
        "src.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.API_WORKERS,
        log_level=settings.LOG_LEVEL.lower()
    )


if __name__ == "__main__":
    main()
