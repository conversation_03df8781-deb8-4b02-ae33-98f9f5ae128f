#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sentinel Hub API Integration for Marine Debris Detection
Real-time satellite imagery processing for marine conservation
"""

import os
import json
import asyncio
import aiohttp
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
from PIL import Image
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class BoundingBox:
    """Geographic bounding box for satellite imagery requests"""
    min_lon: float
    min_lat: float
    max_lon: float
    max_lat: float
    
    def to_bbox_string(self) -> str:
        """Convert to Sentinel Hub bbox format"""
        return f"{self.min_lon},{self.min_lat},{self.max_lon},{self.max_lat}"


@dataclass
class SatelliteImageRequest:
    """Satellite image request configuration"""
    bbox: BoundingBox
    time_range: Tuple[str, str]  # (start_date, end_date) in ISO format
    width: int = 512
    height: int = 512
    max_cloud_coverage: float = 0.3
    data_collection: str = "SENTINEL2_L2A"
    format: str = "image/png"


@dataclass
class MarineDebrisDetection:
    """Marine debris detection result"""
    location: Tuple[float, float]  # (lat, lon)
    confidence: float
    debris_type: str
    size_estimate: float  # in square meters
    timestamp: datetime
    image_url: Optional[str] = None


class SentinelHubAPI:
    """
    Sentinel Hub API client for marine debris detection
    
    Provides OAuth2 authentication and satellite imagery processing
    for real-time marine debris monitoring and detection.
    """
    
    def __init__(self, client_id: str, client_secret: str):
        """
        Initialize Sentinel Hub API client
        
        Args:
            client_id: OAuth2 client ID
            client_secret: OAuth2 client secret
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = "https://services.sentinel-hub.com"
        self.access_token = None
        self.token_expires_at = None
        self.session = None
        
        # Marine debris detection parameters
        self.debris_detection_threshold = 0.75
        self.min_debris_size = 10.0  # square meters
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60)
        )
        await self.authenticate()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def authenticate(self) -> bool:
        """
        Authenticate with Sentinel Hub using OAuth2
        
        Returns:
            bool: True if authentication successful
        """
        try:
            # Prepare OAuth2 credentials
            credentials = base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
            
            headers = {
                "Authorization": f"Basic {credentials}",
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            data = "grant_type=client_credentials"
            
            # Request access token
            async with self.session.post(
                f"{self.base_url}/oauth/token",
                headers=headers,
                data=data
            ) as response:
                
                if response.status == 200:
                    token_data = await response.json()
                    self.access_token = token_data["access_token"]
                    expires_in = token_data.get("expires_in", 3600)
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                    
                    logger.info("✅ Sentinel Hub authentication successful")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Authentication failed: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False
    
    async def _ensure_authenticated(self) -> bool:
        """Ensure we have a valid access token"""
        if not self.access_token or datetime.now() >= self.token_expires_at:
            return await self.authenticate()
        return True
    
    async def get_satellite_image(self, request: SatelliteImageRequest) -> Optional[bytes]:
        """
        Get satellite image for specified area and time range
        
        Args:
            request: Satellite image request configuration
            
        Returns:
            bytes: Image data or None if failed
        """
        if not await self._ensure_authenticated():
            return None
        
        try:
            # Prepare Process API request
            process_request = {
                "input": {
                    "bounds": {
                        "bbox": [
                            request.bbox.min_lon,
                            request.bbox.min_lat,
                            request.bbox.max_lon,
                            request.bbox.max_lat
                        ],
                        "properties": {"crs": "http://www.opengis.net/def/crs/EPSG/0/4326"}
                    },
                    "data": [
                        {
                            "type": request.data_collection,
                            "dataFilter": {
                                "timeRange": {
                                    "from": request.time_range[0],
                                    "to": request.time_range[1]
                                },
                                "maxCloudCoverage": request.max_cloud_coverage
                            }
                        }
                    ]
                },
                "output": {
                    "width": request.width,
                    "height": request.height,
                    "responses": [
                        {
                            "identifier": "default",
                            "format": {"type": request.format}
                        }
                    ]
                },
                "evalscript": self._get_marine_debris_evalscript()
            }
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/process",
                headers=headers,
                json=process_request
            ) as response:
                
                if response.status == 200:
                    image_data = await response.read()
                    logger.info(f"✅ Retrieved satellite image ({len(image_data)} bytes)")
                    return image_data
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Image request failed: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting satellite image: {e}")
            return None
    
    def _get_marine_debris_evalscript(self) -> str:
        """
        Get evalscript for marine debris detection
        
        Returns:
            str: JavaScript evalscript for Sentinel Hub processing
        """
        return """
        //VERSION=3
        
        function setup() {
            return {
                input: ["B02", "B03", "B04", "B08", "B11", "B12"],
                output: { bands: 4 }
            };
        }
        
        function evaluatePixel(sample) {
            // Enhanced marine debris detection algorithm
            let blue = sample.B02;
            let green = sample.B03;
            let red = sample.B04;
            let nir = sample.B08;
            let swir1 = sample.B11;
            let swir2 = sample.B12;
            
            // Calculate indices for marine debris detection
            let ndvi = (nir - red) / (nir + red);
            let ndwi = (green - nir) / (green + nir);
            let plastic_index = (swir1 - blue) / (swir1 + blue);
            
            // Marine debris detection logic
            let debris_score = 0;
            
            // Plastic debris typically has low NDVI and specific spectral signature
            if (ndvi < 0.1 && plastic_index > 0.2) {
                debris_score += 0.4;
            }
            
            // Floating debris often shows up in SWIR bands
            if (swir1 > 0.15 && swir2 > 0.1) {
                debris_score += 0.3;
            }
            
            // Contrast with water (high NDWI indicates water)
            if (ndwi < -0.3) {
                debris_score += 0.3;
            }
            
            // Color enhancement for visualization
            let r = red * 2.5;
            let g = green * 2.5;
            let b = blue * 2.5;
            
            // Highlight potential debris in red
            if (debris_score > 0.6) {
                r = Math.min(1, r + 0.5);
                g = Math.max(0, g - 0.2);
                b = Math.max(0, b - 0.2);
            }
            
            return [r, g, b, debris_score];
        }
        """
    
    async def detect_marine_debris(
        self, 
        bbox: BoundingBox, 
        days_back: int = 7
    ) -> List[MarineDebrisDetection]:
        """
        Detect marine debris in specified area
        
        Args:
            bbox: Geographic bounding box to analyze
            days_back: Number of days to look back for imagery
            
        Returns:
            List[MarineDebrisDetection]: Detected debris locations
        """
        try:
            # Prepare time range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            time_range = (
                start_date.strftime("%Y-%m-%dT00:00:00Z"),
                end_date.strftime("%Y-%m-%dT23:59:59Z")
            )
            
            # Create image request
            request = SatelliteImageRequest(
                bbox=bbox,
                time_range=time_range,
                width=1024,
                height=1024,
                max_cloud_coverage=0.2
            )
            
            # Get satellite image
            image_data = await self.get_satellite_image(request)
            if not image_data:
                return []
            
            # Process image for debris detection
            detections = await self._process_debris_image(image_data, bbox)
            
            logger.info(f"✅ Detected {len(detections)} potential marine debris locations")
            return detections
            
        except Exception as e:
            logger.error(f"❌ Error detecting marine debris: {e}")
            return []
    
    async def _process_debris_image(
        self, 
        image_data: bytes, 
        bbox: BoundingBox
    ) -> List[MarineDebrisDetection]:
        """
        Process satellite image to detect marine debris
        
        Args:
            image_data: Raw image data from Sentinel Hub
            bbox: Geographic bounding box of the image
            
        Returns:
            List[MarineDebrisDetection]: Detected debris
        """
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))
            img_array = np.array(image)
            
            # Extract debris confidence channel (4th channel from evalscript)
            if img_array.shape[2] >= 4:
                debris_channel = img_array[:, :, 3] / 255.0
            else:
                # Fallback: use simple color analysis
                debris_channel = self._simple_debris_detection(img_array)
            
            # Find debris locations
            detections = []
            height, width = debris_channel.shape
            
            # Scan for high-confidence debris pixels
            for y in range(0, height, 32):  # Sample every 32 pixels
                for x in range(0, width, 32):
                    confidence = debris_channel[y, x]
                    
                    if confidence > self.debris_detection_threshold:
                        # Convert pixel coordinates to geographic coordinates
                        lat = bbox.max_lat - (y / height) * (bbox.max_lat - bbox.min_lat)
                        lon = bbox.min_lon + (x / width) * (bbox.max_lon - bbox.min_lon)
                        
                        # Estimate debris size (rough approximation)
                        pixel_size_m = self._calculate_pixel_size(bbox, width, height)
                        debris_size = pixel_size_m * 32 * 32  # 32x32 pixel area
                        
                        if debris_size >= self.min_debris_size:
                            detection = MarineDebrisDetection(
                                location=(lat, lon),
                                confidence=confidence,
                                debris_type="unknown",  # Would need ML model for classification
                                size_estimate=debris_size,
                                timestamp=datetime.now()
                            )
                            detections.append(detection)
            
            return detections
            
        except Exception as e:
            logger.error(f"❌ Error processing debris image: {e}")
            return []
    
    def _simple_debris_detection(self, img_array: np.ndarray) -> np.ndarray:
        """
        Simple debris detection fallback using color analysis
        
        Args:
            img_array: RGB image array
            
        Returns:
            np.ndarray: Debris confidence map
        """
        # Convert to float
        img = img_array.astype(np.float32) / 255.0
        
        # Simple debris detection based on color anomalies
        # Debris often appears as bright spots against dark water
        gray = np.mean(img, axis=2)
        
        # Calculate local contrast
        from scipy import ndimage
        local_mean = ndimage.uniform_filter(gray, size=5)
        contrast = np.abs(gray - local_mean)
        
        # Normalize to 0-1 range
        if contrast.max() > 0:
            contrast = contrast / contrast.max()
        
        return contrast
    
    def _calculate_pixel_size(self, bbox: BoundingBox, width: int, height: int) -> float:
        """
        Calculate approximate pixel size in meters
        
        Args:
            bbox: Geographic bounding box
            width: Image width in pixels
            height: Image height in pixels
            
        Returns:
            float: Pixel size in meters
        """
        # Rough approximation using latitude
        lat_center = (bbox.min_lat + bbox.max_lat) / 2
        
        # Degrees to meters conversion (approximate)
        lat_deg_to_m = 111320  # meters per degree latitude
        lon_deg_to_m = 111320 * np.cos(np.radians(lat_center))  # varies with latitude
        
        # Calculate pixel size
        lat_pixel_size = ((bbox.max_lat - bbox.min_lat) / height) * lat_deg_to_m
        lon_pixel_size = ((bbox.max_lon - bbox.min_lon) / width) * lon_deg_to_m
        
        return (lat_pixel_size + lon_pixel_size) / 2
    
    async def get_available_dates(self, bbox: BoundingBox, days_back: int = 30) -> List[str]:
        """
        Get available satellite imagery dates for specified area
        
        Args:
            bbox: Geographic bounding box
            days_back: Number of days to look back
            
        Returns:
            List[str]: Available dates in ISO format
        """
        if not await self._ensure_authenticated():
            return []
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            catalog_request = {
                "bbox": [bbox.min_lon, bbox.min_lat, bbox.max_lon, bbox.max_lat],
                "datetime": f"{start_date.isoformat()}Z/{end_date.isoformat()}Z",
                "collections": ["sentinel-2-l2a"],
                "limit": 100
            }
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/catalog/search",
                headers=headers,
                json=catalog_request
            ) as response:
                
                if response.status == 200:
                    catalog_data = await response.json()
                    dates = []
                    
                    for feature in catalog_data.get("features", []):
                        date_str = feature["properties"]["datetime"]
                        dates.append(date_str)
                    
                    return sorted(list(set(dates)))
                else:
                    logger.error(f"❌ Catalog search failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting available dates: {e}")
            return []


# Convenience functions for easy usage
async def detect_marine_debris_area(
    bbox: BoundingBox,
    client_id: str = None,
    client_secret: str = None,
    days_back: int = 7
) -> List[MarineDebrisDetection]:
    """
    Convenience function to detect marine debris in an area
    
    Args:
        bbox: Geographic bounding box to analyze
        client_id: Sentinel Hub client ID (uses env var if None)
        client_secret: Sentinel Hub client secret (uses env var if None)
        days_back: Number of days to look back for imagery
        
    Returns:
        List[MarineDebrisDetection]: Detected debris locations
    """
    # Use environment variables if not provided
    if not client_id:
        client_id = os.getenv("SENTINEL_HUB_CLIENT_ID", "91e73709-3e73-4525-9cfc-957258864901")
    if not client_secret:
        client_secret = os.getenv("SENTINEL_HUB_CLIENT_SECRET", "")
    
    async with SentinelHubAPI(client_id, client_secret) as api:
        return await api.detect_marine_debris(bbox, days_back)


if __name__ == "__main__":
    # Test the Sentinel Hub API integration
    async def test_sentinel_hub():
        """Test function for Sentinel Hub API"""
        print("🛰️ Testing Sentinel Hub API for Marine Debris Detection")
        print("=" * 60)
        
        # Test area: Mediterranean Sea near plastic pollution hotspot
        test_bbox = BoundingBox(
            min_lon=2.0,   # Near Barcelona
            min_lat=41.0,
            max_lon=3.0,
            max_lat=42.0
        )
        
        try:
            detections = await detect_marine_debris_area(test_bbox, days_back=14)
            
            print(f"✅ Detection complete: {len(detections)} potential debris locations found")
            
            for i, detection in enumerate(detections[:5]):  # Show first 5
                print(f"  {i+1}. Location: ({detection.location[0]:.4f}, {detection.location[1]:.4f})")
                print(f"     Confidence: {detection.confidence:.2f}")
                print(f"     Size: {detection.size_estimate:.1f} m²")
                print(f"     Time: {detection.timestamp}")
                print()
            
            if len(detections) > 5:
                print(f"  ... and {len(detections) - 5} more detections")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    # Run test
    asyncio.run(test_sentinel_hub())
