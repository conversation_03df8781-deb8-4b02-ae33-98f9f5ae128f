"""
AI-Powered Climate Analysis Agent.

This module provides an intelligent climate analysis agent that leverages
machine learning, pattern recognition, and advanced analytics to generate
actionable climate insights for water treatment optimization.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import json
from sklearn.ensemble import RandomForestRegressor, IsolationForest
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import joblib

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData, ClimateDataPreprocessor
from src.analysis.temperature_trends import analyze_location_temperature_trends
from src.analysis.precipitation_patterns import analyze_location_precipitation_patterns
from src.analysis.extreme_weather_detection import detect_extreme_weather_for_location
from src.analysis.seasonal_modeling import model_seasonal_variations_for_location
from src.analysis.climate_projections import analyze_climate_projections_for_location

logger = logging.getLogger(__name__)


@dataclass
class ClimateInsight:
    """AI-generated climate insight."""
    insight_id: str
    insight_type: str  # 'pattern', 'anomaly', 'prediction', 'recommendation'
    title: str
    description: str
    confidence: float
    importance: str  # 'critical', 'high', 'medium', 'low'
    data_sources: List[str]
    supporting_evidence: Dict[str, Any]
    actionable_recommendations: List[str]
    timestamp: datetime


@dataclass
class ClimateAnalysisResult:
    """Result of AI-powered climate analysis."""
    location: str
    analysis_period: Dict[str, str]
    insights: List[ClimateInsight]
    pattern_analysis: Dict[str, Any]
    anomaly_detection: Dict[str, Any]
    predictive_models: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    optimization_recommendations: Dict[str, Any]
    model_performance: Dict[str, float]
    timestamp: datetime


class ClimateAnalysisAgent:
    """
    AI-powered climate analysis agent.
    
    Provides:
    - Intelligent pattern recognition and analysis
    - Anomaly detection using machine learning
    - Predictive modeling for climate parameters
    - Risk assessment and early warning systems
    - Optimization recommendations for water treatment
    - Automated insight generation and prioritization
    - Continuous learning and model improvement
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # ML models
        self.models = {
            'temperature_predictor': None,
            'precipitation_predictor': None,
            'anomaly_detector': None,
            'pattern_classifier': None
        }
        
        # Model parameters
        self.model_params = {
            'random_forest': {
                'n_estimators': 100,
                'max_depth': 10,
                'random_state': 42
            },
            'isolation_forest': {
                'contamination': 0.1,
                'random_state': 42
            },
            'kmeans': {
                'n_clusters': 5,
                'random_state': 42
            }
        }
        
        # Feature engineering parameters
        self.feature_windows = [7, 14, 30, 90]  # Days for rolling features
        self.lag_features = [1, 3, 7, 14]  # Lag days for time series features
        
        # Insight generation parameters
        self.insight_thresholds = {
            'anomaly_threshold': 0.8,
            'pattern_confidence': 0.7,
            'prediction_accuracy': 0.6,
            'importance_thresholds': {
                'critical': 0.9,
                'high': 0.7,
                'medium': 0.5,
                'low': 0.3
            }
        }
        
        # Scalers for feature normalization
        self.scalers = {}
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the climate analysis agent."""
        try:
            logger.info("Initializing AI-Powered Climate Analysis Agent...")
            
            # Initialize ML models
            await self._initialize_models()
            
            self.is_initialized = True
            logger.info("Climate Analysis Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize climate analysis agent: {e}")
            return False
    
    async def _initialize_models(self):
        """Initialize machine learning models."""
        try:
            # Temperature prediction model
            self.models['temperature_predictor'] = RandomForestRegressor(
                **self.model_params['random_forest']
            )
            
            # Precipitation prediction model
            self.models['precipitation_predictor'] = RandomForestRegressor(
                **self.model_params['random_forest']
            )
            
            # Anomaly detection model
            self.models['anomaly_detector'] = IsolationForest(
                **self.model_params['isolation_forest']
            )
            
            # Pattern classification model
            self.models['pattern_classifier'] = KMeans(
                **self.model_params['kmeans']
            )
            
            # Initialize scalers
            self.scalers = {
                'temperature': StandardScaler(),
                'precipitation': StandardScaler(),
                'features': StandardScaler()
            }
            
            logger.info("ML models initialized successfully")
            
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
    
    async def analyze_climate_data(self, data: List[ProcessedClimateData], 
                                 location: str = None) -> ClimateAnalysisResult:
        """Perform comprehensive AI-powered climate analysis."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not data:
                raise ValueError("No data provided for analysis")
            
            logger.info(f"Starting AI climate analysis for {len(data)} data points")
            
            # Prepare data for analysis
            df = await self._prepare_analysis_dataframe(data)
            analysis_location = location or self._extract_location(data)
            
            if df.empty or len(df) < 30:  # Need minimum data for ML
                raise ValueError("Insufficient data for AI analysis")
            
            # Generate features for ML models
            features_df = await self._generate_features(df)
            
            # Train and evaluate models
            model_performance = await self._train_models(features_df)
            
            # Perform pattern analysis
            pattern_analysis = await self._analyze_patterns(df, features_df)
            
            # Detect anomalies
            anomaly_detection = await self._detect_anomalies(df, features_df)
            
            # Generate predictions
            predictive_models = await self._generate_predictions(df, features_df)
            
            # Assess risks
            risk_assessment = await self._assess_climate_risks(df, pattern_analysis, anomaly_detection)
            
            # Generate optimization recommendations
            optimization_recommendations = await self._generate_optimization_recommendations(
                df, pattern_analysis, predictive_models
            )
            
            # Generate AI insights
            insights = await self._generate_insights(
                df, pattern_analysis, anomaly_detection, predictive_models, risk_assessment
            )
            
            # Create result
            result = ClimateAnalysisResult(
                location=analysis_location,
                analysis_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'duration_days': (df.index.max() - df.index.min()).days
                },
                insights=insights,
                pattern_analysis=pattern_analysis,
                anomaly_detection=anomaly_detection,
                predictive_models=predictive_models,
                risk_assessment=risk_assessment,
                optimization_recommendations=optimization_recommendations,
                model_performance=model_performance,
                timestamp=datetime.now()
            )
            
            logger.info(f"AI climate analysis completed for {analysis_location}")
            logger.info(f"Generated {len(insights)} insights")
            return result
            
        except Exception as e:
            logger.error(f"AI climate analysis failed: {e}")
            raise
    
    async def _prepare_analysis_dataframe(self, data: List[ProcessedClimateData]) -> pd.DataFrame:
        """Prepare DataFrame for AI analysis."""
        try:
            records = []
            for item in data:
                record = {
                    'timestamp': item.timestamp,
                    'temperature': item.temperature,
                    'precipitation': item.precipitation,
                    'quality_score': getattr(item, 'data_quality_score', 1.0)
                }
                
                # Add additional parameters if available
                if hasattr(item, 'humidity') and item.humidity is not None:
                    record['humidity'] = item.humidity
                if hasattr(item, 'pressure') and item.pressure is not None:
                    record['pressure'] = item.pressure
                if hasattr(item, 'wind_speed') and item.wind_speed is not None:
                    record['wind_speed'] = item.wind_speed
                
                records.append(record)
            
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Handle missing values
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if col == 'precipitation':
                    df[col] = df[col].fillna(0.0)
                else:
                    df[col] = df[col].interpolate(method='linear')
            
            return df
            
        except Exception as e:
            logger.error(f"DataFrame preparation failed: {e}")
            return pd.DataFrame()
    
    def _extract_location(self, data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in data if item.location]
        if locations:
            return max(set(locations), key=locations.count)
        return "Unknown Location"
    
    async def _generate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Generate features for machine learning models."""
        try:
            features_df = df.copy()
            
            # Time-based features
            features_df['day_of_year'] = features_df.index.dayofyear
            features_df['month'] = features_df.index.month
            features_df['day_of_week'] = features_df.index.dayofweek
            features_df['is_weekend'] = features_df['day_of_week'].isin([5, 6]).astype(int)
            
            # Seasonal features
            features_df['sin_day'] = np.sin(2 * np.pi * features_df['day_of_year'] / 365.25)
            features_df['cos_day'] = np.cos(2 * np.pi * features_df['day_of_year'] / 365.25)
            
            # Rolling window features
            for param in ['temperature', 'precipitation']:
                if param in features_df.columns:
                    for window in self.feature_windows:
                        features_df[f'{param}_mean_{window}d'] = features_df[param].rolling(window=window).mean()
                        features_df[f'{param}_std_{window}d'] = features_df[param].rolling(window=window).std()
                        features_df[f'{param}_min_{window}d'] = features_df[param].rolling(window=window).min()
                        features_df[f'{param}_max_{window}d'] = features_df[param].rolling(window=window).max()
            
            # Lag features
            for param in ['temperature', 'precipitation']:
                if param in features_df.columns:
                    for lag in self.lag_features:
                        features_df[f'{param}_lag_{lag}d'] = features_df[param].shift(lag)
            
            # Derived features
            if 'temperature' in features_df.columns and 'precipitation' in features_df.columns:
                # Temperature-precipitation interaction
                features_df['temp_precip_interaction'] = features_df['temperature'] * features_df['precipitation']
                
                # Drought index (simplified)
                features_df['drought_index'] = (
                    features_df['temperature'] / features_df['temperature'].mean() - 
                    features_df['precipitation'] / features_df['precipitation'].mean()
                )
            
            # Drop rows with NaN values (from rolling/lag features)
            features_df = features_df.dropna()
            
            return features_df
            
        except Exception as e:
            logger.error(f"Feature generation failed: {e}")
            return df
    
    async def _train_models(self, features_df: pd.DataFrame) -> Dict[str, float]:
        """Train machine learning models."""
        try:
            performance = {}
            
            # Prepare feature columns (exclude target variables)
            feature_cols = [col for col in features_df.columns 
                          if col not in ['temperature', 'precipitation', 'quality_score']]
            
            if len(feature_cols) == 0:
                logger.warning("No features available for model training")
                return {}
            
            X = features_df[feature_cols]
            
            # Scale features
            X_scaled = self.scalers['features'].fit_transform(X)
            
            # Train temperature prediction model
            if 'temperature' in features_df.columns:
                y_temp = features_df['temperature']
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y_temp, test_size=0.2, random_state=42
                )
                
                self.models['temperature_predictor'].fit(X_train, y_train)
                y_pred = self.models['temperature_predictor'].predict(X_test)
                
                performance['temperature_r2'] = r2_score(y_test, y_pred)
                performance['temperature_rmse'] = np.sqrt(mean_squared_error(y_test, y_pred))
            
            # Train precipitation prediction model
            if 'precipitation' in features_df.columns:
                y_precip = features_df['precipitation']
                X_train, X_test, y_train, y_test = train_test_split(
                    X_scaled, y_precip, test_size=0.2, random_state=42
                )
                
                self.models['precipitation_predictor'].fit(X_train, y_train)
                y_pred = self.models['precipitation_predictor'].predict(X_test)
                
                performance['precipitation_r2'] = r2_score(y_test, y_pred)
                performance['precipitation_rmse'] = np.sqrt(mean_squared_error(y_test, y_pred))
            
            # Train anomaly detection model
            self.models['anomaly_detector'].fit(X_scaled)
            anomaly_scores = self.models['anomaly_detector'].decision_function(X_scaled)
            performance['anomaly_detection_score'] = float(np.mean(anomaly_scores))
            
            # Train pattern classification model
            self.models['pattern_classifier'].fit(X_scaled)
            performance['pattern_inertia'] = float(self.models['pattern_classifier'].inertia_)
            
            logger.info(f"Models trained successfully. Performance: {performance}")
            return performance
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            return {}
    
    async def _analyze_patterns(self, df: pd.DataFrame, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze climate patterns using ML."""
        try:
            patterns = {}
            
            # Pattern classification
            if self.models['pattern_classifier'] is not None:
                feature_cols = [col for col in features_df.columns 
                              if col not in ['temperature', 'precipitation', 'quality_score']]
                
                if len(feature_cols) > 0:
                    X = features_df[feature_cols]
                    X_scaled = self.scalers['features'].transform(X)
                    
                    # Get cluster labels
                    cluster_labels = self.models['pattern_classifier'].predict(X_scaled)
                    
                    # Analyze clusters
                    patterns['climate_clusters'] = await self._analyze_clusters(
                        features_df, cluster_labels
                    )
            
            # Trend analysis
            patterns['trend_analysis'] = await self._analyze_trends_ml(df)
            
            # Seasonal pattern analysis
            patterns['seasonal_patterns'] = await self._analyze_seasonal_patterns_ml(df)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Pattern analysis failed: {e}")
            return {}
    
    async def _analyze_clusters(self, features_df: pd.DataFrame, cluster_labels: np.ndarray) -> Dict[str, Any]:
        """Analyze climate clusters."""
        try:
            clusters = {}
            
            for cluster_id in np.unique(cluster_labels):
                cluster_mask = cluster_labels == cluster_id
                cluster_data = features_df[cluster_mask]
                
                if len(cluster_data) > 0:
                    clusters[f'cluster_{cluster_id}'] = {
                        'size': len(cluster_data),
                        'percentage': float(len(cluster_data) / len(features_df) * 100),
                        'temperature_mean': float(cluster_data['temperature'].mean()) if 'temperature' in cluster_data.columns else None,
                        'precipitation_mean': float(cluster_data['precipitation'].mean()) if 'precipitation' in cluster_data.columns else None,
                        'characteristics': self._describe_cluster_characteristics(cluster_data)
                    }
            
            return clusters
            
        except Exception as e:
            logger.error(f"Cluster analysis failed: {e}")
            return {}
    
    def _describe_cluster_characteristics(self, cluster_data: pd.DataFrame) -> List[str]:
        """Describe characteristics of a climate cluster."""
        characteristics = []
        
        try:
            if 'temperature' in cluster_data.columns:
                temp_mean = cluster_data['temperature'].mean()
                if temp_mean > 25:
                    characteristics.append('hot_conditions')
                elif temp_mean < 5:
                    characteristics.append('cold_conditions')
                else:
                    characteristics.append('moderate_temperature')
            
            if 'precipitation' in cluster_data.columns:
                precip_mean = cluster_data['precipitation'].mean()
                if precip_mean > 10:
                    characteristics.append('wet_conditions')
                elif precip_mean < 1:
                    characteristics.append('dry_conditions')
                else:
                    characteristics.append('moderate_precipitation')
            
            # Seasonal characteristics
            if 'month' in cluster_data.columns:
                dominant_months = cluster_data['month'].mode().tolist()
                if any(month in [12, 1, 2] for month in dominant_months):
                    characteristics.append('winter_pattern')
                elif any(month in [6, 7, 8] for month in dominant_months):
                    characteristics.append('summer_pattern')
            
            return characteristics
            
        except Exception as e:
            logger.warning(f"Failed to describe cluster characteristics: {e}")
            return ['unknown_pattern']

    async def _analyze_trends_ml(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze trends using machine learning approaches."""
        try:
            trends = {}

            # Use existing trend analysis but enhance with ML insights
            if 'temperature' in df.columns:
                temp_data = df['temperature'].dropna()
                if len(temp_data) > 30:
                    # Feature importance for temperature trends
                    trends['temperature_trend_factors'] = await self._analyze_trend_factors(
                        temp_data, 'temperature'
                    )

            if 'precipitation' in df.columns:
                precip_data = df['precipitation'].dropna()
                if len(precip_data) > 30:
                    # Feature importance for precipitation trends
                    trends['precipitation_trend_factors'] = await self._analyze_trend_factors(
                        precip_data, 'precipitation'
                    )

            return trends

        except Exception as e:
            logger.error(f"ML trend analysis failed: {e}")
            return {}

    async def _analyze_trend_factors(self, data: pd.Series, parameter: str) -> Dict[str, Any]:
        """Analyze factors contributing to trends."""
        try:
            # Simple trend analysis using linear regression
            time_numeric = np.arange(len(data))

            # Fit linear trend
            coeffs = np.polyfit(time_numeric, data.values, 1)
            trend_slope = coeffs[0]

            # Calculate trend strength
            trend_line = np.polyval(coeffs, time_numeric)
            r_squared = 1 - (np.sum((data.values - trend_line) ** 2) /
                            np.sum((data.values - np.mean(data.values)) ** 2))

            return {
                'trend_slope': float(trend_slope),
                'trend_strength': float(r_squared),
                'trend_direction': 'increasing' if trend_slope > 0 else 'decreasing' if trend_slope < 0 else 'stable',
                'trend_significance': 'significant' if abs(r_squared) > 0.3 else 'weak'
            }

        except Exception as e:
            logger.error(f"Trend factor analysis failed for {parameter}: {e}")
            return {}

    async def _analyze_seasonal_patterns_ml(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze seasonal patterns using ML."""
        try:
            seasonal = {}

            # Add month and season information
            df_seasonal = df.copy()
            df_seasonal['month'] = df_seasonal.index.month
            df_seasonal['season'] = df_seasonal['month'].map(self._get_season)

            # Analyze seasonal variations
            for param in ['temperature', 'precipitation']:
                if param in df_seasonal.columns:
                    seasonal_stats = df_seasonal.groupby('season')[param].agg([
                        'mean', 'std', 'min', 'max', 'count'
                    ]).to_dict()

                    seasonal[f'{param}_seasonal'] = {
                        season: {
                            'mean': float(seasonal_stats['mean'][season]),
                            'std': float(seasonal_stats['std'][season]),
                            'min': float(seasonal_stats['min'][season]),
                            'max': float(seasonal_stats['max'][season]),
                            'count': int(seasonal_stats['count'][season])
                        }
                        for season in seasonal_stats['mean'].keys()
                    }

            return seasonal

        except Exception as e:
            logger.error(f"ML seasonal analysis failed: {e}")
            return {}

    def _get_season(self, month: int) -> str:
        """Map month to season."""
        if month in [12, 1, 2]:
            return 'Winter'
        elif month in [3, 4, 5]:
            return 'Spring'
        elif month in [6, 7, 8]:
            return 'Summer'
        else:
            return 'Autumn'

    async def _detect_anomalies(self, df: pd.DataFrame, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Detect anomalies using machine learning."""
        try:
            anomalies = {}

            if self.models['anomaly_detector'] is not None:
                feature_cols = [col for col in features_df.columns
                              if col not in ['temperature', 'precipitation', 'quality_score']]

                if len(feature_cols) > 0:
                    X = features_df[feature_cols]
                    X_scaled = self.scalers['features'].transform(X)

                    # Detect anomalies
                    anomaly_labels = self.models['anomaly_detector'].predict(X_scaled)
                    anomaly_scores = self.models['anomaly_detector'].decision_function(X_scaled)

                    # Identify anomalous periods
                    anomaly_indices = np.where(anomaly_labels == -1)[0]

                    anomalies['anomaly_detection'] = {
                        'total_anomalies': len(anomaly_indices),
                        'anomaly_percentage': float(len(anomaly_indices) / len(features_df) * 100),
                        'anomaly_periods': await self._analyze_anomaly_periods(
                            features_df, anomaly_indices, anomaly_scores
                        )
                    }

            return anomalies

        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
            return {}

    async def _analyze_anomaly_periods(self, features_df: pd.DataFrame,
                                     anomaly_indices: np.ndarray,
                                     anomaly_scores: np.ndarray) -> List[Dict[str, Any]]:
        """Analyze detected anomaly periods."""
        try:
            anomaly_periods = []

            for idx in anomaly_indices[:10]:  # Limit to top 10 anomalies
                if idx < len(features_df):
                    period_data = features_df.iloc[idx]

                    anomaly_period = {
                        'timestamp': period_data.name.isoformat(),
                        'anomaly_score': float(anomaly_scores[idx]),
                        'temperature': float(period_data.get('temperature', 0)),
                        'precipitation': float(period_data.get('precipitation', 0)),
                        'severity': self._classify_anomaly_severity(anomaly_scores[idx])
                    }

                    anomaly_periods.append(anomaly_period)

            return sorted(anomaly_periods, key=lambda x: x['anomaly_score'])

        except Exception as e:
            logger.error(f"Anomaly period analysis failed: {e}")
            return []

    def _classify_anomaly_severity(self, anomaly_score: float) -> str:
        """Classify anomaly severity based on score."""
        if anomaly_score < -0.5:
            return 'severe'
        elif anomaly_score < -0.3:
            return 'moderate'
        elif anomaly_score < -0.1:
            return 'mild'
        else:
            return 'minor'

    async def _generate_predictions(self, df: pd.DataFrame, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Generate predictions using trained models."""
        try:
            predictions = {}

            feature_cols = [col for col in features_df.columns
                          if col not in ['temperature', 'precipitation', 'quality_score']]

            if len(feature_cols) == 0:
                return predictions

            # Use last available data for prediction
            last_features = features_df[feature_cols].iloc[-1:].values
            last_features_scaled = self.scalers['features'].transform(last_features)

            # Temperature prediction
            if self.models['temperature_predictor'] is not None:
                temp_pred = self.models['temperature_predictor'].predict(last_features_scaled)[0]
                predictions['temperature_prediction'] = {
                    'predicted_value': float(temp_pred),
                    'prediction_horizon': '1_day',
                    'model_confidence': self._calculate_prediction_confidence('temperature')
                }

            # Precipitation prediction
            if self.models['precipitation_predictor'] is not None:
                precip_pred = self.models['precipitation_predictor'].predict(last_features_scaled)[0]
                predictions['precipitation_prediction'] = {
                    'predicted_value': float(max(0, precip_pred)),  # Ensure non-negative
                    'prediction_horizon': '1_day',
                    'model_confidence': self._calculate_prediction_confidence('precipitation')
                }

            return predictions

        except Exception as e:
            logger.error(f"Prediction generation failed: {e}")
            return {}

    def _calculate_prediction_confidence(self, parameter: str) -> float:
        """Calculate prediction confidence based on model performance."""
        try:
            # Use R² score as confidence measure
            r2_key = f'{parameter}_r2'
            if hasattr(self, 'last_performance') and r2_key in self.last_performance:
                r2_score = self.last_performance[r2_key]
                return max(0.0, min(1.0, r2_score))
            return 0.5  # Default confidence

        except Exception as e:
            logger.warning(f"Confidence calculation failed for {parameter}: {e}")
            return 0.5

    async def _assess_climate_risks(self, df: pd.DataFrame,
                                  pattern_analysis: Dict[str, Any],
                                  anomaly_detection: Dict[str, Any]) -> Dict[str, Any]:
        """Assess climate risks using AI analysis."""
        try:
            risks = {
                'overall_risk_level': 'low',
                'risk_factors': [],
                'risk_timeline': {},
                'mitigation_priorities': []
            }

            # Assess anomaly-based risks
            if 'anomaly_detection' in anomaly_detection:
                anomaly_pct = anomaly_detection['anomaly_detection'].get('anomaly_percentage', 0)

                if anomaly_pct > 15:
                    risks['overall_risk_level'] = 'high'
                    risks['risk_factors'].append('high_anomaly_frequency')
                elif anomaly_pct > 8:
                    risks['overall_risk_level'] = 'medium'
                    risks['risk_factors'].append('moderate_anomaly_frequency')

            # Assess trend-based risks
            if 'trend_analysis' in pattern_analysis:
                trend_analysis = pattern_analysis['trend_analysis']

                # Temperature trend risks
                if 'temperature_trend_factors' in trend_analysis:
                    temp_trends = trend_analysis['temperature_trend_factors']
                    trend_slope = temp_trends.get('trend_slope', 0)

                    if abs(trend_slope) > 0.01:  # Significant temperature trend
                        risks['risk_factors'].append('significant_temperature_trend')
                        if trend_slope > 0:
                            risks['risk_factors'].append('warming_trend')
                        else:
                            risks['risk_factors'].append('cooling_trend')

            # Generate mitigation priorities
            risks['mitigation_priorities'] = await self._generate_mitigation_priorities(risks['risk_factors'])

            return risks

        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'overall_risk_level': 'unknown', 'risk_factors': [], 'mitigation_priorities': []}

    async def _generate_mitigation_priorities(self, risk_factors: List[str]) -> List[str]:
        """Generate mitigation priorities based on risk factors."""
        priorities = []

        if 'high_anomaly_frequency' in risk_factors:
            priorities.extend([
                'Implement enhanced monitoring systems',
                'Develop anomaly response protocols',
                'Increase system resilience'
            ])

        if 'warming_trend' in risk_factors:
            priorities.extend([
                'Upgrade cooling systems',
                'Implement heat management protocols',
                'Optimize energy efficiency'
            ])

        if 'cooling_trend' in risk_factors:
            priorities.extend([
                'Implement freeze protection',
                'Upgrade heating systems',
                'Winterize equipment'
            ])

        return priorities

    async def _generate_optimization_recommendations(self, df: pd.DataFrame,
                                                   pattern_analysis: Dict[str, Any],
                                                   predictive_models: Dict[str, Any]) -> Dict[str, Any]:
        """Generate optimization recommendations."""
        try:
            recommendations = {
                'operational_optimizations': [],
                'infrastructure_recommendations': [],
                'monitoring_enhancements': [],
                'energy_efficiency_measures': []
            }

            # Operational optimizations based on patterns
            if 'climate_clusters' in pattern_analysis:
                clusters = pattern_analysis['climate_clusters']

                # Identify dominant patterns
                largest_cluster = max(clusters.items(), key=lambda x: x[1]['size'])
                cluster_characteristics = largest_cluster[1].get('characteristics', [])

                if 'hot_conditions' in cluster_characteristics:
                    recommendations['operational_optimizations'].extend([
                        'Optimize cooling system operation during hot periods',
                        'Adjust chemical dosing for high temperature conditions',
                        'Implement heat stress monitoring'
                    ])

                if 'wet_conditions' in cluster_characteristics:
                    recommendations['operational_optimizations'].extend([
                        'Increase treatment capacity during wet periods',
                        'Optimize drainage and overflow systems',
                        'Implement flood prevention measures'
                    ])

            # Predictive optimizations
            if 'temperature_prediction' in predictive_models:
                temp_pred = predictive_models['temperature_prediction']
                predicted_temp = temp_pred.get('predicted_value', 20)

                if predicted_temp > 30:
                    recommendations['infrastructure_recommendations'].append(
                        'Prepare enhanced cooling systems for high temperature period'
                    )
                elif predicted_temp < 5:
                    recommendations['infrastructure_recommendations'].append(
                        'Activate freeze protection systems'
                    )

            return recommendations

        except Exception as e:
            logger.error(f"Optimization recommendations generation failed: {e}")
            return {}

    async def _generate_insights(self, df: pd.DataFrame,
                               pattern_analysis: Dict[str, Any],
                               anomaly_detection: Dict[str, Any],
                               predictive_models: Dict[str, Any],
                               risk_assessment: Dict[str, Any]) -> List[ClimateInsight]:
        """Generate AI-powered climate insights."""
        try:
            insights = []

            # Pattern-based insights
            insights.extend(await self._generate_pattern_insights(pattern_analysis))

            # Anomaly-based insights
            insights.extend(await self._generate_anomaly_insights(anomaly_detection))

            # Prediction-based insights
            insights.extend(await self._generate_prediction_insights(predictive_models))

            # Risk-based insights
            insights.extend(await self._generate_risk_insights(risk_assessment))

            # Sort insights by importance and confidence
            insights.sort(key=lambda x: (
                self.insight_thresholds['importance_thresholds'].get(x.importance, 0),
                x.confidence
            ), reverse=True)

            return insights[:20]  # Return top 20 insights

        except Exception as e:
            logger.error(f"Insight generation failed: {e}")
            return []

    async def _generate_pattern_insights(self, pattern_analysis: Dict[str, Any]) -> List[ClimateInsight]:
        """Generate insights from pattern analysis."""
        insights = []

        try:
            if 'climate_clusters' in pattern_analysis:
                clusters = pattern_analysis['climate_clusters']

                # Insight about dominant climate pattern
                largest_cluster = max(clusters.items(), key=lambda x: x[1]['size'])
                cluster_id, cluster_data = largest_cluster

                insight = ClimateInsight(
                    insight_id=f"pattern_dominant_{cluster_id}",
                    insight_type='pattern',
                    title=f"Dominant Climate Pattern Identified",
                    description=f"Climate pattern '{cluster_id}' represents {cluster_data['percentage']:.1f}% of conditions with characteristics: {', '.join(cluster_data['characteristics'])}",
                    confidence=0.8,
                    importance='high',
                    data_sources=['pattern_analysis'],
                    supporting_evidence={'cluster_data': cluster_data},
                    actionable_recommendations=[
                        f"Optimize operations for {cluster_data['characteristics'][0]} conditions",
                        "Adjust system parameters based on dominant pattern"
                    ],
                    timestamp=datetime.now()
                )
                insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Pattern insight generation failed: {e}")
            return []

    async def _generate_anomaly_insights(self, anomaly_detection: Dict[str, Any]) -> List[ClimateInsight]:
        """Generate insights from anomaly detection."""
        insights = []

        try:
            if 'anomaly_detection' in anomaly_detection:
                anomaly_data = anomaly_detection['anomaly_detection']
                anomaly_pct = anomaly_data.get('anomaly_percentage', 0)

                if anomaly_pct > 10:
                    insight = ClimateInsight(
                        insight_id="anomaly_high_frequency",
                        insight_type='anomaly',
                        title="High Frequency of Climate Anomalies Detected",
                        description=f"AI analysis detected {anomaly_pct:.1f}% of periods as anomalous, indicating significant climate variability",
                        confidence=0.85,
                        importance='critical' if anomaly_pct > 20 else 'high',
                        data_sources=['anomaly_detection'],
                        supporting_evidence={'anomaly_data': anomaly_data},
                        actionable_recommendations=[
                            "Implement enhanced monitoring during anomalous periods",
                            "Develop adaptive response protocols",
                            "Review system resilience to climate variability"
                        ],
                        timestamp=datetime.now()
                    )
                    insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Anomaly insight generation failed: {e}")
            return []

    async def _generate_prediction_insights(self, predictive_models: Dict[str, Any]) -> List[ClimateInsight]:
        """Generate insights from predictions."""
        insights = []

        try:
            if 'temperature_prediction' in predictive_models:
                temp_pred = predictive_models['temperature_prediction']
                predicted_temp = temp_pred.get('predicted_value', 20)
                confidence = temp_pred.get('model_confidence', 0.5)

                if predicted_temp > 35 or predicted_temp < 0:
                    insight = ClimateInsight(
                        insight_id="prediction_extreme_temperature",
                        insight_type='prediction',
                        title="Extreme Temperature Predicted",
                        description=f"AI model predicts extreme temperature of {predicted_temp:.1f}°C in the next period",
                        confidence=confidence,
                        importance='critical' if abs(predicted_temp - 20) > 20 else 'high',
                        data_sources=['predictive_models'],
                        supporting_evidence={'prediction_data': temp_pred},
                        actionable_recommendations=[
                            "Prepare temperature management systems",
                            "Adjust operational parameters proactively",
                            "Monitor system performance closely"
                        ],
                        timestamp=datetime.now()
                    )
                    insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Prediction insight generation failed: {e}")
            return []

    async def _generate_risk_insights(self, risk_assessment: Dict[str, Any]) -> List[ClimateInsight]:
        """Generate insights from risk assessment."""
        insights = []

        try:
            overall_risk = risk_assessment.get('overall_risk_level', 'low')
            risk_factors = risk_assessment.get('risk_factors', [])

            if overall_risk in ['high', 'critical']:
                insight = ClimateInsight(
                    insight_id="risk_high_climate_risk",
                    insight_type='recommendation',
                    title=f"{overall_risk.title()} Climate Risk Identified",
                    description=f"AI analysis indicates {overall_risk} climate risk based on factors: {', '.join(risk_factors)}",
                    confidence=0.8,
                    importance=overall_risk,
                    data_sources=['risk_assessment'],
                    supporting_evidence={'risk_data': risk_assessment},
                    actionable_recommendations=risk_assessment.get('mitigation_priorities', []),
                    timestamp=datetime.now()
                )
                insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Risk insight generation failed: {e}")
            return []


# Convenience functions
async def analyze_climate_with_ai(data: List[ProcessedClimateData],
                                location: str = None) -> ClimateAnalysisResult:
    """Analyze climate data using AI-powered agent."""
    agent = ClimateAnalysisAgent()
    await agent.initialize()

    return await agent.analyze_climate_data(data, location)


async def get_climate_insights(data: List[ProcessedClimateData],
                             location: str = None) -> List[ClimateInsight]:
    """Get AI-generated climate insights."""
    agent = ClimateAnalysisAgent()
    await agent.initialize()

    result = await agent.analyze_climate_data(data, location)
    return result.insights if result else []


async def predict_climate_parameters(data: List[ProcessedClimateData],
                                   location: str = None) -> Dict[str, Any]:
    """Predict climate parameters using AI models."""
    agent = ClimateAnalysisAgent()
    await agent.initialize()

    result = await agent.analyze_climate_data(data, location)
    return result.predictive_models if result else {}
