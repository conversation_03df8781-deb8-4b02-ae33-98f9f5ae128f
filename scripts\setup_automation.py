"""
Complete Project Setup Automation.

Automated setup for Docker, PostgreSQL, Redis, environment configuration,
backup systems, and performance optimization tools.
"""

import os
import subprocess
import sys
import json
import shutil
from pathlib import Path
import logging
import asyncio
from typing import Dict, List, Any
import docker
import psycopg2
import redis
import yaml

logger = logging.getLogger(__name__)


class ProjectSetupAutomator:
    """Complete project setup automation."""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.setup_results = {}
        
    async def complete_project_setup(self) -> Dict[str, Any]:
        """Complete automated project setup."""
        try:
            logger.info("Starting complete project setup automation")
            
            # 1. Environment setup
            env_result = await self._setup_environment()
            self.setup_results['environment'] = env_result
            
            # 2. Docker setup
            docker_result = await self._setup_docker_environment()
            self.setup_results['docker'] = docker_result
            
            # 3. Database setup
            db_result = await self._setup_databases()
            self.setup_results['databases'] = db_result
            
            # 4. Backup systems
            backup_result = await self._setup_backup_systems()
            self.setup_results['backup'] = backup_result
            
            # 5. Performance tools
            perf_result = await self._setup_performance_tools()
            self.setup_results['performance'] = perf_result
            
            # 6. Automated scheduling
            schedule_result = await self._setup_automated_scheduling()
            self.setup_results['scheduling'] = schedule_result
            
            # 7. Security setup
            security_result = await self._setup_security_features()
            self.setup_results['security'] = security_result
            
            # 8. Monitoring setup
            monitoring_result = await self._setup_monitoring_stack()
            self.setup_results['monitoring'] = monitoring_result
            
            # 9. CI/CD pipeline
            cicd_result = await self._setup_cicd_pipeline()
            self.setup_results['cicd'] = cicd_result
            
            # 10. Development tools
            dev_result = await self._setup_development_tools()
            self.setup_results['development'] = dev_result
            
            return {
                'status': 'success',
                'setup_results': self.setup_results,
                'components_setup': len(self.setup_results),
                'setup_time': 'automated'
            }
            
        except Exception as e:
            logger.error(f"Project setup failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_environment(self) -> Dict[str, str]:
        """Setup environment configuration."""
        try:
            # Create environment files
            env_template = """
# Water Management System Environment Configuration

# Core API Keys
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
OPENWEATHERMAP_API_KEY=your_openweathermap_api_key_here
NASA_API_KEY=your_nasa_api_key_here
NOAA_API_KEY=your_noaa_api_key_here
WORLD_BANK_API_KEY=your_worldbank_api_key_here
ECMWF_API_KEY=your_ecmwf_api_key_here
HUGGINGFACE_TOKEN=your_huggingface_token_here

# Database Configuration
DATABASE_URL=********************************************************/water_management
REDIS_URL=redis://:redis_water_2024@redis:6379/0

# Application Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
DEBUG=false
SECRET_KEY=ultra_secure_secret_key_2024
JWT_SECRET=jwt_secret_key_2024

# Performance Configuration
MAX_WORKERS=8
CACHE_TTL=3600
BATCH_SIZE=1000
ASYNC_POOL_SIZE=20

# Monitoring Configuration
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Security Configuration
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=100
ENABLE_CORS=true
ALLOWED_ORIGINS=*

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=24
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/backups
"""
            
            # Write environment files
            with open('.env.example', 'w') as f:
                f.write(env_template)
            
            with open('.env.production', 'w') as f:
                f.write(env_template.replace('DEBUG=false', 'DEBUG=false\nENVIRONMENT=production'))
            
            with open('.env.development', 'w') as f:
                f.write(env_template.replace('DEBUG=false', 'DEBUG=true\nENVIRONMENT=development'))
            
            return {'status': 'success', 'files_created': 3}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_docker_environment(self) -> Dict[str, str]:
        """Setup Docker environment with optimization."""
        try:
            # Create optimized Dockerfile
            optimized_dockerfile = """
# Multi-stage optimized Dockerfile for Water Management System
FROM python:3.9-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \\
    gcc g++ libpq-dev curl git \\
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install requirements
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \\
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.9-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    libpq5 curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Create app user
RUN useradd --create-home --shell /bin/bash app
WORKDIR /app

# Copy application code
COPY --chown=app:app . .

# Create necessary directories
RUN mkdir -p data/climate data/water_quality data/energy data/processed \\
    logs config backups temp

# Set permissions
RUN chown -R app:app /app

# Switch to app user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Expose ports
EXPOSE 8501 8000

# Default command
CMD ["streamlit", "run", "src/app.py", "--server.port=8501", "--server.address=0.0.0.0"]
"""
            
            with open('Dockerfile.optimized', 'w') as f:
                f.write(optimized_dockerfile)
            
            # Create Docker Compose override for development
            dev_compose = """
version: '3.8'

services:
  water_app:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    volumes:
      - .:/app
      - /app/venv
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    ports:
      - "8501:8501"
      - "8000:8000"
      - "5678:5678"  # Debug port
"""
            
            with open('docker-compose.dev.yml', 'w') as f:
                f.write(dev_compose)
            
            return {'status': 'success', 'files_created': 2}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_databases(self) -> Dict[str, str]:
        """Setup database with advanced configuration."""
        try:
            # Create advanced database initialization
            advanced_init = """
-- Advanced Water Management Database Setup
-- PostgreSQL with TimescaleDB and advanced indexing

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "timescaledb";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create advanced indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_climate_data_location_time 
ON climate_data USING GIST(location, timestamp);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_water_quality_facility_time 
ON water_quality(facility_id, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_energy_consumption_time_type 
ON energy_consumption(timestamp DESC, energy_type);

-- Create materialized views for performance
CREATE MATERIALIZED VIEW IF NOT EXISTS daily_facility_summary AS
SELECT 
    facility_id,
    DATE(timestamp) as date,
    AVG(efficiency) as avg_efficiency,
    SUM(energy_consumption) as total_energy,
    AVG(water_quality_score) as avg_quality,
    COUNT(*) as measurement_count
FROM (
    SELECT f.id as facility_id, wq.timestamp, 
           f.efficiency, ec.consumption as energy_consumption,
           (wq.ph + wq.turbidity + wq.dissolved_oxygen) / 3 as water_quality_score
    FROM water_facilities f
    LEFT JOIN water_quality wq ON f.id = wq.facility_id
    LEFT JOIN energy_consumption ec ON f.id = ec.facility_id
    WHERE wq.timestamp >= CURRENT_DATE - INTERVAL '30 days'
) daily_data
GROUP BY facility_id, DATE(timestamp);

-- Create refresh function
CREATE OR REPLACE FUNCTION refresh_daily_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_facility_summary;
END;
$$ LANGUAGE plpgsql;

-- Create automated refresh
SELECT cron.schedule('refresh-daily-summary', '0 1 * * *', 'SELECT refresh_daily_summary();');

-- Create partitioning for large tables
CREATE TABLE IF NOT EXISTS climate_data_partitioned (
    LIKE climate_data INCLUDING ALL
) PARTITION BY RANGE (timestamp);

-- Create monthly partitions
DO $$
DECLARE
    start_date date := '2024-01-01';
    end_date date := '2025-12-31';
    current_date date := start_date;
BEGIN
    WHILE current_date < end_date LOOP
        EXECUTE format('CREATE TABLE IF NOT EXISTS climate_data_%s PARTITION OF climate_data_partitioned 
                       FOR VALUES FROM (%L) TO (%L)',
                       to_char(current_date, 'YYYY_MM'),
                       current_date,
                       current_date + interval '1 month');
        current_date := current_date + interval '1 month';
    END LOOP;
END $$;

-- Create database maintenance procedures
CREATE OR REPLACE FUNCTION maintain_database()
RETURNS void AS $$
BEGIN
    -- Update statistics
    ANALYZE;
    
    -- Reindex if needed
    REINDEX DATABASE water_management;
    
    -- Clean old logs
    DELETE FROM system_logs WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Vacuum
    VACUUM ANALYZE;
END;
$$ LANGUAGE plpgsql;

-- Schedule maintenance
SELECT cron.schedule('database-maintenance', '0 2 * * 0', 'SELECT maintain_database();');
"""
            
            with open('sql/advanced_init.sql', 'w') as f:
                f.write(advanced_init)
            
            # Create Redis configuration
            redis_config = """
# Redis configuration for Water Management System

# Memory optimization
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
requirepass redis_water_2024
rename-command FLUSHDB ""
rename-command FLUSHALL ""

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Clustering (if needed)
# cluster-enabled yes
# cluster-config-file nodes.conf
# cluster-node-timeout 15000
"""
            
            os.makedirs('config/redis', exist_ok=True)
            with open('config/redis/redis.conf', 'w') as f:
                f.write(redis_config)
            
            return {'status': 'success', 'configurations': 2}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_backup_systems(self) -> Dict[str, str]:
        """Setup automated backup systems."""
        try:
            # Create backup script
            backup_script = """#!/bin/bash
# Automated Backup System for Water Management

set -e

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
echo "Starting database backup..."
docker exec water_management_db pg_dump -U water_admin water_management > $BACKUP_DIR/db_backup_$DATE.sql
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Redis backup
echo "Starting Redis backup..."
docker exec water_management_cache redis-cli --rdb $BACKUP_DIR/redis_backup_$DATE.rdb

# Application data backup
echo "Starting application data backup..."
tar -czf $BACKUP_DIR/app_data_$DATE.tar.gz data/ logs/ config/

# Clean old backups
echo "Cleaning old backups..."
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.rdb" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup completed successfully: $DATE"
"""
            
            os.makedirs('scripts', exist_ok=True)
            with open('scripts/backup.sh', 'w') as f:
                f.write(backup_script)
            
            # Make executable
            os.chmod('scripts/backup.sh', 0o755)
            
            # Create restore script
            restore_script = """#!/bin/bash
# Restore System for Water Management

set -e

if [ $# -eq 0 ]; then
    echo "Usage: $0 <backup_date>"
    echo "Example: $0 20240115_120000"
    exit 1
fi

BACKUP_DATE=$1
BACKUP_DIR="/backups"

# Restore database
echo "Restoring database..."
gunzip -c $BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz | docker exec -i water_management_db psql -U water_admin water_management

# Restore Redis
echo "Restoring Redis..."
docker cp $BACKUP_DIR/redis_backup_$BACKUP_DATE.rdb water_management_cache:/data/dump.rdb
docker restart water_management_cache

# Restore application data
echo "Restoring application data..."
tar -xzf $BACKUP_DIR/app_data_$BACKUP_DATE.tar.gz

echo "Restore completed successfully"
"""
            
            with open('scripts/restore.sh', 'w') as f:
                f.write(restore_script)
            
            os.chmod('scripts/restore.sh', 0o755)
            
            return {'status': 'success', 'scripts_created': 2}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_performance_tools(self) -> Dict[str, str]:
        """Setup performance monitoring and optimization tools."""
        try:
            # Create performance monitoring script
            perf_monitor = """#!/usr/bin/env python3
# Performance Monitoring Tool

import psutil
import time
import json
import logging
from datetime import datetime
import asyncio
import aiohttp

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
        
    async def collect_system_metrics(self):
        \"\"\"Collect system performance metrics.\"\"\"
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'network_io': dict(psutil.net_io_counters()._asdict()),
            'process_count': len(psutil.pids()),
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
        }
    
    async def collect_application_metrics(self):
        \"\"\"Collect application-specific metrics.\"\"\"
        try:
            async with aiohttp.ClientSession() as session:
                # Health check
                async with session.get('http://localhost:8501/_stcore/health') as resp:
                    health_status = resp.status == 200
                
                # Response time check
                start_time = time.time()
                async with session.get('http://localhost:8501') as resp:
                    response_time = time.time() - start_time
                
                return {
                    'health_status': health_status,
                    'response_time': response_time,
                    'status_code': resp.status
                }
        except Exception as e:
            return {
                'health_status': False,
                'response_time': 999,
                'error': str(e)
            }
    
    async def monitor_continuously(self, interval=60):
        \"\"\"Continuously monitor performance.\"\"\"
        while True:
            try:
                system_metrics = await self.collect_system_metrics()
                app_metrics = await self.collect_application_metrics()
                
                combined_metrics = {
                    'system': system_metrics,
                    'application': app_metrics
                }
                
                # Log metrics
                logging.info(f"Performance metrics: {json.dumps(combined_metrics, indent=2)}")
                
                # Store metrics (could be sent to monitoring system)
                self.metrics[datetime.now().isoformat()] = combined_metrics
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logging.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(interval)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    monitor = PerformanceMonitor()
    asyncio.run(monitor.monitor_continuously())
"""
            
            with open('scripts/performance_monitor.py', 'w') as f:
                f.write(perf_monitor)
            
            os.chmod('scripts/performance_monitor.py', 0o755)
            
            return {'status': 'success', 'tools_created': 1}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_automated_scheduling(self) -> Dict[str, str]:
        """Setup automated task scheduling."""
        try:
            # Create cron configuration
            cron_config = """
# Water Management System Cron Jobs

# Backup every day at 2 AM
0 2 * * * /app/scripts/backup.sh >> /var/log/backup.log 2>&1

# Performance monitoring every 5 minutes
*/5 * * * * /usr/bin/python3 /app/scripts/performance_monitor.py >> /var/log/performance.log 2>&1

# Database maintenance every Sunday at 3 AM
0 3 * * 0 /app/scripts/db_maintenance.sh >> /var/log/maintenance.log 2>&1

# Log rotation every day at 1 AM
0 1 * * * /usr/sbin/logrotate /app/config/logrotate.conf

# Health check every minute
* * * * * curl -f http://localhost:8501/_stcore/health || echo "Health check failed" >> /var/log/health.log

# Data cleanup every week
0 4 * * 1 /app/scripts/cleanup_old_data.sh >> /var/log/cleanup.log 2>&1
"""
            
            os.makedirs('config/cron', exist_ok=True)
            with open('config/cron/crontab', 'w') as f:
                f.write(cron_config)
            
            return {'status': 'success', 'schedules_created': 6}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_security_features(self) -> Dict[str, str]:
        """Setup security features."""
        try:
            # Create security configuration
            security_config = """
# Security Configuration for Water Management System

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Authentication
JWT_SECRET_KEY=ultra_secure_jwt_key_2024
JWT_EXPIRATION_HOURS=24
SESSION_TIMEOUT_MINUTES=30

# CORS
CORS_ENABLED=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8501"]
CORS_METHODS=["GET", "POST", "PUT", "DELETE"]

# SSL/TLS
SSL_ENABLED=true
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem

# Input validation
ENABLE_INPUT_SANITIZATION=true
MAX_REQUEST_SIZE=10MB
ALLOWED_FILE_TYPES=["json", "csv", "xlsx"]

# Logging
SECURITY_LOG_ENABLED=true
LOG_FAILED_ATTEMPTS=true
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
"""
            
            os.makedirs('config/security', exist_ok=True)
            with open('config/security/security.conf', 'w') as f:
                f.write(security_config)
            
            return {'status': 'success', 'security_configs': 1}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_monitoring_stack(self) -> Dict[str, str]:
        """Setup comprehensive monitoring stack."""
        try:
            # Create Prometheus configuration
            prometheus_config = """
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'water-management'
    static_configs:
      - targets: ['water_app:8000']
    scrape_interval: 5s
    metrics_path: /metrics

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
"""
            
            os.makedirs('monitoring/prometheus', exist_ok=True)
            with open('monitoring/prometheus/prometheus.yml', 'w') as f:
                f.write(prometheus_config)
            
            return {'status': 'success', 'monitoring_configs': 1}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_cicd_pipeline(self) -> Dict[str, str]:
        """Setup CI/CD pipeline."""
        try:
            # Create GitHub Actions workflow
            github_workflow = """
name: Water Management CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Run tests
      run: |
        pytest tests/ -v --cov=src --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -t water-management:latest .
    
    - name: Run security scan
      run: |
        docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \\
          aquasec/trivy image water-management:latest

  deploy:
    needs: [test, build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      run: |
        echo "Deploying to production..."
        # Add deployment commands here
"""
            
            os.makedirs('.github/workflows', exist_ok=True)
            with open('.github/workflows/ci-cd.yml', 'w') as f:
                f.write(github_workflow)
            
            return {'status': 'success', 'pipelines_created': 1}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    async def _setup_development_tools(self) -> Dict[str, str]:
        """Setup development tools and utilities."""
        try:
            # Create development utilities
            dev_utils = """#!/usr/bin/env python3
# Development Utilities for Water Management System

import subprocess
import sys
import os
from pathlib import Path

class DevUtils:
    def __init__(self):
        self.project_root = Path.cwd()
    
    def setup_dev_environment(self):
        \"\"\"Setup development environment.\"\"\"
        print("Setting up development environment...")
        
        # Install pre-commit hooks
        subprocess.run(['pre-commit', 'install'], check=True)
        
        # Setup virtual environment
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        
        # Install development dependencies
        subprocess.run(['pip', 'install', '-r', 'requirements-dev.txt'], check=True)
        
        print("Development environment setup complete!")
    
    def run_tests(self):
        \"\"\"Run all tests.\"\"\"
        subprocess.run(['pytest', 'tests/', '-v', '--cov=src'], check=True)
    
    def run_linting(self):
        \"\"\"Run code linting.\"\"\"
        subprocess.run(['flake8', 'src/'], check=True)
        subprocess.run(['black', 'src/', '--check'], check=True)
        subprocess.run(['isort', 'src/', '--check-only'], check=True)
    
    def format_code(self):
        \"\"\"Format code.\"\"\"
        subprocess.run(['black', 'src/'], check=True)
        subprocess.run(['isort', 'src/'], check=True)
    
    def generate_docs(self):
        \"\"\"Generate documentation.\"\"\"
        subprocess.run(['sphinx-build', 'docs/', 'docs/_build/'], check=True)

if __name__ == "__main__":
    utils = DevUtils()
    
    if len(sys.argv) < 2:
        print("Usage: python dev_utils.py <command>")
        print("Commands: setup, test, lint, format, docs")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "setup":
        utils.setup_dev_environment()
    elif command == "test":
        utils.run_tests()
    elif command == "lint":
        utils.run_linting()
    elif command == "format":
        utils.format_code()
    elif command == "docs":
        utils.generate_docs()
    else:
        print(f"Unknown command: {command}")
"""
            
            with open('scripts/dev_utils.py', 'w') as f:
                f.write(dev_utils)
            
            os.chmod('scripts/dev_utils.py', 0o755)
            
            return {'status': 'success', 'dev_tools': 1}
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}


# Convenience function
async def setup_complete_project():
    """Setup complete project infrastructure."""
    automator = ProjectSetupAutomator()
    return await automator.complete_project_setup()


if __name__ == "__main__":
    import asyncio
    result = asyncio.run(setup_complete_project())
    print(f"Project setup result: {result}")
