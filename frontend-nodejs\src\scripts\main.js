/**
 * Water Management Application - Main Entry Point
 * Node.js Frontend for Water Management Decarbonisation System
 */

(function() {
    'use strict';

    // Global application namespace
    window.WaterManagementApp = {
        // Application state
        state: {
            currentPage: 'overview',
            isInitialized: false,
            isConnected: false,
            lastUpdate: null,
            user: null,
            settings: {}
        },

        // Core modules
        modules: {
            api: null,
            websocket: null,
            navigation: null,
            dashboard: null,
            pageManager: null,
            charts: null
        },

        // Configuration
        config: {
            apiBaseUrl: '/api',
            websocketUrl: window.location.origin,
            updateInterval: 30000,
            retryAttempts: 3,
            retryDelay: 1000
        },

        // Initialize the application
        async init(initialPage = 'overview') {
            console.log('🚀 Initializing Water Management Application...');
            
            try {
                // Show loading screen
                this.showLoadingScreen();
                
                // Initialize core modules
                await this.initializeModules();
                
                // Set initial page
                this.state.currentPage = initialPage;
                
                // Setup event listeners
                this.setupEventListeners();
                
                // Connect to WebSocket
                await this.connectWebSocket();
                
                // Load initial data
                await this.loadInitialData();
                
                // Initialize navigation
                this.modules.navigation.init();
                
                // Load the initial page
                await this.modules.pageManager.loadPage(initialPage);
                
                // Mark as initialized
                this.state.isInitialized = true;
                
                // Hide loading screen and show app
                this.hideLoadingScreen();
                
                console.log('✅ Water Management Application initialized successfully');
                
                // Show success notification
                this.showNotification('System initialized successfully', 'success');
                
            } catch (error) {
                console.error('❌ Failed to initialize application:', error);
                this.showErrorScreen(error);
            }
        },

        // Initialize core modules
        async initializeModules() {
            console.log('🔧 Initializing core modules...');
            
            // Initialize API client
            this.modules.api = new window.APIClient(this.config.apiBaseUrl);
            
            // Initialize WebSocket client
            this.modules.websocket = new window.WebSocketClient(this.config.websocketUrl);
            
            // Initialize navigation
            this.modules.navigation = new window.NavigationManager();
            
            // Initialize dashboard
            this.modules.dashboard = new window.DashboardManager();
            
            // Initialize page manager
            this.modules.pageManager = new window.PageManager();
            
            // Initialize charts
            this.modules.charts = new window.ChartsManager();
            
            console.log('✅ Core modules initialized');
        },

        // Setup global event listeners
        setupEventListeners() {
            // Handle page visibility changes
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    console.log('📱 Page hidden - pausing updates');
                    this.pauseUpdates();
                } else {
                    console.log('📱 Page visible - resuming updates');
                    this.resumeUpdates();
                }
            });

            // Handle window resize
            window.addEventListener('resize', this.debounce(() => {
                this.handleResize();
            }, 250));

            // Handle beforeunload
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });

            // Handle keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                this.handleKeyboardShortcuts(e);
            });

            // Handle connection status changes
            this.modules.websocket.on('connect', () => {
                this.updateConnectionStatus(true);
            });

            this.modules.websocket.on('disconnect', () => {
                this.updateConnectionStatus(false);
            });

            // Handle data updates
            this.modules.websocket.on('data-update', (data) => {
                this.handleDataUpdate(data);
            });

            // Handle errors
            this.modules.websocket.on('error', (error) => {
                console.error('WebSocket error:', error);
                this.showNotification('Connection error occurred', 'error');
            });
        },

        // Connect to WebSocket
        async connectWebSocket() {
            console.log('🔌 Connecting to WebSocket...');
            
            try {
                await this.modules.websocket.connect();
                console.log('✅ WebSocket connected');
            } catch (error) {
                console.error('❌ WebSocket connection failed:', error);
                // Continue without WebSocket - use polling instead
                this.startPolling();
            }
        },

        // Load initial application data
        async loadInitialData() {
            console.log('📊 Loading initial data...');
            
            try {
                // Load system health
                const health = await this.modules.api.get('/health');
                console.log('System health:', health);
                
                // Load dashboard data
                const dashboardData = await this.modules.api.get('/realtime/dashboard');
                
                // Update dashboard with initial data
                this.modules.dashboard.updateData(dashboardData.data);
                
                // Update last update time
                this.updateLastUpdateTime();
                
                console.log('✅ Initial data loaded');
                
            } catch (error) {
                console.error('❌ Failed to load initial data:', error);
                this.showNotification('Failed to load initial data', 'warning');
            }
        },

        // Show loading screen
        showLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            const app = document.getElementById('app');
            
            if (loadingScreen) {
                loadingScreen.style.display = 'flex';
                this.updateLoadingProgress(0);
            }
            
            if (app) {
                app.style.display = 'none';
            }
        },

        // Hide loading screen
        hideLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            const app = document.getElementById('app');
            
            // Complete loading progress
            this.updateLoadingProgress(100);
            
            setTimeout(() => {
                if (loadingScreen) {
                    loadingScreen.style.display = 'none';
                }
                
                if (app) {
                    app.style.display = 'block';
                }
            }, 500);
        },

        // Update loading progress
        updateLoadingProgress(percentage) {
            const progressBar = document.getElementById('loading-progress-bar');
            const loadingText = document.querySelector('.loading-text');
            
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
            }
            
            if (loadingText) {
                const messages = [
                    'Initializing system...',
                    'Connecting to backend...',
                    'Loading dashboard data...',
                    'Setting up real-time updates...',
                    'Finalizing initialization...'
                ];
                
                const messageIndex = Math.floor((percentage / 100) * (messages.length - 1));
                loadingText.textContent = messages[messageIndex] || 'Loading...';
            }
        },

        // Show error screen
        showErrorScreen(error) {
            const errorScreen = document.getElementById('error-screen');
            const app = document.getElementById('app');
            const loadingScreen = document.getElementById('loading-screen');
            
            // Hide other screens
            if (loadingScreen) loadingScreen.style.display = 'none';
            if (app) app.style.display = 'none';
            
            // Show error screen
            if (errorScreen) {
                errorScreen.style.display = 'flex';
                
                const errorMessage = document.getElementById('error-message');
                const errorStack = document.getElementById('error-stack');
                
                if (errorMessage) {
                    errorMessage.textContent = error.message || 'An unexpected error occurred';
                }
                
                if (errorStack) {
                    errorStack.textContent = error.stack || 'No stack trace available';
                }
            }
        },

        // Update connection status
        updateConnectionStatus(isConnected) {
            this.state.isConnected = isConnected;
            
            const statusIndicator = document.getElementById('connection-status');
            const statusText = document.getElementById('connection-text');
            
            if (statusIndicator && statusText) {
                if (isConnected) {
                    statusIndicator.className = 'fas fa-circle status-online';
                    statusText.textContent = 'Connected';
                } else {
                    statusIndicator.className = 'fas fa-circle status-offline';
                    statusText.textContent = 'Disconnected';
                }
            }
        },

        // Handle data updates
        handleDataUpdate(data) {
            console.log('📊 Received data update:', data);
            
            // Update dashboard
            this.modules.dashboard.updateData(data);
            
            // Update last update time
            this.updateLastUpdateTime();
            
            // Update charts if visible
            if (this.modules.charts) {
                this.modules.charts.updateCharts(data);
            }
        },

        // Update last update time
        updateLastUpdateTime() {
            this.state.lastUpdate = new Date();
            
            const lastUpdateElement = document.getElementById('last-update-time');
            if (lastUpdateElement) {
                lastUpdateElement.textContent = `Last Updated: ${this.state.lastUpdate.toLocaleTimeString()}`;
            }
        },

        // Handle window resize
        handleResize() {
            // Resize charts
            if (this.modules.charts) {
                this.modules.charts.resizeCharts();
            }
            
            // Update dashboard layout
            if (this.modules.dashboard) {
                this.modules.dashboard.handleResize();
            }
        },

        // Handle keyboard shortcuts
        handleKeyboardShortcuts(e) {
            // Ctrl/Cmd + R: Refresh data
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                this.refreshData();
            }
            
            // Escape: Close modals
            if (e.key === 'Escape') {
                this.closeModals();
            }
            
            // F11: Toggle fullscreen
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
        },

        // Utility functions
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Show notification
        showNotification(message, type = 'info', duration = 5000) {
            console.log(`📢 Notification (${type}): ${message}`);
            
            // Implementation would create and show toast notification
            // For now, just log to console
        },

        // Refresh data
        async refreshData() {
            console.log('🔄 Refreshing data...');
            
            try {
                await this.loadInitialData();
                this.showNotification('Data refreshed successfully', 'success');
            } catch (error) {
                console.error('❌ Failed to refresh data:', error);
                this.showNotification('Failed to refresh data', 'error');
            }
        },

        // Close modals
        closeModals() {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                modal.classList.remove('show');
            });
        },

        // Toggle fullscreen
        toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        },

        // Start polling (fallback when WebSocket fails)
        startPolling() {
            console.log('📡 Starting polling mode...');
            
            setInterval(async () => {
                if (!this.state.isConnected) {
                    try {
                        await this.loadInitialData();
                    } catch (error) {
                        console.error('Polling error:', error);
                    }
                }
            }, this.config.updateInterval);
        },

        // Pause updates
        pauseUpdates() {
            if (this.modules.websocket) {
                this.modules.websocket.pause();
            }
        },

        // Resume updates
        resumeUpdates() {
            if (this.modules.websocket) {
                this.modules.websocket.resume();
            }
        },

        // Cleanup
        cleanup() {
            console.log('🧹 Cleaning up application...');
            
            if (this.modules.websocket) {
                this.modules.websocket.disconnect();
            }
            
            // Clear any intervals/timeouts
            // Remove event listeners
            // Clean up resources
        }
    };

    // Auto-initialize if not already done
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // Will be initialized by the template script
        });
    }

})();
