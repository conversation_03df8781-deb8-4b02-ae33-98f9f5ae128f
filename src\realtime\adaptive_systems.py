"""
Real-Time Adaptive Systems for Water Management.

Online learning algorithms, adaptive control systems, edge computing solutions,
and real-time optimization for water treatment processes.
"""

import numpy as np
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import threading
import queue
import time
from collections import deque

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class AdaptationStrategy(Enum):
    """Adaptation strategies for real-time systems."""
    REACTIVE = "reactive"
    PREDICTIVE = "predictive"
    HYBRID = "hybrid"


class ControlMode(Enum):
    """Control modes for adaptive systems."""
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    SEMI_AUTOMATIC = "semi_automatic"


@dataclass
class RealTimeData:
    """Real-time data structure."""
    timestamp: datetime
    sensor_data: Dict[str, float]
    system_state: Dict[str, Any]
    control_actions: Dict[str, float]
    performance_metrics: Dict[str, float]


@dataclass
class AdaptiveControlConfig:
    """Configuration for adaptive control systems."""
    adaptation_rate: float = 0.1
    learning_rate: float = 0.01
    memory_size: int = 1000
    update_frequency: float = 1.0  # Hz
    prediction_horizon: int = 10
    control_constraints: Dict[str, tuple] = field(default_factory=dict)


class OnlineLearningAlgorithm:
    """Online learning algorithm for real-time adaptation."""
    
    def __init__(self, learning_rate: float = 0.01, forgetting_factor: float = 0.95):
        self.learning_rate = learning_rate
        self.forgetting_factor = forgetting_factor
        self.model_parameters = {}
        self.prediction_error_history = deque(maxlen=100)
        
    @log_async_function_call
    async def update_model(self, input_data: np.ndarray, target: float) -> Dict[str, Any]:
        """Update model parameters using online learning."""
        try:
            # Initialize parameters if first update
            if not self.model_parameters:
                self.model_parameters = {
                    'weights': np.random.normal(0, 0.1, len(input_data)),
                    'bias': 0.0,
                    'variance': 1.0
                }
            
            # Make prediction with current model
            prediction = await self._predict(input_data)
            
            # Calculate prediction error
            error = target - prediction
            self.prediction_error_history.append(abs(error))
            
            # Update weights using gradient descent
            gradient = error * input_data
            self.model_parameters['weights'] += self.learning_rate * gradient
            self.model_parameters['bias'] += self.learning_rate * error
            
            # Update variance estimate
            self.model_parameters['variance'] = (
                self.forgetting_factor * self.model_parameters['variance'] +
                (1 - self.forgetting_factor) * error**2
            )
            
            # Calculate model confidence
            confidence = 1.0 / (1.0 + self.model_parameters['variance'])
            
            return {
                'prediction': prediction,
                'error': error,
                'confidence': confidence,
                'model_parameters': self.model_parameters.copy(),
                'update_successful': True
            }
            
        except Exception as e:
            logger.error(f"Online learning update failed: {e}")
            return {'update_successful': False, 'error': str(e)}
    
    async def _predict(self, input_data: np.ndarray) -> float:
        """Make prediction with current model."""
        if not self.model_parameters:
            return 0.0
        
        prediction = (np.dot(self.model_parameters['weights'], input_data) + 
                     self.model_parameters['bias'])
        return prediction
    
    async def predict_with_uncertainty(self, input_data: np.ndarray) -> Dict[str, float]:
        """Make prediction with uncertainty estimate."""
        prediction = await self._predict(input_data)
        uncertainty = np.sqrt(self.model_parameters.get('variance', 1.0))
        
        return {
            'prediction': prediction,
            'uncertainty': uncertainty,
            'confidence_interval_lower': prediction - 1.96 * uncertainty,
            'confidence_interval_upper': prediction + 1.96 * uncertainty
        }
    
    def get_model_performance(self) -> Dict[str, float]:
        """Get current model performance metrics."""
        if not self.prediction_error_history:
            return {'mae': 0.0, 'rmse': 0.0, 'stability': 1.0}
        
        errors = list(self.prediction_error_history)
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean([e**2 for e in errors]))
        
        # Calculate stability (inverse of error variance)
        error_variance = np.var(errors) if len(errors) > 1 else 0.0
        stability = 1.0 / (1.0 + error_variance)
        
        return {
            'mae': mae,
            'rmse': rmse,
            'stability': stability,
            'recent_error_trend': self._calculate_error_trend()
        }
    
    def _calculate_error_trend(self) -> str:
        """Calculate trend in recent prediction errors."""
        if len(self.prediction_error_history) < 10:
            return 'insufficient_data'
        
        recent_errors = list(self.prediction_error_history)[-10:]
        early_avg = np.mean(recent_errors[:5])
        late_avg = np.mean(recent_errors[5:])
        
        if late_avg < early_avg * 0.9:
            return 'improving'
        elif late_avg > early_avg * 1.1:
            return 'degrading'
        else:
            return 'stable'


class AdaptiveController:
    """Adaptive control system for water treatment processes."""
    
    def __init__(self, config: AdaptiveControlConfig):
        self.config = config
        self.control_mode = ControlMode.AUTOMATIC
        self.adaptation_strategy = AdaptationStrategy.HYBRID
        
        # Control components
        self.online_learner = OnlineLearningAlgorithm(config.learning_rate)
        self.control_history = deque(maxlen=config.memory_size)
        self.setpoint_history = deque(maxlen=config.memory_size)
        
        # Control parameters
        self.pid_parameters = {'kp': 1.0, 'ki': 0.1, 'kd': 0.01}
        self.integral_error = 0.0
        self.previous_error = 0.0
        
        # Real-time processing
        self.is_running = False
        self.control_thread = None
        self.data_queue = queue.Queue()
        
    @log_async_function_call
    async def start_adaptive_control(self) -> Dict[str, Any]:
        """Start the adaptive control system."""
        try:
            logger.info("Starting adaptive control system")
            
            self.is_running = True
            
            # Start control loop in separate thread
            self.control_thread = threading.Thread(target=self._control_loop)
            self.control_thread.daemon = True
            self.control_thread.start()
            
            return {
                'status': 'success',
                'control_mode': self.control_mode.value,
                'adaptation_strategy': self.adaptation_strategy.value,
                'update_frequency': self.config.update_frequency,
                'start_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to start adaptive control: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _control_loop(self):
        """Main control loop running in separate thread."""
        while self.is_running:
            try:
                # Get latest data
                if not self.data_queue.empty():
                    data = self.data_queue.get_nowait()
                    
                    # Process data and calculate control action
                    control_action = asyncio.run(self._calculate_control_action(data))
                    
                    # Apply control action
                    asyncio.run(self._apply_control_action(control_action, data))
                
                # Sleep for update frequency
                time.sleep(1.0 / self.config.update_frequency)
                
            except Exception as e:
                logger.error(f"Control loop error: {e}")
                time.sleep(1.0)
    
    async def process_real_time_data(self, data: RealTimeData) -> Dict[str, Any]:
        """Process incoming real-time data."""
        try:
            # Add data to queue for control loop
            self.data_queue.put(data)
            
            # Update online learning model
            if len(self.control_history) > 0:
                # Use previous control action and current performance for learning
                previous_control = self.control_history[-1]
                current_performance = data.performance_metrics.get('efficiency', 0.0)
                
                # Create input vector from sensor data and control actions
                input_vector = np.array(list(data.sensor_data.values()) + 
                                      list(previous_control.values()))
                
                # Update model
                learning_result = await self.online_learner.update_model(
                    input_vector, current_performance
                )
                
                # Adapt control parameters if needed
                if learning_result['update_successful']:
                    await self._adapt_control_parameters(learning_result)
            
            return {
                'status': 'success',
                'data_processed': True,
                'queue_size': self.data_queue.qsize(),
                'model_performance': self.online_learner.get_model_performance()
            }
            
        except Exception as e:
            logger.error(f"Real-time data processing failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_control_action(self, data: RealTimeData) -> Dict[str, float]:
        """Calculate control action based on current data."""
        control_action = {}
        
        # Get current setpoints (would come from optimization or operator)
        setpoints = {
            'flow_rate': 2000.0,
            'chemical_dose': 1.5,
            'ph_target': 7.2
        }
        
        # Calculate control actions for each controlled variable
        for variable, setpoint in setpoints.items():
            if variable in data.sensor_data:
                current_value = data.sensor_data[variable]
                
                # Calculate PID control action
                error = setpoint - current_value
                
                # Proportional term
                proportional = self.pid_parameters['kp'] * error
                
                # Integral term
                self.integral_error += error
                integral = self.pid_parameters['ki'] * self.integral_error
                
                # Derivative term
                derivative = self.pid_parameters['kd'] * (error - self.previous_error)
                self.previous_error = error
                
                # Combined PID output
                pid_output = proportional + integral + derivative
                
                # Apply constraints
                if variable in self.config.control_constraints:
                    min_val, max_val = self.config.control_constraints[variable]
                    pid_output = np.clip(pid_output, min_val, max_val)
                
                control_action[variable] = pid_output
        
        return control_action
    
    async def _apply_control_action(self, control_action: Dict[str, float], 
                                  data: RealTimeData):
        """Apply calculated control action to the system."""
        # Store control action in history
        self.control_history.append(control_action)
        
        # In a real system, this would send commands to actuators
        logger.debug(f"Applied control action: {control_action}")
        
        # Simulate control action application
        for variable, action in control_action.items():
            # This would interface with actual control hardware
            pass
    
    async def _adapt_control_parameters(self, learning_result: Dict[str, Any]):
        """Adapt control parameters based on learning results."""
        if self.adaptation_strategy == AdaptationStrategy.REACTIVE:
            # Adapt based on recent performance
            if learning_result['error'] > 0.1:  # High error threshold
                # Increase proportional gain
                self.pid_parameters['kp'] *= 1.05
            elif learning_result['error'] < 0.01:  # Low error threshold
                # Decrease proportional gain slightly
                self.pid_parameters['kp'] *= 0.98
        
        elif self.adaptation_strategy == AdaptationStrategy.PREDICTIVE:
            # Use model predictions to adapt parameters
            confidence = learning_result.get('confidence', 0.5)
            if confidence > 0.8:
                # High confidence - make larger adaptations
                adaptation_factor = self.config.adaptation_rate * 2.0
            else:
                # Low confidence - make smaller adaptations
                adaptation_factor = self.config.adaptation_rate * 0.5
            
            # Adapt based on model parameters
            model_params = learning_result.get('model_parameters', {})
            if 'weights' in model_params:
                weight_magnitude = np.linalg.norm(model_params['weights'])
                if weight_magnitude > 1.0:
                    self.pid_parameters['kp'] *= (1.0 + adaptation_factor)
        
        # Ensure parameters stay within reasonable bounds
        self.pid_parameters['kp'] = np.clip(self.pid_parameters['kp'], 0.1, 10.0)
        self.pid_parameters['ki'] = np.clip(self.pid_parameters['ki'], 0.01, 1.0)
        self.pid_parameters['kd'] = np.clip(self.pid_parameters['kd'], 0.001, 0.1)
    
    async def stop_adaptive_control(self) -> Dict[str, Any]:
        """Stop the adaptive control system."""
        try:
            logger.info("Stopping adaptive control system")
            
            self.is_running = False
            
            if self.control_thread and self.control_thread.is_alive():
                self.control_thread.join(timeout=5.0)
            
            return {
                'status': 'success',
                'stop_time': datetime.now().isoformat(),
                'total_control_actions': len(self.control_history),
                'final_performance': self.online_learner.get_model_performance()
            }
            
        except Exception as e:
            logger.error(f"Failed to stop adaptive control: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_control_status(self) -> Dict[str, Any]:
        """Get current control system status."""
        return {
            'is_running': self.is_running,
            'control_mode': self.control_mode.value,
            'adaptation_strategy': self.adaptation_strategy.value,
            'pid_parameters': self.pid_parameters.copy(),
            'queue_size': self.data_queue.qsize(),
            'control_history_size': len(self.control_history),
            'model_performance': self.online_learner.get_model_performance()
        }


class EdgeComputingNode:
    """Edge computing node for distributed real-time processing."""
    
    def __init__(self, node_id: str, processing_capacity: float = 1.0):
        self.node_id = node_id
        self.processing_capacity = processing_capacity
        self.current_load = 0.0
        self.task_queue = queue.PriorityQueue()
        self.is_active = False
        self.processing_thread = None
        
        # Performance metrics
        self.processed_tasks = 0
        self.total_processing_time = 0.0
        self.error_count = 0
        
    @log_async_function_call
    async def start_edge_processing(self) -> Dict[str, Any]:
        """Start edge computing node."""
        try:
            logger.info(f"Starting edge computing node {self.node_id}")
            
            self.is_active = True
            
            # Start processing thread
            self.processing_thread = threading.Thread(target=self._processing_loop)
            self.processing_thread.daemon = True
            self.processing_thread.start()
            
            return {
                'status': 'success',
                'node_id': self.node_id,
                'processing_capacity': self.processing_capacity,
                'start_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to start edge node {self.node_id}: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _processing_loop(self):
        """Main processing loop for edge node."""
        while self.is_active:
            try:
                # Get task from queue (with timeout)
                try:
                    priority, task = self.task_queue.get(timeout=1.0)
                    
                    # Process task
                    start_time = time.time()
                    result = self._process_task(task)
                    processing_time = time.time() - start_time
                    
                    # Update metrics
                    self.processed_tasks += 1
                    self.total_processing_time += processing_time
                    self.current_load = self.task_queue.qsize() / 100.0  # Normalize to 0-1
                    
                    # Mark task as done
                    self.task_queue.task_done()
                    
                except queue.Empty:
                    # No tasks to process
                    self.current_load = 0.0
                    continue
                    
            except Exception as e:
                logger.error(f"Edge processing error in node {self.node_id}: {e}")
                self.error_count += 1
                time.sleep(0.1)
    
    def _process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single task on the edge node."""
        task_type = task.get('type', 'unknown')
        
        if task_type == 'sensor_data_processing':
            return self._process_sensor_data(task['data'])
        elif task_type == 'anomaly_detection':
            return self._detect_anomalies(task['data'])
        elif task_type == 'control_optimization':
            return self._optimize_control(task['data'])
        else:
            return {'status': 'error', 'error': f'Unknown task type: {task_type}'}
    
    def _process_sensor_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process sensor data on edge node."""
        # Simulate sensor data processing
        processed_data = {}
        
        for sensor, value in data.items():
            # Apply filtering and validation
            if isinstance(value, (int, float)):
                # Simple moving average filter
                processed_data[sensor] = value * 0.9 + 0.1 * np.random.normal(value, 0.01)
            else:
                processed_data[sensor] = value
        
        return {
            'status': 'success',
            'processed_data': processed_data,
            'processing_node': self.node_id,
            'timestamp': datetime.now().isoformat()
        }
    
    def _detect_anomalies(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect anomalies in sensor data."""
        anomalies = []
        
        for sensor, value in data.items():
            if isinstance(value, (int, float)):
                # Simple threshold-based anomaly detection
                if abs(value) > 100:  # Arbitrary threshold
                    anomalies.append({
                        'sensor': sensor,
                        'value': value,
                        'anomaly_type': 'threshold_exceeded',
                        'severity': 'high' if abs(value) > 200 else 'medium'
                    })
        
        return {
            'status': 'success',
            'anomalies_detected': len(anomalies),
            'anomalies': anomalies,
            'processing_node': self.node_id
        }
    
    def _optimize_control(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize control parameters on edge node."""
        # Simplified local optimization
        current_efficiency = data.get('efficiency', 0.85)
        target_efficiency = data.get('target_efficiency', 0.90)
        
        if current_efficiency < target_efficiency:
            # Suggest control adjustments
            adjustments = {
                'flow_rate_adjustment': 0.05,
                'chemical_dose_adjustment': 0.02,
                'confidence': 0.7
            }
        else:
            adjustments = {
                'flow_rate_adjustment': 0.0,
                'chemical_dose_adjustment': 0.0,
                'confidence': 0.9
            }
        
        return {
            'status': 'success',
            'control_adjustments': adjustments,
            'processing_node': self.node_id,
            'optimization_time': 0.1  # Simulated processing time
        }
    
    async def submit_task(self, task: Dict[str, Any], priority: int = 5) -> bool:
        """Submit task to edge node for processing."""
        try:
            if not self.is_active:
                return False
            
            # Check if node has capacity
            if self.current_load > 0.9:
                return False  # Node overloaded
            
            # Add task to queue
            self.task_queue.put((priority, task))
            return True
            
        except Exception as e:
            logger.error(f"Failed to submit task to node {self.node_id}: {e}")
            return False
    
    def get_node_status(self) -> Dict[str, Any]:
        """Get current node status and performance metrics."""
        avg_processing_time = (self.total_processing_time / self.processed_tasks 
                             if self.processed_tasks > 0 else 0.0)
        
        return {
            'node_id': self.node_id,
            'is_active': self.is_active,
            'current_load': self.current_load,
            'queue_size': self.task_queue.qsize(),
            'processed_tasks': self.processed_tasks,
            'average_processing_time': avg_processing_time,
            'error_count': self.error_count,
            'error_rate': self.error_count / max(1, self.processed_tasks),
            'processing_capacity': self.processing_capacity
        }
    
    async def stop_edge_processing(self) -> Dict[str, Any]:
        """Stop edge computing node."""
        try:
            logger.info(f"Stopping edge computing node {self.node_id}")
            
            self.is_active = False
            
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=5.0)
            
            return {
                'status': 'success',
                'node_id': self.node_id,
                'final_metrics': self.get_node_status(),
                'stop_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to stop edge node {self.node_id}: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience functions
async def create_adaptive_controller(learning_rate: float = 0.01,
                                   update_frequency: float = 1.0) -> AdaptiveController:
    """Create an adaptive controller with specified parameters."""
    config = AdaptiveControlConfig(
        learning_rate=learning_rate,
        update_frequency=update_frequency
    )
    return AdaptiveController(config)


async def create_edge_computing_cluster(num_nodes: int = 3) -> List[EdgeComputingNode]:
    """Create a cluster of edge computing nodes."""
    nodes = []
    for i in range(num_nodes):
        node = EdgeComputingNode(f"edge_node_{i}", processing_capacity=1.0)
        await node.start_edge_processing()
        nodes.append(node)
    
    return nodes
