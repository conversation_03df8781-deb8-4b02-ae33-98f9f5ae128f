"""
NASA Climate Data API Collector for satellite and climate observations.

This module provides integration with various NASA APIs for collecting
climate and environmental data from satellite observations.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


@dataclass
class NASAClimateData:
    """NASA climate data structure."""
    timestamp: datetime
    dataset: str
    location: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    parameters: Dict[str, Any]
    metadata: Dict[str, Any]
    source: str = "nasa"


class NASAClimateCollector:
    """
    NASA Climate Data API collector.
    
    Supports multiple NASA APIs:
    - NASA Earth Data API
    - MODIS (Moderate Resolution Imaging Spectroradiometer)
    - GISS (Goddard Institute for Space Studies) Temperature Data
    - NASA Power API (Prediction of Worldwide Energy Resources)
    - Landsat Data
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = self.settings.NASA_API_KEY
        self.base_url = self.settings.NASA_BASE_URL
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_initialized = False
        
        # NASA API endpoints
        self.endpoints = {
            'earth_imagery': f"{self.base_url}/planetary/earth/imagery",
            'earth_assets': f"{self.base_url}/planetary/earth/assets",
            'power_api': "https://power.larc.nasa.gov/api/temporal/daily/point",
            'giss_temp': "https://data.giss.nasa.gov/gistemp/graphs/graph_data",
            'modis': "https://modis.gsfc.nasa.gov/data/dataprod",
            'apod': f"{self.base_url}/planetary/apod"  # For testing
        }
        
        # Rate limiting
        self.requests_per_hour = 1000
        self.request_timestamps = []
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the NASA Climate collector."""
        try:
            logger.info("Initializing NASA Climate Data collector...")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=60),
                headers={"User-Agent": "WaterManagement-DecarbonisationSystem/1.0"}
            )
            
            # Test API connection
            test_success = await self._test_api_connection()
            
            if test_success:
                self.is_initialized = True
                logger.info("NASA Climate Data collector initialized successfully")
                return True
            else:
                logger.warning("NASA API test failed, but collector initialized for offline use")
                self.is_initialized = True
                return True  # Allow initialization even if API test fails
                
        except Exception as e:
            logger.error(f"Failed to initialize NASA collector: {e}")
            return False
    
    async def _test_api_connection(self) -> bool:
        """Test API connection with APOD endpoint (doesn't require special permissions)."""
        try:
            if not self.api_key:
                logger.warning("NASA API key not configured, using DEMO_KEY")
                test_key = "DEMO_KEY"
            else:
                test_key = self.api_key
            
            url = self.endpoints['apod']
            params = {"api_key": test_key}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"NASA API test successful: {data.get('title', 'APOD data retrieved')}")
                    return True
                elif response.status == 403:
                    logger.warning("NASA API key has limited permissions, but connection works")
                    return True
                else:
                    logger.warning(f"NASA API test returned status {response.status}")
                    return False
                    
        except Exception as e:
            logger.warning(f"NASA API connection test failed: {e}")
            return False
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        now = datetime.now()
        
        # Remove timestamps older than 1 hour
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if now - ts < timedelta(hours=1)
        ]
        
        # Check if we're at the limit
        if len(self.request_timestamps) >= self.requests_per_hour:
            sleep_time = 3600 - (now - self.request_timestamps[0]).total_seconds()
            if sleep_time > 0:
                logger.warning(f"NASA API rate limit reached, sleeping for {sleep_time:.1f} seconds")
                await asyncio.sleep(sleep_time)
        
        # Add current request timestamp
        self.request_timestamps.append(now)
    
    @cache_decorator(ttl=3600, key_prefix="nasa_earth_imagery")
    async def get_earth_imagery(self, lat: float, lon: float, date: str = None, dim: float = 0.1) -> Optional[Dict[str, Any]]:
        """Get Earth imagery for a specific location and date."""
        try:
            if not self.is_initialized:
                logger.error("Collector not initialized")
                return None
            
            await self._check_rate_limit()
            
            url = self.endpoints['earth_imagery']
            params = {
                "lat": lat,
                "lon": lon,
                "dim": dim,
                "api_key": self.api_key or "DEMO_KEY"
            }
            
            if date:
                params["date"] = date
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    # For imagery, we get the image URL and metadata
                    return {
                        "image_url": str(response.url),
                        "latitude": lat,
                        "longitude": lon,
                        "date": date or datetime.now().strftime("%Y-%m-%d"),
                        "dimension": dim,
                        "timestamp": datetime.now(),
                        "status": "success"
                    }
                else:
                    logger.error(f"NASA Earth Imagery API error {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get Earth imagery: {e}")
            return None
    
    @cache_decorator(ttl=7200, key_prefix="nasa_power_data")
    async def get_power_data(self, lat: float, lon: float, start_date: str, end_date: str, 
                           parameters: List[str] = None) -> Optional[NASAClimateData]:
        """Get NASA POWER meteorological data."""
        try:
            if not self.is_initialized:
                return None
            
            # Default parameters for climate analysis
            if not parameters:
                parameters = [
                    "T2M",      # Temperature at 2 Meters
                    "PRECTOTCORR",  # Precipitation Corrected
                    "RH2M",     # Relative Humidity at 2 Meters
                    "WS2M",     # Wind Speed at 2 Meters
                    "PS",       # Surface Pressure
                    "ALLSKY_SFC_SW_DWN"  # Solar Irradiance
                ]
            
            await self._check_rate_limit()
            
            url = self.endpoints['power_api']
            params = {
                "latitude": lat,
                "longitude": lon,
                "start": start_date,
                "end": end_date,
                "community": "RE",  # Renewable Energy
                "parameters": ",".join(parameters),
                "format": "JSON"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_power_data(data, lat, lon)
                else:
                    logger.error(f"NASA POWER API error {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get NASA POWER data: {e}")
            return None
    
    def _parse_power_data(self, data: Dict[str, Any], lat: float, lon: float) -> NASAClimateData:
        """Parse NASA POWER API response."""
        properties = data.get("properties", {})
        parameter_data = properties.get("parameter", {})
        
        # Extract metadata
        metadata = {
            "source": "NASA POWER",
            "version": data.get("header", {}).get("api_version", "unknown"),
            "fill_value": data.get("header", {}).get("fill_value", -999),
            "start_date": data.get("header", {}).get("start", "unknown"),
            "end_date": data.get("header", {}).get("end", "unknown")
        }
        
        return NASAClimateData(
            timestamp=datetime.now(),
            dataset="NASA_POWER",
            location=f"Lat:{lat}, Lon:{lon}",
            latitude=lat,
            longitude=lon,
            parameters=parameter_data,
            metadata=metadata
        )
    
    async def get_giss_temperature_data(self) -> Optional[Dict[str, Any]]:
        """Get GISS global temperature anomaly data."""
        try:
            if not self.is_initialized:
                return None
            
            await self._check_rate_limit()
            
            # GISS temperature data (publicly available)
            url = "https://data.giss.nasa.gov/gistemp/graphs_v4/graph_data/Global_Mean_Estimates_based_on_Land_and_Ocean_Data/graph.txt"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    text_data = await response.text()
                    return self._parse_giss_temperature_data(text_data)
                else:
                    logger.error(f"GISS temperature data API error {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get GISS temperature data: {e}")
            return None
    
    def _parse_giss_temperature_data(self, text_data: str) -> Dict[str, Any]:
        """Parse GISS temperature data from text format."""
        lines = text_data.strip().split('\n')
        
        # Skip header lines and parse data
        data_lines = [line for line in lines if not line.startswith('#') and line.strip()]
        
        temperature_data = []
        for line in data_lines[-10:]:  # Get last 10 years
            parts = line.split()
            if len(parts) >= 2:
                try:
                    year = int(parts[0])
                    anomaly = float(parts[1])
                    temperature_data.append({
                        "year": year,
                        "temperature_anomaly": anomaly
                    })
                except (ValueError, IndexError):
                    continue
        
        return {
            "dataset": "GISS_Global_Temperature",
            "description": "Global mean temperature anomalies (°C) relative to 1951-1980 average",
            "data": temperature_data,
            "timestamp": datetime.now(),
            "source": "NASA GISS"
        }
    
    async def get_climate_summary(self, lat: float, lon: float, location_name: str = None) -> Dict[str, Any]:
        """Get comprehensive climate summary for a location."""
        try:
            logger.info(f"Getting NASA climate summary for {location_name or f'Lat:{lat}, Lon:{lon}'}")
            
            # Get recent meteorological data (last 30 days)
            end_date = datetime.now().strftime("%Y%m%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
            
            # Collect data from multiple sources
            power_data = await self.get_power_data(lat, lon, start_date, end_date)
            earth_imagery = await self.get_earth_imagery(lat, lon)
            giss_temp = await self.get_giss_temperature_data()
            
            summary = {
                "location": location_name or f"Lat:{lat}, Lon:{lon}",
                "latitude": lat,
                "longitude": lon,
                "timestamp": datetime.now().isoformat(),
                "data_sources": []
            }
            
            if power_data:
                summary["meteorological_data"] = power_data.__dict__
                summary["data_sources"].append("NASA POWER")
            
            if earth_imagery:
                summary["earth_imagery"] = earth_imagery
                summary["data_sources"].append("NASA Earth Imagery")
            
            if giss_temp:
                summary["global_temperature_context"] = giss_temp
                summary["data_sources"].append("NASA GISS")
            
            # Calculate data availability score
            summary["data_availability_score"] = len(summary["data_sources"]) / 3.0
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get NASA climate summary: {e}")
            return {}
    
    async def collect_global_climate_indicators(self) -> List[Dict[str, Any]]:
        """Collect global climate indicators from NASA sources."""
        indicators = []
        
        try:
            # Global temperature data
            giss_temp = await self.get_giss_temperature_data()
            if giss_temp:
                indicators.append({
                    "indicator": "global_temperature_anomaly",
                    "data": giss_temp,
                    "importance": "high",
                    "description": "Global mean temperature anomalies indicate climate change trends"
                })
            
            # Sample locations for regional data
            sample_locations = [
                {"name": "Arctic", "lat": 70.0, "lon": -150.0},
                {"name": "Tropical", "lat": 0.0, "lon": 0.0},
                {"name": "Temperate", "lat": 45.0, "lon": 0.0}
            ]
            
            for location in sample_locations:
                try:
                    # Get recent data for each region
                    end_date = datetime.now().strftime("%Y%m%d")
                    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y%m%d")
                    
                    power_data = await self.get_power_data(
                        location["lat"], location["lon"], start_date, end_date
                    )
                    
                    if power_data:
                        indicators.append({
                            "indicator": f"regional_climate_{location['name'].lower()}",
                            "data": power_data.__dict__,
                            "importance": "medium",
                            "description": f"Regional climate data for {location['name']} zone"
                        })
                    
                    # Small delay between requests
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.warning(f"Failed to get data for {location['name']}: {e}")
            
            logger.info(f"Collected {len(indicators)} global climate indicators")
            return indicators
            
        except Exception as e:
            logger.error(f"Failed to collect global climate indicators: {e}")
            return []
    
    async def shutdown(self):
        """Shutdown the collector."""
        try:
            if self.session:
                await self.session.close()
            logger.info("NASA Climate Data collector shutdown completed")
        except Exception as e:
            logger.error(f"Error during NASA collector shutdown: {e}")


# Convenience functions
async def get_nasa_climate_for_location(location_name: str, lat: float, lon: float) -> Dict[str, Any]:
    """Get NASA climate data for a specific location."""
    collector = NASAClimateCollector()
    await collector.initialize()
    
    summary = await collector.get_climate_summary(lat, lon, location_name)
    
    await collector.shutdown()
    return summary


async def get_global_climate_indicators() -> List[Dict[str, Any]]:
    """Get global climate indicators from NASA sources."""
    collector = NASAClimateCollector()
    await collector.initialize()
    
    indicators = await collector.collect_global_climate_indicators()
    
    await collector.shutdown()
    return indicators
