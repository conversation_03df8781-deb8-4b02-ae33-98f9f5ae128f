#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NASA Open Data APIs Integration for Large-Scale Marine Debris Monitoring
Satellite data and Earth observations for marine conservation
"""

import os
import json
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class NASAEarthData:
    """NASA Earth observation data"""
    dataset_id: str
    latitude: float
    longitude: float
    timestamp: datetime
    value: float
    parameter: str
    unit: str
    quality_flag: Optional[str] = None
    satellite: Optional[str] = None


@dataclass
class NASAImageryData:
    """NASA satellite imagery data"""
    image_id: str
    latitude: float
    longitude: float
    timestamp: datetime
    image_url: str
    dataset: str
    resolution: Optional[float] = None


class NASAOpenAPI:
    """NASA Open Data APIs client for marine monitoring"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("NASA_API_KEY", "AxwO7eGNcFT3TOZ3H4AVEx8OsaTwzxorspqxWlkL")
        self.base_url = "https://api.nasa.gov"
        self.earth_url = "https://api.nasa.gov/planetary/earth"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={'X-API-Key': self.api_key}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_earth_imagery(
        self,
        latitude: float,
        longitude: float,
        date: datetime,
        dim: float = 0.1
    ) -> Optional[NASAImageryData]:
        """Get NASA Earth imagery for specified location and date"""
        try:
            params = {
                'lat': latitude,
                'lon': longitude,
                'date': date.strftime('%Y-%m-%d'),
                'dim': dim,
                'api_key': self.api_key
            }
            
            async with self.session.get(f"{self.earth_url}/imagery", params=params) as response:
                if response.status == 200:
                    image_url = str(response.url)
                    
                    return NASAImageryData(
                        image_id=f"nasa_earth_{latitude}_{longitude}_{date.strftime('%Y%m%d')}",
                        latitude=latitude,
                        longitude=longitude,
                        timestamp=date,
                        image_url=image_url,
                        dataset="NASA Earth Imagery",
                        resolution=30.0  # Landsat resolution
                    )
                else:
                    logger.error(f"❌ NASA Earth imagery request failed: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting NASA Earth imagery: {e}")
            return None
    
    async def get_modis_data(
        self,
        latitude: float,
        longitude: float,
        start_date: datetime,
        end_date: datetime
    ) -> List[NASAEarthData]:
        """Get MODIS satellite data for marine monitoring"""
        try:
            # Simulate MODIS data (in production would use actual MODIS API)
            data_points = []
            current_date = start_date
            
            while current_date <= end_date:
                # Sea surface temperature
                sst_data = NASAEarthData(
                    dataset_id="MODIS_SST",
                    latitude=latitude,
                    longitude=longitude,
                    timestamp=current_date,
                    value=15.0 + (current_date.day % 10),  # Simulated SST
                    parameter="sea_surface_temperature",
                    unit="celsius",
                    satellite="MODIS_Aqua"
                )
                data_points.append(sst_data)
                
                # Chlorophyll concentration
                chl_data = NASAEarthData(
                    dataset_id="MODIS_CHL",
                    latitude=latitude,
                    longitude=longitude,
                    timestamp=current_date,
                    value=2.0 + (current_date.day % 5),  # Simulated chlorophyll
                    parameter="chlorophyll_concentration",
                    unit="mg/m3",
                    satellite="MODIS_Aqua"
                )
                data_points.append(chl_data)
                
                current_date += timedelta(days=1)
            
            logger.info(f"✅ Retrieved {len(data_points)} MODIS data points")
            return data_points
            
        except Exception as e:
            logger.error(f"❌ Error getting MODIS data: {e}")
            return []
    
    async def get_marine_debris_indicators(
        self,
        bbox: Tuple[float, float, float, float],
        days_back: int = 30
    ) -> List[NASAEarthData]:
        """Get marine debris indicators from NASA data"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Center of bounding box
            center_lat = (bbox[1] + bbox[3]) / 2
            center_lon = (bbox[0] + bbox[2]) / 2
            
            # Get various indicators
            indicators = []
            
            # Ocean color anomalies (potential debris)
            for i in range(days_back):
                date = start_date + timedelta(days=i)
                
                anomaly_data = NASAEarthData(
                    dataset_id="NASA_OCEAN_COLOR",
                    latitude=center_lat,
                    longitude=center_lon,
                    timestamp=date,
                    value=0.1 + (i % 7) * 0.05,  # Simulated anomaly index
                    parameter="ocean_color_anomaly",
                    unit="index",
                    satellite="MODIS"
                )
                indicators.append(anomaly_data)
            
            return indicators
            
        except Exception as e:
            logger.error(f"❌ Error getting marine debris indicators: {e}")
            return []


# Convenience function
async def get_nasa_marine_data(
    latitude: float,
    longitude: float,
    days_back: int = 7,
    api_key: str = None
) -> Dict[str, Any]:
    """Get comprehensive NASA marine data"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    async with NASAOpenAPI(api_key) as api:
        # Get MODIS data and recent imagery
        modis_task = api.get_modis_data(latitude, longitude, start_date, end_date)
        imagery_task = api.get_earth_imagery(latitude, longitude, end_date)
        
        modis_data, imagery = await asyncio.gather(modis_task, imagery_task, return_exceptions=True)
        
        return {
            'location': {'latitude': latitude, 'longitude': longitude},
            'modis_data': modis_data if not isinstance(modis_data, Exception) else [],
            'imagery': imagery if not isinstance(imagery, Exception) else None,
            'timestamp': datetime.now().isoformat(),
            'data_source': 'NASA Open Data APIs'
        }


if __name__ == "__main__":
    async def test_nasa_api():
        print("🛰️ Testing NASA Open Data APIs")
        test_lat, test_lon = 40.0, -70.0  # Atlantic Ocean
        
        try:
            data = await get_nasa_marine_data(test_lat, test_lon, days_back=5)
            print(f"✅ NASA data retrieved for {test_lat}, {test_lon}")
            print(f"   MODIS data points: {len(data['modis_data'])}")
            print(f"   Imagery available: {'Yes' if data['imagery'] else 'No'}")
            
            if data['modis_data']:
                sample = data['modis_data'][0]
                print(f"   Sample SST: {sample.value:.1f}°C")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_nasa_api())
