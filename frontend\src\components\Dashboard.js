import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Water as WaterIcon,
  Waves as WavesIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';

const Dashboard = ({ data, systemStatus, connected, onRefresh }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    if (data) {
      setLastUpdate(new Date(data.timestamp));
    }
  }, [data]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'operational':
      case 'active':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'operational':
      case 'active':
        return <CheckCircleIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'error':
        return <ErrorIcon />;
      default:
        return <CheckCircleIcon />;
    }
  };

  // Sample chart data (would be replaced with real data)
  const chartData = [
    { time: '00:00', marine: 85, water: 92, integrated: 88 },
    { time: '04:00', marine: 87, water: 89, integrated: 88 },
    { time: '08:00', marine: 82, water: 94, integrated: 88 },
    { time: '12:00', marine: 89, water: 91, integrated: 90 },
    { time: '16:00', marine: 91, water: 88, integrated: 89 },
    { time: '20:00', marine: 88, water: 93, integrated: 90 },
  ];

  const pieData = [
    { name: 'Marine Health', value: data?.marine_conservation?.health_score * 100 || 85, color: '#2196f3' },
    { name: 'Water Efficiency', value: data?.water_management?.treatment_efficiency * 100 || 92, color: '#4caf50' },
    { name: 'Integration Score', value: data?.integrated_analytics?.environmental_score * 100 || 88, color: '#ff9800' },
  ];

  if (!data) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography variant="h6" color="text.secondary">
          Loading dashboard data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          🌊💧 Unified Environmental Platform
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <Chip
            icon={connected ? <CheckCircleIcon /> : <ErrorIcon />}
            label={connected ? 'Connected' : 'Disconnected'}
            color={connected ? 'success' : 'error'}
            variant="outlined"
          />
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* System Status Alert */}
      {data.system_status !== 'operational' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          System Status: {data.system_status}
        </Alert>
      )}

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Marine Conservation
                  </Typography>
                  <Typography variant="h5">
                    {data.marine_conservation?.health_score ? 
                      `${(data.marine_conservation.health_score * 100).toFixed(1)}%` : 
                      'N/A'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Health Score
                  </Typography>
                </Box>
                <WavesIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.marine_conservation?.health_score * 100 || 0} 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Water Management
                  </Typography>
                  <Typography variant="h5">
                    {data.water_management?.treatment_efficiency ? 
                      `${(data.water_management.treatment_efficiency * 100).toFixed(1)}%` : 
                      'N/A'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Treatment Efficiency
                  </Typography>
                </Box>
                <WaterIcon color="secondary" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.water_management?.treatment_efficiency * 100 || 0} 
                color="secondary"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Integration Score
                  </Typography>
                  <Typography variant="h5">
                    {data.integrated_analytics?.environmental_score ? 
                      `${(data.integrated_analytics.environmental_score * 100).toFixed(1)}%` : 
                      'N/A'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Environmental Score
                  </Typography>
                </Box>
                <AnalyticsIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.integrated_analytics?.environmental_score * 100 || 0} 
                color="warning"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    System Health
                  </Typography>
                  <Typography variant="h5">
                    {data.real_time?.system_health ? 
                      `${(data.real_time.system_health * 100).toFixed(1)}%` : 
                      'N/A'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Overall Health
                  </Typography>
                </Box>
                <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.real_time?.system_health * 100 || 0} 
                color="info"
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3}>
        {/* Performance Trends */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance Trends (24h)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type="monotone" dataKey="marine" stroke="#2196f3" name="Marine Conservation" />
                  <Line type="monotone" dataKey="water" stroke="#4caf50" name="Water Management" />
                  <Line type="monotone" dataKey="integrated" stroke="#ff9800" name="Integrated Score" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* System Overview */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Overview
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Status Information */}
      <Box mt={3}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Last Update: {lastUpdate ? lastUpdate.toLocaleString() : 'Never'} | 
            Active Operations: {data.real_time?.active_operations || 0} | 
            Alerts: {data.real_time?.alerts_count || 0}
          </Typography>
        </Paper>
      </Box>
    </Box>
  );
};

export default Dashboard;
