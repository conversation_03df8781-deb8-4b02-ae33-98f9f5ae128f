"""Prompt Optimization System for Water Management LLM Interactions."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import re

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class PromptType(Enum):
    """Types of prompts for different use cases."""
    ANALYSIS = "analysis"
    OPTIMIZATION = "optimization"
    PREDICTION = "prediction"
    EXPLANATION = "explanation"
    RECOMMENDATION = "recommendation"
    TROUBLESHOOTING = "troubleshooting"
    REPORTING = "reporting"


class OptimizationStrategy(Enum):
    """Prompt optimization strategies."""
    CLARITY = "clarity"
    SPECIFICITY = "specificity"
    CONTEXT_ENRICHMENT = "context_enrichment"
    STRUCTURE_IMPROVEMENT = "structure_improvement"
    EXAMPLE_ADDITION = "example_addition"
    CONSTRAINT_SPECIFICATION = "constraint_specification"


@dataclass
class PromptTemplate:
    """Prompt template definition."""
    template_id: str
    name: str
    prompt_type: PromptType
    template: str
    variables: List[str]
    description: str
    performance_score: float = 0.0
    usage_count: int = 0
    success_rate: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class PromptExecution:
    """Record of prompt execution."""
    execution_id: str
    template_id: str
    prompt: str
    response: str
    success: bool
    response_time: float
    quality_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


class PromptOptimizationSystem:
    """Advanced prompt optimization system for water management."""
    
    def __init__(self):
        self.templates: Dict[str, PromptTemplate] = {}
        self.executions: List[PromptExecution] = []
        self.optimization_rules = {}
        self.performance_metrics = {}
        
        # Initialize system
        self._initialize_templates()
        self._initialize_optimization_rules()
    
    def _initialize_templates(self):
        """Initialize prompt templates for water management."""
        templates = [
            PromptTemplate(
                template_id="water_quality_analysis",
                name="Water Quality Analysis",
                prompt_type=PromptType.ANALYSIS,
                template="""
                As a water quality expert, analyze the following water quality data:
                
                Water Quality Parameters:
                {parameters}
                
                Standards and Regulations:
                {standards}
                
                Please provide:
                1. Compliance assessment against standards
                2. Identification of any quality issues
                3. Risk assessment for public health
                4. Immediate actions required (if any)
                5. Long-term monitoring recommendations
                
                Format your response with clear sections and specific recommendations.
                """,
                variables=["parameters", "standards"],
                description="Analyze water quality data for compliance and safety"
            ),
            
            PromptTemplate(
                template_id="treatment_optimization",
                name="Treatment Process Optimization",
                prompt_type=PromptType.OPTIMIZATION,
                template="""
                As a water treatment optimization specialist, analyze the current treatment process:
                
                Current Process Parameters:
                {current_parameters}
                
                Performance Metrics:
                {performance_metrics}
                
                Optimization Objectives:
                {objectives}
                
                Constraints:
                {constraints}
                
                Please provide:
                1. Analysis of current performance
                2. Identification of optimization opportunities
                3. Specific parameter adjustments recommended
                4. Expected improvements (quantified)
                5. Implementation timeline and priorities
                6. Risk assessment of proposed changes
                
                Prioritize solutions that achieve {primary_objective} while maintaining safety and compliance.
                """,
                variables=["current_parameters", "performance_metrics", "objectives", "constraints", "primary_objective"],
                description="Optimize water treatment processes for efficiency and effectiveness"
            ),
            
            PromptTemplate(
                template_id="maintenance_prediction",
                name="Predictive Maintenance Analysis",
                prompt_type=PromptType.PREDICTION,
                template="""
                As a predictive maintenance expert for water treatment systems, analyze the equipment data:
                
                Equipment Information:
                {equipment_data}
                
                Operational History:
                {operational_history}
                
                Performance Trends:
                {performance_trends}
                
                Please provide:
                1. Equipment health assessment
                2. Failure risk analysis for next 30, 60, and 90 days
                3. Recommended maintenance actions with priorities
                4. Cost-benefit analysis of preventive vs reactive maintenance
                5. Optimal maintenance scheduling
                6. Parts and resource requirements
                
                Focus on preventing unplanned downtime while optimizing maintenance costs.
                """,
                variables=["equipment_data", "operational_history", "performance_trends"],
                description="Predict maintenance needs and optimize maintenance scheduling"
            ),
            
            PromptTemplate(
                template_id="climate_impact_analysis",
                name="Climate Impact Analysis",
                prompt_type=PromptType.ANALYSIS,
                template="""
                As a climate adaptation specialist for water systems, analyze the climate impact:
                
                Climate Data:
                {climate_data}
                
                System Vulnerabilities:
                {vulnerabilities}
                
                Historical Climate Events:
                {historical_events}
                
                Please provide:
                1. Climate risk assessment for water infrastructure
                2. Vulnerability analysis of current systems
                3. Adaptation strategies for identified risks
                4. Infrastructure resilience recommendations
                5. Emergency preparedness measures
                6. Long-term planning considerations
                
                Prioritize solutions that enhance system resilience to climate change impacts.
                """,
                variables=["climate_data", "vulnerabilities", "historical_events"],
                description="Analyze climate impacts on water systems and recommend adaptations"
            ),
            
            PromptTemplate(
                template_id="energy_efficiency_optimization",
                name="Energy Efficiency Optimization",
                prompt_type=PromptType.OPTIMIZATION,
                template="""
                As an energy efficiency expert for water treatment facilities, analyze the energy usage:
                
                Current Energy Consumption:
                {energy_consumption}
                
                System Configuration:
                {system_configuration}
                
                Efficiency Targets:
                {efficiency_targets}
                
                Available Technologies:
                {available_technologies}
                
                Please provide:
                1. Energy consumption analysis and benchmarking
                2. Identification of energy waste and inefficiencies
                3. Technology recommendations for energy savings
                4. Operational optimization strategies
                5. Investment priorities with ROI analysis
                6. Implementation roadmap
                
                Focus on achieving maximum energy savings while maintaining treatment effectiveness.
                """,
                variables=["energy_consumption", "system_configuration", "efficiency_targets", "available_technologies"],
                description="Optimize energy efficiency in water treatment operations"
            ),
            
            PromptTemplate(
                template_id="troubleshooting_guide",
                name="System Troubleshooting",
                prompt_type=PromptType.TROUBLESHOOTING,
                template="""
                As a water treatment system troubleshooting expert, help diagnose the following issue:
                
                Problem Description:
                {problem_description}
                
                System Status:
                {system_status}
                
                Recent Changes:
                {recent_changes}
                
                Error Messages/Alarms:
                {error_messages}
                
                Please provide:
                1. Problem diagnosis with root cause analysis
                2. Step-by-step troubleshooting procedure
                3. Immediate actions to stabilize the system
                4. Temporary workarounds (if applicable)
                5. Permanent solution recommendations
                6. Prevention measures for future occurrences
                
                Prioritize safety and system stability in all recommendations.
                """,
                variables=["problem_description", "system_status", "recent_changes", "error_messages"],
                description="Troubleshoot water treatment system issues"
            )
        ]
        
        for template in templates:
            self.templates[template.template_id] = template
    
    def _initialize_optimization_rules(self):
        """Initialize prompt optimization rules."""
        self.optimization_rules = {
            OptimizationStrategy.CLARITY: {
                'rules': [
                    'Use clear, specific language',
                    'Avoid ambiguous terms',
                    'Define technical terms when necessary',
                    'Use active voice'
                ],
                'patterns': [
                    (r'\b(maybe|perhaps|possibly)\b', 'Use definitive language'),
                    (r'\b(thing|stuff|it)\b', 'Use specific nouns'),
                    (r'passive voice patterns', 'Convert to active voice')
                ]
            },
            
            OptimizationStrategy.SPECIFICITY: {
                'rules': [
                    'Include specific parameters and units',
                    'Provide concrete examples',
                    'Specify exact requirements',
                    'Use quantitative measures'
                ],
                'patterns': [
                    (r'\b(some|many|few)\b', 'Use specific quantities'),
                    (r'\b(good|bad|high|low)\b', 'Use quantitative measures'),
                    (r'general terms', 'Use specific technical terms')
                ]
            },
            
            OptimizationStrategy.CONTEXT_ENRICHMENT: {
                'rules': [
                    'Provide relevant background information',
                    'Include system constraints',
                    'Specify operating conditions',
                    'Add regulatory context'
                ],
                'enhancements': [
                    'Add regulatory standards',
                    'Include operational constraints',
                    'Provide historical context',
                    'Specify environmental conditions'
                ]
            },
            
            OptimizationStrategy.STRUCTURE_IMPROVEMENT: {
                'rules': [
                    'Use numbered lists for procedures',
                    'Organize information hierarchically',
                    'Use clear section headers',
                    'Separate different types of information'
                ],
                'structure_patterns': [
                    'Introduction -> Analysis -> Recommendations -> Conclusion',
                    'Problem -> Diagnosis -> Solution -> Prevention',
                    'Current State -> Optimization -> Implementation -> Monitoring'
                ]
            }
        }
    
    @log_async_function_call
    async def optimize_prompt(self, template_id: str, 
                            optimization_strategies: List[OptimizationStrategy] = None) -> Dict[str, Any]:
        """Optimize a prompt template."""
        try:
            if template_id not in self.templates:
                return {'status': 'error', 'error': 'Template not found'}
            
            template = self.templates[template_id]
            
            if optimization_strategies is None:
                optimization_strategies = [
                    OptimizationStrategy.CLARITY,
                    OptimizationStrategy.SPECIFICITY,
                    OptimizationStrategy.STRUCTURE_IMPROVEMENT
                ]
            
            optimized_prompt = template.template
            optimization_log = []
            
            # Apply optimization strategies
            for strategy in optimization_strategies:
                result = await self._apply_optimization_strategy(optimized_prompt, strategy)
                optimized_prompt = result['optimized_prompt']
                optimization_log.extend(result['changes'])
            
            # Calculate improvement score
            improvement_score = await self._calculate_improvement_score(
                template.template, optimized_prompt
            )
            
            # Create optimized template
            optimized_template_id = f"{template_id}_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            optimized_template = PromptTemplate(
                template_id=optimized_template_id,
                name=f"{template.name} (Optimized)",
                prompt_type=template.prompt_type,
                template=optimized_prompt,
                variables=template.variables,
                description=f"Optimized version of {template.description}"
            )
            
            self.templates[optimized_template_id] = optimized_template
            
            return {
                'status': 'success',
                'original_template_id': template_id,
                'optimized_template_id': optimized_template_id,
                'improvement_score': improvement_score,
                'optimization_log': optimization_log,
                'strategies_applied': [s.value for s in optimization_strategies]
            }
            
        except Exception as e:
            logger.error(f"Prompt optimization failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _apply_optimization_strategy(self, prompt: str, 
                                         strategy: OptimizationStrategy) -> Dict[str, Any]:
        """Apply specific optimization strategy."""
        optimized_prompt = prompt
        changes = []
        
        if strategy == OptimizationStrategy.CLARITY:
            # Improve clarity
            clarity_improvements = [
                ('Please provide:', 'Provide the following:'),
                ('analyze the following', 'analyze the following data'),
                ('give recommendations', 'provide specific recommendations'),
                ('consider the', 'analyze the')
            ]
            
            for old, new in clarity_improvements:
                if old in optimized_prompt:
                    optimized_prompt = optimized_prompt.replace(old, new)
                    changes.append(f"Clarity: Replaced '{old}' with '{new}'")
        
        elif strategy == OptimizationStrategy.SPECIFICITY:
            # Add specificity
            if 'water quality' in optimized_prompt and 'parameters' not in optimized_prompt:
                optimized_prompt = optimized_prompt.replace(
                    'water quality',
                    'water quality parameters (pH, turbidity, chlorine residual, bacteria count)'
                )
                changes.append("Specificity: Added specific water quality parameters")
            
            if 'optimization' in optimized_prompt and 'objectives' not in optimized_prompt:
                optimized_prompt = optimized_prompt.replace(
                    'optimization',
                    'optimization (efficiency, cost reduction, energy savings)'
                )
                changes.append("Specificity: Added specific optimization objectives")
        
        elif strategy == OptimizationStrategy.CONTEXT_ENRICHMENT:
            # Add context
            if 'water treatment' in optimized_prompt and 'regulatory' not in optimized_prompt:
                context_addition = "\n\nRegulatory Context: Ensure all recommendations comply with EPA standards and local regulations."
                optimized_prompt += context_addition
                changes.append("Context: Added regulatory compliance context")
            
            if 'maintenance' in optimized_prompt and 'safety' not in optimized_prompt:
                safety_context = "\n\nSafety Considerations: Prioritize worker safety and system reliability in all recommendations."
                optimized_prompt += safety_context
                changes.append("Context: Added safety considerations")
        
        elif strategy == OptimizationStrategy.STRUCTURE_IMPROVEMENT:
            # Improve structure
            if 'Please provide:' in optimized_prompt and not re.search(r'\d+\.', optimized_prompt):
                # Add numbering to lists
                lines = optimized_prompt.split('\n')
                improved_lines = []
                in_list = False
                list_counter = 1
                
                for line in lines:
                    if 'Please provide:' in line:
                        improved_lines.append(line)
                        in_list = True
                    elif in_list and line.strip() and not line.strip().startswith(('1.', '2.', '3.')):
                        if line.strip().startswith(('-', '•', '*')):
                            improved_lines.append(f"{list_counter}. {line.strip()[1:].strip()}")
                            list_counter += 1
                        else:
                            improved_lines.append(line)
                            in_list = False
                            list_counter = 1
                    else:
                        improved_lines.append(line)
                
                optimized_prompt = '\n'.join(improved_lines)
                changes.append("Structure: Added numbering to recommendation lists")
        
        return {
            'optimized_prompt': optimized_prompt,
            'changes': changes
        }
    
    async def _calculate_improvement_score(self, original: str, optimized: str) -> float:
        """Calculate improvement score between original and optimized prompts."""
        # Simple scoring based on various factors
        score = 0.0
        
        # Length improvement (more detailed is generally better)
        length_ratio = len(optimized) / len(original)
        if 1.1 <= length_ratio <= 1.5:  # 10-50% increase is good
            score += 0.2
        
        # Structure improvement (numbered lists, clear sections)
        if re.search(r'\d+\.', optimized) and not re.search(r'\d+\.', original):
            score += 0.3
        
        # Specificity improvement (more specific terms)
        specific_terms = ['parameters', 'standards', 'compliance', 'quantified', 'specific']
        original_specific = sum(1 for term in specific_terms if term in original.lower())
        optimized_specific = sum(1 for term in specific_terms if term in optimized.lower())
        
        if optimized_specific > original_specific:
            score += 0.3
        
        # Context enrichment (regulatory, safety, constraints)
        context_terms = ['regulatory', 'safety', 'constraints', 'compliance', 'standards']
        original_context = sum(1 for term in context_terms if term in original.lower())
        optimized_context = sum(1 for term in context_terms if term in optimized.lower())
        
        if optimized_context > original_context:
            score += 0.2
        
        return min(1.0, score)
    
    @log_async_function_call
    async def generate_prompt(self, template_id: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """Generate prompt from template with variables."""
        try:
            if template_id not in self.templates:
                return {'status': 'error', 'error': 'Template not found'}
            
            template = self.templates[template_id]
            
            # Check if all required variables are provided
            missing_variables = [var for var in template.variables if var not in variables]
            if missing_variables:
                return {
                    'status': 'error',
                    'error': f'Missing variables: {missing_variables}'
                }
            
            # Generate prompt
            prompt = template.template
            for variable, value in variables.items():
                placeholder = f"{{{variable}}}"
                prompt = prompt.replace(placeholder, str(value))
            
            # Update template usage
            template.usage_count += 1
            template.last_updated = datetime.now()
            
            return {
                'status': 'success',
                'template_id': template_id,
                'prompt': prompt,
                'variables_used': list(variables.keys())
            }
            
        except Exception as e:
            logger.error(f"Prompt generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def record_execution(self, template_id: str, prompt: str, response: str,
                             success: bool, response_time: float,
                             quality_score: float = None) -> Dict[str, Any]:
        """Record prompt execution for performance tracking."""
        try:
            execution_id = f"exec_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Calculate quality score if not provided
            if quality_score is None:
                quality_score = await self._calculate_response_quality(response)
            
            execution = PromptExecution(
                execution_id=execution_id,
                template_id=template_id,
                prompt=prompt,
                response=response,
                success=success,
                response_time=response_time,
                quality_score=quality_score
            )
            
            self.executions.append(execution)
            
            # Update template performance
            if template_id in self.templates:
                template = self.templates[template_id]
                
                # Update success rate
                total_executions = sum(1 for e in self.executions if e.template_id == template_id)
                successful_executions = sum(1 for e in self.executions 
                                          if e.template_id == template_id and e.success)
                
                template.success_rate = successful_executions / total_executions
                
                # Update performance score (weighted average of quality scores)
                quality_scores = [e.quality_score for e in self.executions 
                                if e.template_id == template_id]
                template.performance_score = sum(quality_scores) / len(quality_scores)
            
            return {
                'status': 'success',
                'execution_id': execution_id,
                'quality_score': quality_score
            }
            
        except Exception as e:
            logger.error(f"Execution recording failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_response_quality(self, response: str) -> float:
        """Calculate quality score for response."""
        score = 0.5  # Base score
        
        # Length check (not too short, not too long)
        length = len(response)
        if 200 <= length <= 2000:
            score += 0.2
        elif length > 100:
            score += 0.1
        
        # Structure check (numbered lists, sections)
        if re.search(r'\d+\.', response):
            score += 0.1
        
        # Technical content check
        technical_terms = [
            'efficiency', 'optimization', 'compliance', 'standards',
            'parameters', 'analysis', 'recommendation', 'implementation'
        ]
        
        term_count = sum(1 for term in technical_terms if term.lower() in response.lower())
        score += min(0.2, term_count * 0.05)
        
        return min(1.0, score)
    
    def get_template_performance(self, template_id: str) -> Dict[str, Any]:
        """Get performance metrics for a template."""
        if template_id not in self.templates:
            return {'status': 'error', 'error': 'Template not found'}
        
        template = self.templates[template_id]
        
        # Get execution statistics
        template_executions = [e for e in self.executions if e.template_id == template_id]
        
        if not template_executions:
            return {
                'status': 'success',
                'template_id': template_id,
                'performance': {
                    'usage_count': template.usage_count,
                    'success_rate': 0.0,
                    'performance_score': 0.0,
                    'avg_response_time': 0.0,
                    'executions': 0
                }
            }
        
        avg_response_time = sum(e.response_time for e in template_executions) / len(template_executions)
        avg_quality = sum(e.quality_score for e in template_executions) / len(template_executions)
        
        return {
            'status': 'success',
            'template_id': template_id,
            'performance': {
                'usage_count': template.usage_count,
                'success_rate': template.success_rate,
                'performance_score': template.performance_score,
                'avg_response_time': avg_response_time,
                'avg_quality_score': avg_quality,
                'executions': len(template_executions),
                'last_used': template.last_updated.isoformat()
            }
        }
    
    def list_templates(self, prompt_type: PromptType = None) -> Dict[str, Any]:
        """List available prompt templates."""
        templates = list(self.templates.values())
        
        if prompt_type:
            templates = [t for t in templates if t.prompt_type == prompt_type]
        
        template_list = []
        for template in templates:
            template_list.append({
                'template_id': template.template_id,
                'name': template.name,
                'prompt_type': template.prompt_type.value,
                'description': template.description,
                'variables': template.variables,
                'performance_score': template.performance_score,
                'usage_count': template.usage_count,
                'success_rate': template.success_rate
            })
        
        # Sort by performance score
        template_list.sort(key=lambda x: x['performance_score'], reverse=True)
        
        return {
            'status': 'success',
            'templates': template_list,
            'total_templates': len(template_list)
        }


# Convenience functions
async def optimize_water_management_prompt(template_id: str) -> Dict[str, Any]:
    """Optimize a water management prompt template."""
    optimizer = PromptOptimizationSystem()
    return await optimizer.optimize_prompt(template_id)


async def generate_water_management_prompt(template_id: str, variables: Dict[str, Any]) -> Dict[str, Any]:
    """Generate prompt for water management task."""
    optimizer = PromptOptimizationSystem()
    return await optimizer.generate_prompt(template_id, variables)
