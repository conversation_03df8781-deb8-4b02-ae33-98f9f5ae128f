"""Data Quality Monitoring System for Water Management."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class QualityStatus(Enum):
    """Data quality status levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class QualityMetric:
    """Data quality metric definition."""
    name: str
    value: float
    threshold: float
    status: QualityStatus
    description: str
    timestamp: datetime


class DataQualityMonitor:
    """Comprehensive data quality monitoring system."""
    
    def __init__(self):
        self.quality_thresholds = {
            'completeness': {'excellent': 0.98, 'good': 0.95, 'acceptable': 0.90, 'poor': 0.80},
            'accuracy': {'excellent': 0.99, 'good': 0.95, 'acceptable': 0.90, 'poor': 0.85},
            'consistency': {'excellent': 0.98, 'good': 0.95, 'acceptable': 0.90, 'poor': 0.85},
            'timeliness': {'excellent': 0.95, 'good': 0.90, 'acceptable': 0.85, 'poor': 0.75},
            'validity': {'excellent': 0.99, 'good': 0.95, 'acceptable': 0.90, 'poor': 0.85}
        }
        self.quality_history = []
        self.alerts = []
    
    @log_async_function_call
    async def assess_data_quality(self, data: Dict[str, Any], 
                                metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Comprehensive data quality assessment."""
        try:
            assessment = {
                'overall_score': 0.0,
                'overall_status': QualityStatus.POOR,
                'metrics': {},
                'issues': [],
                'recommendations': [],
                'timestamp': datetime.now()
            }
            
            # Convert to DataFrame for analysis
            if isinstance(data, dict):
                df = pd.DataFrame(data) if data else pd.DataFrame()
            else:
                df = pd.DataFrame(data)
            
            if df.empty:
                assessment['issues'].append("No data available for quality assessment")
                return {'status': 'error', 'assessment': assessment}
            
            # Assess completeness
            completeness_metric = await self._assess_completeness(df)
            assessment['metrics']['completeness'] = completeness_metric
            
            # Assess accuracy
            accuracy_metric = await self._assess_accuracy(df, metadata)
            assessment['metrics']['accuracy'] = accuracy_metric
            
            # Assess consistency
            consistency_metric = await self._assess_consistency(df)
            assessment['metrics']['consistency'] = consistency_metric
            
            # Assess timeliness
            timeliness_metric = await self._assess_timeliness(df, metadata)
            assessment['metrics']['timeliness'] = timeliness_metric
            
            # Assess validity
            validity_metric = await self._assess_validity(df)
            assessment['metrics']['validity'] = validity_metric
            
            # Calculate overall score
            metrics = list(assessment['metrics'].values())
            assessment['overall_score'] = sum(m.value for m in metrics) / len(metrics)
            assessment['overall_status'] = self._determine_status(
                assessment['overall_score'], 'completeness'
            )
            
            # Generate issues and recommendations
            assessment['issues'] = self._identify_issues(assessment['metrics'])
            assessment['recommendations'] = self._generate_recommendations(assessment['metrics'])
            
            # Store in history
            self.quality_history.append(assessment)
            
            # Check for alerts
            await self._check_quality_alerts(assessment)
            
            return {
                'status': 'success',
                'assessment': assessment,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Data quality assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _assess_completeness(self, df: pd.DataFrame) -> QualityMetric:
        """Assess data completeness."""
        if df.empty:
            completeness = 0.0
        else:
            total_cells = df.size
            non_null_cells = df.count().sum()
            completeness = non_null_cells / total_cells if total_cells > 0 else 0.0
        
        status = self._determine_status(completeness, 'completeness')
        
        return QualityMetric(
            name='completeness',
            value=completeness,
            threshold=self.quality_thresholds['completeness']['acceptable'],
            status=status,
            description=f"Data completeness: {completeness:.2%}",
            timestamp=datetime.now()
        )
    
    async def _assess_accuracy(self, df: pd.DataFrame, 
                             metadata: Dict[str, Any] = None) -> QualityMetric:
        """Assess data accuracy."""
        accuracy_score = 0.95  # Default high accuracy
        
        # Check for obvious data errors
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if col in df.columns:
                # Check for impossible values
                if col.lower() in ['ph']:
                    invalid_ph = ((df[col] < 0) | (df[col] > 14)).sum()
                    if invalid_ph > 0:
                        accuracy_score -= 0.1
                
                elif col.lower() in ['temperature']:
                    invalid_temp = ((df[col] < -50) | (df[col] > 100)).sum()
                    if invalid_temp > 0:
                        accuracy_score -= 0.1
                
                elif col.lower() in ['turbidity']:
                    invalid_turbidity = (df[col] < 0).sum()
                    if invalid_turbidity > 0:
                        accuracy_score -= 0.1
        
        accuracy_score = max(0.0, min(1.0, accuracy_score))
        status = self._determine_status(accuracy_score, 'accuracy')
        
        return QualityMetric(
            name='accuracy',
            value=accuracy_score,
            threshold=self.quality_thresholds['accuracy']['acceptable'],
            status=status,
            description=f"Data accuracy: {accuracy_score:.2%}",
            timestamp=datetime.now()
        )
    
    async def _assess_consistency(self, df: pd.DataFrame) -> QualityMetric:
        """Assess data consistency."""
        consistency_score = 1.0
        
        # Check for data type consistency
        for col in df.columns:
            if df[col].dtype == 'object':
                # Check for mixed data types in string columns
                try:
                    pd.to_numeric(df[col], errors='raise')
                except (ValueError, TypeError):
                    pass  # Expected for string columns
            
            # Check for outliers that might indicate inconsistency
            if df[col].dtype in ['int64', 'float64']:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                outliers = ((df[col] < (Q1 - 3 * IQR)) | (df[col] > (Q3 + 3 * IQR))).sum()
                outlier_ratio = outliers / len(df) if len(df) > 0 else 0
                
                if outlier_ratio > 0.05:  # More than 5% outliers
                    consistency_score -= 0.1
        
        consistency_score = max(0.0, min(1.0, consistency_score))
        status = self._determine_status(consistency_score, 'consistency')
        
        return QualityMetric(
            name='consistency',
            value=consistency_score,
            threshold=self.quality_thresholds['consistency']['acceptable'],
            status=status,
            description=f"Data consistency: {consistency_score:.2%}",
            timestamp=datetime.now()
        )
    
    async def _assess_timeliness(self, df: pd.DataFrame, 
                               metadata: Dict[str, Any] = None) -> QualityMetric:
        """Assess data timeliness."""
        timeliness_score = 0.95  # Default good timeliness
        
        # Check if timestamp column exists
        timestamp_cols = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
        
        if timestamp_cols:
            for col in timestamp_cols:
                try:
                    timestamps = pd.to_datetime(df[col])
                    now = datetime.now()
                    
                    # Check how recent the data is
                    latest_data = timestamps.max()
                    if pd.notna(latest_data):
                        age_hours = (now - latest_data).total_seconds() / 3600
                        
                        if age_hours > 24:  # Data older than 24 hours
                            timeliness_score -= 0.2
                        elif age_hours > 12:  # Data older than 12 hours
                            timeliness_score -= 0.1
                
                except (ValueError, TypeError):
                    timeliness_score -= 0.1
        
        timeliness_score = max(0.0, min(1.0, timeliness_score))
        status = self._determine_status(timeliness_score, 'timeliness')
        
        return QualityMetric(
            name='timeliness',
            value=timeliness_score,
            threshold=self.quality_thresholds['timeliness']['acceptable'],
            status=status,
            description=f"Data timeliness: {timeliness_score:.2%}",
            timestamp=datetime.now()
        )
    
    async def _assess_validity(self, df: pd.DataFrame) -> QualityMetric:
        """Assess data validity."""
        validity_score = 1.0
        
        # Check for valid data formats and ranges
        for col in df.columns:
            if col.lower() in ['ph']:
                invalid_count = ((df[col] < 0) | (df[col] > 14) | df[col].isna()).sum()
                validity_score -= (invalid_count / len(df)) * 0.5
            
            elif col.lower() in ['turbidity']:
                invalid_count = ((df[col] < 0) | df[col].isna()).sum()
                validity_score -= (invalid_count / len(df)) * 0.3
            
            elif col.lower() in ['temperature']:
                invalid_count = ((df[col] < -50) | (df[col] > 100) | df[col].isna()).sum()
                validity_score -= (invalid_count / len(df)) * 0.3
        
        validity_score = max(0.0, min(1.0, validity_score))
        status = self._determine_status(validity_score, 'validity')
        
        return QualityMetric(
            name='validity',
            value=validity_score,
            threshold=self.quality_thresholds['validity']['acceptable'],
            status=status,
            description=f"Data validity: {validity_score:.2%}",
            timestamp=datetime.now()
        )
    
    def _determine_status(self, score: float, metric_type: str) -> QualityStatus:
        """Determine quality status based on score."""
        thresholds = self.quality_thresholds[metric_type]
        
        if score >= thresholds['excellent']:
            return QualityStatus.EXCELLENT
        elif score >= thresholds['good']:
            return QualityStatus.GOOD
        elif score >= thresholds['acceptable']:
            return QualityStatus.ACCEPTABLE
        elif score >= thresholds['poor']:
            return QualityStatus.POOR
        else:
            return QualityStatus.CRITICAL
    
    def _identify_issues(self, metrics: Dict[str, QualityMetric]) -> List[str]:
        """Identify data quality issues."""
        issues = []
        
        for metric_name, metric in metrics.items():
            if metric.status in [QualityStatus.POOR, QualityStatus.CRITICAL]:
                issues.append(f"Poor {metric_name}: {metric.value:.2%} (threshold: {metric.threshold:.2%})")
            elif metric.status == QualityStatus.ACCEPTABLE:
                issues.append(f"Acceptable {metric_name}: {metric.value:.2%} (could be improved)")
        
        return issues
    
    def _generate_recommendations(self, metrics: Dict[str, QualityMetric]) -> List[str]:
        """Generate recommendations for improving data quality."""
        recommendations = []
        
        for metric_name, metric in metrics.items():
            if metric.status in [QualityStatus.POOR, QualityStatus.CRITICAL]:
                if metric_name == 'completeness':
                    recommendations.append("Improve data collection processes to reduce missing values")
                elif metric_name == 'accuracy':
                    recommendations.append("Implement data validation rules and range checks")
                elif metric_name == 'consistency':
                    recommendations.append("Standardize data formats and implement consistency checks")
                elif metric_name == 'timeliness':
                    recommendations.append("Increase data collection frequency and reduce processing delays")
                elif metric_name == 'validity':
                    recommendations.append("Implement stricter data validation and format checking")
        
        if not recommendations:
            recommendations.append("Data quality is good - maintain current processes")
        
        return recommendations
    
    async def _check_quality_alerts(self, assessment: Dict[str, Any]):
        """Check for quality alerts and notifications."""
        overall_status = assessment['overall_status']
        
        if overall_status in [QualityStatus.POOR, QualityStatus.CRITICAL]:
            alert = {
                'timestamp': datetime.now(),
                'severity': 'high' if overall_status == QualityStatus.CRITICAL else 'medium',
                'message': f"Data quality alert: {overall_status.value} quality detected",
                'score': assessment['overall_score'],
                'issues': assessment['issues']
            }
            self.alerts.append(alert)
            logger.warning(f"Data quality alert: {alert['message']}")
    
    @log_async_function_call
    async def get_quality_trends(self, days: int = 7) -> Dict[str, Any]:
        """Get data quality trends over time."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_assessments = [
                a for a in self.quality_history 
                if a['timestamp'] >= cutoff_date
            ]
            
            if not recent_assessments:
                return {
                    'status': 'success',
                    'trends': {},
                    'message': 'No recent quality assessments available'
                }
            
            trends = {}
            for metric_name in ['completeness', 'accuracy', 'consistency', 'timeliness', 'validity']:
                values = [
                    a['metrics'][metric_name].value 
                    for a in recent_assessments 
                    if metric_name in a['metrics']
                ]
                
                if values:
                    trends[metric_name] = {
                        'current': values[-1],
                        'average': sum(values) / len(values),
                        'trend': 'improving' if len(values) > 1 and values[-1] > values[0] else 'stable',
                        'min': min(values),
                        'max': max(values)
                    }
            
            return {
                'status': 'success',
                'trends': trends,
                'period_days': days,
                'assessments_count': len(recent_assessments),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Quality trends analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_quality_alerts(self, severity: str = None) -> List[Dict[str, Any]]:
        """Get quality alerts, optionally filtered by severity."""
        if severity:
            return [alert for alert in self.alerts if alert['severity'] == severity]
        return self.alerts
    
    def clear_alerts(self):
        """Clear all quality alerts."""
        self.alerts.clear()


# Convenience functions
async def assess_water_data_quality(data: Dict[str, Any], 
                                   metadata: Dict[str, Any] = None) -> Dict[str, Any]:
    """Assess water management data quality."""
    monitor = DataQualityMonitor()
    return await monitor.assess_data_quality(data, metadata)


async def monitor_data_quality_trends(days: int = 7) -> Dict[str, Any]:
    """Monitor data quality trends."""
    monitor = DataQualityMonitor()
    return await monitor.get_quality_trends(days)
