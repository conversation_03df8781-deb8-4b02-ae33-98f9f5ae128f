import { io } from 'socket.io-client';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.listeners = new Map();
  }

  connect() {
    try {
      const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';
      
      console.log('🔗 Connecting to WebSocket:', wsUrl);
      
      this.socket = io(wsUrl, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
      });

      this.setupEventListeners();
      
    } catch (error) {
      console.error('❌ WebSocket connection error:', error);
      this.scheduleReconnect();
    }
  }

  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      this.connected = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      this.emit('connect');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      this.connected = false;
      this.emit('disconnect', reason);
      
      // Auto-reconnect unless it was a manual disconnect
      if (reason !== 'io client disconnect') {
        this.scheduleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection error:', error);
      this.connected = false;
      this.emit('error', error);
      this.scheduleReconnect();
    });

    // Handle dashboard updates
    this.socket.on('dashboard_update', (data) => {
      console.log('📊 Dashboard update received');
      this.emit('dashboard_update', data);
    });

    // Handle system alerts
    this.socket.on('system_alert', (data) => {
      console.log('🚨 System alert received:', data);
      this.emit('system_alert', data);
    });

    // Handle operation updates
    this.socket.on('operation_update', (data) => {
      console.log('🔄 Operation update received:', data);
      this.emit('operation_update', data);
    });

    // Handle pong responses
    this.socket.on('pong', (data) => {
      console.log('🏓 Pong received:', data);
      this.emit('pong', data);
    });

    // Generic message handler
    this.socket.on('message', (data) => {
      console.log('📨 Message received:', data);
      this.emit('message', data);
    });
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting WebSocket');
      this.socket.disconnect();
      this.socket = null;
      this.connected = false;
    }
  }

  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      this.emit('max_reconnect_attempts');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.connected) {
        this.connect();
      }
    }, delay);
  }

  // Send messages
  send(eventName, data) {
    if (this.socket && this.connected) {
      console.log(`📤 Sending ${eventName}:`, data);
      this.socket.emit(eventName, data);
      return true;
    } else {
      console.warn('⚠️ Cannot send message: WebSocket not connected');
      return false;
    }
  }

  // Send ping
  ping() {
    return this.send('ping', { timestamp: new Date().toISOString() });
  }

  // Request immediate dashboard update
  requestUpdate() {
    return this.send('request_update', { timestamp: new Date().toISOString() });
  }

  // Event listener management
  on(eventName, callback) {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }
    this.listeners.get(eventName).push(callback);
  }

  off(eventName, callback) {
    if (this.listeners.has(eventName)) {
      const callbacks = this.listeners.get(eventName);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(eventName, data) {
    if (this.listeners.has(eventName)) {
      this.listeners.get(eventName).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in event listener for ${eventName}:`, error);
        }
      });
    }
  }

  // Connection status
  isConnected() {
    return this.connected && this.socket && this.socket.connected;
  }

  // Get connection info
  getConnectionInfo() {
    return {
      connected: this.connected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      socketId: this.socket?.id || null,
    };
  }

  // Utility methods for common operations
  subscribeToUpdates(callback) {
    this.on('dashboard_update', callback);
    this.on('system_alert', callback);
    this.on('operation_update', callback);
  }

  unsubscribeFromUpdates(callback) {
    this.off('dashboard_update', callback);
    this.off('system_alert', callback);
    this.off('operation_update', callback);
  }

  // Health check
  healthCheck() {
    return new Promise((resolve, reject) => {
      if (!this.isConnected()) {
        reject(new Error('WebSocket not connected'));
        return;
      }

      const timeout = setTimeout(() => {
        this.off('pong', pongHandler);
        reject(new Error('Health check timeout'));
      }, 5000);

      const pongHandler = () => {
        clearTimeout(timeout);
        this.off('pong', pongHandler);
        resolve(true);
      };

      this.on('pong', pongHandler);
      this.ping();
    });
  }
}

export const websocketService = new WebSocketService();
