#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenStreetMap Overpass API Integration for Coastal Infrastructure Mapping
Open-source geographic data for marine conservation planning
"""

import os
import json
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CoastalInfrastructure:
    """Coastal infrastructure data from OpenStreetMap"""
    osm_id: str
    name: Optional[str]
    latitude: float
    longitude: float
    infrastructure_type: str
    tags: Dict[str, str]
    amenity: Optional[str] = None
    operator: Optional[str] = None


@dataclass
class MarineArea:
    """Marine area or protected zone data"""
    osm_id: str
    name: Optional[str]
    area_type: str
    boundary_coords: List[Tuple[float, float]]
    protection_level: Optional[str] = None
    tags: Dict[str, str] = None


class OpenStreetMapAPI:
    """OpenStreetMap Overpass API client for coastal infrastructure"""
    
    def __init__(self):
        self.base_url = "https://overpass-api.de/api"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={'User-Agent': 'WaterManagementSystem/1.0 (<EMAIL>)'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_coastal_infrastructure(
        self,
        bbox: Tuple[float, float, float, float],
        infrastructure_types: List[str] = None
    ) -> List[CoastalInfrastructure]:
        """Get coastal infrastructure within bounding box"""
        if not infrastructure_types:
            infrastructure_types = ['harbour', 'marina', 'port', 'pier', 'lighthouse', 'coastguard']
        
        try:
            # Build Overpass query
            query = self._build_infrastructure_query(bbox, infrastructure_types)
            
            async with self.session.post(
                f"{self.base_url}/interpreter",
                data=query,
                headers={'Content-Type': 'text/plain'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_infrastructure_data(data)
                else:
                    logger.error(f"❌ OSM infrastructure query failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting coastal infrastructure: {e}")
            return []
    
    async def get_marine_protected_areas(
        self,
        bbox: Tuple[float, float, float, float]
    ) -> List[MarineArea]:
        """Get marine protected areas and conservation zones"""
        try:
            query = f"""
            [out:json][timeout:25];
            (
              relation["boundary"="protected_area"]["protect_class"~"^[1-6]$"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});
              relation["boundary"="marine_protected_area"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});
              relation["leisure"="nature_reserve"]["natural"="water"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});
            );
            out geom;
            """
            
            async with self.session.post(
                f"{self.base_url}/interpreter",
                data=query,
                headers={'Content-Type': 'text/plain'}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_marine_areas(data)
                else:
                    logger.error(f"❌ OSM marine areas query failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting marine protected areas: {e}")
            return []
    
    async def get_coastal_features(
        self,
        bbox: Tuple[float, float, float, float]
    ) -> Dict[str, List[CoastalInfrastructure]]:
        """Get comprehensive coastal features"""
        try:
            # Get different types of coastal features
            tasks = [
                self.get_coastal_infrastructure(bbox, ['harbour', 'marina', 'port']),
                self.get_coastal_infrastructure(bbox, ['pier', 'jetty', 'breakwater']),
                self.get_coastal_infrastructure(bbox, ['lighthouse', 'beacon']),
                self.get_coastal_infrastructure(bbox, ['coastguard', 'lifeboat_station'])
            ]
            
            ports, structures, navigation, emergency = await asyncio.gather(*tasks, return_exceptions=True)
            
            return {
                'ports_harbours': ports if not isinstance(ports, Exception) else [],
                'coastal_structures': structures if not isinstance(structures, Exception) else [],
                'navigation_aids': navigation if not isinstance(navigation, Exception) else [],
                'emergency_services': emergency if not isinstance(emergency, Exception) else []
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting coastal features: {e}")
            return {}
    
    def _build_infrastructure_query(
        self,
        bbox: Tuple[float, float, float, float],
        infrastructure_types: List[str]
    ) -> str:
        """Build Overpass query for infrastructure"""
        # Create query for each infrastructure type
        type_queries = []
        
        for infra_type in infrastructure_types:
            if infra_type in ['harbour', 'marina', 'port']:
                type_queries.append(f'node["harbour"="{infra_type}"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});')
                type_queries.append(f'way["harbour"="{infra_type}"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});')
            elif infra_type in ['pier', 'jetty', 'breakwater']:
                type_queries.append(f'node["man_made"="{infra_type}"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});')
                type_queries.append(f'way["man_made"="{infra_type}"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});')
            elif infra_type == 'lighthouse':
                type_queries.append(f'node["man_made"="lighthouse"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});')
            elif infra_type in ['coastguard', 'lifeboat_station']:
                type_queries.append(f'node["emergency"="{infra_type}"]({bbox[1]},{bbox[0]},{bbox[3]},{bbox[2]});')
        
        query = f"""
        [out:json][timeout:25];
        (
          {chr(10).join(type_queries)}
        );
        out center meta;
        """
        
        return query
    
    def _parse_infrastructure_data(self, data: Dict) -> List[CoastalInfrastructure]:
        """Parse infrastructure data from Overpass response"""
        infrastructure_list = []
        
        try:
            for element in data.get('elements', []):
                tags = element.get('tags', {})
                
                # Determine infrastructure type
                infra_type = 'unknown'
                if 'harbour' in tags:
                    infra_type = tags['harbour']
                elif 'man_made' in tags:
                    infra_type = tags['man_made']
                elif 'emergency' in tags:
                    infra_type = tags['emergency']
                elif 'amenity' in tags:
                    infra_type = tags['amenity']
                
                # Get coordinates
                if element['type'] == 'node':
                    lat, lon = element['lat'], element['lon']
                elif 'center' in element:
                    lat, lon = element['center']['lat'], element['center']['lon']
                else:
                    continue
                
                infrastructure = CoastalInfrastructure(
                    osm_id=str(element['id']),
                    name=tags.get('name'),
                    latitude=lat,
                    longitude=lon,
                    infrastructure_type=infra_type,
                    tags=tags,
                    amenity=tags.get('amenity'),
                    operator=tags.get('operator')
                )
                
                infrastructure_list.append(infrastructure)
                
        except Exception as e:
            logger.error(f"❌ Error parsing infrastructure data: {e}")
        
        return infrastructure_list
    
    def _parse_marine_areas(self, data: Dict) -> List[MarineArea]:
        """Parse marine protected areas from Overpass response"""
        areas_list = []
        
        try:
            for element in data.get('elements', []):
                if element['type'] != 'relation':
                    continue
                
                tags = element.get('tags', {})
                
                # Determine area type
                area_type = 'protected_area'
                if 'boundary' in tags:
                    area_type = tags['boundary']
                elif 'leisure' in tags:
                    area_type = tags['leisure']
                
                # Extract boundary coordinates (simplified)
                boundary_coords = []
                for member in element.get('members', []):
                    if member.get('type') == 'way' and 'geometry' in member:
                        for coord in member['geometry']:
                            boundary_coords.append((coord['lat'], coord['lon']))
                
                # Calculate center point
                if boundary_coords:
                    center_lat = sum(coord[0] for coord in boundary_coords) / len(boundary_coords)
                    center_lon = sum(coord[1] for coord in boundary_coords) / len(boundary_coords)
                else:
                    center_lat, center_lon = 0.0, 0.0
                
                area = MarineArea(
                    osm_id=str(element['id']),
                    name=tags.get('name'),
                    area_type=area_type,
                    boundary_coords=boundary_coords,
                    protection_level=tags.get('protect_class'),
                    tags=tags
                )
                
                areas_list.append(area)
                
        except Exception as e:
            logger.error(f"❌ Error parsing marine areas: {e}")
        
        return areas_list


# Convenience functions
async def get_coastal_features(
    bbox: Tuple[float, float, float, float]
) -> Dict[str, List[CoastalInfrastructure]]:
    """Get coastal features for marine conservation analysis"""
    async with OpenStreetMapAPI() as api:
        return await api.get_coastal_features(bbox)

async def get_coastal_infrastructure_data(
    bbox: Tuple[float, float, float, float]
) -> Dict[str, Any]:
    """Get comprehensive coastal infrastructure data"""
    async with OpenStreetMapAPI() as api:
        # Get infrastructure and protected areas
        infrastructure_task = api.get_coastal_features(bbox)
        protected_areas_task = api.get_marine_protected_areas(bbox)
        
        infrastructure, protected_areas = await asyncio.gather(
            infrastructure_task, protected_areas_task, return_exceptions=True
        )
        
        return {
            'bbox': bbox,
            'infrastructure': infrastructure if not isinstance(infrastructure, Exception) else {},
            'protected_areas': protected_areas if not isinstance(protected_areas, Exception) else [],
            'timestamp': datetime.now().isoformat(),
            'data_source': 'OpenStreetMap'
        }


if __name__ == "__main__":
    async def test_osm_api():
        print("🗺️ Testing OpenStreetMap Overpass API")
        # Test area: San Francisco Bay
        test_bbox = (-122.5, 37.7, -122.3, 37.9)
        
        try:
            data = await get_coastal_infrastructure_data(test_bbox)
            print(f"✅ OSM data retrieved for bbox {test_bbox}")
            
            infra = data['infrastructure']
            print(f"   Ports/Harbours: {len(infra.get('ports_harbours', []))}")
            print(f"   Coastal Structures: {len(infra.get('coastal_structures', []))}")
            print(f"   Navigation Aids: {len(infra.get('navigation_aids', []))}")
            print(f"   Emergency Services: {len(infra.get('emergency_services', []))}")
            print(f"   Protected Areas: {len(data['protected_areas'])}")
            
            # Show sample infrastructure
            for category, items in infra.items():
                if items:
                    sample = items[0]
                    print(f"   Sample {category}: {sample.name or 'Unnamed'} ({sample.infrastructure_type})")
                    
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_osm_api())
