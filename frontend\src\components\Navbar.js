import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Waves as WavesIcon,
  Water as WaterIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Notifications as NotificationsIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';

const Navbar = ({ connected, systemStatus, onRefresh }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationAnchor, setNotificationAnchor] = useState(null);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationOpen = (event) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchor(null);
  };

  const navigationItems = [
    { path: '/', label: 'Dashboard', icon: <DashboardIcon /> },
    { path: '/marine', label: 'Marine Conservation', icon: <WavesIcon /> },
    { path: '/water', label: 'Water Management', icon: <WaterIcon /> },
    { path: '/analytics', label: 'Integrated Analytics', icon: <AnalyticsIcon /> },
    { path: '/status', label: 'System Status', icon: <InfoIcon /> },
    { path: '/settings', label: 'Settings', icon: <SettingsIcon /> },
  ];

  const isActive = (path) => location.pathname === path;

  const getStatusColor = () => {
    if (!connected) return 'error';
    if (systemStatus?.platform_status === 'operational') return 'success';
    return 'warning';
  };

  const getStatusLabel = () => {
    if (!connected) return 'Disconnected';
    if (systemStatus?.platform_status === 'operational') return 'Operational';
    return 'Warning';
  };

  return (
    <AppBar position="sticky" elevation={2}>
      <Toolbar>
        {/* Logo and Title */}
        <Box display="flex" alignItems="center" sx={{ flexGrow: 1 }}>
          <Typography
            variant="h6"
            component="div"
            sx={{ 
              fontWeight: 'bold',
              background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mr: 2
            }}
          >
            🌊💧 Unified Environmental Platform
          </Typography>
        </Box>

        {/* Desktop Navigation */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 1 }}>
          {navigationItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              startIcon={item.icon}
              onClick={() => navigate(item.path)}
              sx={{
                backgroundColor: isActive(item.path) ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>

        {/* Status and Actions */}
        <Box display="flex" alignItems="center" gap={2} sx={{ ml: 2 }}>
          {/* System Status */}
          <Chip
            label={getStatusLabel()}
            color={getStatusColor()}
            size="small"
            variant="outlined"
          />

          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton
              color="inherit"
              onClick={handleNotificationOpen}
            >
              <Badge badgeContent={systemStatus?.active_alerts || 0} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Refresh */}
          <Tooltip title="Refresh Data">
            <IconButton color="inherit" onClick={onRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          {/* Mobile Menu */}
          <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
            <IconButton
              color="inherit"
              onClick={handleMenuOpen}
            >
              <MenuIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Mobile Navigation Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          sx={{ display: { xs: 'block', md: 'none' } }}
        >
          {navigationItems.map((item) => (
            <MenuItem
              key={item.path}
              onClick={() => {
                navigate(item.path);
                handleMenuClose();
              }}
              selected={isActive(item.path)}
            >
              <Box display="flex" alignItems="center" gap={1}>
                {item.icon}
                {item.label}
              </Box>
            </MenuItem>
          ))}
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationAnchor}
          open={Boolean(notificationAnchor)}
          onClose={handleNotificationClose}
          PaperProps={{
            sx: { width: 300, maxHeight: 400 }
          }}
        >
          <MenuItem disabled>
            <Typography variant="subtitle2" fontWeight="bold">
              System Notifications
            </Typography>
          </MenuItem>
          
          {systemStatus?.active_alerts > 0 ? (
            <>
              <MenuItem>
                <Box>
                  <Typography variant="body2">
                    🚨 High debris count detected
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    2 minutes ago
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem>
                <Box>
                  <Typography variant="body2">
                    ⚠️ Water treatment efficiency below threshold
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    5 minutes ago
                  </Typography>
                </Box>
              </MenuItem>
            </>
          ) : (
            <MenuItem disabled>
              <Typography variant="body2" color="text.secondary">
                No active alerts
              </Typography>
            </MenuItem>
          )}
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
