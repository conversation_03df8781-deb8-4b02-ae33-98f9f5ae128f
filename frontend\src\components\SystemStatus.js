import React from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Computer as ComputerIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';

const SystemStatus = ({ systemStatus, dashboardData, connected }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'operational':
      case 'active':
        return <CheckCircleIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'operational':
      case 'active':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  // Sample system metrics
  const systemMetrics = [
    { name: 'CPU Usage', value: 45, unit: '%', status: 'good' },
    { name: 'Memory Usage', value: 62, unit: '%', status: 'good' },
    { name: 'Disk Usage', value: 78, unit: '%', status: 'warning' },
    { name: 'Network Latency', value: 23, unit: 'ms', status: 'good' },
  ];

  const serviceStatus = [
    { service: 'Marine Conservation API', status: 'operational', uptime: '99.9%', lastCheck: '2 min ago' },
    { service: 'Water Management API', status: 'operational', uptime: '99.8%', lastCheck: '1 min ago' },
    { service: 'Integrated Analytics', status: 'operational', uptime: '99.7%', lastCheck: '3 min ago' },
    { service: 'WebSocket Service', status: connected ? 'operational' : 'error', uptime: connected ? '99.9%' : '0%', lastCheck: 'now' },
    { service: 'Database', status: 'operational', uptime: '99.9%', lastCheck: '1 min ago' },
  ];

  const getMetricColor = (status) => {
    switch (status) {
      case 'good': return 'success';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Typography variant="h4" component="h1" gutterBottom>
        🔧 System Status Dashboard
      </Typography>

      {/* Overall Status */}
      <Alert 
        severity={systemStatus?.platform_status === 'operational' ? 'success' : 'warning'} 
        sx={{ mb: 3 }}
        icon={getStatusIcon(systemStatus?.platform_status)}
      >
        Platform Status: {systemStatus?.platform_status || 'Unknown'} - 
        {systemStatus?.platform_status === 'operational' ? 
          ' All systems operational' : 
          ' Some systems require attention'
        }
      </Alert>

      {/* System Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Marine Conservation
                  </Typography>
                  <Chip 
                    label={systemStatus?.marine_conservation || 'Active'}
                    color={getStatusColor(systemStatus?.marine_conservation || 'active')}
                  />
                </Box>
                {getStatusIcon(systemStatus?.marine_conservation || 'active')}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Water Management
                  </Typography>
                  <Chip 
                    label={systemStatus?.water_management || 'Active'}
                    color={getStatusColor(systemStatus?.water_management || 'active')}
                  />
                </Box>
                {getStatusIcon(systemStatus?.water_management || 'active')}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Integration Status
                  </Typography>
                  <Chip 
                    label={systemStatus?.integration_status || 'Active'}
                    color={getStatusColor(systemStatus?.integration_status || 'active')}
                  />
                </Box>
                {getStatusIcon(systemStatus?.integration_status || 'active')}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Connection Status
                  </Typography>
                  <Chip 
                    label={connected ? 'Connected' : 'Disconnected'}
                    color={connected ? 'success' : 'error'}
                  />
                </Box>
                {connected ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Performance Metrics
              </Typography>
              <List>
                {systemMetrics.map((metric, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {metric.name.includes('CPU') && <ComputerIcon color={getMetricColor(metric.status)} />}
                      {metric.name.includes('Memory') && <StorageIcon color={getMetricColor(metric.status)} />}
                      {metric.name.includes('Disk') && <StorageIcon color={getMetricColor(metric.status)} />}
                      {metric.name.includes('Network') && <NetworkIcon color={getMetricColor(metric.status)} />}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography>{metric.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {metric.value}{metric.unit}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <LinearProgress 
                          variant="determinate" 
                          value={metric.value} 
                          color={getMetricColor(metric.status)}
                          sx={{ mt: 1 }}
                        />
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Service Health
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Service</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Uptime</TableCell>
                      <TableCell>Last Check</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {serviceStatus.map((service, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            {getStatusIcon(service.status)}
                            {service.service}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={service.status} 
                            color={getStatusColor(service.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{service.uptime}</TableCell>
                        <TableCell>{service.lastCheck}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Operation Statistics */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Operation Statistics
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <SpeedIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Total Operations"
                    secondary={systemStatus?.total_operations || 0}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <WarningIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Active Alerts"
                    secondary={systemStatus?.active_alerts || 0}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="System Uptime"
                    secondary={systemStatus?.uptime || '99.9%'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon color="info" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Sync"
                    secondary={systemStatus?.last_sync ? new Date(systemStatus.last_sync).toLocaleString() : 'Never'}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Real-time Data */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Real-time Data Status
              </Typography>
              {dashboardData ? (
                <List>
                  <ListItem>
                    <ListItemText
                      primary="Last Update"
                      secondary={new Date(dashboardData.timestamp).toLocaleString()}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="System Health"
                      secondary={`${(dashboardData.real_time?.system_health * 100 || 0).toFixed(1)}%`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Active Operations"
                      secondary={dashboardData.real_time?.active_operations || 0}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Alert Count"
                      secondary={dashboardData.real_time?.alerts_count || 0}
                    />
                  </ListItem>
                </List>
              ) : (
                <Typography color="text.secondary">
                  No real-time data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemStatus;
