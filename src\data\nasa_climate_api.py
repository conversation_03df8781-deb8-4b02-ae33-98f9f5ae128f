"""NASA Climate API Integration for Climate Data Collection."""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class NASAClimateAPI:
    """NASA Climate API integration."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://power.larc.nasa.gov/api/temporal"
        self.session = None
    
    @log_async_function_call
    async def get_climate_data(self, location: Dict[str, float], 
                              start_date: str, end_date: str) -> Dict[str, Any]:
        """Get NASA climate data."""
        try:
            parameters = [
                'T2M',      # Temperature at 2 meters
                'PRECTOTCORR',  # Precipitation
                'RH2M',     # Relative humidity
                'WS2M',     # Wind speed
                'PS'        # Surface pressure
            ]
            
            url = f"{self.base_url}/daily/point"
            params = {
                'parameters': ','.join(parameters),
                'community': 'RE',
                'longitude': location['lon'],
                'latitude': location['lat'],
                'start': start_date.replace('-', ''),
                'end': end_date.replace('-', ''),
                'format': 'JSON'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Process the data
                        processed_data = await self._process_nasa_data(data)
                        
                        return {
                            'status': 'success',
                            'source': 'NASA POWER',
                            'location': location,
                            'date_range': {'start': start_date, 'end': end_date},
                            'climate_data': processed_data,
                            'timestamp': datetime.now().isoformat()
                        }
                    else:
                        return {'status': 'error', 'error': f'API returned status {response.status}'}
        
        except Exception as e:
            logger.error(f"NASA Climate API error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _process_nasa_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process raw NASA climate data."""
        properties = raw_data.get('properties', {}).get('parameter', {})
        
        processed = {
            'temperature': list(properties.get('T2M', {}).values()),
            'precipitation': list(properties.get('PRECTOTCORR', {}).values()),
            'humidity': list(properties.get('RH2M', {}).values()),
            'wind_speed': list(properties.get('WS2M', {}).values()),
            'pressure': list(properties.get('PS', {}).values()),
            'dates': list(properties.get('T2M', {}).keys()) if 'T2M' in properties else []
        }
        
        # Calculate statistics
        stats = {}
        for param, values in processed.items():
            if param != 'dates' and values:
                numeric_values = [v for v in values if isinstance(v, (int, float)) and v != -999]
                if numeric_values:
                    stats[param] = {
                        'mean': sum(numeric_values) / len(numeric_values),
                        'min': min(numeric_values),
                        'max': max(numeric_values),
                        'count': len(numeric_values)
                    }
        
        processed['statistics'] = stats
        return processed
    
    @log_async_function_call
    async def get_satellite_data(self, location: Dict[str, float]) -> Dict[str, Any]:
        """Get NASA satellite data."""
        try:
            # Simulate satellite data (in real implementation, would use actual NASA APIs)
            satellite_data = {
                'land_surface_temperature': {
                    'day': 28.5,
                    'night': 16.2,
                    'quality': 'good'
                },
                'vegetation_indices': {
                    'ndvi': 0.65,
                    'evi': 0.58,
                    'lai': 2.3
                },
                'precipitation_estimates': {
                    'daily_total': 2.5,
                    'quality_flag': 'high'
                },
                'soil_moisture': {
                    'surface': 0.35,
                    'root_zone': 0.42
                }
            }
            
            return {
                'status': 'success',
                'source': 'NASA Satellite',
                'location': location,
                'satellite_data': satellite_data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"NASA Satellite API error: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience function
async def get_nasa_climate_data(api_key: str, location: Dict[str, float], 
                               days_back: int = 30) -> Dict[str, Any]:
    """Get comprehensive NASA climate data."""
    api = NASAClimateAPI(api_key)
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
    
    climate_data = await api.get_climate_data(location, start_date, end_date)
    satellite_data = await api.get_satellite_data(location)
    
    return {
        'climate_data': climate_data,
        'satellite_data': satellite_data,
        'collection_timestamp': datetime.now().isoformat()
    }
