"""
NOAA Climate Data API Collector for historical climate records and forecasts.

This module provides integration with NOAA (National Oceanic and Atmospheric Administration)
APIs for collecting comprehensive climate and weather data.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


@dataclass
class NOAAClimateData:
    """NOAA climate data structure."""
    timestamp: datetime
    station_id: Optional[str]
    location: str
    latitude: float
    longitude: float
    dataset: str
    datatype: str
    value: float
    unit: str
    attributes: Dict[str, Any]
    source: str = "noaa"


@dataclass
class NOAAStation:
    """NOAA weather station information."""
    id: str
    name: str
    latitude: float
    longitude: float
    elevation: float
    min_date: str
    max_date: str
    datacoverage: float


class NOAAClimateCollector:
    """
    NOAA Climate Data API collector.
    
    Supports:
    - Climate Data Online (CDO) API
    - Historical weather data
    - Climate normals
    - Extreme weather events
    - Weather station data
    - Climate summaries
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_token = self.settings.NOAA_API_TOKEN
        self.base_url = self.settings.NOAA_BASE_URL
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_initialized = False
        
        # NOAA API endpoints
        self.endpoints = {
            'datasets': f"{self.base_url}/datasets",
            'datacategories': f"{self.base_url}/datacategories",
            'datatypes': f"{self.base_url}/datatypes",
            'locationcategories': f"{self.base_url}/locationcategories",
            'locations': f"{self.base_url}/locations",
            'stations': f"{self.base_url}/stations",
            'data': f"{self.base_url}/data"
        }
        
        # Common data types for climate analysis
        self.climate_datatypes = {
            'TMAX': 'Maximum temperature',
            'TMIN': 'Minimum temperature',
            'TAVG': 'Average temperature',
            'PRCP': 'Precipitation',
            'SNOW': 'Snowfall',
            'SNWD': 'Snow depth',
            'AWND': 'Average wind speed',
            'WSF2': 'Fastest 2-minute wind speed',
            'WT01': 'Fog, ice fog, or freezing fog',
            'WT02': 'Heavy fog or heaving freezing fog',
            'WT03': 'Thunder',
            'WT04': 'Ice pellets, sleet, snow pellets, or small hail',
            'WT05': 'Hail',
            'WT06': 'Glaze or rime',
            'WT07': 'Dust, volcanic ash, blowing dust, blowing sand, or blowing obstruction',
            'WT08': 'Smoke or haze',
            'WT09': 'Blowing or drifting snow',
            'WT11': 'High or damaging winds',
            'WT13': 'Mist',
            'WT14': 'Drizzle',
            'WT15': 'Freezing drizzle',
            'WT16': 'Rain',
            'WT17': 'Freezing rain',
            'WT18': 'Snow, snow pellets, snow grains, or ice crystals',
            'WT19': 'Unknown source of precipitation',
            'WT21': 'Ground fog',
            'WT22': 'Ice fog or freezing fog'
        }
        
        # Rate limiting
        self.requests_per_second = 5  # NOAA allows 5 requests per second
        self.last_request_time = 0
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the NOAA Climate collector."""
        try:
            logger.info("Initializing NOAA Climate Data collector...")
            
            # Create HTTP session with authentication
            headers = {"User-Agent": "WaterManagement-DecarbonisationSystem/1.0"}
            if self.api_token:
                headers["token"] = self.api_token
            
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=60),
                headers=headers
            )
            
            # Test API connection
            test_success = await self._test_api_connection()
            
            if test_success:
                self.is_initialized = True
                logger.info("NOAA Climate Data collector initialized successfully")
                return True
            else:
                logger.warning("NOAA API test failed, but collector initialized for limited use")
                self.is_initialized = True
                return True
                
        except Exception as e:
            logger.error(f"Failed to initialize NOAA collector: {e}")
            return False
    
    async def _test_api_connection(self) -> bool:
        """Test API connection with datasets endpoint."""
        try:
            await self._check_rate_limit()
            
            url = self.endpoints['datasets']
            params = {"limit": 1}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('results'):
                        logger.info("NOAA API test successful")
                        return True
                    else:
                        logger.warning("NOAA API returned empty results")
                        return False
                elif response.status == 401:
                    logger.warning("NOAA API authentication failed - API token may be invalid")
                    return False
                else:
                    logger.warning(f"NOAA API test returned status {response.status}")
                    return False
                    
        except Exception as e:
            logger.warning(f"NOAA API connection test failed: {e}")
            return False
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting (5 requests per second)."""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < 0.2:  # 0.2 seconds = 5 requests per second
            sleep_time = 0.2 - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = asyncio.get_event_loop().time()
    
    @cache_decorator(ttl=3600, key_prefix="noaa_datasets")
    async def get_datasets(self) -> List[Dict[str, Any]]:
        """Get available NOAA datasets."""
        try:
            if not self.is_initialized:
                return []
            
            await self._check_rate_limit()
            
            url = self.endpoints['datasets']
            params = {"limit": 1000}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('results', [])
                else:
                    logger.error(f"NOAA datasets API error {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to get NOAA datasets: {e}")
            return []
    
    @cache_decorator(ttl=7200, key_prefix="noaa_stations")
    async def find_stations(self, latitude: float, longitude: float, 
                          radius_km: float = 50, limit: int = 10) -> List[NOAAStation]:
        """Find weather stations near a location."""
        try:
            if not self.is_initialized:
                return []
            
            await self._check_rate_limit()
            
            url = self.endpoints['stations']
            params = {
                "extent": f"{latitude-0.5},{longitude-0.5},{latitude+0.5},{longitude+0.5}",
                "limit": limit,
                "datasetid": "GHCND"  # Global Historical Climatology Network Daily
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    stations = []
                    
                    for station_data in data.get('results', []):
                        station = NOAAStation(
                            id=station_data.get('id', ''),
                            name=station_data.get('name', ''),
                            latitude=station_data.get('latitude', 0.0),
                            longitude=station_data.get('longitude', 0.0),
                            elevation=station_data.get('elevation', 0.0),
                            min_date=station_data.get('mindate', ''),
                            max_date=station_data.get('maxdate', ''),
                            datacoverage=station_data.get('datacoverage', 0.0)
                        )
                        stations.append(station)
                    
                    return stations
                else:
                    logger.error(f"NOAA stations API error {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to find NOAA stations: {e}")
            return []
    
    async def get_station_data(self, station_id: str, start_date: str, end_date: str,
                             datatypes: List[str] = None) -> List[NOAAClimateData]:
        """Get climate data from a specific weather station."""
        try:
            if not self.is_initialized:
                return []
            
            # Default to basic climate parameters
            if not datatypes:
                datatypes = ['TMAX', 'TMIN', 'PRCP', 'AWND']
            
            await self._check_rate_limit()
            
            url = self.endpoints['data']
            params = {
                "datasetid": "GHCND",
                "stationid": station_id,
                "startdate": start_date,
                "enddate": end_date,
                "datatypeid": ",".join(datatypes),
                "limit": 1000,
                "units": "metric"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_station_data(data.get('results', []))
                else:
                    logger.error(f"NOAA station data API error {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to get station data for {station_id}: {e}")
            return []
    
    def _parse_station_data(self, data: List[Dict[str, Any]]) -> List[NOAAClimateData]:
        """Parse NOAA station data response."""
        parsed_data = []
        
        for item in data:
            try:
                # Parse date more safely
                date_str = item.get('date', '')
                if 'T' in date_str:
                    timestamp = datetime.fromisoformat(date_str.replace('T', ' '))
                else:
                    timestamp = datetime.strptime(date_str, '%Y-%m-%d')

                climate_data = NOAAClimateData(
                    timestamp=timestamp,
                    station_id=item.get('station', ''),
                    location=f"Station {item.get('station', 'Unknown')}",
                    latitude=0.0,  # Will be filled from station info if available
                    longitude=0.0,  # Will be filled from station info if available
                    dataset=item.get('datasetid', ''),
                    datatype=item.get('datatype', ''),
                    value=float(item.get('value', 0)),
                    unit=self._get_unit_for_datatype(item.get('datatype', '')),
                    attributes=item.get('attributes', {})
                )
                parsed_data.append(climate_data)
            except (ValueError, TypeError) as e:
                logger.warning(f"Failed to parse NOAA data item: {e}")
                continue
        
        return parsed_data
    
    def _get_unit_for_datatype(self, datatype: str) -> str:
        """Get the unit for a specific NOAA datatype."""
        unit_mapping = {
            'TMAX': '°C',
            'TMIN': '°C', 
            'TAVG': '°C',
            'PRCP': 'mm',
            'SNOW': 'mm',
            'SNWD': 'mm',
            'AWND': 'm/s',
            'WSF2': 'm/s'
        }
        return unit_mapping.get(datatype, 'unknown')
    
    async def get_climate_summary_for_location(self, latitude: float, longitude: float,
                                             location_name: str = None,
                                             days_back: int = 30) -> Dict[str, Any]:
        """Get comprehensive climate summary for a location."""
        try:
            logger.info(f"Getting NOAA climate summary for {location_name or f'Lat:{latitude}, Lon:{longitude}'}")
            
            # Find nearby stations
            stations = await self.find_stations(latitude, longitude, radius_km=100, limit=5)
            
            if not stations:
                logger.warning("No NOAA stations found near the location")
                return {
                    "location": location_name or f"Lat:{latitude}, Lon:{longitude}",
                    "error": "No weather stations found in the area",
                    "stations_found": 0
                }
            
            # Get recent data from the best station (highest data coverage)
            best_station = max(stations, key=lambda s: s.datacoverage)
            
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
            
            climate_data = await self.get_station_data(
                best_station.id, start_date, end_date,
                ['TMAX', 'TMIN', 'PRCP', 'AWND']
            )
            
            summary = {
                "location": location_name or f"Lat:{latitude}, Lon:{longitude}",
                "latitude": latitude,
                "longitude": longitude,
                "timestamp": datetime.now().isoformat(),
                "stations_found": len(stations),
                "primary_station": {
                    "id": best_station.id,
                    "name": best_station.name,
                    "distance_km": self._calculate_distance(
                        latitude, longitude, best_station.latitude, best_station.longitude
                    ),
                    "data_coverage": best_station.datacoverage,
                    "elevation": best_station.elevation
                },
                "data_period": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "days": days_back
                },
                "climate_data": self._analyze_climate_data(climate_data),
                "raw_data_points": len(climate_data)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get NOAA climate summary: {e}")
            return {}
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate approximate distance between two points in kilometers."""
        # Simple approximation using Pythagorean theorem
        lat_diff = lat2 - lat1
        lon_diff = lon2 - lon1
        return ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 111  # Rough km per degree
    
    def _analyze_climate_data(self, data: List[NOAAClimateData]) -> Dict[str, Any]:
        """Analyze climate data and calculate statistics."""
        if not data:
            return {"error": "No data available for analysis"}
        
        # Group data by type
        data_by_type = {}
        for item in data:
            if item.datatype not in data_by_type:
                data_by_type[item.datatype] = []
            data_by_type[item.datatype].append(item.value)
        
        analysis = {}
        
        for datatype, values in data_by_type.items():
            if values:
                analysis[datatype] = {
                    "name": self.climate_datatypes.get(datatype, datatype),
                    "unit": self._get_unit_for_datatype(datatype),
                    "count": len(values),
                    "average": sum(values) / len(values),
                    "minimum": min(values),
                    "maximum": max(values),
                    "latest": values[-1] if values else None
                }
        
        return analysis
    
    async def get_extreme_weather_events(self, latitude: float, longitude: float,
                                       start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get extreme weather events for a location and time period."""
        try:
            # Find stations
            stations = await self.find_stations(latitude, longitude, limit=3)
            
            if not stations:
                return []
            
            extreme_events = []
            
            for station in stations:
                # Get weather type data (WT codes indicate weather phenomena)
                weather_types = ['WT01', 'WT02', 'WT03', 'WT04', 'WT05', 'WT11']
                
                data = await self.get_station_data(
                    station.id, start_date, end_date, weather_types
                )
                
                for item in data:
                    if item.value > 0:  # Weather type occurred
                        extreme_events.append({
                            "date": item.timestamp.strftime("%Y-%m-%d"),
                            "station": station.name,
                            "event_type": self.climate_datatypes.get(item.datatype, item.datatype),
                            "datatype_code": item.datatype,
                            "station_distance_km": self._calculate_distance(
                                latitude, longitude, station.latitude, station.longitude
                            )
                        })
                
                # Small delay between station requests
                await asyncio.sleep(0.3)
            
            # Sort by date
            extreme_events.sort(key=lambda x: x["date"])
            
            return extreme_events
            
        except Exception as e:
            logger.error(f"Failed to get extreme weather events: {e}")
            return []
    
    async def get_climate_normals(self, latitude: float, longitude: float) -> Dict[str, Any]:
        """Get climate normals (30-year averages) for a location."""
        try:
            # This would typically use NOAA's climate normals dataset
            # For now, we'll provide a placeholder implementation
            
            stations = await self.find_stations(latitude, longitude, limit=1)
            
            if not stations:
                return {}
            
            # Get historical data for trend analysis
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
            
            data = await self.get_station_data(
                stations[0].id, start_date, end_date,
                ['TMAX', 'TMIN', 'PRCP']
            )
            
            if not data:
                return {}
            
            # Calculate annual averages as proxy for normals
            analysis = self._analyze_climate_data(data)
            
            normals = {
                "location": f"Lat:{latitude}, Lon:{longitude}",
                "station": stations[0].name,
                "period": "Annual average (proxy for 30-year normal)",
                "temperature": {
                    "max_avg": analysis.get('TMAX', {}).get('average'),
                    "min_avg": analysis.get('TMIN', {}).get('average'),
                    "unit": "°C"
                },
                "precipitation": {
                    "annual_avg": analysis.get('PRCP', {}).get('average', 0) * 365,  # Rough annual estimate
                    "unit": "mm"
                },
                "data_quality": {
                    "station_coverage": stations[0].datacoverage,
                    "data_points": len(data)
                }
            }
            
            return normals
            
        except Exception as e:
            logger.error(f"Failed to get climate normals: {e}")
            return {}
    
    async def shutdown(self):
        """Shutdown the collector."""
        try:
            if self.session:
                await self.session.close()
            logger.info("NOAA Climate Data collector shutdown completed")
        except Exception as e:
            logger.error(f"Error during NOAA collector shutdown: {e}")


# Convenience functions
async def get_noaa_climate_for_location(location_name: str, lat: float, lon: float, 
                                      days_back: int = 30) -> Dict[str, Any]:
    """Get NOAA climate data for a specific location."""
    collector = NOAAClimateCollector()
    await collector.initialize()
    
    summary = await collector.get_climate_summary_for_location(lat, lon, location_name, days_back)
    
    await collector.shutdown()
    return summary


async def get_extreme_weather_for_location(location_name: str, lat: float, lon: float,
                                         start_date: str, end_date: str) -> List[Dict[str, Any]]:
    """Get extreme weather events for a specific location."""
    collector = NOAAClimateCollector()
    await collector.initialize()
    
    events = await collector.get_extreme_weather_events(lat, lon, start_date, end_date)
    
    await collector.shutdown()
    return events


async def get_climate_normals_for_location(location_name: str, lat: float, lon: float) -> Dict[str, Any]:
    """Get climate normals for a specific location."""
    collector = NOAAClimateCollector()
    await collector.initialize()
    
    normals = await collector.get_climate_normals(lat, lon)
    
    await collector.shutdown()
    return normals
