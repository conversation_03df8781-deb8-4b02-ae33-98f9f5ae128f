"""Compliance Agent for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ComplianceStandard(Enum):
    """Compliance standards."""
    WHO_DRINKING_WATER = "who_drinking_water"
    EPA_SAFE_DRINKING = "epa_safe_drinking"
    EU_DRINKING_WATER = "eu_drinking_water"
    ISO_14001 = "iso_14001"
    ISO_9001 = "iso_9001"
    LOCAL_REGULATIONS = "local_regulations"
    ENVIRONMENTAL_PERMITS = "environmental_permits"
    SAFETY_STANDARDS = "safety_standards"


class ComplianceStatus(Enum):
    """Compliance status levels."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    AT_RISK = "at_risk"
    UNDER_REVIEW = "under_review"
    PENDING_VERIFICATION = "pending_verification"


class ViolationType(Enum):
    """Types of compliance violations."""
    WATER_QUALITY = "water_quality"
    ENVIRONMENTAL = "environmental"
    SAFETY = "safety"
    OPERATIONAL = "operational"
    REPORTING = "reporting"
    DOCUMENTATION = "documentation"
    TRAINING = "training"
    EQUIPMENT = "equipment"


@dataclass
class ComplianceRequirement:
    """Individual compliance requirement."""
    requirement_id: str
    standard: ComplianceStandard
    description: str
    parameter: str
    limit_value: float
    unit: str
    monitoring_frequency: str
    current_status: ComplianceStatus
    last_check: datetime
    next_check: datetime


@dataclass
class ComplianceViolation:
    """Compliance violation record."""
    violation_id: str
    requirement_id: str
    violation_type: ViolationType
    description: str
    severity: str
    detected_date: datetime
    current_value: float
    limit_value: float
    corrective_actions: List[str]
    resolution_deadline: datetime
    status: str = "open"


@dataclass
class ComplianceAssessment:
    """Comprehensive compliance assessment."""
    assessment_id: str
    overall_compliance_score: float  # 0-100
    compliance_by_standard: Dict[str, float]
    active_violations: List[ComplianceViolation]
    at_risk_requirements: List[str]
    recommendations: List[str]
    assessment_date: datetime = field(default_factory=datetime.now)


class ComplianceAgent:
    """AI agent for regulatory compliance monitoring and management."""
    
    def __init__(self):
        self.compliance_requirements: Dict[str, ComplianceRequirement] = {}
        self.compliance_history: List[ComplianceAssessment] = []
        self.active_violations: List[ComplianceViolation] = []
        self.compliance_standards: Dict[ComplianceStandard, Dict[str, Any]] = {}
        
        # Initialize compliance standards and requirements
        self._initialize_compliance_standards()
        self._initialize_compliance_requirements()
    
    def _initialize_compliance_standards(self):
        """Initialize compliance standards and their requirements."""
        self.compliance_standards = {
            ComplianceStandard.WHO_DRINKING_WATER: {
                'ph': {'min': 6.5, 'max': 8.5, 'unit': 'pH units', 'frequency': 'daily'},
                'turbidity': {'min': 0.0, 'max': 4.0, 'unit': 'NTU', 'frequency': 'continuous'},
                'chlorine_residual': {'min': 0.2, 'max': 5.0, 'unit': 'mg/L', 'frequency': 'daily'},
                'bacteria_count': {'min': 0.0, 'max': 0.0, 'unit': 'CFU/100mL', 'frequency': 'daily'},
                'nitrates': {'min': 0.0, 'max': 50.0, 'unit': 'mg/L', 'frequency': 'monthly'},
                'fluoride': {'min': 0.0, 'max': 1.5, 'unit': 'mg/L', 'frequency': 'monthly'}
            },
            ComplianceStandard.EPA_SAFE_DRINKING: {
                'ph': {'min': 6.5, 'max': 8.5, 'unit': 'pH units', 'frequency': 'daily'},
                'turbidity': {'min': 0.0, 'max': 1.0, 'unit': 'NTU', 'frequency': 'continuous'},
                'chlorine_residual': {'min': 0.2, 'max': 4.0, 'unit': 'mg/L', 'frequency': 'daily'},
                'bacteria_count': {'min': 0.0, 'max': 0.0, 'unit': 'CFU/100mL', 'frequency': 'daily'},
                'lead': {'min': 0.0, 'max': 0.015, 'unit': 'mg/L', 'frequency': 'quarterly'},
                'copper': {'min': 0.0, 'max': 1.3, 'unit': 'mg/L', 'frequency': 'quarterly'}
            },
            ComplianceStandard.ISO_14001: {
                'environmental_objectives': {'target': 100, 'unit': '% achievement', 'frequency': 'quarterly'},
                'waste_reduction': {'target': 90, 'unit': '% reduction', 'frequency': 'monthly'},
                'energy_efficiency': {'target': 85, 'unit': '% efficiency', 'frequency': 'monthly'},
                'emissions_monitoring': {'target': 100, 'unit': '% compliance', 'frequency': 'continuous'}
            },
            ComplianceStandard.SAFETY_STANDARDS: {
                'safety_training': {'target': 100, 'unit': '% completion', 'frequency': 'quarterly'},
                'incident_rate': {'max': 2.0, 'unit': 'incidents/year', 'frequency': 'monthly'},
                'safety_inspections': {'target': 100, 'unit': '% completion', 'frequency': 'monthly'},
                'emergency_drills': {'target': 4, 'unit': 'drills/year', 'frequency': 'quarterly'}
            }
        }
    
    def _initialize_compliance_requirements(self):
        """Initialize compliance requirements from standards."""
        requirement_id = 1
        
        for standard, parameters in self.compliance_standards.items():
            for param, limits in parameters.items():
                req_id = f"REQ_{requirement_id:03d}"
                
                # Determine next check date based on frequency
                if limits['frequency'] == 'continuous':
                    next_check = datetime.now() + timedelta(hours=1)
                elif limits['frequency'] == 'daily':
                    next_check = datetime.now() + timedelta(days=1)
                elif limits['frequency'] == 'monthly':
                    next_check = datetime.now() + timedelta(days=30)
                elif limits['frequency'] == 'quarterly':
                    next_check = datetime.now() + timedelta(days=90)
                else:
                    next_check = datetime.now() + timedelta(days=7)
                
                requirement = ComplianceRequirement(
                    requirement_id=req_id,
                    standard=standard,
                    description=f"{param} compliance for {standard.value}",
                    parameter=param,
                    limit_value=limits.get('max', limits.get('target', 0)),
                    unit=limits['unit'],
                    monitoring_frequency=limits['frequency'],
                    current_status=ComplianceStatus.COMPLIANT,
                    last_check=datetime.now() - timedelta(days=1),
                    next_check=next_check
                )
                
                self.compliance_requirements[req_id] = requirement
                requirement_id += 1
    
    @log_async_function_call
    async def assess_compliance(self, operational_data: Dict[str, Any],
                              standards: List[ComplianceStandard] = None) -> Dict[str, Any]:
        """Conduct comprehensive compliance assessment."""
        try:
            if standards is None:
                standards = [ComplianceStandard.WHO_DRINKING_WATER, ComplianceStandard.EPA_SAFE_DRINKING]
            
            assessment_id = f"compliance_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Check compliance for each standard
            compliance_by_standard = {}
            all_violations = []
            at_risk_requirements = []
            
            for standard in standards:
                standard_compliance = await self._assess_standard_compliance(
                    standard, operational_data
                )
                
                compliance_by_standard[standard.value] = standard_compliance['compliance_score']
                all_violations.extend(standard_compliance['violations'])
                at_risk_requirements.extend(standard_compliance['at_risk'])
            
            # Calculate overall compliance score
            overall_score = sum(compliance_by_standard.values()) / len(compliance_by_standard)
            
            # Generate compliance recommendations
            recommendations = await self._generate_compliance_recommendations(
                all_violations, at_risk_requirements, operational_data
            )
            
            # Update active violations
            self.active_violations.extend(all_violations)
            
            # Create assessment
            assessment = ComplianceAssessment(
                assessment_id=assessment_id,
                overall_compliance_score=overall_score,
                compliance_by_standard=compliance_by_standard,
                active_violations=all_violations,
                at_risk_requirements=at_risk_requirements,
                recommendations=recommendations
            )
            
            self.compliance_history.append(assessment)
            
            return {
                'status': 'success',
                'assessment_id': assessment_id,
                'compliance_summary': {
                    'overall_compliance_score': overall_score,
                    'compliance_status': self._get_compliance_status(overall_score),
                    'standards_assessed': [s.value for s in standards],
                    'total_violations': len(all_violations),
                    'critical_violations': len([v for v in all_violations if v.severity == 'critical'])
                },
                'compliance_by_standard': compliance_by_standard,
                'violations': [
                    {
                        'violation_id': v.violation_id,
                        'requirement_id': v.requirement_id,
                        'violation_type': v.violation_type.value,
                        'description': v.description,
                        'severity': v.severity,
                        'current_value': v.current_value,
                        'limit_value': v.limit_value,
                        'resolution_deadline': v.resolution_deadline.isoformat()
                    }
                    for v in all_violations
                ],
                'at_risk_requirements': at_risk_requirements,
                'recommendations': recommendations,
                'assessment_date': assessment.assessment_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Compliance assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _assess_standard_compliance(self, standard: ComplianceStandard,
                                        data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess compliance with specific standard."""
        standard_requirements = self.compliance_standards.get(standard, {})
        violations = []
        at_risk = []
        compliant_count = 0
        total_count = 0
        
        for param, limits in standard_requirements.items():
            total_count += 1
            current_value = data.get(param, 0)
            
            # Check compliance
            is_compliant = True
            violation_description = ""
            
            if 'min' in limits and 'max' in limits:
                # Range check
                if current_value < limits['min']:
                    is_compliant = False
                    violation_description = f"{param} below minimum: {current_value} < {limits['min']}"
                elif current_value > limits['max']:
                    is_compliant = False
                    violation_description = f"{param} above maximum: {current_value} > {limits['max']}"
            elif 'max' in limits:
                # Maximum check
                if current_value > limits['max']:
                    is_compliant = False
                    violation_description = f"{param} exceeds limit: {current_value} > {limits['max']}"
            elif 'target' in limits:
                # Target achievement check
                if current_value < limits['target']:
                    is_compliant = False
                    violation_description = f"{param} below target: {current_value} < {limits['target']}"
            
            if is_compliant:
                compliant_count += 1
                
                # Check if at risk (within 10% of limit)
                if 'max' in limits:
                    if current_value > limits['max'] * 0.9:
                        at_risk.append(f"{param} approaching limit")
                elif 'min' in limits:
                    if current_value < limits['min'] * 1.1:
                        at_risk.append(f"{param} approaching minimum")
            else:
                # Create violation
                violation = await self._create_violation(
                    standard, param, violation_description, current_value, limits
                )
                violations.append(violation)
        
        # Calculate compliance score
        compliance_score = (compliant_count / total_count * 100) if total_count > 0 else 100
        
        return {
            'compliance_score': compliance_score,
            'violations': violations,
            'at_risk': at_risk,
            'compliant_parameters': compliant_count,
            'total_parameters': total_count
        }
    
    async def _create_violation(self, standard: ComplianceStandard, parameter: str,
                              description: str, current_value: float,
                              limits: Dict[str, Any]) -> ComplianceViolation:
        """Create compliance violation record."""
        violation_id = f"VIO_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{parameter}"
        
        # Determine violation type
        if parameter in ['ph', 'turbidity', 'chlorine_residual', 'bacteria_count']:
            violation_type = ViolationType.WATER_QUALITY
        elif parameter in ['emissions_monitoring', 'waste_reduction']:
            violation_type = ViolationType.ENVIRONMENTAL
        elif parameter in ['safety_training', 'incident_rate']:
            violation_type = ViolationType.SAFETY
        else:
            violation_type = ViolationType.OPERATIONAL
        
        # Determine severity
        limit_value = limits.get('max', limits.get('target', 0))
        if limit_value > 0:
            deviation = abs(current_value - limit_value) / limit_value
            if deviation > 0.5:
                severity = 'critical'
            elif deviation > 0.2:
                severity = 'high'
            elif deviation > 0.1:
                severity = 'medium'
            else:
                severity = 'low'
        else:
            severity = 'high' if current_value > 0 else 'low'
        
        # Generate corrective actions
        corrective_actions = await self._generate_corrective_actions(parameter, violation_type)
        
        # Set resolution deadline based on severity
        if severity == 'critical':
            deadline = datetime.now() + timedelta(hours=24)
        elif severity == 'high':
            deadline = datetime.now() + timedelta(days=3)
        elif severity == 'medium':
            deadline = datetime.now() + timedelta(days=7)
        else:
            deadline = datetime.now() + timedelta(days=30)
        
        return ComplianceViolation(
            violation_id=violation_id,
            requirement_id=f"REQ_{parameter}",
            violation_type=violation_type,
            description=description,
            severity=severity,
            detected_date=datetime.now(),
            current_value=current_value,
            limit_value=limit_value,
            corrective_actions=corrective_actions,
            resolution_deadline=deadline
        )
    
    async def _generate_corrective_actions(self, parameter: str,
                                         violation_type: ViolationType) -> List[str]:
        """Generate corrective actions for violations."""
        actions = []
        
        if parameter == 'ph':
            actions.extend([
                'Adjust chemical dosing to correct pH levels',
                'Check pH sensor calibration',
                'Verify chemical feed system operation',
                'Increase monitoring frequency until corrected'
            ])
        elif parameter == 'turbidity':
            actions.extend([
                'Increase coagulant dosing',
                'Check filter performance and backwash if needed',
                'Inspect raw water quality',
                'Verify turbidity meter calibration'
            ])
        elif parameter == 'chlorine_residual':
            actions.extend([
                'Adjust chlorine dosing rate',
                'Check chlorine feed system',
                'Verify contact time adequacy',
                'Test chlorine analyzer accuracy'
            ])
        elif parameter == 'bacteria_count':
            actions.extend([
                'Increase disinfection immediately',
                'Resample and retest',
                'Inspect distribution system',
                'Implement emergency disinfection protocol'
            ])
        elif violation_type == ViolationType.SAFETY:
            actions.extend([
                'Conduct immediate safety review',
                'Provide additional safety training',
                'Inspect safety equipment',
                'Update safety procedures if needed'
            ])
        elif violation_type == ViolationType.ENVIRONMENTAL:
            actions.extend([
                'Review environmental management procedures',
                'Implement additional monitoring',
                'Assess environmental impact',
                'Develop improvement plan'
            ])
        else:
            actions.extend([
                f'Investigate {parameter} deviation',
                'Implement corrective measures',
                'Increase monitoring frequency',
                'Review operational procedures'
            ])
        
        return actions
    
    async def _generate_compliance_recommendations(self, violations: List[ComplianceViolation],
                                                 at_risk: List[str],
                                                 data: Dict[str, Any]) -> List[str]:
        """Generate compliance improvement recommendations."""
        recommendations = []
        
        # Address critical violations first
        critical_violations = [v for v in violations if v.severity == 'critical']
        if critical_violations:
            recommendations.append("URGENT: Address critical compliance violations immediately")
            for violation in critical_violations:
                recommendations.extend(violation.corrective_actions[:2])  # Top 2 actions
        
        # Address high-priority violations
        high_violations = [v for v in violations if v.severity == 'high']
        if high_violations:
            recommendations.append("Address high-priority compliance violations within 3 days")
        
        # Preventive measures for at-risk parameters
        if at_risk:
            recommendations.append("Implement preventive measures for parameters approaching limits")
            recommendations.append("Increase monitoring frequency for at-risk parameters")
        
        # General compliance improvements
        violation_types = {}
        for violation in violations:
            vtype = violation.violation_type.value
            violation_types[vtype] = violation_types.get(vtype, 0) + 1
        
        # Most common violation type
        if violation_types:
            most_common = max(violation_types, key=violation_types.get)
            recommendations.append(f"Focus on improving {most_common} compliance procedures")
        
        # System-wide recommendations
        recommendations.extend([
            "Conduct comprehensive compliance training for all staff",
            "Implement automated compliance monitoring where possible",
            "Establish regular compliance review meetings",
            "Develop compliance dashboard for real-time monitoring"
        ])
        
        return recommendations
    
    def _get_compliance_status(self, score: float) -> str:
        """Get compliance status description."""
        if score >= 95:
            return 'Fully Compliant'
        elif score >= 85:
            return 'Mostly Compliant'
        elif score >= 70:
            return 'Partially Compliant'
        elif score >= 50:
            return 'Non-Compliant'
        else:
            return 'Severely Non-Compliant'
    
    @log_async_function_call
    async def generate_compliance_report(self, assessment_id: str) -> Dict[str, Any]:
        """Generate detailed compliance report."""
        try:
            # Find assessment
            assessment = next((a for a in self.compliance_history if a.assessment_id == assessment_id), None)
            if not assessment:
                return {'status': 'error', 'error': 'Assessment not found'}
            
            # Generate report sections
            executive_summary = await self._generate_executive_summary(assessment)
            detailed_findings = await self._generate_detailed_findings(assessment)
            action_plan = await self._generate_action_plan(assessment)
            
            report = {
                'report_id': f"RPT_{assessment_id}",
                'assessment_id': assessment_id,
                'report_date': datetime.now().isoformat(),
                'executive_summary': executive_summary,
                'detailed_findings': detailed_findings,
                'action_plan': action_plan,
                'compliance_trends': await self._analyze_compliance_trends(),
                'next_assessment_date': (datetime.now() + timedelta(days=30)).isoformat()
            }
            
            return {
                'status': 'success',
                'compliance_report': report
            }
            
        except Exception as e:
            logger.error(f"Compliance report generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_executive_summary(self, assessment: ComplianceAssessment) -> Dict[str, Any]:
        """Generate executive summary of compliance assessment."""
        return {
            'overall_compliance_score': assessment.overall_compliance_score,
            'compliance_status': self._get_compliance_status(assessment.overall_compliance_score),
            'total_violations': len(assessment.active_violations),
            'critical_violations': len([v for v in assessment.active_violations if v.severity == 'critical']),
            'standards_assessed': list(assessment.compliance_by_standard.keys()),
            'key_findings': [
                f"Overall compliance score: {assessment.overall_compliance_score:.1f}%",
                f"Total violations identified: {len(assessment.active_violations)}",
                f"Parameters at risk: {len(assessment.at_risk_requirements)}"
            ]
        }
    
    async def _generate_detailed_findings(self, assessment: ComplianceAssessment) -> Dict[str, Any]:
        """Generate detailed findings section."""
        findings_by_standard = {}
        
        for standard, score in assessment.compliance_by_standard.items():
            standard_violations = [v for v in assessment.active_violations 
                                 if any(req.standard.value == standard 
                                       for req in self.compliance_requirements.values() 
                                       if req.requirement_id == v.requirement_id)]
            
            findings_by_standard[standard] = {
                'compliance_score': score,
                'status': self._get_compliance_status(score),
                'violations_count': len(standard_violations),
                'violations': [
                    {
                        'parameter': v.requirement_id,
                        'description': v.description,
                        'severity': v.severity
                    }
                    for v in standard_violations
                ]
            }
        
        return findings_by_standard
    
    async def _generate_action_plan(self, assessment: ComplianceAssessment) -> Dict[str, Any]:
        """Generate action plan for compliance improvement."""
        immediate_actions = []
        short_term_actions = []
        long_term_actions = []
        
        # Categorize actions by timeline
        for violation in assessment.active_violations:
            if violation.severity == 'critical':
                immediate_actions.extend(violation.corrective_actions[:2])
            elif violation.severity == 'high':
                short_term_actions.extend(violation.corrective_actions[:2])
            else:
                long_term_actions.extend(violation.corrective_actions[:1])
        
        # Add general recommendations
        short_term_actions.extend(assessment.recommendations[:3])
        long_term_actions.extend(assessment.recommendations[3:])
        
        return {
            'immediate_actions': list(set(immediate_actions)),  # Remove duplicates
            'short_term_actions': list(set(short_term_actions)),
            'long_term_actions': list(set(long_term_actions)),
            'timeline': {
                'immediate': '24 hours',
                'short_term': '1-4 weeks',
                'long_term': '1-6 months'
            }
        }
    
    async def _analyze_compliance_trends(self) -> Dict[str, Any]:
        """Analyze compliance trends over time."""
        if len(self.compliance_history) < 2:
            return {'trend': 'insufficient_data'}
        
        # Get recent assessments
        recent_assessments = sorted(self.compliance_history, key=lambda x: x.assessment_date)[-5:]
        
        scores = [a.overall_compliance_score for a in recent_assessments]
        
        # Calculate trend
        if len(scores) >= 2:
            trend_slope = (scores[-1] - scores[0]) / len(scores)
            
            if trend_slope > 2:
                trend = 'improving'
            elif trend_slope < -2:
                trend = 'declining'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'recent_scores': scores,
            'average_score': sum(scores) / len(scores),
            'trend_analysis': f"Compliance trend is {trend} over the last {len(scores)} assessments"
        }


# Convenience functions
async def check_compliance(operational_data: Dict[str, Any], 
                         standards: List[str] = None) -> Dict[str, Any]:
    """Check compliance with specified standards."""
    agent = ComplianceAgent()
    
    if standards:
        standard_enums = [ComplianceStandard(s) for s in standards]
    else:
        standard_enums = None
    
    return await agent.assess_compliance(operational_data, standard_enums)


async def get_compliance_violations(operational_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Get current compliance violations."""
    agent = ComplianceAgent()
    result = await agent.assess_compliance(operational_data)
    
    if result['status'] == 'success':
        return result['violations']
    else:
        return []
