"""
Additional API Integrations for Water Management System.

Integrations for NASA Climate Data, NOAA Climate Data, World Bank Climate API,
and ECMWF (European Centre for Medium-Range Weather Forecasts) API.
"""

import logging
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import os

from src.utils.config import get_settings
from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class NASAClimateAPI:
    """
    NASA Climate Data API integration.
    
    Provides access to NASA's Earth science data including:
    - Landsat imagery
    - MODIS data
    - Earth temperature data
    - Atmospheric data
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = os.getenv('NASA_API_KEY', 'DEMO_KEY')
        self.base_url = 'https://api.nasa.gov'
        
    @log_async_function_call
    async def get_earth_imagery(self, lat: float, lon: float, 
                               date: str = None, dim: float = 0.15) -> Dict[str, Any]:
        """Get Earth imagery for specific coordinates."""
        try:
            if date is None:
                date = datetime.now().strftime('%Y-%m-%d')
            
            url = f"{self.base_url}/planetary/earth/imagery"
            params = {
                'lon': lon,
                'lat': lat,
                'date': date,
                'dim': dim,
                'api_key': self.api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.read()
                        return {
                            'status': 'success',
                            'imagery_data': data,
                            'coordinates': {'lat': lat, 'lon': lon},
                            'date': date,
                            'source': 'NASA Earth Imagery'
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"NASA API error: {response.status} - {error_text}")
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"NASA Earth imagery request failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_earth_assets(self, lat: float, lon: float, 
                              date: str = None, dim: float = 0.15) -> Dict[str, Any]:
        """Get Earth assets metadata for specific coordinates."""
        try:
            if date is None:
                date = datetime.now().strftime('%Y-%m-%d')
            
            url = f"{self.base_url}/planetary/earth/assets"
            params = {
                'lon': lon,
                'lat': lat,
                'date': date,
                'dim': dim,
                'api_key': self.api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'assets': data,
                            'coordinates': {'lat': lat, 'lon': lon},
                            'date': date,
                            'source': 'NASA Earth Assets'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"NASA Earth assets request failed: {e}")
            return {'status': 'error', 'error': str(e)}


class NOAAClimateAPI:
    """
    NOAA Climate Data API integration.
    
    Provides access to NOAA's climate data including:
    - Historical weather data
    - Climate normals
    - Extreme weather events
    - Precipitation data
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = os.getenv('NOAA_API_KEY', 'demo_key')
        self.base_url = 'https://www.ncdc.noaa.gov/cdo-web/api/v2'
        
    @log_async_function_call
    async def get_climate_data(self, dataset_id: str, location_id: str,
                              start_date: str, end_date: str,
                              data_types: List[str] = None) -> Dict[str, Any]:
        """Get climate data from NOAA."""
        try:
            if data_types is None:
                data_types = ['TMAX', 'TMIN', 'PRCP', 'SNOW']
            
            url = f"{self.base_url}/data"
            headers = {'token': self.api_key}
            params = {
                'datasetid': dataset_id,
                'locationid': location_id,
                'startdate': start_date,
                'enddate': end_date,
                'datatypeid': ','.join(data_types),
                'limit': 1000,
                'units': 'metric'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'climate_data': data.get('results', []),
                            'metadata': data.get('metadata', {}),
                            'dataset': dataset_id,
                            'location': location_id,
                            'source': 'NOAA Climate Data'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"NOAA climate data request failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_stations(self, dataset_id: str, location_id: str = None,
                          limit: int = 100) -> Dict[str, Any]:
        """Get weather stations information."""
        try:
            url = f"{self.base_url}/stations"
            headers = {'token': self.api_key}
            params = {
                'datasetid': dataset_id,
                'limit': limit
            }
            
            if location_id:
                params['locationid'] = location_id
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'stations': data.get('results', []),
                            'metadata': data.get('metadata', {}),
                            'source': 'NOAA Stations'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"NOAA stations request failed: {e}")
            return {'status': 'error', 'error': str(e)}


class WorldBankClimateAPI:
    """
    World Bank Climate Data API integration.
    
    Provides access to World Bank's climate data including:
    - Country-level climate data
    - Climate change indicators
    - Temperature and precipitation projections
    - Climate risk assessments
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = 'https://climateknowledgeportal.worldbank.org/api'
        
    @log_async_function_call
    async def get_country_climate_data(self, country_code: str, 
                                     variable: str = 'tas',
                                     period: str = 'year') -> Dict[str, Any]:
        """Get climate data for a specific country."""
        try:
            url = f"{self.base_url}/country/{country_code}/{variable}/{period}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'climate_data': data,
                            'country': country_code,
                            'variable': variable,
                            'period': period,
                            'source': 'World Bank Climate'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"World Bank climate data request failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_climate_projections(self, country_code: str,
                                    scenario: str = 'rcp85',
                                    period: str = '2020_2039') -> Dict[str, Any]:
        """Get climate projections for a country."""
        try:
            url = f"{self.base_url}/projections/{country_code}/{scenario}/{period}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'projections': data,
                            'country': country_code,
                            'scenario': scenario,
                            'period': period,
                            'source': 'World Bank Projections'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"World Bank projections request failed: {e}")
            return {'status': 'error', 'error': str(e)}


class ECMWFClimateAPI:
    """
    ECMWF (European Centre for Medium-Range Weather Forecasts) API integration.
    
    Provides access to ECMWF's climate data including:
    - ERA5 reanalysis data
    - Seasonal forecasts
    - Climate reanalysis
    - Atmospheric data
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.api_key = os.getenv('ECMWF_API_KEY', 'demo_key')
        self.base_url = 'https://api.ecmwf.int/v1'
        
    @log_async_function_call
    async def get_era5_data(self, variables: List[str], area: List[float],
                           date_range: Dict[str, str],
                           format: str = 'netcdf') -> Dict[str, Any]:
        """Get ERA5 reanalysis data."""
        try:
            url = f"{self.base_url}/resources/era5-single-levels"
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            request_data = {
                'product_type': 'reanalysis',
                'variable': variables,
                'year': date_range.get('year', '2023'),
                'month': date_range.get('month', '01'),
                'day': date_range.get('day', '01'),
                'time': date_range.get('time', '12:00'),
                'area': area,  # [north, west, south, east]
                'format': format
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=request_data) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'era5_data': data,
                            'variables': variables,
                            'area': area,
                            'date_range': date_range,
                            'source': 'ECMWF ERA5'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"ECMWF ERA5 data request failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_seasonal_forecast(self, variables: List[str], area: List[float],
                                  forecast_month: str) -> Dict[str, Any]:
        """Get seasonal forecast data."""
        try:
            url = f"{self.base_url}/resources/seasonal-original-single-levels"
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            request_data = {
                'system': '5',
                'variable': variables,
                'year': datetime.now().year,
                'month': forecast_month,
                'leadtime_month': ['1', '2', '3', '4', '5', '6'],
                'area': area,
                'format': 'netcdf'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=request_data) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'forecast_data': data,
                            'variables': variables,
                            'area': area,
                            'forecast_month': forecast_month,
                            'source': 'ECMWF Seasonal Forecast'
                        }
                    else:
                        return {'status': 'error', 'error': f"API error: {response.status}"}
                        
        except Exception as e:
            logger.error(f"ECMWF seasonal forecast request failed: {e}")
            return {'status': 'error', 'error': str(e)}


class MultiSourceClimateCollector:
    """
    Multi-source climate data collector that integrates all APIs.
    """
    
    def __init__(self):
        self.nasa_api = NASAClimateAPI()
        self.noaa_api = NOAAClimateAPI()
        self.worldbank_api = WorldBankClimateAPI()
        self.ecmwf_api = ECMWFClimateAPI()
        
    @log_async_function_call
    async def collect_comprehensive_climate_data(self, location: Dict[str, float],
                                               country_code: str,
                                               date_range: Dict[str, str]) -> Dict[str, Any]:
        """Collect climate data from all available sources."""
        try:
            logger.info("Collecting comprehensive climate data from multiple sources")
            
            # Collect data from all sources concurrently
            tasks = [
                self.nasa_api.get_earth_imagery(location['lat'], location['lon']),
                self.nasa_api.get_earth_assets(location['lat'], location['lon']),
                self.noaa_api.get_climate_data(
                    'GHCND', f"CITY:{country_code}000001",
                    date_range['start'], date_range['end']
                ),
                self.worldbank_api.get_country_climate_data(country_code),
                self.worldbank_api.get_climate_projections(country_code),
                self.ecmwf_api.get_era5_data(
                    ['2m_temperature', 'total_precipitation'],
                    [location['lat'] + 1, location['lon'] - 1, 
                     location['lat'] - 1, location['lon'] + 1],
                    date_range
                )
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            nasa_imagery, nasa_assets, noaa_data, wb_climate, wb_projections, ecmwf_data = results
            
            # Aggregate successful results
            aggregated_data = {
                'collection_timestamp': datetime.now(),
                'location': location,
                'country_code': country_code,
                'date_range': date_range,
                'data_sources': {}
            }
            
            # Add NASA data
            if isinstance(nasa_imagery, dict) and nasa_imagery.get('status') == 'success':
                aggregated_data['data_sources']['nasa_imagery'] = nasa_imagery
            
            if isinstance(nasa_assets, dict) and nasa_assets.get('status') == 'success':
                aggregated_data['data_sources']['nasa_assets'] = nasa_assets
            
            # Add NOAA data
            if isinstance(noaa_data, dict) and noaa_data.get('status') == 'success':
                aggregated_data['data_sources']['noaa_climate'] = noaa_data
            
            # Add World Bank data
            if isinstance(wb_climate, dict) and wb_climate.get('status') == 'success':
                aggregated_data['data_sources']['worldbank_climate'] = wb_climate
            
            if isinstance(wb_projections, dict) and wb_projections.get('status') == 'success':
                aggregated_data['data_sources']['worldbank_projections'] = wb_projections
            
            # Add ECMWF data
            if isinstance(ecmwf_data, dict) and ecmwf_data.get('status') == 'success':
                aggregated_data['data_sources']['ecmwf_era5'] = ecmwf_data
            
            # Calculate data quality score
            successful_sources = len(aggregated_data['data_sources'])
            total_sources = 6
            data_quality_score = successful_sources / total_sources
            
            aggregated_data['data_quality_score'] = data_quality_score
            aggregated_data['successful_sources'] = successful_sources
            aggregated_data['total_sources'] = total_sources
            
            return {
                'status': 'success',
                'aggregated_data': aggregated_data,
                'collection_summary': {
                    'sources_collected': successful_sources,
                    'data_quality_score': data_quality_score,
                    'collection_time': datetime.now()
                }
            }
            
        except Exception as e:
            logger.error(f"Multi-source climate data collection failed: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience functions
async def collect_nasa_earth_data(lat: float, lon: float, date: str = None) -> Dict[str, Any]:
    """Collect NASA Earth data for specific coordinates."""
    nasa_api = NASAClimateAPI()
    return await nasa_api.get_earth_imagery(lat, lon, date)


async def collect_noaa_climate_data(dataset_id: str, location_id: str,
                                   start_date: str, end_date: str) -> Dict[str, Any]:
    """Collect NOAA climate data."""
    noaa_api = NOAAClimateAPI()
    return await noaa_api.get_climate_data(dataset_id, location_id, start_date, end_date)


async def collect_worldbank_climate_data(country_code: str) -> Dict[str, Any]:
    """Collect World Bank climate data."""
    wb_api = WorldBankClimateAPI()
    return await wb_api.get_country_climate_data(country_code)


async def collect_ecmwf_era5_data(variables: List[str], area: List[float],
                                 date_range: Dict[str, str]) -> Dict[str, Any]:
    """Collect ECMWF ERA5 data."""
    ecmwf_api = ECMWFClimateAPI()
    return await ecmwf_api.get_era5_data(variables, area, date_range)


async def collect_all_climate_sources(location: Dict[str, float], country_code: str,
                                     date_range: Dict[str, str]) -> Dict[str, Any]:
    """Collect climate data from all available sources."""
    collector = MultiSourceClimateCollector()
    return await collector.collect_comprehensive_climate_data(location, country_code, date_range)
