"""
API routes for the Water Management Decarbonisation System.

This module defines all REST API endpoints for the system.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

from src.utils.database import health_check, get_recent_climate_data
from src.utils.config import get_settings

logger = logging.getLogger(__name__)

# Create main router
router = APIRouter()


@router.get("/health")
async def api_health_check():
    """API health check endpoint."""
    try:
        db_health = await health_check()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": db_health,
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@router.get("/climate/recent")
async def get_recent_climate(
    location: Optional[str] = None,
    hours: int = 24
):
    """Get recent climate data."""
    try:
        data = await get_recent_climate_data(location, hours)
        
        return {
            "success": True,
            "data": data,
            "count": len(data),
            "location": location,
            "time_range_hours": hours
        }
    except Exception as e:
        logger.error(f"Failed to get climate data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/status")
async def get_system_status():
    """Get overall system status."""
    try:
        settings = get_settings()
        
        return {
            "status": "operational",
            "environment": settings.APP_ENVIRONMENT,
            "features": {
                "climate_integration": settings.ENABLE_CLIMATE_INTEGRATION,
                "real_time_optimization": settings.ENABLE_REAL_TIME_OPTIMIZATION,
                "multi_agent_system": settings.ENABLE_MULTI_AGENT_SYSTEM
            },
            "apis": {
                "gemini": bool(settings.GOOGLE_API_KEY),
                "openweather": bool(settings.OPENWEATHER_API_KEY),
                "nasa": bool(settings.NASA_API_KEY)
            }
        }
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
