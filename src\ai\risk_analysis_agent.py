"""
Risk Analysis Agent.

Comprehensive risk assessment and management for water management systems
with predictive analytics, scenario modeling, and mitigation strategies.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum

from src.utils.config import get_settings

logger = logging.getLogger(__name__)


class RiskCategory(Enum):
    """Risk categories."""
    OPERATIONAL = "operational"
    FINANCIAL = "financial"
    ENVIRONMENTAL = "environmental"
    REGULATORY = "regulatory"
    TECHNICAL = "technical"
    SAFETY = "safety"
    CYBERSECURITY = "cybersecurity"
    CLIMATE = "climate"


class RiskLevel(Enum):
    """Risk severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NEGLIGIBLE = "negligible"


@dataclass
class Risk:
    """Risk assessment data structure."""
    risk_id: str
    name: str
    category: RiskCategory
    description: str
    probability: float  # 0.0 to 1.0
    impact: float  # 0.0 to 1.0
    risk_score: float  # probability * impact
    risk_level: RiskLevel
    triggers: List[str]
    consequences: List[str]
    mitigation_strategies: List[str]
    monitoring_indicators: List[str]
    last_assessment: datetime


class RiskAnalysisAgent:
    """
    Comprehensive risk analysis agent for water management systems.
    
    Provides:
    - Multi-dimensional risk assessment
    - Predictive risk modeling
    - Scenario-based risk analysis
    - Real-time risk monitoring
    - Mitigation strategy optimization
    - Risk portfolio management
    """
    
    def __init__(self):
        self.settings = get_settings()
        
        # Risk database
        self.risks: Dict[str, Risk] = {}
        self.risk_history: List[Dict[str, Any]] = []
        
        # Risk models
        self.risk_models = self._initialize_risk_models()
        
        # Initialize standard risks
        self._populate_standard_risks()
    
    async def assess_system_risks(self, system_data: Dict[str, Any],
                                assessment_scope: str = 'comprehensive') -> Dict[str, Any]:
        """Perform comprehensive system risk assessment."""
        try:
            logger.info(f"Performing {assessment_scope} risk assessment")
            
            # Operational risks
            operational_risks = await self._assess_operational_risks(system_data)
            
            # Financial risks
            financial_risks = await self._assess_financial_risks(system_data)
            
            # Environmental risks
            environmental_risks = await self._assess_environmental_risks(system_data)
            
            # Technical risks
            technical_risks = await self._assess_technical_risks(system_data)
            
            # Climate risks
            climate_risks = await self._assess_climate_risks(system_data)
            
            # Cybersecurity risks
            cyber_risks = await self._assess_cybersecurity_risks(system_data)
            
            # Aggregate risk assessment
            all_risks = (operational_risks + financial_risks + environmental_risks + 
                        technical_risks + climate_risks + cyber_risks)
            
            # Calculate overall risk metrics
            risk_metrics = self._calculate_risk_metrics(all_risks)
            
            # Generate risk matrix
            risk_matrix = self._create_risk_matrix(all_risks)
            
            # Prioritize risks
            prioritized_risks = self._prioritize_risks(all_risks)
            
            # Generate mitigation plan
            mitigation_plan = await self._generate_mitigation_plan(prioritized_risks)
            
            return {
                'assessment_id': f"risk_assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'assessment_scope': assessment_scope,
                'risk_categories': {
                    'operational': operational_risks,
                    'financial': financial_risks,
                    'environmental': environmental_risks,
                    'technical': technical_risks,
                    'climate': climate_risks,
                    'cybersecurity': cyber_risks
                },
                'risk_metrics': risk_metrics,
                'risk_matrix': risk_matrix,
                'prioritized_risks': prioritized_risks,
                'mitigation_plan': mitigation_plan,
                'assessment_date': datetime.now(),
                'next_assessment_due': datetime.now() + timedelta(days=90)
            }
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def predict_risk_evolution(self, risk_id: str, time_horizon: int = 365) -> Dict[str, Any]:
        """Predict how risk will evolve over time."""
        try:
            if risk_id not in self.risks:
                raise ValueError(f"Risk {risk_id} not found")
            
            risk = self.risks[risk_id]
            
            # Time series prediction
            time_points = np.linspace(0, time_horizon, 12)  # Monthly predictions
            
            # Base probability evolution
            base_trend = self._calculate_risk_trend(risk)
            seasonal_factor = self._calculate_seasonal_factor(risk, time_points)
            random_factor = np.random.normal(0, 0.1, len(time_points))
            
            predicted_probabilities = []
            predicted_impacts = []
            
            for i, t in enumerate(time_points):
                # Probability evolution
                prob = risk.probability * (1 + base_trend * t/365) * seasonal_factor[i] + random_factor[i]
                prob = max(0.0, min(1.0, prob))
                predicted_probabilities.append(prob)
                
                # Impact evolution (usually more stable)
                impact = risk.impact * (1 + base_trend * 0.1 * t/365)
                impact = max(0.0, min(1.0, impact))
                predicted_impacts.append(impact)
            
            # Calculate risk scores
            predicted_scores = [p * i for p, i in zip(predicted_probabilities, predicted_impacts)]
            
            return {
                'risk_id': risk_id,
                'time_horizon_days': time_horizon,
                'time_points': time_points.tolist(),
                'predicted_probabilities': predicted_probabilities,
                'predicted_impacts': predicted_impacts,
                'predicted_scores': predicted_scores,
                'trend_analysis': {
                    'base_trend': base_trend,
                    'peak_risk_period': time_points[np.argmax(predicted_scores)],
                    'average_risk_score': np.mean(predicted_scores),
                    'risk_volatility': np.std(predicted_scores)
                }
            }
            
        except Exception as e:
            logger.error(f"Risk prediction failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def scenario_analysis(self, scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform scenario-based risk analysis."""
        try:
            logger.info(f"Performing scenario analysis for {len(scenarios)} scenarios")
            
            scenario_results = []
            
            for i, scenario in enumerate(scenarios):
                scenario_name = scenario.get('name', f'Scenario_{i+1}')
                scenario_data = scenario.get('data', {})
                
                # Assess risks under this scenario
                risk_assessment = await self.assess_system_risks(scenario_data)
                
                # Calculate scenario-specific metrics
                scenario_metrics = {
                    'scenario_name': scenario_name,
                    'total_risk_score': risk_assessment['risk_metrics']['total_risk_score'],
                    'critical_risks': len([r for r in risk_assessment['prioritized_risks'] 
                                         if r['risk_level'] == 'critical']),
                    'high_risks': len([r for r in risk_assessment['prioritized_risks'] 
                                     if r['risk_level'] == 'high']),
                    'risk_distribution': risk_assessment['risk_metrics']['risk_distribution'],
                    'mitigation_cost': risk_assessment['mitigation_plan']['total_cost']
                }
                
                scenario_results.append({
                    'scenario': scenario_name,
                    'metrics': scenario_metrics,
                    'full_assessment': risk_assessment
                })
            
            # Compare scenarios
            comparison = self._compare_scenarios(scenario_results)
            
            return {
                'scenario_analysis_id': f"scenario_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'scenarios_analyzed': len(scenarios),
                'scenario_results': scenario_results,
                'scenario_comparison': comparison,
                'recommendations': self._generate_scenario_recommendations(comparison)
            }
            
        except Exception as e:
            logger.error(f"Scenario analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _assess_operational_risks(self, system_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess operational risks."""
        risks = []
        
        # Equipment failure risk
        equipment_age = system_data.get('equipment_age', 5)  # years
        maintenance_frequency = system_data.get('maintenance_frequency', 12)  # per year
        
        failure_probability = min(0.9, equipment_age * 0.05 + (12 - maintenance_frequency) * 0.02)
        failure_impact = 0.8
        
        risks.append({
            'name': 'Equipment Failure',
            'category': 'operational',
            'probability': failure_probability,
            'impact': failure_impact,
            'risk_score': failure_probability * failure_impact,
            'description': 'Risk of critical equipment failure disrupting operations'
        })
        
        # Supply chain disruption
        supplier_diversity = system_data.get('supplier_diversity', 3)
        supply_probability = max(0.1, 0.5 - supplier_diversity * 0.1)
        
        risks.append({
            'name': 'Supply Chain Disruption',
            'category': 'operational',
            'probability': supply_probability,
            'impact': 0.6,
            'risk_score': supply_probability * 0.6,
            'description': 'Risk of chemical or equipment supply disruptions'
        })
        
        # Staff shortage
        staff_turnover = system_data.get('staff_turnover', 0.15)
        training_level = system_data.get('training_level', 0.8)
        
        staff_probability = staff_turnover + (1 - training_level) * 0.3
        
        risks.append({
            'name': 'Staff Shortage',
            'category': 'operational',
            'probability': staff_probability,
            'impact': 0.5,
            'risk_score': staff_probability * 0.5,
            'description': 'Risk of insufficient qualified staff'
        })
        
        return risks
    
    async def _assess_financial_risks(self, system_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess financial risks."""
        risks = []
        
        # Budget overrun
        budget_variance = system_data.get('budget_variance', 0.1)
        project_complexity = system_data.get('project_complexity', 0.5)
        
        overrun_probability = min(0.8, budget_variance + project_complexity * 0.3)
        
        risks.append({
            'name': 'Budget Overrun',
            'category': 'financial',
            'probability': overrun_probability,
            'impact': 0.7,
            'risk_score': overrun_probability * 0.7,
            'description': 'Risk of exceeding allocated budget'
        })
        
        # Energy cost volatility
        energy_dependency = system_data.get('energy_dependency', 0.8)
        renewable_ratio = system_data.get('renewable_ratio', 0.2)
        
        energy_risk_probability = energy_dependency * (1 - renewable_ratio)
        
        risks.append({
            'name': 'Energy Cost Volatility',
            'category': 'financial',
            'probability': energy_risk_probability,
            'impact': 0.4,
            'risk_score': energy_risk_probability * 0.4,
            'description': 'Risk of energy price fluctuations'
        })
        
        return risks
    
    async def _assess_environmental_risks(self, system_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess environmental risks."""
        risks = []
        
        # Water source contamination
        source_vulnerability = system_data.get('source_vulnerability', 0.3)
        protection_measures = system_data.get('protection_measures', 0.7)
        
        contamination_probability = source_vulnerability * (1 - protection_measures)
        
        risks.append({
            'name': 'Water Source Contamination',
            'category': 'environmental',
            'probability': contamination_probability,
            'impact': 0.9,
            'risk_score': contamination_probability * 0.9,
            'description': 'Risk of source water contamination'
        })
        
        # Discharge compliance violation
        discharge_monitoring = system_data.get('discharge_monitoring', 0.8)
        treatment_efficiency = system_data.get('treatment_efficiency', 0.85)
        
        violation_probability = (1 - discharge_monitoring) * (1 - treatment_efficiency)
        
        risks.append({
            'name': 'Discharge Violation',
            'category': 'environmental',
            'probability': violation_probability,
            'impact': 0.8,
            'risk_score': violation_probability * 0.8,
            'description': 'Risk of environmental discharge violations'
        })
        
        return risks
    
    async def _assess_technical_risks(self, system_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess technical risks."""
        risks = []
        
        # Technology obsolescence
        technology_age = system_data.get('technology_age', 3)
        upgrade_frequency = system_data.get('upgrade_frequency', 0.2)
        
        obsolescence_probability = min(0.7, technology_age * 0.1 + (1 - upgrade_frequency))
        
        risks.append({
            'name': 'Technology Obsolescence',
            'category': 'technical',
            'probability': obsolescence_probability,
            'impact': 0.6,
            'risk_score': obsolescence_probability * 0.6,
            'description': 'Risk of technology becoming outdated'
        })
        
        # System integration failure
        system_complexity = system_data.get('system_complexity', 0.5)
        integration_testing = system_data.get('integration_testing', 0.7)
        
        integration_probability = system_complexity * (1 - integration_testing)
        
        risks.append({
            'name': 'Integration Failure',
            'category': 'technical',
            'probability': integration_probability,
            'impact': 0.7,
            'risk_score': integration_probability * 0.7,
            'description': 'Risk of system integration failures'
        })
        
        return risks
    
    async def _assess_climate_risks(self, system_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess climate-related risks."""
        risks = []
        
        # Extreme weather events
        climate_vulnerability = system_data.get('climate_vulnerability', 0.4)
        resilience_measures = system_data.get('resilience_measures', 0.6)
        
        weather_probability = climate_vulnerability * (1 - resilience_measures * 0.5)
        
        risks.append({
            'name': 'Extreme Weather',
            'category': 'climate',
            'probability': weather_probability,
            'impact': 0.8,
            'risk_score': weather_probability * 0.8,
            'description': 'Risk from extreme weather events'
        })
        
        # Water scarcity
        water_stress = system_data.get('water_stress', 0.3)
        conservation_measures = system_data.get('conservation_measures', 0.5)
        
        scarcity_probability = water_stress * (1 - conservation_measures * 0.7)
        
        risks.append({
            'name': 'Water Scarcity',
            'category': 'climate',
            'probability': scarcity_probability,
            'impact': 0.9,
            'risk_score': scarcity_probability * 0.9,
            'description': 'Risk of water source scarcity'
        })
        
        return risks
    
    async def _assess_cybersecurity_risks(self, system_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess cybersecurity risks."""
        risks = []
        
        # Cyber attack
        digitalization_level = system_data.get('digitalization_level', 0.6)
        security_measures = system_data.get('security_measures', 0.7)
        
        cyber_probability = digitalization_level * (1 - security_measures)
        
        risks.append({
            'name': 'Cyber Attack',
            'category': 'cybersecurity',
            'probability': cyber_probability,
            'impact': 0.8,
            'risk_score': cyber_probability * 0.8,
            'description': 'Risk of cybersecurity breaches'
        })
        
        # Data breach
        data_sensitivity = system_data.get('data_sensitivity', 0.5)
        data_protection = system_data.get('data_protection', 0.8)
        
        breach_probability = data_sensitivity * (1 - data_protection)
        
        risks.append({
            'name': 'Data Breach',
            'category': 'cybersecurity',
            'probability': breach_probability,
            'impact': 0.6,
            'risk_score': breach_probability * 0.6,
            'description': 'Risk of sensitive data exposure'
        })
        
        return risks
    
    def _calculate_risk_metrics(self, risks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall risk metrics."""
        if not risks:
            return {}
        
        risk_scores = [r['risk_score'] for r in risks]
        
        # Risk distribution by level
        risk_levels = []
        for risk in risks:
            score = risk['risk_score']
            if score >= 0.7:
                risk_levels.append('critical')
            elif score >= 0.5:
                risk_levels.append('high')
            elif score >= 0.3:
                risk_levels.append('medium')
            else:
                risk_levels.append('low')
        
        level_counts = {level: risk_levels.count(level) for level in ['critical', 'high', 'medium', 'low']}
        
        return {
            'total_risks': len(risks),
            'total_risk_score': sum(risk_scores),
            'average_risk_score': np.mean(risk_scores),
            'max_risk_score': max(risk_scores),
            'risk_distribution': level_counts,
            'risk_concentration': max(level_counts.values()) / len(risks) if risks else 0
        }
    
    def _create_risk_matrix(self, risks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create risk probability-impact matrix."""
        matrix = {
            'high_prob_high_impact': [],
            'high_prob_low_impact': [],
            'low_prob_high_impact': [],
            'low_prob_low_impact': []
        }
        
        for risk in risks:
            prob = risk['probability']
            impact = risk['impact']
            
            if prob >= 0.5 and impact >= 0.5:
                matrix['high_prob_high_impact'].append(risk['name'])
            elif prob >= 0.5 and impact < 0.5:
                matrix['high_prob_low_impact'].append(risk['name'])
            elif prob < 0.5 and impact >= 0.5:
                matrix['low_prob_high_impact'].append(risk['name'])
            else:
                matrix['low_prob_low_impact'].append(risk['name'])
        
        return matrix
    
    def _prioritize_risks(self, risks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize risks by score and impact."""
        # Sort by risk score (probability * impact)
        sorted_risks = sorted(risks, key=lambda x: x['risk_score'], reverse=True)
        
        # Add priority ranking
        for i, risk in enumerate(sorted_risks):
            risk['priority_rank'] = i + 1
            
            # Assign risk level
            score = risk['risk_score']
            if score >= 0.7:
                risk['risk_level'] = 'critical'
            elif score >= 0.5:
                risk['risk_level'] = 'high'
            elif score >= 0.3:
                risk['risk_level'] = 'medium'
            else:
                risk['risk_level'] = 'low'
        
        return sorted_risks
    
    async def _generate_mitigation_plan(self, prioritized_risks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive mitigation plan."""
        mitigation_strategies = []
        total_cost = 0
        
        for risk in prioritized_risks[:10]:  # Top 10 risks
            if risk['risk_level'] in ['critical', 'high']:
                strategy = {
                    'risk_name': risk['name'],
                    'risk_level': risk['risk_level'],
                    'mitigation_actions': self._get_mitigation_actions(risk),
                    'estimated_cost': self._estimate_mitigation_cost(risk),
                    'timeline': self._get_mitigation_timeline(risk),
                    'effectiveness': self._estimate_effectiveness(risk)
                }
                mitigation_strategies.append(strategy)
                total_cost += strategy['estimated_cost']
        
        return {
            'mitigation_strategies': mitigation_strategies,
            'total_cost': total_cost,
            'implementation_timeline': '12 months',
            'expected_risk_reduction': 0.4,  # 40% reduction
            'roi_estimate': 2.5  # Return on investment
        }
    
    def _get_mitigation_actions(self, risk: Dict[str, Any]) -> List[str]:
        """Get mitigation actions for specific risk."""
        actions_map = {
            'Equipment Failure': [
                'Implement predictive maintenance',
                'Establish equipment redundancy',
                'Create spare parts inventory',
                'Train maintenance staff'
            ],
            'Supply Chain Disruption': [
                'Diversify supplier base',
                'Establish strategic partnerships',
                'Create buffer inventory',
                'Develop local sourcing options'
            ],
            'Water Source Contamination': [
                'Implement source protection measures',
                'Install advanced monitoring systems',
                'Develop alternative water sources',
                'Create emergency response protocols'
            ],
            'Cyber Attack': [
                'Implement cybersecurity framework',
                'Conduct regular security audits',
                'Train staff on security protocols',
                'Install advanced threat detection'
            ]
        }
        
        return actions_map.get(risk['name'], ['Develop specific mitigation plan', 'Monitor risk indicators'])
    
    def _estimate_mitigation_cost(self, risk: Dict[str, Any]) -> float:
        """Estimate mitigation cost."""
        base_costs = {
            'critical': 100000,
            'high': 50000,
            'medium': 25000,
            'low': 10000
        }
        
        return base_costs.get(risk['risk_level'], 25000)
    
    def _get_mitigation_timeline(self, risk: Dict[str, Any]) -> str:
        """Get mitigation timeline."""
        timelines = {
            'critical': '3 months',
            'high': '6 months',
            'medium': '9 months',
            'low': '12 months'
        }
        
        return timelines.get(risk['risk_level'], '6 months')
    
    def _estimate_effectiveness(self, risk: Dict[str, Any]) -> float:
        """Estimate mitigation effectiveness."""
        effectiveness = {
            'critical': 0.8,
            'high': 0.7,
            'medium': 0.6,
            'low': 0.5
        }
        
        return effectiveness.get(risk['risk_level'], 0.6)
    
    def _calculate_risk_trend(self, risk: Risk) -> float:
        """Calculate risk trend factor."""
        # Simplified trend calculation
        if risk.category == RiskCategory.CLIMATE:
            return 0.02  # Climate risks increasing
        elif risk.category == RiskCategory.CYBERSECURITY:
            return 0.03  # Cyber risks increasing
        elif risk.category == RiskCategory.TECHNICAL:
            return -0.01  # Technical risks decreasing with improvements
        else:
            return 0.0  # Stable
    
    def _calculate_seasonal_factor(self, risk: Risk, time_points: np.ndarray) -> np.ndarray:
        """Calculate seasonal risk factors."""
        if risk.category == RiskCategory.CLIMATE:
            # Higher risk in summer/winter
            return 1 + 0.2 * np.sin(2 * np.pi * time_points / 365)
        elif risk.category == RiskCategory.OPERATIONAL:
            # Higher risk during maintenance seasons
            return 1 + 0.1 * np.sin(2 * np.pi * time_points / 365 + np.pi/2)
        else:
            return np.ones_like(time_points)
    
    def _compare_scenarios(self, scenario_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compare risk scenarios."""
        if len(scenario_results) < 2:
            return {}
        
        # Extract metrics for comparison
        scenarios = []
        for result in scenario_results:
            scenarios.append({
                'name': result['scenario'],
                'total_risk': result['metrics']['total_risk_score'],
                'critical_risks': result['metrics']['critical_risks'],
                'mitigation_cost': result['metrics']['mitigation_cost']
            })
        
        # Find best and worst scenarios
        best_scenario = min(scenarios, key=lambda x: x['total_risk'])
        worst_scenario = max(scenarios, key=lambda x: x['total_risk'])
        
        return {
            'best_scenario': best_scenario,
            'worst_scenario': worst_scenario,
            'risk_variance': np.var([s['total_risk'] for s in scenarios]),
            'cost_variance': np.var([s['mitigation_cost'] for s in scenarios])
        }
    
    def _generate_scenario_recommendations(self, comparison: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on scenario comparison."""
        if not comparison:
            return []
        
        recommendations = []
        
        best = comparison.get('best_scenario', {})
        worst = comparison.get('worst_scenario', {})
        
        if best and worst:
            recommendations.append(f"Prioritize strategies from {best['name']} scenario")
            recommendations.append(f"Develop contingency plans for {worst['name']} scenario")
            
            if comparison.get('risk_variance', 0) > 1.0:
                recommendations.append("High risk variability - implement adaptive risk management")
            
            if comparison.get('cost_variance', 0) > 10000:
                recommendations.append("Significant cost differences - optimize mitigation strategies")
        
        return recommendations
    
    def _initialize_risk_models(self) -> Dict[str, Any]:
        """Initialize risk assessment models."""
        return {
            'operational': {'base_probability': 0.3, 'impact_multiplier': 1.0},
            'financial': {'base_probability': 0.2, 'impact_multiplier': 0.8},
            'environmental': {'base_probability': 0.25, 'impact_multiplier': 1.2},
            'technical': {'base_probability': 0.35, 'impact_multiplier': 0.9},
            'climate': {'base_probability': 0.4, 'impact_multiplier': 1.1},
            'cybersecurity': {'base_probability': 0.3, 'impact_multiplier': 0.7}
        }
    
    def _populate_standard_risks(self):
        """Populate with standard water management risks."""
        # This would typically load from a database
        pass


# Convenience functions
async def assess_risks(system_data: Dict[str, Any], scope: str = 'comprehensive') -> Dict[str, Any]:
    """Assess system risks."""
    agent = RiskAnalysisAgent()
    return await agent.assess_system_risks(system_data, scope)


async def predict_risk(risk_id: str, time_horizon: int = 365) -> Dict[str, Any]:
    """Predict risk evolution."""
    agent = RiskAnalysisAgent()
    return await agent.predict_risk_evolution(risk_id, time_horizon)
