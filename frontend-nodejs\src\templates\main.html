<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - Water Management Dashboard</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/components.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <!-- Socket.IO -->
    <script src="/socket.io/socket.io.js"></script>
    
    <!-- Meta tags -->
    <meta name="description" content="Advanced Water Management Decarbonisation System with AI-powered optimization">
    <meta name="keywords" content="water management, climate, AI, sustainability, decarbonisation">
    <meta name="author" content="Water Management Team">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-container">
            <div class="loading-logo">
                <i class="fas fa-leaf"></i>
                <span>Climate AI</span>
            </div>
            <div class="loading-spinner"></div>
            <p class="loading-text">Initializing Water Management System...</p>
            <div class="loading-progress">
                <div class="progress-bar" id="loading-progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-leaf"></i>
                    <span>Climate AI</span>
                    <small>Energy & Climate Platform</small>
                </div>
            </div>
            <div class="header-center">
                <nav class="nav-tabs" id="main-navigation">
                    <!-- Navigation will be populated by JavaScript -->
                </nav>
            </div>
            <div class="header-right">
                <div class="status-indicators">
                    <div class="status-item">
                        <i class="fas fa-circle status-indicator" id="connection-status"></i>
                        <span id="connection-text">Connecting...</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-globe"></i>
                        <span>Global Coverage</span>
                    </div>
                    <div class="status-item">
                        <span class="uptime" id="system-uptime">--% Uptime</span>
                    </div>
                </div>
                <div class="user-menu">
                    <button class="btn-icon" id="search-btn" title="Search">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn-icon" id="notifications-btn" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count" style="display: none;">0</span>
                    </button>
                    <button class="btn-icon" id="settings-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                    <div class="user-avatar">GA</div>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-section" id="sidebar-navigation">
                <!-- Sidebar navigation will be populated by JavaScript -->
            </div>
            <div class="sidebar-footer">
                <div class="version">Climate AI v2.1.0</div>
                <div class="status-icons">
                    <i class="fas fa-wifi" id="wifi-status"></i>
                    <i class="fas fa-shield-alt" id="security-status"></i>
                    <i class="fas fa-cog" id="system-status"></i>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- System Status Bar -->
            <div class="system-status">
                <div class="status-left">
                    <i class="fas fa-circle status-online" id="main-status-indicator"></i>
                    <span id="main-status-text">System Online</span>
                    <span class="last-update" id="last-update-time">Last Updated: --</span>
                </div>
                <div class="status-right">
                    <span id="processing-status">Processing: Complete</span>
                    <div class="status-actions">
                        <button class="btn-help" id="help-btn">
                            <i class="fas fa-question-circle"></i>
                            Help
                        </button>
                        <button class="btn-settings" id="main-settings-btn">
                            <i class="fas fa-cog"></i>
                            Settings
                        </button>
                        <button class="btn-fullscreen" id="fullscreen-btn">
                            <i class="fas fa-expand"></i>
                            Fullscreen
                        </button>
                    </div>
                </div>
            </div>

            <!-- Page Container -->
            <div class="page-container" id="page-container">
                <!-- Dynamic content will be loaded here -->
                <div class="page-loading">
                    <div class="loading-spinner"></div>
                    <p>Loading dashboard content...</p>
                </div>
            </div>
        </main>

        <!-- Modals and Overlays -->
        <div id="modal-container"></div>
        
        <!-- Notification Toast Container -->
        <div id="toast-container" class="toast-container"></div>
        
        <!-- Context Menu -->
        <div id="context-menu" class="context-menu" style="display: none;"></div>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="error-screen" style="display: none;">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h2>System Error</h2>
            <p id="error-message">An unexpected error occurred. Please try refreshing the page.</p>
            <div class="error-actions">
                <button class="btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> Refresh Page
                </button>
                <button class="btn-secondary" id="error-details-btn">
                    <i class="fas fa-info-circle"></i> Show Details
                </button>
            </div>
            <div id="error-details" class="error-details" style="display: none;">
                <pre id="error-stack"></pre>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/utils/constants.js"></script>
    <script src="/js/utils/helpers.js"></script>
    <script src="/js/utils/api.js"></script>
    <script src="/js/utils/websocket.js"></script>
    <script src="/js/components/charts.js"></script>
    <script src="/js/components/navigation.js"></script>
    <script src="/js/components/dashboard.js"></script>
    <script src="/js/pages/pageManager.js"></script>
    <script src="/js/main.js"></script>

    <!-- Initialize Application -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize the Water Management Application
            window.WaterManagementApp.init('{{page}}');
        });
    </script>
</body>
</html>
