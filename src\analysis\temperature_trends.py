"""
Temperature Trend Analysis Module.

This module provides comprehensive temperature trend analysis capabilities
for climate data, including trend detection, seasonal analysis, and
water treatment optimization insights.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
from scipy import stats
from scipy.stats import linregress
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class TrendAnalysisResult:
    """Result of temperature trend analysis."""
    location: str
    analysis_period: Dict[str, str]
    trend_direction: str  # 'increasing', 'decreasing', 'stable', 'no_trend'
    trend_magnitude: float  # degrees per year
    trend_confidence: float  # 0-1 scale
    seasonal_patterns: Dict[str, Any]
    anomalies_detected: List[Dict[str, Any]]
    statistical_summary: Dict[str, float]
    water_treatment_insights: Dict[str, Any]
    timestamp: datetime


@dataclass
class SeasonalPattern:
    """Seasonal temperature pattern analysis."""
    season: str
    avg_temperature: float
    temperature_range: Tuple[float, float]
    variability: float
    trend_within_season: float
    anomaly_frequency: float


class TemperatureTrendAnalyzer:
    """
    Comprehensive temperature trend analysis system.
    
    Provides:
    - Long-term trend detection using multiple statistical methods
    - Seasonal pattern analysis and decomposition
    - Anomaly detection and extreme event identification
    - Water treatment optimization insights
    - Climate change impact assessment
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Analysis parameters
        self.trend_methods = ['linear_regression', 'mann_kendall', 'theil_sen']
        self.anomaly_threshold = 2.0  # Standard deviations
        self.seasonal_window = 30  # Days for seasonal smoothing
        self.min_data_points = 10  # Minimum points for reliable analysis
        
        # Water treatment temperature thresholds
        self.treatment_thresholds = {
            'optimal_range': (15, 25),  # °C - optimal for most treatment processes
            'efficiency_drop': (5, 35),  # °C - efficiency starts dropping
            'critical_range': (0, 40),   # °C - critical operational limits
            'biological_activity': (10, 30),  # °C - optimal for biological processes
            'chemical_reaction': (20, 25)     # °C - optimal for chemical treatment
        }
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the temperature trend analyzer."""
        try:
            logger.info("Initializing Temperature Trend Analyzer...")
            self.is_initialized = True
            logger.info("Temperature Trend Analyzer initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize temperature analyzer: {e}")
            return False
    
    async def analyze_temperature_trends(self, data: List[ProcessedClimateData], 
                                       location: str = None) -> TrendAnalysisResult:
        """Perform comprehensive temperature trend analysis."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not data:
                raise ValueError("No data provided for analysis")
            
            logger.info(f"Analyzing temperature trends for {len(data)} data points")
            
            # Convert to DataFrame for analysis
            df = await self._prepare_dataframe(data)
            
            if df.empty or len(df) < self.min_data_points:
                raise ValueError(f"Insufficient data for analysis (need at least {self.min_data_points} points)")
            
            # Determine location
            analysis_location = location or self._extract_location(data)
            
            # Perform trend analysis
            trend_results = await self._detect_trends(df)
            
            # Analyze seasonal patterns
            seasonal_analysis = await self._analyze_seasonal_patterns(df)
            
            # Detect anomalies
            anomalies = await self._detect_temperature_anomalies(df)
            
            # Calculate statistical summary
            stats_summary = await self._calculate_statistical_summary(df)
            
            # Generate water treatment insights
            treatment_insights = await self._generate_treatment_insights(df, trend_results)
            
            # Create result
            result = TrendAnalysisResult(
                location=analysis_location,
                analysis_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'duration_days': (df.index.max() - df.index.min()).days
                },
                trend_direction=trend_results['direction'],
                trend_magnitude=trend_results['magnitude'],
                trend_confidence=trend_results['confidence'],
                seasonal_patterns=seasonal_analysis,
                anomalies_detected=anomalies,
                statistical_summary=stats_summary,
                water_treatment_insights=treatment_insights,
                timestamp=datetime.now()
            )
            
            logger.info(f"Temperature trend analysis completed for {analysis_location}")
            return result
            
        except Exception as e:
            logger.error(f"Temperature trend analysis failed: {e}")
            raise
    
    async def _prepare_dataframe(self, data: List[ProcessedClimateData]) -> pd.DataFrame:
        """Prepare DataFrame from climate data for analysis."""
        try:
            records = []
            for item in data:
                # Use available temperature data
                temp_value = None
                if item.temperature is not None:
                    temp_value = item.temperature
                elif item.temperature_max is not None and item.temperature_min is not None:
                    temp_value = (item.temperature_max + item.temperature_min) / 2
                elif item.temperature_max is not None:
                    temp_value = item.temperature_max
                elif item.temperature_min is not None:
                    temp_value = item.temperature_min
                
                if temp_value is not None:
                    records.append({
                        'timestamp': item.timestamp,
                        'temperature': temp_value,
                        'location': item.location,
                        'source': item.source,
                        'quality_score': getattr(item, 'data_quality_score', 1.0)
                    })
            
            if not records:
                return pd.DataFrame()
            
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Remove duplicates and handle missing values
            df = df.groupby(df.index).mean(numeric_only=True)  # Average duplicate timestamps, numeric only
            df['temperature'] = df['temperature'].interpolate(method='linear')
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to prepare DataFrame: {e}")
            return pd.DataFrame()
    
    def _extract_location(self, data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in data if item.location]
        if locations:
            # Return most common location
            return max(set(locations), key=locations.count)
        return "Unknown Location"
    
    async def _detect_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Detect temperature trends using multiple methods."""
        try:
            if df.empty or 'temperature' not in df.columns:
                return {'direction': 'no_trend', 'magnitude': 0.0, 'confidence': 0.0}
            
            temperatures = df['temperature'].dropna()
            if len(temperatures) < 3:
                return {'direction': 'no_trend', 'magnitude': 0.0, 'confidence': 0.0}
            
            # Convert timestamps to numeric (days since start)
            time_numeric = (df.index - df.index.min()).days
            
            results = {}
            
            # Method 1: Linear Regression
            if len(temperatures) >= 3:
                slope, intercept, r_value, p_value, std_err = stats.linregress(time_numeric, temperatures)
                results['linear_regression'] = {
                    'slope': slope,
                    'r_squared': r_value ** 2,
                    'p_value': p_value,
                    'significance': p_value < 0.05
                }
            
            # Method 2: Mann-Kendall Trend Test (simplified)
            try:
                mk_result = self._mann_kendall_test(temperatures.values)
                results['mann_kendall'] = mk_result
            except Exception as e:
                logger.warning(f"Mann-Kendall test failed: {e}")
                results['mann_kendall'] = {'trend': 'no_trend', 'p_value': 1.0}
            
            # Method 3: Theil-Sen Estimator
            try:
                if len(time_numeric) >= 3:
                    theil_slope, theil_intercept, _, _ = stats.theilslopes(temperatures, time_numeric)
                    results['theil_sen'] = {
                        'slope': theil_slope,
                        'intercept': theil_intercept
                    }
            except Exception as e:
                logger.warning(f"Theil-Sen estimator failed: {e}")
                results['theil_sen'] = {'slope': 0.0}
            
            # Combine results
            trend_direction = self._determine_trend_direction(results)
            trend_magnitude = self._calculate_trend_magnitude(results, len(time_numeric))
            confidence = self._calculate_trend_confidence(results)
            
            return {
                'direction': trend_direction,
                'magnitude': trend_magnitude,  # degrees per year
                'confidence': confidence,
                'methods': results
            }
            
        except Exception as e:
            logger.error(f"Trend detection failed: {e}")
            return {'direction': 'no_trend', 'magnitude': 0.0, 'confidence': 0.0}
    
    def _mann_kendall_test(self, data: np.ndarray) -> Dict[str, Any]:
        """Simplified Mann-Kendall trend test."""
        try:
            n = len(data)
            if n < 3:
                return {'trend': 'no_trend', 'p_value': 1.0}
            
            # Calculate S statistic
            s = 0
            for i in range(n - 1):
                for j in range(i + 1, n):
                    if data[j] > data[i]:
                        s += 1
                    elif data[j] < data[i]:
                        s -= 1
            
            # Calculate variance
            var_s = n * (n - 1) * (2 * n + 5) / 18
            
            # Calculate Z statistic
            if s > 0:
                z = (s - 1) / np.sqrt(var_s)
            elif s < 0:
                z = (s + 1) / np.sqrt(var_s)
            else:
                z = 0
            
            # Calculate p-value (two-tailed)
            p_value = 2 * (1 - stats.norm.cdf(abs(z)))
            
            # Determine trend
            if p_value < 0.05:
                trend = 'increasing' if s > 0 else 'decreasing'
            else:
                trend = 'no_trend'
            
            return {
                'trend': trend,
                'p_value': p_value,
                's_statistic': s,
                'z_statistic': z
            }
            
        except Exception as e:
            logger.warning(f"Mann-Kendall test calculation failed: {e}")
            return {'trend': 'no_trend', 'p_value': 1.0}
    
    def _determine_trend_direction(self, results: Dict[str, Any]) -> str:
        """Determine overall trend direction from multiple methods."""
        try:
            directions = []
            
            # Linear regression
            if 'linear_regression' in results:
                lr = results['linear_regression']
                if lr.get('significance', False):
                    if lr['slope'] > 0:
                        directions.append('increasing')
                    elif lr['slope'] < 0:
                        directions.append('decreasing')
                    else:
                        directions.append('stable')
            
            # Mann-Kendall
            if 'mann_kendall' in results:
                mk_trend = results['mann_kendall'].get('trend', 'no_trend')
                if mk_trend != 'no_trend':
                    directions.append(mk_trend)
            
            # Theil-Sen
            if 'theil_sen' in results:
                ts_slope = results['theil_sen'].get('slope', 0)
                if abs(ts_slope) > 0.01:  # Threshold for significance
                    if ts_slope > 0:
                        directions.append('increasing')
                    else:
                        directions.append('decreasing')
            
            # Determine consensus
            if not directions:
                return 'no_trend'
            
            # Return most common direction
            return max(set(directions), key=directions.count)
            
        except Exception as e:
            logger.warning(f"Failed to determine trend direction: {e}")
            return 'no_trend'
    
    def _calculate_trend_magnitude(self, results: Dict[str, Any], duration_days: int) -> float:
        """Calculate trend magnitude in degrees per year."""
        try:
            if duration_days == 0:
                return 0.0
            
            slopes = []
            
            # Collect slopes from different methods
            if 'linear_regression' in results:
                slopes.append(results['linear_regression']['slope'])
            
            if 'theil_sen' in results:
                slopes.append(results['theil_sen']['slope'])
            
            if not slopes:
                return 0.0
            
            # Average slope (degrees per day)
            avg_slope = np.mean(slopes)
            
            # Convert to degrees per year
            degrees_per_year = avg_slope * 365.25
            
            return float(degrees_per_year)
            
        except Exception as e:
            logger.warning(f"Failed to calculate trend magnitude: {e}")
            return 0.0
    
    def _calculate_trend_confidence(self, results: Dict[str, Any]) -> float:
        """Calculate confidence in trend analysis."""
        try:
            confidence_scores = []
            
            # Linear regression confidence
            if 'linear_regression' in results:
                lr = results['linear_regression']
                r_squared = lr.get('r_squared', 0)
                p_value = lr.get('p_value', 1)
                
                # Higher R² and lower p-value = higher confidence
                lr_confidence = r_squared * (1 - p_value)
                confidence_scores.append(lr_confidence)
            
            # Mann-Kendall confidence
            if 'mann_kendall' in results:
                mk = results['mann_kendall']
                p_value = mk.get('p_value', 1)
                mk_confidence = 1 - p_value
                confidence_scores.append(mk_confidence)
            
            if not confidence_scores:
                return 0.0
            
            # Return average confidence
            return float(np.mean(confidence_scores))
            
        except Exception as e:
            logger.warning(f"Failed to calculate trend confidence: {e}")
            return 0.0
    
    async def _analyze_seasonal_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze seasonal temperature patterns."""
        try:
            if df.empty or 'temperature' not in df.columns:
                return {}
            
            # Add time-based features
            df_seasonal = df.copy()
            df_seasonal['month'] = df_seasonal.index.month
            df_seasonal['season'] = df_seasonal['month'].map(self._get_season)
            df_seasonal['day_of_year'] = df_seasonal.index.dayofyear
            
            seasonal_stats = {}
            
            # Analyze by season
            for season in ['Spring', 'Summer', 'Autumn', 'Winter']:
                season_data = df_seasonal[df_seasonal['season'] == season]['temperature']
                
                if len(season_data) > 0:
                    seasonal_stats[season] = {
                        'avg_temperature': float(season_data.mean()),
                        'min_temperature': float(season_data.min()),
                        'max_temperature': float(season_data.max()),
                        'std_temperature': float(season_data.std()),
                        'data_points': len(season_data)
                    }
            
            # Calculate seasonal amplitude
            if seasonal_stats:
                temps = [stats['avg_temperature'] for stats in seasonal_stats.values()]
                seasonal_amplitude = max(temps) - min(temps)
            else:
                seasonal_amplitude = 0.0
            
            # Detect seasonal trends
            seasonal_trends = {}
            for season, stats in seasonal_stats.items():
                if stats['data_points'] >= 3:
                    season_data = df_seasonal[df_seasonal['season'] == season]
                    if len(season_data) >= 3:
                        time_numeric = (season_data.index - season_data.index.min()).days
                        slope, _, r_value, p_value, _ = linregress(time_numeric, season_data['temperature'])
                        
                        seasonal_trends[season] = {
                            'trend_slope': float(slope * 365.25),  # degrees per year
                            'trend_confidence': float(r_value ** 2),
                            'trend_significance': p_value < 0.05
                        }
            
            return {
                'seasonal_statistics': seasonal_stats,
                'seasonal_amplitude': seasonal_amplitude,
                'seasonal_trends': seasonal_trends,
                'analysis_summary': {
                    'total_seasons_analyzed': len(seasonal_stats),
                    'strongest_seasonal_signal': seasonal_amplitude > 5.0,  # >5°C difference
                    'seasonal_trend_detected': any(
                        trend.get('trend_significance', False) 
                        for trend in seasonal_trends.values()
                    )
                }
            }
            
        except Exception as e:
            logger.error(f"Seasonal pattern analysis failed: {e}")
            return {}
    
    def _get_season(self, month: int) -> str:
        """Map month to season."""
        if month in [12, 1, 2]:
            return 'Winter'
        elif month in [3, 4, 5]:
            return 'Spring'
        elif month in [6, 7, 8]:
            return 'Summer'
        else:
            return 'Autumn'
    
    async def _detect_temperature_anomalies(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect temperature anomalies."""
        try:
            if df.empty or 'temperature' not in df.columns:
                return []
            
            temperatures = df['temperature'].dropna()
            if len(temperatures) < 5:
                return []
            
            # Calculate rolling statistics for anomaly detection
            window_size = min(30, len(temperatures) // 3)  # Adaptive window
            rolling_mean = temperatures.rolling(window=window_size, center=True).mean()
            rolling_std = temperatures.rolling(window=window_size, center=True).std()
            
            # Detect anomalies using z-score method
            z_scores = np.abs((temperatures - rolling_mean) / rolling_std)
            anomaly_mask = z_scores > self.anomaly_threshold
            
            anomalies = []
            for timestamp, is_anomaly in anomaly_mask.items():
                if is_anomaly and not pd.isna(z_scores[timestamp]):
                    temp_value = temperatures[timestamp]
                    z_score = z_scores[timestamp]
                    
                    # Classify anomaly type
                    if temp_value > rolling_mean[timestamp]:
                        anomaly_type = 'hot_anomaly'
                    else:
                        anomaly_type = 'cold_anomaly'
                    
                    anomalies.append({
                        'timestamp': timestamp.isoformat(),
                        'temperature': float(temp_value),
                        'anomaly_type': anomaly_type,
                        'z_score': float(z_score),
                        'severity': 'extreme' if z_score > 3.0 else 'moderate'
                    })
            
            # Sort by severity
            anomalies.sort(key=lambda x: x['z_score'], reverse=True)
            
            return anomalies[:20]  # Return top 20 anomalies
            
        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
            return []
    
    async def _calculate_statistical_summary(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate statistical summary of temperature data."""
        try:
            if df.empty or 'temperature' not in df.columns:
                return {}
            
            temperatures = df['temperature'].dropna()
            if len(temperatures) == 0:
                return {}
            
            return {
                'mean_temperature': float(temperatures.mean()),
                'median_temperature': float(temperatures.median()),
                'std_temperature': float(temperatures.std()),
                'min_temperature': float(temperatures.min()),
                'max_temperature': float(temperatures.max()),
                'temperature_range': float(temperatures.max() - temperatures.min()),
                'percentile_25': float(temperatures.quantile(0.25)),
                'percentile_75': float(temperatures.quantile(0.75)),
                'data_points': len(temperatures),
                'data_span_days': (df.index.max() - df.index.min()).days
            }
            
        except Exception as e:
            logger.error(f"Statistical summary calculation failed: {e}")
            return {}
    
    async def _generate_treatment_insights(self, df: pd.DataFrame, 
                                         trend_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate water treatment optimization insights."""
        try:
            if df.empty or 'temperature' not in df.columns:
                return {}
            
            temperatures = df['temperature'].dropna()
            if len(temperatures) == 0:
                return {}
            
            insights = {}
            
            # Analyze temperature ranges relative to treatment thresholds
            optimal_min, optimal_max = self.treatment_thresholds['optimal_range']
            efficiency_min, efficiency_max = self.treatment_thresholds['efficiency_drop']
            
            # Calculate time in different operational zones
            total_points = len(temperatures)
            optimal_points = len(temperatures[(temperatures >= optimal_min) & (temperatures <= optimal_max)])
            suboptimal_points = len(temperatures[(temperatures < efficiency_min) | (temperatures > efficiency_max)])
            
            insights['operational_efficiency'] = {
                'optimal_conditions_percentage': (optimal_points / total_points) * 100,
                'suboptimal_conditions_percentage': (suboptimal_points / total_points) * 100,
                'average_temperature': float(temperatures.mean())
            }
            
            # Trend impact on treatment
            trend_direction = trend_results.get('direction', 'no_trend')
            trend_magnitude = trend_results.get('magnitude', 0.0)
            
            if trend_direction == 'increasing' and trend_magnitude > 0.5:
                insights['trend_impact'] = {
                    'concern_level': 'moderate' if trend_magnitude < 2.0 else 'high',
                    'recommendation': 'Consider cooling systems or process adjustments',
                    'projected_impact': 'Decreasing treatment efficiency over time'
                }
            elif trend_direction == 'decreasing' and abs(trend_magnitude) > 0.5:
                insights['trend_impact'] = {
                    'concern_level': 'moderate' if abs(trend_magnitude) < 2.0 else 'high',
                    'recommendation': 'Consider heating systems or cold-weather adaptations',
                    'projected_impact': 'Potential biological process slowdown'
                }
            else:
                insights['trend_impact'] = {
                    'concern_level': 'low',
                    'recommendation': 'Current temperature trends are manageable',
                    'projected_impact': 'Minimal impact on treatment processes'
                }
            
            # Seasonal optimization recommendations
            if 'seasonal_patterns' in df.columns or len(temperatures) > 30:
                temp_std = temperatures.std()
                if temp_std > 10:  # High variability
                    insights['seasonal_recommendations'] = {
                        'adaptive_control': 'Implement seasonal temperature control systems',
                        'process_adjustment': 'Adjust treatment parameters seasonally',
                        'monitoring': 'Increase monitoring frequency during extreme seasons'
                    }
                else:
                    insights['seasonal_recommendations'] = {
                        'adaptive_control': 'Current temperature stability is good',
                        'process_adjustment': 'Minor seasonal adjustments may be beneficial',
                        'monitoring': 'Standard monitoring frequency is sufficient'
                    }
            
            return insights
            
        except Exception as e:
            logger.error(f"Treatment insights generation failed: {e}")
            return {}


# Convenience functions
async def analyze_location_temperature_trends(data: List[ProcessedClimateData], 
                                            location: str = None) -> TrendAnalysisResult:
    """Analyze temperature trends for a specific location."""
    analyzer = TemperatureTrendAnalyzer()
    await analyzer.initialize()
    
    return await analyzer.analyze_temperature_trends(data, location)


async def compare_temperature_trends(data_sets: Dict[str, List[ProcessedClimateData]]) -> Dict[str, TrendAnalysisResult]:
    """Compare temperature trends across multiple locations."""
    analyzer = TemperatureTrendAnalyzer()
    await analyzer.initialize()
    
    results = {}
    for location, data in data_sets.items():
        try:
            result = await analyzer.analyze_temperature_trends(data, location)
            results[location] = result
        except Exception as e:
            logger.error(f"Failed to analyze trends for {location}: {e}")
    
    return results
