#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marine Debris AI Management Agent
Task 1.22: AI agent with computer vision capabilities for comprehensive debris management
"""

import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# Import marine conservation components
from ..ai_algorithms.debris_detection_engine import MarineDebrisDetectionEngine, detect_marine_debris
from ..ai_algorithms.multi_source_intelligence import generate_marine_intelligence
from ..apis.sentinel_hub_api import detect_marine_debris_area, BoundingBox
from ..apis.aisstream_api import get_maritime_traffic_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DebrisManagementAction(Enum):
    """Types of debris management actions"""
    MONITOR = "monitor"
    ALERT = "alert"
    CLEANUP = "cleanup"
    INVESTIGATE = "investigate"
    PREVENT = "prevent"
    EDUCATE = "educate"


class Priority(Enum):
    """Priority levels for debris management"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class DebrisManagementTask:
    """Debris management task"""
    task_id: str
    action: DebrisManagementAction
    priority: Priority
    location: Tuple[float, float]
    description: str
    estimated_effort: float  # hours
    required_resources: List[str]
    deadline: datetime
    assigned_to: Optional[str]
    status: str  # "pending", "in_progress", "completed", "cancelled"
    created_at: datetime
    metadata: Dict[str, Any]


@dataclass
class CleanupOperation:
    """Cleanup operation plan"""
    operation_id: str
    target_area: Tuple[float, float, float, float]
    debris_targets: List[Dict[str, Any]]
    vessel_assignments: List[Dict[str, Any]]
    estimated_duration: float  # hours
    estimated_cost: float
    environmental_impact: Dict[str, Any]
    success_probability: float
    created_at: datetime


@dataclass
class DebrisAnalysis:
    """Comprehensive debris analysis result"""
    analysis_id: str
    area_analyzed: Tuple[float, float, float, float]
    total_debris_detected: int
    debris_by_type: Dict[str, int]
    hotspots_identified: List[Dict[str, Any]]
    risk_assessment: Dict[str, Any]
    trend_analysis: Dict[str, Any]
    recommendations: List[str]
    confidence_score: float
    analysis_timestamp: datetime


class MarineDebrisAIAgent:
    """AI agent for comprehensive marine debris management"""
    
    def __init__(self):
        self.detection_engine = MarineDebrisDetectionEngine()
        self.active_tasks = []
        self.completed_operations = []
        self.agent_config = {
            'detection_threshold': 0.7,
            'cleanup_threshold': 5,  # debris count to trigger cleanup
            'monitoring_interval_hours': 6,
            'max_concurrent_operations': 3
        }
        self.resource_database = self._initialize_resources()
        self.performance_metrics = {
            'total_debris_detected': 0,
            'cleanup_operations_completed': 0,
            'prevention_actions_taken': 0,
            'average_response_time_hours': 0.0
        }
    
    def _initialize_resources(self) -> Dict[str, Dict[str, Any]]:
        """Initialize available resources for debris management"""
        return {
            'cleanup_vessels': [
                {
                    'id': 'vessel_001',
                    'name': 'Ocean Cleaner 1',
                    'capacity': 50.0,  # tons
                    'speed': 12.0,  # knots
                    'daily_cost': 5000.0,
                    'available': True,
                    'location': (25.0, 121.0)  # Taiwan waters
                },
                {
                    'id': 'vessel_002',
                    'name': 'Marine Guardian',
                    'capacity': 30.0,
                    'speed': 15.0,
                    'daily_cost': 3500.0,
                    'available': True,
                    'location': (22.0, 120.0)
                }
            ],
            'monitoring_drones': [
                {
                    'id': 'drone_001',
                    'name': 'Sky Monitor 1',
                    'range_km': 50.0,
                    'flight_time_hours': 8.0,
                    'camera_resolution': '4K',
                    'available': True
                }
            ],
            'personnel': [
                {
                    'id': 'team_001',
                    'name': 'Marine Response Team Alpha',
                    'size': 5,
                    'specialization': 'cleanup_operations',
                    'hourly_cost': 200.0,
                    'available': True
                }
            ]
        }
    
    async def analyze_debris_situation(
        self,
        area_bbox: Tuple[float, float, float, float],
        analysis_depth: str = "comprehensive"
    ) -> DebrisAnalysis:
        """Perform comprehensive debris situation analysis"""
        try:
            analysis_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            logger.info(f"🔍 Starting debris analysis for area {area_bbox}")
            
            # Collect debris detection data
            bbox_obj = BoundingBox(area_bbox[0], area_bbox[1], area_bbox[2], area_bbox[3])
            debris_detections = await detect_marine_debris_area(bbox_obj, days_back=7)
            
            # Generate intelligence report
            intelligence = await generate_marine_intelligence(area_bbox, time_window_hours=24)
            
            # Analyze debris by type
            debris_by_type = {}
            for detection in debris_detections:
                debris_type = detection.debris_type
                debris_by_type[debris_type] = debris_by_type.get(debris_type, 0) + 1
            
            # Identify hotspots using clustering
            hotspots = await self._identify_debris_hotspots(debris_detections)
            
            # Perform risk assessment
            risk_assessment = self._assess_debris_risk(debris_detections, intelligence)
            
            # Analyze trends
            trend_analysis = await self._analyze_debris_trends(area_bbox, debris_detections)
            
            # Generate recommendations
            recommendations = self._generate_management_recommendations(
                debris_detections, hotspots, risk_assessment
            )
            
            # Calculate confidence score
            confidence_score = self._calculate_analysis_confidence(
                debris_detections, intelligence, hotspots
            )
            
            analysis = DebrisAnalysis(
                analysis_id=analysis_id,
                area_analyzed=area_bbox,
                total_debris_detected=len(debris_detections),
                debris_by_type=debris_by_type,
                hotspots_identified=hotspots,
                risk_assessment=risk_assessment,
                trend_analysis=trend_analysis,
                recommendations=recommendations,
                confidence_score=confidence_score,
                analysis_timestamp=datetime.now()
            )
            
            logger.info(f"✅ Debris analysis completed: {len(debris_detections)} debris detected")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Error in debris analysis: {e}")
            return DebrisAnalysis(
                analysis_id=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                area_analyzed=area_bbox,
                total_debris_detected=0,
                debris_by_type={},
                hotspots_identified=[],
                risk_assessment={'error': str(e)},
                trend_analysis={},
                recommendations=["Analysis failed - retry with different parameters"],
                confidence_score=0.0,
                analysis_timestamp=datetime.now()
            )
    
    async def _identify_debris_hotspots(self, debris_detections: List) -> List[Dict[str, Any]]:
        """Identify debris hotspots using spatial clustering"""
        hotspots = []
        
        if len(debris_detections) < 3:
            return hotspots
        
        # Simple spatial clustering (would use DBSCAN in production)
        processed = set()
        
        for i, debris in enumerate(debris_detections):
            if i in processed:
                continue
            
            cluster = [debris]
            cluster_indices = {i}
            
            # Find nearby debris within 0.01 degrees (~1km)
            for j, other_debris in enumerate(debris_detections):
                if j in processed or j == i:
                    continue
                
                lat_diff = abs(debris.location[0] - other_debris.location[0])
                lon_diff = abs(debris.location[1] - other_debris.location[1])
                
                if lat_diff < 0.01 and lon_diff < 0.01:
                    cluster.append(other_debris)
                    cluster_indices.add(j)
            
            # Create hotspot if cluster is significant
            if len(cluster) >= 3:
                center_lat = sum(d.location[0] for d in cluster) / len(cluster)
                center_lon = sum(d.location[1] for d in cluster) / len(cluster)
                total_size = sum(d.size_estimate for d in cluster)
                avg_confidence = sum(d.confidence for d in cluster) / len(cluster)
                
                hotspot = {
                    'id': f"hotspot_{len(hotspots)}",
                    'center': (center_lat, center_lon),
                    'debris_count': len(cluster),
                    'total_size_m2': total_size,
                    'density': len(cluster) / 1.0,  # per km²
                    'confidence': avg_confidence,
                    'priority': self._calculate_hotspot_priority(len(cluster), total_size),
                    'estimated_cleanup_time': self._estimate_cleanup_time(total_size),
                    'timestamp': datetime.now().isoformat()
                }
                hotspots.append(hotspot)
                processed.update(cluster_indices)
        
        return hotspots
    
    def _calculate_hotspot_priority(self, debris_count: int, total_size: float) -> str:
        """Calculate priority level for hotspot"""
        if debris_count >= 10 or total_size >= 500:
            return "critical"
        elif debris_count >= 5 or total_size >= 200:
            return "high"
        elif debris_count >= 3 or total_size >= 50:
            return "medium"
        else:
            return "low"
    
    def _estimate_cleanup_time(self, total_size: float) -> float:
        """Estimate cleanup time in hours based on debris size"""
        # Simple estimation: 1 hour per 10 m² of debris
        return max(1.0, total_size / 10.0)
    
    def _assess_debris_risk(self, debris_detections: List, intelligence) -> Dict[str, Any]:
        """Assess environmental and economic risk from debris"""
        risk_factors = []
        risk_score = 0.0
        
        # Quantity risk
        debris_count = len(debris_detections)
        if debris_count > 20:
            risk_score += 0.4
            risk_factors.append("high_debris_count")
        elif debris_count > 10:
            risk_score += 0.2
            risk_factors.append("moderate_debris_count")
        
        # Size risk
        total_size = sum(d.size_estimate for d in debris_detections)
        if total_size > 1000:
            risk_score += 0.3
            risk_factors.append("large_debris_area")
        
        # Intelligence risk
        if hasattr(intelligence, 'risk_assessment'):
            intel_risk = intelligence.risk_assessment.get('risk_score', 0)
            risk_score += intel_risk * 0.3
            if intel_risk > 0.7:
                risk_factors.append("high_intelligence_risk")
        
        # Determine risk level
        if risk_score >= 0.8:
            risk_level = "critical"
        elif risk_score >= 0.6:
            risk_level = "high"
        elif risk_score >= 0.4:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'environmental_impact': self._assess_environmental_impact(debris_detections),
            'economic_impact': self._assess_economic_impact(debris_detections)
        }
    
    def _assess_environmental_impact(self, debris_detections: List) -> Dict[str, Any]:
        """Assess environmental impact of debris"""
        plastic_count = len([d for d in debris_detections if d.debris_type == "plastic"])
        organic_count = len([d for d in debris_detections if d.debris_type == "organic"])
        
        return {
            'marine_life_threat': "high" if plastic_count > 10 else "medium" if plastic_count > 5 else "low",
            'ecosystem_disruption': "significant" if len(debris_detections) > 15 else "moderate",
            'biodiversity_impact': "high" if plastic_count > 8 else "low",
            'plastic_pollution_level': plastic_count,
            'biodegradable_ratio': organic_count / max(1, len(debris_detections))
        }
    
    def _assess_economic_impact(self, debris_detections: List) -> Dict[str, Any]:
        """Assess economic impact of debris"""
        total_size = sum(d.size_estimate for d in debris_detections)
        cleanup_cost = total_size * 100  # $100 per m² estimate
        
        return {
            'estimated_cleanup_cost': cleanup_cost,
            'tourism_impact': "high" if len(debris_detections) > 20 else "low",
            'fishing_industry_impact': "moderate" if len(debris_detections) > 10 else "low",
            'property_value_impact': "significant" if total_size > 500 else "minimal"
        }
    
    async def _analyze_debris_trends(
        self,
        area_bbox: Tuple[float, float, float, float],
        current_detections: List
    ) -> Dict[str, Any]:
        """Analyze debris trends over time"""
        # Simplified trend analysis (would use historical data in production)
        current_count = len(current_detections)
        
        # Simulate historical data for trend calculation
        historical_counts = [
            current_count * 0.8,  # 1 week ago
            current_count * 0.9,  # 3 days ago
            current_count * 1.1,  # 1 day ago
            current_count         # current
        ]
        
        # Calculate trend
        trend_slope = np.polyfit(range(len(historical_counts)), historical_counts, 1)[0]
        
        if trend_slope > 0.5:
            trend_direction = "increasing"
        elif trend_slope < -0.5:
            trend_direction = "decreasing"
        else:
            trend_direction = "stable"
        
        return {
            'trend_direction': trend_direction,
            'trend_slope': trend_slope,
            'weekly_change_percent': ((current_count - historical_counts[0]) / max(1, historical_counts[0])) * 100,
            'prediction_next_week': current_count + (trend_slope * 7),
            'seasonal_factor': self._get_seasonal_factor()
        }
    
    def _get_seasonal_factor(self) -> str:
        """Get current seasonal factor affecting debris"""
        month = datetime.now().month
        if month in [6, 7, 8]:
            return "summer_tourism_increase"
        elif month in [12, 1, 2]:
            return "winter_storm_transport"
        elif month in [3, 4, 5]:
            return "spring_runoff_increase"
        else:
            return "autumn_stable_period"
    
    def _generate_management_recommendations(
        self,
        debris_detections: List,
        hotspots: List[Dict[str, Any]],
        risk_assessment: Dict[str, Any]
    ) -> List[str]:
        """Generate AI-powered management recommendations"""
        recommendations = []
        
        # Hotspot-based recommendations
        critical_hotspots = [h for h in hotspots if h.get('priority') == 'critical']
        if critical_hotspots:
            recommendations.append(f"Deploy immediate cleanup to {len(critical_hotspots)} critical hotspots")
            recommendations.append("Coordinate with Taiwan Coast Guard for emergency response")
        
        # Risk-based recommendations
        risk_level = risk_assessment.get('risk_level', 'low')
        if risk_level in ['high', 'critical']:
            recommendations.append("Activate enhanced monitoring protocols")
            recommendations.append("Alert nearby vessels and coastal communities")
        
        # Type-specific recommendations
        plastic_debris = [d for d in debris_detections if d.debris_type == "plastic"]
        if len(plastic_debris) > 10:
            recommendations.append("Focus on plastic debris removal to protect marine life")
            recommendations.append("Implement source reduction measures for plastic pollution")
        
        # Resource optimization
        total_size = sum(d.size_estimate for d in debris_detections)
        if total_size > 200:
            recommendations.append("Deploy multiple cleanup vessels for efficient operation")
        else:
            recommendations.append("Single vessel cleanup operation recommended")
        
        # Prevention recommendations
        recommendations.append("Increase public education on marine debris prevention")
        recommendations.append("Enhance monitoring of high-risk vessel activities")
        
        return recommendations
    
    def _calculate_analysis_confidence(
        self,
        debris_detections: List,
        intelligence,
        hotspots: List[Dict[str, Any]]
    ) -> float:
        """Calculate confidence score for analysis"""
        confidence = 0.0
        
        # Data availability confidence
        if len(debris_detections) > 0:
            confidence += 0.3
        
        # Intelligence confidence
        if hasattr(intelligence, 'confidence_score'):
            confidence += intelligence.confidence_score * 0.4
        else:
            confidence += 0.2  # Base confidence without intelligence
        
        # Hotspot identification confidence
        if hotspots:
            avg_hotspot_confidence = sum(h.get('confidence', 0) for h in hotspots) / len(hotspots)
            confidence += avg_hotspot_confidence * 0.3
        else:
            confidence += 0.1  # Lower confidence without hotspots
        
        return min(1.0, confidence)
    
    async def create_cleanup_operation(
        self,
        target_area: Tuple[float, float, float, float],
        debris_targets: List[Dict[str, Any]],
        priority: Priority = Priority.MEDIUM
    ) -> CleanupOperation:
        """Create optimized cleanup operation plan"""
        try:
            operation_id = f"cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Calculate total debris to clean
            total_debris_size = sum(target.get('size_estimate', 0) for target in debris_targets)
            
            # Assign vessels based on capacity and location
            vessel_assignments = self._assign_cleanup_vessels(target_area, total_debris_size)
            
            # Estimate duration and cost
            estimated_duration = self._estimate_operation_duration(total_debris_size, vessel_assignments)
            estimated_cost = self._estimate_operation_cost(vessel_assignments, estimated_duration)
            
            # Assess environmental impact
            environmental_impact = {
                'fuel_consumption': estimated_duration * 50,  # liters per hour
                'carbon_footprint': estimated_duration * 50 * 2.3,  # kg CO2
                'marine_life_disruption': 'minimal' if estimated_duration < 8 else 'moderate',
                'ecosystem_benefit': 'high' if total_debris_size > 100 else 'medium'
            }
            
            # Calculate success probability
            success_probability = self._calculate_success_probability(
                total_debris_size, vessel_assignments, priority
            )
            
            operation = CleanupOperation(
                operation_id=operation_id,
                target_area=target_area,
                debris_targets=debris_targets,
                vessel_assignments=vessel_assignments,
                estimated_duration=estimated_duration,
                estimated_cost=estimated_cost,
                environmental_impact=environmental_impact,
                success_probability=success_probability,
                created_at=datetime.now()
            )
            
            logger.info(f"✅ Cleanup operation created: {operation_id}")
            return operation
            
        except Exception as e:
            logger.error(f"❌ Error creating cleanup operation: {e}")
            raise
    
    def _assign_cleanup_vessels(
        self,
        target_area: Tuple[float, float, float, float],
        total_debris_size: float
    ) -> List[Dict[str, Any]]:
        """Assign optimal vessels for cleanup operation"""
        available_vessels = [v for v in self.resource_database['cleanup_vessels'] if v['available']]
        
        if not available_vessels:
            return []
        
        # Calculate area center
        center_lat = (target_area[1] + target_area[3]) / 2
        center_lon = (target_area[0] + target_area[2]) / 2
        
        # Score vessels by distance and capacity
        vessel_scores = []
        for vessel in available_vessels:
            # Calculate distance (simplified)
            lat_diff = abs(vessel['location'][0] - center_lat)
            lon_diff = abs(vessel['location'][1] - center_lon)
            distance = (lat_diff ** 2 + lon_diff ** 2) ** 0.5
            
            # Score based on capacity and proximity
            capacity_score = vessel['capacity'] / 50.0  # Normalize to 50 tons
            distance_score = max(0, 1.0 - distance / 5.0)  # Penalty for distance > 5 degrees
            cost_score = 1.0 - (vessel['daily_cost'] / 10000.0)  # Normalize to $10k/day
            
            total_score = (capacity_score * 0.5) + (distance_score * 0.3) + (cost_score * 0.2)
            vessel_scores.append((vessel, total_score))
        
        # Sort by score and select best vessels
        vessel_scores.sort(key=lambda x: x[1], reverse=True)
        
        assignments = []
        remaining_capacity_needed = total_debris_size / 1000  # Convert to tons
        
        for vessel, score in vessel_scores:
            if remaining_capacity_needed <= 0:
                break
            
            assignment = {
                'vessel_id': vessel['id'],
                'vessel_name': vessel['name'],
                'capacity_tons': vessel['capacity'],
                'daily_cost': vessel['daily_cost'],
                'assigned_capacity': min(vessel['capacity'], remaining_capacity_needed),
                'travel_time_hours': distance * 2,  # Simplified travel time
                'score': score
            }
            assignments.append(assignment)
            remaining_capacity_needed -= vessel['capacity']
        
        return assignments
    
    def _estimate_operation_duration(
        self,
        total_debris_size: float,
        vessel_assignments: List[Dict[str, Any]]
    ) -> float:
        """Estimate operation duration in hours"""
        if not vessel_assignments:
            return 0.0
        
        # Base time for debris collection
        collection_time = total_debris_size / 100.0  # 1 hour per 100 m²
        
        # Add travel time
        max_travel_time = max(v.get('travel_time_hours', 0) for v in vessel_assignments)
        
        # Add setup and coordination time
        setup_time = 2.0  # 2 hours setup
        
        return collection_time + max_travel_time + setup_time
    
    def _estimate_operation_cost(
        self,
        vessel_assignments: List[Dict[str, Any]],
        duration_hours: float
    ) -> float:
        """Estimate total operation cost"""
        vessel_cost = sum(v['daily_cost'] * (duration_hours / 24.0) for v in vessel_assignments)
        fuel_cost = duration_hours * 100  # $100/hour fuel estimate
        personnel_cost = duration_hours * 200  # $200/hour personnel
        equipment_cost = 1000  # Fixed equipment cost
        
        return vessel_cost + fuel_cost + personnel_cost + equipment_cost
    
    def _calculate_success_probability(
        self,
        total_debris_size: float,
        vessel_assignments: List[Dict[str, Any]],
        priority: Priority
    ) -> float:
        """Calculate probability of successful cleanup operation"""
        base_probability = 0.7
        
        # Adjust for vessel capacity
        total_capacity = sum(v['capacity'] for v in vessel_assignments)
        capacity_ratio = total_capacity / max(1, total_debris_size / 1000)  # tons
        if capacity_ratio >= 1.5:
            base_probability += 0.2
        elif capacity_ratio < 0.8:
            base_probability -= 0.2
        
        # Adjust for priority
        if priority == Priority.CRITICAL:
            base_probability += 0.1
        elif priority == Priority.LOW:
            base_probability -= 0.1
        
        # Weather factor (simplified)
        base_probability -= 0.1  # Account for weather uncertainty
        
        return max(0.1, min(0.95, base_probability))
    
    async def generate_management_report(
        self,
        area_bbox: Tuple[float, float, float, float]
    ) -> Dict[str, Any]:
        """Generate comprehensive debris management report"""
        try:
            # Perform analysis
            analysis = await self.analyze_debris_situation(area_bbox)
            
            # Create cleanup operations for critical hotspots
            cleanup_operations = []
            critical_hotspots = [h for h in analysis.hotspots_identified if h.get('priority') == 'critical']
            
            for hotspot in critical_hotspots[:3]:  # Limit to 3 operations
                # Create debris targets from hotspot
                debris_targets = [{
                    'location': hotspot['center'],
                    'size_estimate': hotspot['total_size_m2'],
                    'debris_count': hotspot['debris_count'],
                    'priority': hotspot['priority']
                }]
                
                # Create small area around hotspot
                lat, lon = hotspot['center']
                hotspot_area = (lon - 0.01, lat - 0.01, lon + 0.01, lat + 0.01)
                
                operation = await self.create_cleanup_operation(
                    hotspot_area, debris_targets, Priority.CRITICAL
                )
                cleanup_operations.append(asdict(operation))
            
            return {
                'report_id': f"mgmt_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'area_analyzed': area_bbox,
                'analysis': asdict(analysis),
                'cleanup_operations': cleanup_operations,
                'resource_utilization': {
                    'vessels_available': len([v for v in self.resource_database['cleanup_vessels'] if v['available']]),
                    'total_vessel_capacity': sum(v['capacity'] for v in self.resource_database['cleanup_vessels']),
                    'estimated_total_cost': sum(op['estimated_cost'] for op in cleanup_operations)
                },
                'performance_metrics': self.performance_metrics,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating management report: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# Convenience function
async def create_marine_debris_agent() -> MarineDebrisAIAgent:
    """Create and initialize marine debris AI agent"""
    return MarineDebrisAIAgent()


if __name__ == "__main__":
    async def test_debris_agent():
        print("🤖 Testing Marine Debris AI Management Agent")
        
        # Test area: Taiwan Strait
        test_bbox = (119.0, 23.0, 121.0, 25.0)
        
        try:
            agent = await create_marine_debris_agent()
            
            # Generate management report
            report = await agent.generate_management_report(test_bbox)
            
            print("✅ Management report generated")
            print(f"   Report ID: {report.get('report_id', 'N/A')}")
            
            if 'analysis' in report:
                analysis = report['analysis']
                print(f"   Debris detected: {analysis.get('total_debris_detected', 0)}")
                print(f"   Hotspots identified: {len(analysis.get('hotspots_identified', []))}")
                print(f"   Risk level: {analysis.get('risk_assessment', {}).get('risk_level', 'unknown')}")
                print(f"   Confidence: {analysis.get('confidence_score', 0):.2f}")
            
            if 'cleanup_operations' in report:
                ops = report['cleanup_operations']
                print(f"   Cleanup operations planned: {len(ops)}")
                for i, op in enumerate(ops[:2], 1):
                    print(f"     Operation {i}: {op.get('estimated_duration', 0):.1f}h, ${op.get('estimated_cost', 0):,.0f}")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_debris_agent())
