"""World Bank API Integration for Economic and Environmental Data."""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class WorldBankAPI:
    """World Bank API integration for economic and environmental indicators."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.base_url = "https://api.worldbank.org/v2"
        self.session = None
    
    @log_async_function_call
    async def get_climate_indicators(self, country_code: str, 
                                   indicators: List[str] = None) -> Dict[str, Any]:
        """Get climate-related indicators from World Bank."""
        try:
            if indicators is None:
                indicators = [
                    'EN.ATM.CO2E.PC',    # CO2 emissions per capita
                    'AG.LND.PRCP.MM',    # Average precipitation
                    'EN.CLC.MDAT.ZS',    # Climate change knowledge index
                    'EG.USE.ELEC.KH.PC', # Electric power consumption
                    'EN.ATM.GHGT.KT.CE'  # Total GHG emissions
                ]
            
            results = {}
            
            for indicator in indicators:
                url = f"{self.base_url}/country/{country_code}/indicator/{indicator}"
                params = {
                    'format': 'json',
                    'date': '2015:2023',  # Last 8 years
                    'per_page': 100
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if len(data) > 1 and data[1]:  # World Bank returns metadata in first element
                                results[indicator] = data[1]
                        else:
                            logger.warning(f"Failed to get indicator {indicator}: status {response.status}")
            
            # Process the results
            processed_data = await self._process_wb_indicators(results)
            
            return {
                'status': 'success',
                'source': 'World Bank',
                'country_code': country_code,
                'indicators_collected': len(results),
                'climate_indicators': processed_data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"World Bank API error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _process_wb_indicators(self, raw_data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """Process World Bank indicator data."""
        processed = {}
        
        indicator_names = {
            'EN.ATM.CO2E.PC': 'co2_emissions_per_capita',
            'AG.LND.PRCP.MM': 'average_precipitation',
            'EN.CLC.MDAT.ZS': 'climate_knowledge_index',
            'EG.USE.ELEC.KH.PC': 'electricity_consumption_per_capita',
            'EN.ATM.GHGT.KT.CE': 'total_ghg_emissions'
        }
        
        for indicator_code, data_points in raw_data.items():
            indicator_name = indicator_names.get(indicator_code, indicator_code)
            
            # Extract values and years
            values = []
            years = []
            
            for point in data_points:
                if point.get('value') is not None:
                    values.append(point['value'])
                    years.append(point['date'])
            
            if values:
                # Calculate trends
                if len(values) > 1:
                    trend = 'increasing' if values[-1] > values[0] else 'decreasing' if values[-1] < values[0] else 'stable'
                    change_rate = (values[-1] - values[0]) / values[0] * 100 if values[0] != 0 else 0
                else:
                    trend = 'insufficient_data'
                    change_rate = 0
                
                processed[indicator_name] = {
                    'values': values,
                    'years': years,
                    'latest_value': values[-1] if values else None,
                    'latest_year': years[-1] if years else None,
                    'trend': trend,
                    'change_rate_percent': change_rate,
                    'average': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values)
                }
        
        return processed
    
    @log_async_function_call
    async def get_water_indicators(self, country_code: str) -> Dict[str, Any]:
        """Get water-related indicators."""
        try:
            water_indicators = [
                'SH.H2O.BASW.ZS',    # Basic water services
                'SH.STA.BASS.ZS',    # Basic sanitation services
                'ER.H2O.FWTL.K3',    # Annual freshwater withdrawals
                'AG.LND.IRIG.AG.ZS', # Agricultural irrigated land
                'EN.H2O.FWST.ZS'     # Annual freshwater withdrawals as % of internal resources
            ]
            
            results = {}
            
            for indicator in water_indicators:
                url = f"{self.base_url}/country/{country_code}/indicator/{indicator}"
                params = {
                    'format': 'json',
                    'date': '2015:2023',
                    'per_page': 100
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if len(data) > 1 and data[1]:
                                results[indicator] = data[1]
            
            # Process water indicators
            processed_data = await self._process_water_indicators(results)
            
            return {
                'status': 'success',
                'source': 'World Bank Water',
                'country_code': country_code,
                'water_indicators': processed_data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"World Bank Water API error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _process_water_indicators(self, raw_data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """Process water-related indicators."""
        processed = {}
        
        indicator_names = {
            'SH.H2O.BASW.ZS': 'basic_water_services_percent',
            'SH.STA.BASS.ZS': 'basic_sanitation_services_percent',
            'ER.H2O.FWTL.K3': 'freshwater_withdrawals_billion_m3',
            'AG.LND.IRIG.AG.ZS': 'irrigated_land_percent',
            'EN.H2O.FWST.ZS': 'water_stress_percent'
        }
        
        for indicator_code, data_points in raw_data.items():
            indicator_name = indicator_names.get(indicator_code, indicator_code)
            
            values = []
            years = []
            
            for point in data_points:
                if point.get('value') is not None:
                    values.append(point['value'])
                    years.append(point['date'])
            
            if values:
                processed[indicator_name] = {
                    'latest_value': values[-1],
                    'latest_year': years[-1],
                    'values': values,
                    'years': years,
                    'trend': self._calculate_trend(values),
                    'water_security_score': self._calculate_water_security_score(indicator_name, values[-1])
                }
        
        return processed
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend from values."""
        if len(values) < 2:
            return 'insufficient_data'
        
        recent_avg = sum(values[-3:]) / len(values[-3:])
        earlier_avg = sum(values[:3]) / len(values[:3])
        
        if recent_avg > earlier_avg * 1.05:
            return 'improving'
        elif recent_avg < earlier_avg * 0.95:
            return 'declining'
        else:
            return 'stable'
    
    def _calculate_water_security_score(self, indicator: str, value: float) -> float:
        """Calculate water security score based on indicator."""
        if 'basic_water_services' in indicator:
            return min(1.0, value / 100)  # Percentage to 0-1 scale
        elif 'water_stress' in indicator:
            return max(0.0, 1.0 - value / 100)  # Inverse for stress
        elif 'sanitation' in indicator:
            return min(1.0, value / 100)
        else:
            return 0.5  # Default neutral score
    
    @log_async_function_call
    async def get_economic_indicators(self, country_code: str) -> Dict[str, Any]:
        """Get economic indicators relevant to water management."""
        try:
            economic_indicators = [
                'NY.GDP.PCAP.CD',     # GDP per capita
                'SP.POP.TOTL',        # Total population
                'SP.URB.TOTL.IN.ZS',  # Urban population %
                'NV.AGR.TOTL.ZS',     # Agriculture value added % GDP
                'NV.IND.TOTL.ZS'      # Industry value added % GDP
            ]
            
            results = {}
            
            for indicator in economic_indicators:
                url = f"{self.base_url}/country/{country_code}/indicator/{indicator}"
                params = {
                    'format': 'json',
                    'date': '2015:2023',
                    'per_page': 100
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if len(data) > 1 and data[1]:
                                results[indicator] = data[1]
            
            # Process economic indicators
            processed_data = await self._process_economic_indicators(results)
            
            return {
                'status': 'success',
                'source': 'World Bank Economic',
                'country_code': country_code,
                'economic_indicators': processed_data,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"World Bank Economic API error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _process_economic_indicators(self, raw_data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """Process economic indicators."""
        processed = {}
        
        indicator_names = {
            'NY.GDP.PCAP.CD': 'gdp_per_capita_usd',
            'SP.POP.TOTL': 'total_population',
            'SP.URB.TOTL.IN.ZS': 'urban_population_percent',
            'NV.AGR.TOTL.ZS': 'agriculture_gdp_percent',
            'NV.IND.TOTL.ZS': 'industry_gdp_percent'
        }
        
        for indicator_code, data_points in raw_data.items():
            indicator_name = indicator_names.get(indicator_code, indicator_code)
            
            values = []
            years = []
            
            for point in data_points:
                if point.get('value') is not None:
                    values.append(point['value'])
                    years.append(point['date'])
            
            if values:
                processed[indicator_name] = {
                    'latest_value': values[-1],
                    'latest_year': years[-1],
                    'values': values,
                    'years': years,
                    'growth_rate': self._calculate_growth_rate(values),
                    'economic_impact_score': self._calculate_economic_impact(indicator_name, values[-1])
                }
        
        return processed
    
    def _calculate_growth_rate(self, values: List[float]) -> float:
        """Calculate annual growth rate."""
        if len(values) < 2:
            return 0.0
        
        years = len(values) - 1
        growth_rate = ((values[-1] / values[0]) ** (1/years) - 1) * 100
        return growth_rate
    
    def _calculate_economic_impact(self, indicator: str, value: float) -> float:
        """Calculate economic impact score for water management."""
        if 'gdp_per_capita' in indicator:
            # Higher GDP generally means better water infrastructure capacity
            return min(1.0, value / 50000)  # Normalize to $50k
        elif 'urban_population' in indicator:
            # Higher urbanization creates both challenges and opportunities
            return 0.5 + (value - 50) / 200  # Neutral at 50%, scale around that
        else:
            return 0.5  # Default neutral score


# Convenience functions
async def get_world_bank_climate_data(country_code: str) -> Dict[str, Any]:
    """Get comprehensive World Bank climate data."""
    api = WorldBankAPI()
    
    climate_data = await api.get_climate_indicators(country_code)
    water_data = await api.get_water_indicators(country_code)
    economic_data = await api.get_economic_indicators(country_code)
    
    return {
        'climate_indicators': climate_data,
        'water_indicators': water_data,
        'economic_indicators': economic_data,
        'collection_timestamp': datetime.now().isoformat()
    }
