// Complete backend feature configurations
// Generated on: 2025-06-15 15:50:28
// Total features: 72

const allFeatureConfigurations = {
  "health-check-endpoint": {
    "name": "Health Check Endpoint",
    "description": "System health monitoring and status verification endpoint",
    "icon": "fas fa-heartbeat",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 1,
    "endpoint": "/health",
    "options": {
      "configuration": {
        "title": "Health Check Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Health Check Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Health Check Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Health Check Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "21ms",
      "Success Rate": "96.1%",
      "Requests/min": "110"
    }
  },
  "root-api-endpoint": {
    "name": "Root API Endpoint",
    "description": "Main API entry point and routing configuration",
    "icon": "fas fa-home",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 2,
    "endpoint": "/",
    "options": {
      "configuration": {
        "title": "Root API Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Root API Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Root API Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Root API Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "22ms",
      "Success Rate": "97.2%",
      "Requests/min": "120"
    }
  },
  "system-status-endpoint": {
    "name": "System Status Endpoint",
    "description": "Comprehensive system operational status tracking",
    "icon": "fas fa-server",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 3,
    "endpoint": "/api/status",
    "options": {
      "configuration": {
        "title": "System Status Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom System Status Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "System Status Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "System Status Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "23ms",
      "Success Rate": "98.3%",
      "Requests/min": "130"
    }
  },
  "dashboard-data-endpoint": {
    "name": "Dashboard Data Endpoint",
    "description": "Unified data delivery API for dashboard components",
    "icon": "fas fa-chart-pie",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 4,
    "endpoint": "/api/dashboard",
    "options": {
      "configuration": {
        "title": "Dashboard Data Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Dashboard Data Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Dashboard Data Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Dashboard Data Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "24ms",
      "Success Rate": "99.4%",
      "Requests/min": "140"
    }
  },
  "operation-execution-endpoint": {
    "name": "Operation Execution Endpoint",
    "description": "Execute unified environmental operations",
    "icon": "fas fa-play-circle",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 5,
    "endpoint": "/api/operation",
    "options": {
      "configuration": {
        "title": "Operation Execution Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Operation Execution Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Operation Execution Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Operation Execution Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "25ms",
      "Success Rate": "95.5%",
      "Requests/min": "150"
    }
  },
  "marine-analysis-endpoint": {
    "name": "Marine Analysis Endpoint",
    "description": "Marine conservation analysis and processing",
    "icon": "fas fa-fish",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 6,
    "endpoint": "/api/marine/analyze",
    "options": {
      "configuration": {
        "title": "Marine Analysis Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Marine Analysis Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Marine Analysis Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Marine Analysis Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "26ms",
      "Success Rate": "96.6%",
      "Requests/min": "160"
    }
  },
  "water-analysis-endpoint": {
    "name": "Water Analysis Endpoint",
    "description": "Water management analysis and optimization",
    "icon": "fas fa-tint",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 7,
    "endpoint": "/api/water/analyze",
    "options": {
      "configuration": {
        "title": "Water Analysis Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Water Analysis Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Water Analysis Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Water Analysis Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "27ms",
      "Success Rate": "97.7%",
      "Requests/min": "170"
    }
  },
  "operation-history-endpoint": {
    "name": "Operation History Endpoint",
    "description": "Historical operation tracking and analysis",
    "icon": "fas fa-history",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 8,
    "endpoint": "/api/history",
    "options": {
      "configuration": {
        "title": "Operation History Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Operation History Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "Operation History Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Operation History Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "28ms",
      "Success Rate": "98.8%",
      "Requests/min": "180"
    }
  },
  "websocket-real-time-endpoint": {
    "name": "WebSocket Real-time Endpoint",
    "description": "Real-time data streaming and communication",
    "icon": "fas fa-broadcast-tower",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 9,
    "endpoint": "/ws",
    "options": {
      "configuration": {
        "title": "WebSocket Real-time Endpoint Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom WebSocket Real-time Endpoint settings"
          }
        ]
      },
      "monitoring": {
        "title": "WebSocket Real-time Endpoint Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "WebSocket Real-time Endpoint Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "29ms",
      "Success Rate": "99.9%",
      "Requests/min": "190"
    }
  },
  "openapi-documentation": {
    "name": "OpenAPI Documentation",
    "description": "Interactive API documentation and testing",
    "icon": "fas fa-book",
    "type": "backend",
    "status": "active",
    "category": "Core APIs",
    "feature_id": 10,
    "endpoint": "/docs",
    "options": {
      "configuration": {
        "title": "OpenAPI Documentation Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom OpenAPI Documentation settings"
          }
        ]
      },
      "monitoring": {
        "title": "OpenAPI Documentation Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "OpenAPI Documentation Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Response Time": "30ms",
      "Success Rate": "95.0%",
      "Requests/min": "200"
    }
  },
  "debris-detection-engine": {
    "name": "Debris Detection Engine",
    "description": "AI-powered marine debris identification and tracking system",
    "icon": "fas fa-search",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 11,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Debris Detection Engine Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Debris Detection Engine settings"
          }
        ]
      },
      "monitoring": {
        "title": "Debris Detection Engine Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Debris Detection Engine Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,100",
      "Accuracy": "96.1%",
      "Processing Speed": "21 fps"
    }
  },
  "multi-source-intelligence": {
    "name": "Multi-source Intelligence",
    "description": "Integrated intelligence from multiple marine data sources",
    "icon": "fas fa-brain",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 12,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Multi-source Intelligence Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Multi-source Intelligence settings"
          }
        ]
      },
      "monitoring": {
        "title": "Multi-source Intelligence Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Multi-source Intelligence Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,200",
      "Accuracy": "97.2%",
      "Processing Speed": "22 fps"
    }
  },
  "sentinel-hub-api-integration": {
    "name": "Sentinel Hub API Integration",
    "description": "Satellite imagery analysis for marine monitoring",
    "icon": "fas fa-satellite",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 13,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Sentinel Hub API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Sentinel Hub API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "Sentinel Hub API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Sentinel Hub API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,300",
      "Accuracy": "98.3%",
      "Processing Speed": "23 fps"
    }
  },
  "noaa-ocean-api-integration": {
    "name": "NOAA Ocean API Integration",
    "description": "Ocean conditions and weather data integration",
    "icon": "fas fa-cloud-sun",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 14,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "NOAA Ocean API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom NOAA Ocean API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "NOAA Ocean API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "NOAA Ocean API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,400",
      "Accuracy": "99.4%",
      "Processing Speed": "24 fps"
    }
  },
  "copernicus-marine-api-integration": {
    "name": "Copernicus Marine API Integration",
    "description": "European marine data and oceanographic information",
    "icon": "fas fa-globe-europe",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 15,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Copernicus Marine API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Copernicus Marine API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "Copernicus Marine API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Copernicus Marine API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,500",
      "Accuracy": "85.5%",
      "Processing Speed": "25 fps"
    }
  },
  "planet-labs-api-integration": {
    "name": "Planet Labs API Integration",
    "description": "High-resolution satellite imagery for marine analysis",
    "icon": "fas fa-satellite-dish",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 16,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Planet Labs API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Planet Labs API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "Planet Labs API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Planet Labs API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,600",
      "Accuracy": "86.6%",
      "Processing Speed": "26 fps"
    }
  },
  "nasa-open-api-integration": {
    "name": "NASA Open API Integration",
    "description": "NASA Earth observation data for marine research",
    "icon": "fas fa-rocket",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 17,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "NASA Open API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom NASA Open API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "NASA Open API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "NASA Open API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,700",
      "Accuracy": "87.7%",
      "Processing Speed": "27 fps"
    }
  },
  "ais-stream-api-integration": {
    "name": "AIS Stream API Integration",
    "description": "Real-time vessel tracking and maritime traffic analysis",
    "icon": "fas fa-ship",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 18,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "AIS Stream API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom AIS Stream API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "AIS Stream API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "AIS Stream API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,800",
      "Accuracy": "88.8%",
      "Processing Speed": "28 fps"
    }
  },
  "openstreetmap-api-integration": {
    "name": "OpenStreetMap API Integration",
    "description": "Coastal infrastructure and geographic data",
    "icon": "fas fa-map",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 19,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "OpenStreetMap API Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom OpenStreetMap API Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "OpenStreetMap API Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "OpenStreetMap API Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "2,900",
      "Accuracy": "89.9%",
      "Processing Speed": "29 fps"
    }
  },
  "climate-marine-agent": {
    "name": "Climate Marine Agent",
    "description": "AI agent for climate impact analysis on marine ecosystems",
    "icon": "fas fa-thermometer-half",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 20,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Climate Marine Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Climate Marine Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Climate Marine Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Climate Marine Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,000",
      "Accuracy": "90.0%",
      "Processing Speed": "10 fps"
    }
  },
  "water-treatment-marine-agent": {
    "name": "Water Treatment Marine Agent",
    "description": "AI agent for marine water treatment optimization",
    "icon": "fas fa-filter",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 21,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Water Treatment Marine Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Water Treatment Marine Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Water Treatment Marine Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Water Treatment Marine Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,100",
      "Accuracy": "91.1%",
      "Processing Speed": "11 fps"
    }
  },
  "energy-efficiency-marine-agent": {
    "name": "Energy Efficiency Marine Agent",
    "description": "AI agent for marine energy efficiency optimization",
    "icon": "fas fa-bolt",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 22,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Energy Efficiency Marine Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Energy Efficiency Marine Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Energy Efficiency Marine Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Energy Efficiency Marine Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,200",
      "Accuracy": "92.2%",
      "Processing Speed": "12 fps"
    }
  },
  "sustainability-marine-agent": {
    "name": "Sustainability Marine Agent",
    "description": "AI agent for marine sustainability assessment",
    "icon": "fas fa-leaf",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 23,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Sustainability Marine Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Sustainability Marine Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Sustainability Marine Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Sustainability Marine Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,300",
      "Accuracy": "93.3%",
      "Processing Speed": "13 fps"
    }
  },
  "risk-analysis-marine-agent": {
    "name": "Risk Analysis Marine Agent",
    "description": "AI agent for marine environmental risk assessment",
    "icon": "fas fa-exclamation-triangle",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 24,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Risk Analysis Marine Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Risk Analysis Marine Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Risk Analysis Marine Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Risk Analysis Marine Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,400",
      "Accuracy": "94.4%",
      "Processing Speed": "14 fps"
    }
  },
  "marine-debris-ai-agent": {
    "name": "Marine Debris AI Agent",
    "description": "Specialized AI agent for marine debris analysis",
    "icon": "fas fa-trash-alt",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 25,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Marine Debris AI Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Marine Debris AI Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Marine Debris AI Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Marine Debris AI Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,500",
      "Accuracy": "95.5%",
      "Processing Speed": "15 fps"
    }
  },
  "debris-tracking-dashboard": {
    "name": "Debris Tracking Dashboard",
    "description": "Real-time dashboard for marine debris monitoring",
    "icon": "fas fa-chart-line",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 26,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Debris Tracking Dashboard Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Debris Tracking Dashboard settings"
          }
        ]
      },
      "monitoring": {
        "title": "Debris Tracking Dashboard Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Debris Tracking Dashboard Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,600",
      "Accuracy": "96.6%",
      "Processing Speed": "16 fps"
    }
  },
  "hotspot-detection": {
    "name": "Hotspot Detection",
    "description": "AI-powered detection of marine pollution hotspots",
    "icon": "fas fa-fire",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 27,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Hotspot Detection Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Hotspot Detection settings"
          }
        ]
      },
      "monitoring": {
        "title": "Hotspot Detection Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Hotspot Detection Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,700",
      "Accuracy": "97.7%",
      "Processing Speed": "17 fps"
    }
  },
  "cleanup-route-optimizer": {
    "name": "Cleanup Route Optimizer",
    "description": "Optimal route planning for marine cleanup operations",
    "icon": "fas fa-route",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 28,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Cleanup Route Optimizer Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Cleanup Route Optimizer settings"
          }
        ]
      },
      "monitoring": {
        "title": "Cleanup Route Optimizer Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Cleanup Route Optimizer Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,800",
      "Accuracy": "98.8%",
      "Processing Speed": "18 fps"
    }
  },
  "ml-debris-categorizer": {
    "name": "ML Debris Categorizer",
    "description": "Machine learning system for debris classification",
    "icon": "fas fa-tags",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 29,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "ML Debris Categorizer Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom ML Debris Categorizer settings"
          }
        ]
      },
      "monitoring": {
        "title": "ML Debris Categorizer Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "ML Debris Categorizer Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "3,900",
      "Accuracy": "99.9%",
      "Processing Speed": "19 fps"
    }
  },
  "ai-recycling-optimizer": {
    "name": "AI Recycling Optimizer",
    "description": "AI-powered recycling process optimization",
    "icon": "fas fa-recycle",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 30,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "AI Recycling Optimizer Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom AI Recycling Optimizer settings"
          }
        ]
      },
      "monitoring": {
        "title": "AI Recycling Optimizer Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "AI Recycling Optimizer Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,000",
      "Accuracy": "85.0%",
      "Processing Speed": "20 fps"
    }
  },
  "taiwan-government-platform-integration": {
    "name": "Taiwan Government Platform Integration",
    "description": "Integration with Taiwan government marine systems",
    "icon": "fas fa-flag",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 31,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Taiwan Government Platform Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Taiwan Government Platform Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "Taiwan Government Platform Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Taiwan Government Platform Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,100",
      "Accuracy": "86.1%",
      "Processing Speed": "21 fps"
    }
  },
  "community-engagement-agent": {
    "name": "Community Engagement Agent",
    "description": "AI agent for community involvement in marine conservation",
    "icon": "fas fa-users",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 32,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Community Engagement Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Community Engagement Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Community Engagement Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Community Engagement Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,200",
      "Accuracy": "87.2%",
      "Processing Speed": "22 fps"
    }
  },
  "policy-analysis-agent": {
    "name": "Policy Analysis Agent",
    "description": "AI agent for marine policy analysis and compliance",
    "icon": "fas fa-gavel",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 33,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Policy Analysis Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Policy Analysis Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Policy Analysis Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Policy Analysis Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,300",
      "Accuracy": "88.3%",
      "Processing Speed": "23 fps"
    }
  },
  "innovation-agent": {
    "name": "Innovation Agent",
    "description": "AI agent for identifying marine conservation innovations",
    "icon": "fas fa-lightbulb",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 34,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Innovation Agent Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Innovation Agent settings"
          }
        ]
      },
      "monitoring": {
        "title": "Innovation Agent Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Innovation Agent Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,400",
      "Accuracy": "89.4%",
      "Processing Speed": "24 fps"
    }
  },
  "advanced-analytics-engine": {
    "name": "Advanced Analytics Engine",
    "description": "Advanced analytics for marine data processing",
    "icon": "fas fa-chart-area",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 35,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Advanced Analytics Engine Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Advanced Analytics Engine settings"
          }
        ]
      },
      "monitoring": {
        "title": "Advanced Analytics Engine Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Advanced Analytics Engine Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,500",
      "Accuracy": "90.5%",
      "Processing Speed": "25 fps"
    }
  },
  "blockchain-integration": {
    "name": "Blockchain Integration",
    "description": "Blockchain technology for marine data integrity",
    "icon": "fas fa-link",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 36,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Blockchain Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Blockchain Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "Blockchain Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Blockchain Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,600",
      "Accuracy": "91.6%",
      "Processing Speed": "26 fps"
    }
  },
  "ar-vr-experiences": {
    "name": "AR/VR Experiences",
    "description": "Augmented and virtual reality marine experiences",
    "icon": "fas fa-vr-cardboard",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 37,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "AR/VR Experiences Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom AR/VR Experiences settings"
          }
        ]
      },
      "monitoring": {
        "title": "AR/VR Experiences Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "AR/VR Experiences Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,700",
      "Accuracy": "92.7%",
      "Processing Speed": "27 fps"
    }
  },
  "iot-sensor-networks": {
    "name": "IoT Sensor Networks",
    "description": "Internet of Things sensors for marine monitoring",
    "icon": "fas fa-wifi",
    "type": "backend",
    "status": "active",
    "category": "Marine Conservation",
    "feature_id": 38,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "IoT Sensor Networks Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom IoT Sensor Networks settings"
          }
        ]
      },
      "monitoring": {
        "title": "IoT Sensor Networks Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "IoT Sensor Networks Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Data Points": "4,800",
      "Accuracy": "93.8%",
      "Processing Speed": "28 fps"
    }
  },
  "water-quality-monitoring": {
    "name": "Water Quality Monitoring",
    "description": "Real-time water quality assessment and tracking",
    "icon": "fas fa-tint",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 39,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Water Quality Monitoring Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Water Quality Monitoring settings"
          }
        ]
      },
      "monitoring": {
        "title": "Water Quality Monitoring Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Water Quality Monitoring Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "99%",
      "Efficiency": "94.9%",
      "Compliance": "100%"
    }
  },
  "treatment-efficiency-analysis": {
    "name": "Treatment Efficiency Analysis",
    "description": "Analysis and optimization of water treatment processes",
    "icon": "fas fa-filter",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 40,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Treatment Efficiency Analysis Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Treatment Efficiency Analysis settings"
          }
        ]
      },
      "monitoring": {
        "title": "Treatment Efficiency Analysis Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Treatment Efficiency Analysis Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "80%",
      "Efficiency": "95.0%",
      "Compliance": "100%"
    }
  },
  "energy-efficiency-optimization": {
    "name": "Energy Efficiency Optimization",
    "description": "Energy consumption optimization for water systems",
    "icon": "fas fa-bolt",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 41,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Energy Efficiency Optimization Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Energy Efficiency Optimization settings"
          }
        ]
      },
      "monitoring": {
        "title": "Energy Efficiency Optimization Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Energy Efficiency Optimization Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "81%",
      "Efficiency": "96.1%",
      "Compliance": "100%"
    }
  },
  "carbon-footprint-calculation": {
    "name": "Carbon Footprint Calculation",
    "description": "Carbon footprint assessment for water operations",
    "icon": "fas fa-leaf",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 42,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Carbon Footprint Calculation Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Carbon Footprint Calculation settings"
          }
        ]
      },
      "monitoring": {
        "title": "Carbon Footprint Calculation Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Carbon Footprint Calculation Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "82%",
      "Efficiency": "97.2%",
      "Compliance": "100%"
    }
  },
  "daily-capacity-management": {
    "name": "Daily Capacity Management",
    "description": "Daily water treatment capacity planning and management",
    "icon": "fas fa-calendar-day",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 43,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Daily Capacity Management Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Daily Capacity Management settings"
          }
        ]
      },
      "monitoring": {
        "title": "Daily Capacity Management Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Daily Capacity Management Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "83%",
      "Efficiency": "98.3%",
      "Compliance": "100%"
    }
  },
  "active-plants-monitoring": {
    "name": "Active Plants Monitoring",
    "description": "Real-time monitoring of active water treatment plants",
    "icon": "fas fa-industry",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 44,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Active Plants Monitoring Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Active Plants Monitoring settings"
          }
        ]
      },
      "monitoring": {
        "title": "Active Plants Monitoring Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Active Plants Monitoring Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "84%",
      "Efficiency": "99.4%",
      "Compliance": "100%"
    }
  },
  "system-status-tracking": {
    "name": "System Status Tracking",
    "description": "Comprehensive water system status monitoring",
    "icon": "fas fa-chart-line",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 45,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "System Status Tracking Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom System Status Tracking settings"
          }
        ]
      },
      "monitoring": {
        "title": "System Status Tracking Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "System Status Tracking Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "85%",
      "Efficiency": "85.5%",
      "Compliance": "100%"
    }
  },
  "performance-metrics": {
    "name": "Performance Metrics",
    "description": "Key performance indicators for water management",
    "icon": "fas fa-chart-bar",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 46,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Performance Metrics Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Performance Metrics settings"
          }
        ]
      },
      "monitoring": {
        "title": "Performance Metrics Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Performance Metrics Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "86%",
      "Efficiency": "86.6%",
      "Compliance": "100%"
    }
  },
  "maintenance-scheduling": {
    "name": "Maintenance Scheduling",
    "description": "Automated maintenance scheduling for water systems",
    "icon": "fas fa-wrench",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 47,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Maintenance Scheduling Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Maintenance Scheduling settings"
          }
        ]
      },
      "monitoring": {
        "title": "Maintenance Scheduling Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Maintenance Scheduling Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "87%",
      "Efficiency": "87.7%",
      "Compliance": "100%"
    }
  },
  "resource-optimization": {
    "name": "Resource Optimization",
    "description": "Optimization of resources across all systems",
    "icon": "fas fa-optimize",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 59,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Resource Optimization Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Resource Optimization settings"
          }
        ]
      },
      "monitoring": {
        "title": "Resource Optimization Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Resource Optimization Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "climate-impact-assessment": {
    "name": "Climate Impact Assessment",
    "description": "Assessment of climate impact on water systems",
    "icon": "fas fa-thermometer-half",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 49,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Climate Impact Assessment Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Climate Impact Assessment settings"
          }
        ]
      },
      "monitoring": {
        "title": "Climate Impact Assessment Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Climate Impact Assessment Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "89%",
      "Efficiency": "89.9%",
      "Compliance": "100%"
    }
  },
  "energy-consumption-tracking": {
    "name": "Energy Consumption Tracking",
    "description": "Real-time energy consumption monitoring",
    "icon": "fas fa-plug",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 50,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Energy Consumption Tracking Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Energy Consumption Tracking settings"
          }
        ]
      },
      "monitoring": {
        "title": "Energy Consumption Tracking Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Energy Consumption Tracking Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "90%",
      "Efficiency": "90.0%",
      "Compliance": "100%"
    }
  },
  "treatment-process-control": {
    "name": "Treatment Process Control",
    "description": "Automated control of water treatment processes",
    "icon": "fas fa-sliders-h",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 51,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Treatment Process Control Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Treatment Process Control settings"
          }
        ]
      },
      "monitoring": {
        "title": "Treatment Process Control Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Treatment Process Control Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "91%",
      "Efficiency": "91.1%",
      "Compliance": "100%"
    }
  },
  "quality-assurance": {
    "name": "Quality Assurance",
    "description": "Quality assurance and compliance monitoring",
    "icon": "fas fa-check-circle",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 52,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Quality Assurance Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Quality Assurance settings"
          }
        ]
      },
      "monitoring": {
        "title": "Quality Assurance Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Quality Assurance Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "92%",
      "Efficiency": "92.2%",
      "Compliance": "100%"
    }
  },
  "regulatory-compliance": {
    "name": "Regulatory Compliance",
    "description": "Regulatory compliance tracking and reporting",
    "icon": "fas fa-gavel",
    "type": "backend",
    "status": "active",
    "category": "Water Management",
    "feature_id": 53,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Regulatory Compliance Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Regulatory Compliance settings"
          }
        ]
      },
      "monitoring": {
        "title": "Regulatory Compliance Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Regulatory Compliance Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Quality Score": "93%",
      "Efficiency": "93.3%",
      "Compliance": "100%"
    }
  },
  "environmental-score-calculation": {
    "name": "Environmental Score Calculation",
    "description": "Comprehensive environmental impact scoring",
    "icon": "fas fa-calculator",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 54,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Environmental Score Calculation Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Environmental Score Calculation settings"
          }
        ]
      },
      "monitoring": {
        "title": "Environmental Score Calculation Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Environmental Score Calculation Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "synergy-score-analysis": {
    "name": "Synergy Score Analysis",
    "description": "Analysis of synergies between marine and water systems",
    "icon": "fas fa-link",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 55,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Synergy Score Analysis Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Synergy Score Analysis settings"
          }
        ]
      },
      "monitoring": {
        "title": "Synergy Score Analysis Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Synergy Score Analysis Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "cross-system-correlations": {
    "name": "Cross-System Correlations",
    "description": "Correlation analysis across environmental systems",
    "icon": "fas fa-project-diagram",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 56,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Cross-System Correlations Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Cross-System Correlations settings"
          }
        ]
      },
      "monitoring": {
        "title": "Cross-System Correlations Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Cross-System Correlations Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "ai-recommendations-engine": {
    "name": "AI Recommendations Engine",
    "description": "AI-powered recommendations for system optimization",
    "icon": "fas fa-robot",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 57,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "AI Recommendations Engine Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom AI Recommendations Engine settings"
          }
        ]
      },
      "monitoring": {
        "title": "AI Recommendations Engine Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "AI Recommendations Engine Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "cross-system-insights": {
    "name": "Cross-System Insights",
    "description": "Insights from integrated system analysis",
    "icon": "fas fa-lightbulb",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 58,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Cross-System Insights Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Cross-System Insights settings"
          }
        ]
      },
      "monitoring": {
        "title": "Cross-System Insights Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Cross-System Insights Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "synergy-opportunities": {
    "name": "Synergy Opportunities",
    "description": "Identification of synergy opportunities",
    "icon": "fas fa-handshake",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 60,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Synergy Opportunities Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Synergy Opportunities settings"
          }
        ]
      },
      "monitoring": {
        "title": "Synergy Opportunities Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Synergy Opportunities Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "predictive-analytics": {
    "name": "Predictive Analytics",
    "description": "Predictive modeling for environmental systems",
    "icon": "fas fa-crystal-ball",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 61,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Predictive Analytics Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Predictive Analytics settings"
          }
        ]
      },
      "monitoring": {
        "title": "Predictive Analytics Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Predictive Analytics Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "performance-benchmarking": {
    "name": "Performance Benchmarking",
    "description": "Benchmarking against industry standards",
    "icon": "fas fa-trophy",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 62,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Performance Benchmarking Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Performance Benchmarking settings"
          }
        ]
      },
      "monitoring": {
        "title": "Performance Benchmarking Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Performance Benchmarking Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "impact-assessment": {
    "name": "Impact Assessment",
    "description": "Comprehensive environmental impact assessment",
    "icon": "fas fa-balance-scale",
    "type": "backend",
    "status": "active",
    "category": "Integrated Analytics",
    "feature_id": 63,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Impact Assessment Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Impact Assessment settings"
          }
        ]
      },
      "monitoring": {
        "title": "Impact Assessment Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Impact Assessment Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "real-time-data-processing": {
    "name": "Real-time Data Processing",
    "description": "Real-time processing of environmental data streams",
    "icon": "fas fa-stream",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 64,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Real-time Data Processing Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Real-time Data Processing settings"
          }
        ]
      },
      "monitoring": {
        "title": "Real-time Data Processing Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Real-time Data Processing Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "data-validation-system": {
    "name": "Data Validation System",
    "description": "Automated data validation and quality control",
    "icon": "fas fa-check-double",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 65,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Data Validation System Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Data Validation System settings"
          }
        ]
      },
      "monitoring": {
        "title": "Data Validation System Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Data Validation System Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "multi-source-data-integration": {
    "name": "Multi-source Data Integration",
    "description": "Integration of data from multiple sources",
    "icon": "fas fa-layer-group",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 66,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Multi-source Data Integration Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Multi-source Data Integration settings"
          }
        ]
      },
      "monitoring": {
        "title": "Multi-source Data Integration Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Multi-source Data Integration Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "data-quality-assessment": {
    "name": "Data Quality Assessment",
    "description": "Assessment and scoring of data quality",
    "icon": "fas fa-star",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 67,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Data Quality Assessment Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Data Quality Assessment settings"
          }
        ]
      },
      "monitoring": {
        "title": "Data Quality Assessment Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Data Quality Assessment Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "automated-data-cleaning": {
    "name": "Automated Data Cleaning",
    "description": "Automated cleaning and preprocessing of data",
    "icon": "fas fa-broom",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 68,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Automated Data Cleaning Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Automated Data Cleaning settings"
          }
        ]
      },
      "monitoring": {
        "title": "Automated Data Cleaning Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Automated Data Cleaning Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "data-storage-management": {
    "name": "Data Storage Management",
    "description": "Efficient storage and retrieval of environmental data",
    "icon": "fas fa-database",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 69,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Data Storage Management Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Data Storage Management settings"
          }
        ]
      },
      "monitoring": {
        "title": "Data Storage Management Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Data Storage Management Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "backup-systems": {
    "name": "Backup Systems",
    "description": "Automated backup and recovery systems",
    "icon": "fas fa-save",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 70,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Backup Systems Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Backup Systems settings"
          }
        ]
      },
      "monitoring": {
        "title": "Backup Systems Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Backup Systems Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "data-security": {
    "name": "Data Security",
    "description": "Security and encryption for environmental data",
    "icon": "fas fa-shield-alt",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 71,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Data Security Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Data Security settings"
          }
        ]
      },
      "monitoring": {
        "title": "Data Security Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Data Security Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "api-rate-limiting": {
    "name": "API Rate Limiting",
    "description": "Rate limiting and throttling for API endpoints",
    "icon": "fas fa-stopwatch",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 72,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "API Rate Limiting Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom API Rate Limiting settings"
          }
        ]
      },
      "monitoring": {
        "title": "API Rate Limiting Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "API Rate Limiting Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  },
  "caching-system": {
    "name": "Caching System",
    "description": "Intelligent caching for improved performance",
    "icon": "fas fa-memory",
    "type": "backend",
    "status": "active",
    "category": "Data Management",
    "feature_id": 73,
    "endpoint": "",
    "options": {
      "configuration": {
        "title": "Caching System Configuration",
        "icon": "fas fa-cog",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Feature",
            "id": "enabled",
            "checked": true
          },
          {
            "type": "select",
            "label": "Priority Level",
            "id": "priority",
            "options": [
              "Low",
              "Medium",
              "High",
              "Critical"
            ],
            "value": "High"
          },
          {
            "type": "range",
            "label": "Update Frequency (seconds)",
            "id": "frequency",
            "min": 1,
            "max": 300,
            "value": 30
          },
          {
            "type": "text",
            "label": "Description",
            "id": "description",
            "value": "Custom Caching System settings"
          }
        ]
      },
      "monitoring": {
        "title": "Caching System Monitoring",
        "icon": "fas fa-eye",
        "controls": [
          {
            "type": "checkbox",
            "label": "Enable Monitoring",
            "id": "monitoring",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Real-time Alerts",
            "id": "alerts",
            "checked": true
          },
          {
            "type": "checkbox",
            "label": "Performance Tracking",
            "id": "performance",
            "checked": true
          },
          {
            "type": "select",
            "label": "Log Level",
            "id": "log_level",
            "options": [
              "DEBUG",
              "INFO",
              "WARNING",
              "ERROR"
            ],
            "value": "INFO"
          }
        ]
      },
      "actions": {
        "title": "Caching System Actions",
        "icon": "fas fa-play",
        "controls": [
          {
            "type": "button",
            "label": "Start Feature",
            "action": "startFeature",
            "class": "btn-success"
          },
          {
            "type": "button",
            "label": "Stop Feature",
            "action": "stopFeature",
            "class": "btn-danger"
          },
          {
            "type": "button",
            "label": "Test Feature",
            "action": "testFeature",
            "class": "btn"
          },
          {
            "type": "button",
            "label": "Export Data",
            "action": "exportData",
            "class": "btn-secondary"
          }
        ]
      }
    },
    "metrics": {
      "Status": "Active",
      "Performance": "Optimal",
      "Uptime": "99.9%",
      "Last Updated": "5 minutes ago"
    }
  }
};

// Export for use in individual_feature_interfaces.html
if (typeof window !== 'undefined') {
    window.allFeatureConfigurations = allFeatureConfigurations;
}