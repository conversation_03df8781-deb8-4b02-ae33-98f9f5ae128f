"""OpenWeatherMap API Integration for Weather Data Collection."""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class OpenWeatherMapAPI:
    """OpenWeatherMap API integration for weather data."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openweathermap.org/data/2.5"
        self.session = None
    
    @log_async_function_call
    async def get_current_weather(self, location: Dict[str, float]) -> Dict[str, Any]:
        """Get current weather data."""
        try:
            url = f"{self.base_url}/weather"
            params = {
                'lat': location['lat'],
                'lon': location['lon'],
                'appid': self.api_key,
                'units': 'metric'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'status': 'success',
                            'temperature': data['main']['temp'],
                            'humidity': data['main']['humidity'],
                            'pressure': data['main']['pressure'],
                            'wind_speed': data['wind']['speed'],
                            'description': data['weather'][0]['description'],
                            'timestamp': datetime.now().isoformat()
                        }
                    else:
                        return {'status': 'error', 'error': f'API returned status {response.status}'}
        
        except Exception as e:
            logger.error(f"OpenWeatherMap API error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_forecast(self, location: Dict[str, float], days: int = 5) -> Dict[str, Any]:
        """Get weather forecast."""
        try:
            url = f"{self.base_url}/forecast"
            params = {
                'lat': location['lat'],
                'lon': location['lon'],
                'appid': self.api_key,
                'units': 'metric',
                'cnt': days * 8  # 8 forecasts per day (3-hour intervals)
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        forecasts = []
                        
                        for item in data['list']:
                            forecasts.append({
                                'datetime': item['dt_txt'],
                                'temperature': item['main']['temp'],
                                'humidity': item['main']['humidity'],
                                'pressure': item['main']['pressure'],
                                'wind_speed': item['wind']['speed'],
                                'precipitation': item.get('rain', {}).get('3h', 0),
                                'description': item['weather'][0]['description']
                            })
                        
                        return {
                            'status': 'success',
                            'forecasts': forecasts,
                            'location': data['city']['name'],
                            'timestamp': datetime.now().isoformat()
                        }
                    else:
                        return {'status': 'error', 'error': f'API returned status {response.status}'}
        
        except Exception as e:
            logger.error(f"OpenWeatherMap forecast error: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience function
async def get_weather_data(api_key: str, location: Dict[str, float]) -> Dict[str, Any]:
    """Get comprehensive weather data."""
    api = OpenWeatherMapAPI(api_key)
    
    current = await api.get_current_weather(location)
    forecast = await api.get_forecast(location)
    
    return {
        'current_weather': current,
        'forecast': forecast,
        'collection_timestamp': datetime.now().isoformat()
    }
