"""User Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import secrets

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """User roles in the system."""
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    MAINTENANCE = "maintenance"
    ANALYST = "analyst"


class UserStatus(Enum):
    """User account status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class UserManager:
    """User management and authentication system."""
    
    def __init__(self):
        self.users: Dict[str, Dict[str, Any]] = {}
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.login_attempts: Dict[str, List[datetime]] = {}
        self.max_login_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        
        # Initialize default admin user
        self._create_default_users()
    
    def _create_default_users(self):
        """Create default system users."""
        default_users = [
            {
                'user_id': 'admin',
                'username': 'admin',
                'email': '<EMAIL>',
                'role': UserRole.ADMIN,
                'password_hash': self._hash_password('admin123'),
                'status': UserStatus.ACTIVE,
                'created_at': datetime.now(),
                'permissions': ['*']  # All permissions
            },
            {
                'user_id': 'operator1',
                'username': 'operator',
                'email': '<EMAIL>',
                'role': UserRole.OPERATOR,
                'password_hash': self._hash_password('operator123'),
                'status': UserStatus.ACTIVE,
                'created_at': datetime.now(),
                'permissions': ['read_data', 'control_systems', 'view_reports']
            },
            {
                'user_id': 'viewer1',
                'username': 'viewer',
                'email': '<EMAIL>',
                'role': UserRole.VIEWER,
                'password_hash': self._hash_password('viewer123'),
                'status': UserStatus.ACTIVE,
                'created_at': datetime.now(),
                'permissions': ['read_data', 'view_reports']
            }
        ]
        
        for user in default_users:
            self.users[user['user_id']] = user
        
        logger.info(f"Created {len(default_users)} default users")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256."""
        salt = "water_management_salt"  # In production, use random salt per user
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash."""
        return self._hash_password(password) == password_hash
    
    async def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user credentials."""
        try:
            # Check for account lockout
            if await self._is_account_locked(username):
                return {
                    'status': 'error',
                    'error': 'Account temporarily locked due to too many failed attempts'
                }
            
            # Find user
            user = None
            for user_data in self.users.values():
                if user_data['username'] == username:
                    user = user_data
                    break
            
            if not user:
                await self._record_failed_attempt(username)
                return {
                    'status': 'error',
                    'error': 'Invalid username or password'
                }
            
            # Check user status
            if user['status'] != UserStatus.ACTIVE:
                return {
                    'status': 'error',
                    'error': f'Account is {user["status"].value}'
                }
            
            # Verify password
            if not self._verify_password(password, user['password_hash']):
                await self._record_failed_attempt(username)
                return {
                    'status': 'error',
                    'error': 'Invalid username or password'
                }
            
            # Clear failed attempts
            if username in self.login_attempts:
                del self.login_attempts[username]
            
            # Create session
            session_token = await self._create_session(user)
            
            logger.info(f"User authenticated: {username}")
            
            return {
                'status': 'success',
                'user_id': user['user_id'],
                'username': user['username'],
                'role': user['role'].value,
                'permissions': user['permissions'],
                'session_token': session_token
            }
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return {
                'status': 'error',
                'error': 'Authentication system error'
            }
    
    async def _is_account_locked(self, username: str) -> bool:
        """Check if account is locked due to failed attempts."""
        if username not in self.login_attempts:
            return False
        
        attempts = self.login_attempts[username]
        recent_attempts = [
            attempt for attempt in attempts
            if datetime.now() - attempt < self.lockout_duration
        ]
        
        return len(recent_attempts) >= self.max_login_attempts
    
    async def _record_failed_attempt(self, username: str):
        """Record failed login attempt."""
        if username not in self.login_attempts:
            self.login_attempts[username] = []
        
        self.login_attempts[username].append(datetime.now())
        
        # Keep only recent attempts
        cutoff_time = datetime.now() - self.lockout_duration
        self.login_attempts[username] = [
            attempt for attempt in self.login_attempts[username]
            if attempt > cutoff_time
        ]
    
    async def _create_session(self, user: Dict[str, Any]) -> str:
        """Create user session."""
        session_token = secrets.token_urlsafe(32)
        
        session_data = {
            'user_id': user['user_id'],
            'username': user['username'],
            'role': user['role'].value,
            'permissions': user['permissions'],
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'expires_at': datetime.now() + timedelta(hours=8)
        }
        
        self.sessions[session_token] = session_data
        
        return session_token
    
    async def validate_session(self, session_token: str) -> Dict[str, Any]:
        """Validate user session."""
        if session_token not in self.sessions:
            return {
                'status': 'error',
                'error': 'Invalid session token'
            }
        
        session = self.sessions[session_token]
        
        # Check expiration
        if datetime.now() > session['expires_at']:
            del self.sessions[session_token]
            return {
                'status': 'error',
                'error': 'Session expired'
            }
        
        # Update last activity
        session['last_activity'] = datetime.now()
        
        return {
            'status': 'success',
            'user_id': session['user_id'],
            'username': session['username'],
            'role': session['role'],
            'permissions': session['permissions']
        }
    
    async def logout_user(self, session_token: str) -> Dict[str, Any]:
        """Logout user and invalidate session."""
        if session_token in self.sessions:
            username = self.sessions[session_token]['username']
            del self.sessions[session_token]
            logger.info(f"User logged out: {username}")
            
            return {
                'status': 'success',
                'message': 'Logged out successfully'
            }
        
        return {
            'status': 'error',
            'error': 'Invalid session token'
        }
    
    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new user account."""
        try:
            # Validate required fields
            required_fields = ['username', 'email', 'password', 'role']
            for field in required_fields:
                if field not in user_data:
                    return {
                        'status': 'error',
                        'error': f'Missing required field: {field}'
                    }
            
            # Check if username already exists
            for user in self.users.values():
                if user['username'] == user_data['username']:
                    return {
                        'status': 'error',
                        'error': 'Username already exists'
                    }
            
            # Create user
            user_id = f"user_{len(self.users) + 1}"
            
            new_user = {
                'user_id': user_id,
                'username': user_data['username'],
                'email': user_data['email'],
                'role': UserRole(user_data['role']),
                'password_hash': self._hash_password(user_data['password']),
                'status': UserStatus.ACTIVE,
                'created_at': datetime.now(),
                'permissions': self._get_default_permissions(UserRole(user_data['role']))
            }
            
            self.users[user_id] = new_user
            
            logger.info(f"User created: {user_data['username']}")
            
            return {
                'status': 'success',
                'user_id': user_id,
                'username': new_user['username'],
                'role': new_user['role'].value
            }
            
        except Exception as e:
            logger.error(f"User creation failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _get_default_permissions(self, role: UserRole) -> List[str]:
        """Get default permissions for role."""
        permission_map = {
            UserRole.ADMIN: ['*'],
            UserRole.OPERATOR: ['read_data', 'control_systems', 'view_reports', 'manage_alerts'],
            UserRole.VIEWER: ['read_data', 'view_reports'],
            UserRole.MAINTENANCE: ['read_data', 'view_reports', 'manage_maintenance', 'equipment_control'],
            UserRole.ANALYST: ['read_data', 'view_reports', 'advanced_analytics', 'export_data']
        }
        
        return permission_map.get(role, ['read_data'])
    
    async def check_permission(self, session_token: str, permission: str) -> bool:
        """Check if user has specific permission."""
        session_result = await self.validate_session(session_token)
        
        if session_result['status'] != 'success':
            return False
        
        permissions = session_result['permissions']
        
        # Admin has all permissions
        if '*' in permissions:
            return True
        
        return permission in permissions
    
    async def get_user_stats(self) -> Dict[str, Any]:
        """Get user management statistics."""
        total_users = len(self.users)
        active_sessions = len(self.sessions)
        
        role_counts = {}
        status_counts = {}
        
        for user in self.users.values():
            role = user['role'].value
            status = user['status'].value
            
            role_counts[role] = role_counts.get(role, 0) + 1
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'total_users': total_users,
            'active_sessions': active_sessions,
            'role_distribution': role_counts,
            'status_distribution': status_counts,
            'failed_attempts': len(self.login_attempts)
        }
