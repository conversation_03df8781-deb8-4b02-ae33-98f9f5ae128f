
    // Add dashboard navigation links
    function addDashboardLinks() {
        const navHeader = document.querySelector('.nav-header');
        if (navHeader) {
            const dashboardLink = document.createElement('div');
            dashboardLink.style.cssText = `
                margin-top: 15px;
                padding: 10px;
                background: rgba(59, 130, 246, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(59, 130, 246, 0.2);
            `;
            dashboardLink.innerHTML = `
                <a href="/dashboards" style="color: #3b82f6; text-decoration: none; font-size: 12px; font-weight: 600;">
                    <i class="fas fa-chart-pie"></i> View Individual Dashboards
                </a>
            `;
            navHeader.appendChild(dashboardLink);
        }
    }
    
    // Add to existing initialization
    document.addEventListener('DOMContentLoaded', function() {
        addDashboardLinks();
    });
    