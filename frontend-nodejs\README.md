# Water Management Frontend (Node.js)

A modern Node.js-based frontend for the Water Management Decarbonisation System, featuring real-time dashboards, AI integration, and advanced visualization capabilities.

## 🚀 Features

- **Real-time Dashboard**: Live data updates via WebSocket connections
- **AI Integration**: Chat interface with Gemini AI for system insights
- **Advanced Visualizations**: Charts, maps, and 3D models
- **Responsive Design**: Mobile-first, adaptive UI
- **Modular Architecture**: Component-based structure
- **Performance Optimized**: Compression, caching, and lazy loading
- **Security First**: Helmet.js, CORS, and input validation

## 📋 Prerequisites

- Node.js >= 16.0.0
- npm >= 8.0.0
- Backend API running on port 8001 (configurable)

## 🛠️ Installation

1. **Clone and navigate to the frontend directory:**
   ```bash
   cd frontend-nodejs
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

5. **Access the application:**
   ```
   http://localhost:3000
   ```

## 📦 Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run watch` - Watch mode for development
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## 🏗️ Architecture

```
frontend-nodejs/
├── src/
│   ├── routes/           # Express routes
│   │   ├── api.js        # API proxy routes
│   │   └── dashboard.js  # Dashboard page routes
│   ├── scripts/          # Client-side JavaScript
│   │   ├── main.js       # Main application entry
│   │   ├── utils/        # Utility modules
│   │   ├── components/   # UI components
│   │   └── pages/        # Page-specific code
│   ├── templates/        # HTML templates
│   ├── styles/           # CSS stylesheets
│   ├── assets/           # Static assets
│   └── websocket/        # WebSocket handlers
├── public/               # Public static files
├── server.js             # Express server
└── package.json          # Dependencies and scripts
```

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example`):

- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (development/production)
- `BACKEND_API_URL` - Backend API URL
- `WEBSOCKET_ENABLED` - Enable WebSocket features

### API Integration

The frontend acts as a proxy to the backend API:

- All `/api/*` routes are proxied to the backend
- Real-time data via WebSocket connections
- Automatic retry and error handling
- Request/response interceptors

## 📊 Dashboard Pages

1. **Overview** - System status and key metrics
2. **Water Quality** - Water quality monitoring
3. **Treatment Systems** - Treatment plant management
4. **Energy Grid** - Energy consumption and efficiency
5. **AI Agents** - AI agent status and control
6. **ML Optimization** - Machine learning models
7. **Workflow Orchestration** - Process automation
8. **Knowledge Graphs** - Semantic data visualization
9. **LLM Integration** - AI chat interface
10. **Climate Impact** - Environmental analysis
11. **Sensors** - IoT sensor network
12. **Analytics** - Advanced data analytics
13. **Reports** - Report generation and scheduling
14. **System Management** - Infrastructure control
15. **Advanced AI** - Advanced AI features
16. **Digital Twin** - 3D system visualization
17. **Blockchain** - Distributed ledger
18. **Predictive Maintenance** - Equipment health monitoring

## 🔌 WebSocket Events

### Client → Server
- `subscribe` - Subscribe to data stream
- `unsubscribe` - Unsubscribe from data stream
- `ai-chat` - Send AI chat message
- `system-command` - Execute system command
- `page-change` - Notify page navigation

### Server → Client
- `initial-data` - Initial dashboard data
- `data-update` - Real-time data updates
- `ai-chat-response` - AI chat response
- `system-command-result` - Command execution result

## 🎨 Styling

- CSS Grid and Flexbox layouts
- CSS Custom Properties for theming
- Responsive breakpoints
- Dark/light theme support
- Component-based styles

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Docker Deployment

```bash
# Build image
docker build -t water-management-frontend .

# Run container
docker run -p 3000:3000 water-management-frontend
```

### Environment-specific Configurations

- **Development**: Hot reload, detailed logging
- **Production**: Compression, caching, minification

## 🔒 Security

- Helmet.js for security headers
- CORS configuration
- Input validation and sanitization
- Rate limiting
- Session management

## 📈 Performance

- Gzip compression
- Static file caching
- Lazy loading
- Code splitting
- Image optimization

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Change port in .env file
   PORT=3001
   ```

2. **Backend connection failed**
   ```bash
   # Check backend URL in .env
   BACKEND_API_URL=http://localhost:8001
   ```

3. **WebSocket connection issues**
   ```bash
   # Disable WebSocket in .env
   WEBSOCKET_ENABLED=false
   ```

### Logs

- Application logs: `logs/frontend.log`
- Error logs: Console output
- Access logs: Morgan middleware

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Review the logs
- Contact the development team
