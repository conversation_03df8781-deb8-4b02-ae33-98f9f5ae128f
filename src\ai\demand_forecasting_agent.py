"""Demand Forecasting Agent for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ForecastHorizon(Enum):
    """Forecast time horizons."""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    SEASONAL = "seasonal"
    ANNUAL = "annual"


class DemandCategory(Enum):
    """Water demand categories."""
    RESIDENTIAL = "residential"
    COMMERCIAL = "commercial"
    INDUSTRIAL = "industrial"
    AGRICULTURAL = "agricultural"
    MUNICIPAL = "municipal"
    EMERGENCY = "emergency"


@dataclass
class DemandForecast:
    """Water demand forecast."""
    forecast_id: str
    horizon: ForecastHorizon
    category: DemandCategory
    predicted_demand: float  # m³/h
    confidence_interval: Tuple[float, float]
    confidence_level: float
    forecast_date: datetime
    valid_from: datetime
    valid_until: datetime
    factors_considered: List[str]
    model_used: str


@dataclass
class DemandPattern:
    """Historical demand pattern."""
    pattern_id: str
    category: DemandCategory
    time_period: str
    average_demand: float
    peak_demand: float
    minimum_demand: float
    variability: float
    seasonal_factors: Dict[str, float]
    trend_direction: str


class DemandForecastingAgent:
    """AI agent for water demand forecasting and planning."""
    
    def __init__(self):
        self.historical_data: List[Dict[str, Any]] = []
        self.demand_patterns: Dict[str, DemandPattern] = {}
        self.forecast_models: Dict[str, Dict[str, Any]] = {}
        self.active_forecasts: Dict[str, DemandForecast] = {}
        
        # Initialize forecasting models
        self._initialize_forecast_models()
        self._initialize_demand_patterns()
    
    def _initialize_forecast_models(self):
        """Initialize demand forecasting models."""
        self.forecast_models = {
            'time_series': {
                'type': 'ARIMA',
                'accuracy': 0.85,
                'best_for': ['daily', 'weekly', 'monthly'],
                'factors': ['historical_demand', 'seasonal_patterns', 'trends']
            },
            'regression': {
                'type': 'Multiple Linear Regression',
                'accuracy': 0.80,
                'best_for': ['hourly', 'daily'],
                'factors': ['weather', 'population', 'economic_indicators', 'events']
            },
            'machine_learning': {
                'type': 'Random Forest',
                'accuracy': 0.88,
                'best_for': ['hourly', 'daily', 'weekly'],
                'factors': ['weather', 'demographics', 'economic', 'seasonal', 'events']
            },
            'neural_network': {
                'type': 'LSTM',
                'accuracy': 0.90,
                'best_for': ['hourly', 'daily'],
                'factors': ['complex_patterns', 'non_linear_relationships', 'multiple_variables']
            }
        }
    
    def _initialize_demand_patterns(self):
        """Initialize typical demand patterns."""
        # Residential patterns
        self.demand_patterns['residential_daily'] = DemandPattern(
            pattern_id='res_daily',
            category=DemandCategory.RESIDENTIAL,
            time_period='daily',
            average_demand=800.0,  # m³/h
            peak_demand=1200.0,
            minimum_demand=400.0,
            variability=0.25,
            seasonal_factors={
                'spring': 1.0,
                'summer': 1.3,
                'autumn': 0.9,
                'winter': 0.8
            },
            trend_direction='stable'
        )
        
        # Commercial patterns
        self.demand_patterns['commercial_daily'] = DemandPattern(
            pattern_id='com_daily',
            category=DemandCategory.COMMERCIAL,
            time_period='daily',
            average_demand=300.0,
            peak_demand=450.0,
            minimum_demand=150.0,
            variability=0.20,
            seasonal_factors={
                'spring': 1.0,
                'summer': 1.1,
                'autumn': 1.0,
                'winter': 0.9
            },
            trend_direction='growing'
        )
        
        # Industrial patterns
        self.demand_patterns['industrial_daily'] = DemandPattern(
            pattern_id='ind_daily',
            category=DemandCategory.INDUSTRIAL,
            time_period='daily',
            average_demand=500.0,
            peak_demand=600.0,
            minimum_demand=400.0,
            variability=0.15,
            seasonal_factors={
                'spring': 1.0,
                'summer': 1.0,
                'autumn': 1.0,
                'winter': 1.0
            },
            trend_direction='stable'
        )
    
    @log_async_function_call
    async def generate_demand_forecast(self, forecast_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate water demand forecast."""
        try:
            horizon = ForecastHorizon(forecast_config['horizon'])
            category = DemandCategory(forecast_config.get('category', 'residential'))
            forecast_periods = forecast_config.get('periods', 24)  # Default 24 periods
            
            # Select appropriate model
            model_name = await self._select_forecast_model(horizon, category)
            model = self.forecast_models[model_name]
            
            # Generate base forecast
            base_forecast = await self._generate_base_forecast(
                horizon, category, forecast_periods, model
            )
            
            # Apply external factors
            adjusted_forecast = await self._apply_external_factors(
                base_forecast, forecast_config
            )
            
            # Calculate confidence intervals
            confidence_intervals = await self._calculate_confidence_intervals(
                adjusted_forecast, model['accuracy']
            )
            
            # Create forecast objects
            forecasts = []
            start_time = datetime.now()
            
            for i, demand in enumerate(adjusted_forecast):
                if horizon == ForecastHorizon.HOURLY:
                    valid_from = start_time + timedelta(hours=i)
                    valid_until = start_time + timedelta(hours=i+1)
                elif horizon == ForecastHorizon.DAILY:
                    valid_from = start_time + timedelta(days=i)
                    valid_until = start_time + timedelta(days=i+1)
                elif horizon == ForecastHorizon.WEEKLY:
                    valid_from = start_time + timedelta(weeks=i)
                    valid_until = start_time + timedelta(weeks=i+1)
                else:
                    valid_from = start_time + timedelta(days=i*30)
                    valid_until = start_time + timedelta(days=(i+1)*30)
                
                forecast = DemandForecast(
                    forecast_id=f"forecast_{horizon.value}_{category.value}_{i}",
                    horizon=horizon,
                    category=category,
                    predicted_demand=demand,
                    confidence_interval=confidence_intervals[i],
                    confidence_level=model['accuracy'],
                    forecast_date=datetime.now(),
                    valid_from=valid_from,
                    valid_until=valid_until,
                    factors_considered=model['factors'],
                    model_used=model_name
                )
                
                forecasts.append(forecast)
                self.active_forecasts[forecast.forecast_id] = forecast
            
            # Generate forecast summary
            forecast_summary = await self._generate_forecast_summary(forecasts)
            
            return {
                'status': 'success',
                'forecast_config': {
                    'horizon': horizon.value,
                    'category': category.value,
                    'periods': forecast_periods,
                    'model_used': model_name
                },
                'forecast_summary': forecast_summary,
                'forecasts': [
                    {
                        'forecast_id': f.forecast_id,
                        'predicted_demand': f.predicted_demand,
                        'confidence_interval': f.confidence_interval,
                        'valid_from': f.valid_from.isoformat(),
                        'valid_until': f.valid_until.isoformat()
                    }
                    for f in forecasts
                ],
                'model_performance': {
                    'model_name': model_name,
                    'accuracy': model['accuracy'],
                    'factors_considered': model['factors']
                },
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Demand forecast generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _select_forecast_model(self, horizon: ForecastHorizon, 
                                   category: DemandCategory) -> str:
        """Select best forecasting model for given horizon and category."""
        # Model selection logic based on horizon and requirements
        if horizon == ForecastHorizon.HOURLY:
            # For hourly forecasts, use neural networks for complex patterns
            return 'neural_network'
        elif horizon in [ForecastHorizon.DAILY, ForecastHorizon.WEEKLY]:
            # For daily/weekly, use machine learning for good balance
            return 'machine_learning'
        elif horizon in [ForecastHorizon.MONTHLY, ForecastHorizon.SEASONAL]:
            # For longer horizons, use time series analysis
            return 'time_series'
        else:
            # Default to regression for other cases
            return 'regression'
    
    async def _generate_base_forecast(self, horizon: ForecastHorizon,
                                    category: DemandCategory,
                                    periods: int,
                                    model: Dict[str, Any]) -> List[float]:
        """Generate base demand forecast."""
        # Get relevant demand pattern
        pattern_key = f"{category.value}_daily"
        pattern = self.demand_patterns.get(pattern_key)
        
        if not pattern:
            # Use default pattern if specific pattern not found
            base_demand = 1000.0  # Default base demand
            variability = 0.2
        else:
            base_demand = pattern.average_demand
            variability = pattern.variability
        
        forecasts = []
        
        for i in range(periods):
            # Apply time-based patterns
            if horizon == ForecastHorizon.HOURLY:
                # Hourly patterns (peak during day, low at night)
                hour = i % 24
                if 6 <= hour <= 22:  # Daytime
                    time_factor = 1.0 + 0.3 * np.sin((hour - 6) * np.pi / 16)
                else:  # Nighttime
                    time_factor = 0.6
            elif horizon == ForecastHorizon.DAILY:
                # Daily patterns (weekday vs weekend)
                day = i % 7
                if day < 5:  # Weekday
                    time_factor = 1.0
                else:  # Weekend
                    time_factor = 0.8 if category == DemandCategory.COMMERCIAL else 1.1
            else:
                time_factor = 1.0
            
            # Apply seasonal factors if available
            current_season = self._get_current_season()
            seasonal_factor = 1.0
            if pattern and current_season in pattern.seasonal_factors:
                seasonal_factor = pattern.seasonal_factors[current_season]
            
            # Apply trend
            trend_factor = 1.0
            if pattern:
                if pattern.trend_direction == 'growing':
                    trend_factor = 1.0 + (i * 0.001)  # Small growth trend
                elif pattern.trend_direction == 'declining':
                    trend_factor = 1.0 - (i * 0.001)  # Small decline trend
            
            # Add random variation
            random_factor = 1.0 + np.random.normal(0, variability * 0.1)
            
            # Calculate final forecast
            forecast_demand = (base_demand * time_factor * seasonal_factor * 
                             trend_factor * random_factor)
            
            forecasts.append(max(0, forecast_demand))  # Ensure non-negative
        
        return forecasts
    
    async def _apply_external_factors(self, base_forecast: List[float],
                                    config: Dict[str, Any]) -> List[float]:
        """Apply external factors to base forecast."""
        adjusted_forecast = base_forecast.copy()
        
        # Weather factors
        weather_impact = config.get('weather_impact', 1.0)
        
        # Economic factors
        economic_impact = config.get('economic_impact', 1.0)
        
        # Special events
        events = config.get('special_events', [])
        
        # Population changes
        population_growth = config.get('population_growth', 0.0)
        
        for i in range(len(adjusted_forecast)):
            # Apply weather impact
            adjusted_forecast[i] *= weather_impact
            
            # Apply economic impact
            adjusted_forecast[i] *= economic_impact
            
            # Apply population growth
            adjusted_forecast[i] *= (1.0 + population_growth)
            
            # Apply event impacts
            for event in events:
                if event.get('period') == i:
                    adjusted_forecast[i] *= event.get('impact_factor', 1.0)
        
        return adjusted_forecast
    
    async def _calculate_confidence_intervals(self, forecasts: List[float],
                                            model_accuracy: float) -> List[Tuple[float, float]]:
        """Calculate confidence intervals for forecasts."""
        confidence_intervals = []
        
        # Error margin based on model accuracy
        error_margin = (1.0 - model_accuracy) * 0.5
        
        for forecast in forecasts:
            margin = forecast * error_margin
            lower_bound = max(0, forecast - margin)
            upper_bound = forecast + margin
            confidence_intervals.append((lower_bound, upper_bound))
        
        return confidence_intervals
    
    def _get_current_season(self) -> str:
        """Get current season."""
        month = datetime.now().month
        if month in [12, 1, 2]:
            return 'winter'
        elif month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        else:
            return 'autumn'
    
    async def _generate_forecast_summary(self, forecasts: List[DemandForecast]) -> Dict[str, Any]:
        """Generate summary of forecast results."""
        if not forecasts:
            return {}
        
        demands = [f.predicted_demand for f in forecasts]
        
        return {
            'total_forecasts': len(forecasts),
            'average_demand': np.mean(demands),
            'peak_demand': max(demands),
            'minimum_demand': min(demands),
            'demand_variability': np.std(demands) / np.mean(demands) if np.mean(demands) > 0 else 0,
            'forecast_horizon': forecasts[0].horizon.value,
            'category': forecasts[0].category.value,
            'overall_confidence': np.mean([f.confidence_level for f in forecasts]),
            'forecast_period': {
                'start': forecasts[0].valid_from.isoformat(),
                'end': forecasts[-1].valid_until.isoformat()
            }
        }
    
    @log_async_function_call
    async def analyze_demand_patterns(self, historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze historical demand patterns."""
        try:
            if not historical_data:
                return {'status': 'error', 'error': 'No historical data provided'}
            
            # Extract demand values and timestamps
            demands = []
            timestamps = []
            
            for record in historical_data:
                if 'demand' in record and 'timestamp' in record:
                    demands.append(record['demand'])
                    timestamps.append(datetime.fromisoformat(record['timestamp']))
            
            if not demands:
                return {'status': 'error', 'error': 'No valid demand data found'}
            
            # Calculate basic statistics
            basic_stats = {
                'total_records': len(demands),
                'average_demand': np.mean(demands),
                'peak_demand': max(demands),
                'minimum_demand': min(demands),
                'standard_deviation': np.std(demands),
                'coefficient_of_variation': np.std(demands) / np.mean(demands) if np.mean(demands) > 0 else 0
            }
            
            # Analyze temporal patterns
            temporal_patterns = await self._analyze_temporal_patterns(demands, timestamps)
            
            # Detect trends
            trend_analysis = await self._analyze_trends(demands, timestamps)
            
            # Identify anomalies
            anomalies = await self._detect_demand_anomalies(demands, timestamps)
            
            # Generate insights
            insights = await self._generate_demand_insights(
                basic_stats, temporal_patterns, trend_analysis, anomalies
            )
            
            return {
                'status': 'success',
                'analysis_period': {
                    'start': min(timestamps).isoformat(),
                    'end': max(timestamps).isoformat(),
                    'duration_days': (max(timestamps) - min(timestamps)).days
                },
                'basic_statistics': basic_stats,
                'temporal_patterns': temporal_patterns,
                'trend_analysis': trend_analysis,
                'anomalies': anomalies,
                'insights': insights,
                'analyzed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Demand pattern analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _analyze_temporal_patterns(self, demands: List[float], 
                                       timestamps: List[datetime]) -> Dict[str, Any]:
        """Analyze temporal demand patterns."""
        # Hourly patterns
        hourly_demands = {}
        for i, ts in enumerate(timestamps):
            hour = ts.hour
            if hour not in hourly_demands:
                hourly_demands[hour] = []
            hourly_demands[hour].append(demands[i])
        
        hourly_averages = {hour: np.mean(values) for hour, values in hourly_demands.items()}
        
        # Daily patterns (weekday vs weekend)
        weekday_demands = []
        weekend_demands = []
        
        for i, ts in enumerate(timestamps):
            if ts.weekday() < 5:  # Monday = 0, Sunday = 6
                weekday_demands.append(demands[i])
            else:
                weekend_demands.append(demands[i])
        
        # Monthly patterns
        monthly_demands = {}
        for i, ts in enumerate(timestamps):
            month = ts.month
            if month not in monthly_demands:
                monthly_demands[month] = []
            monthly_demands[month].append(demands[i])
        
        monthly_averages = {month: np.mean(values) for month, values in monthly_demands.items()}
        
        return {
            'hourly_patterns': hourly_averages,
            'weekday_vs_weekend': {
                'weekday_average': np.mean(weekday_demands) if weekday_demands else 0,
                'weekend_average': np.mean(weekend_demands) if weekend_demands else 0,
                'weekend_factor': (np.mean(weekend_demands) / np.mean(weekday_demands)) if weekday_demands and weekend_demands else 1.0
            },
            'monthly_patterns': monthly_averages,
            'peak_hour': max(hourly_averages, key=hourly_averages.get) if hourly_averages else 0,
            'low_hour': min(hourly_averages, key=hourly_averages.get) if hourly_averages else 0
        }
    
    async def _analyze_trends(self, demands: List[float], 
                            timestamps: List[datetime]) -> Dict[str, Any]:
        """Analyze demand trends."""
        if len(demands) < 2:
            return {'trend_direction': 'insufficient_data'}
        
        # Simple linear trend analysis
        x = np.arange(len(demands))
        coefficients = np.polyfit(x, demands, 1)
        slope = coefficients[0]
        
        # Determine trend direction
        if abs(slope) < 0.01:
            trend_direction = 'stable'
        elif slope > 0:
            trend_direction = 'increasing'
        else:
            trend_direction = 'decreasing'
        
        # Calculate trend strength
        correlation = np.corrcoef(x, demands)[0, 1]
        trend_strength = abs(correlation)
        
        return {
            'trend_direction': trend_direction,
            'trend_slope': slope,
            'trend_strength': trend_strength,
            'correlation_coefficient': correlation,
            'trend_significance': 'strong' if trend_strength > 0.7 else 'moderate' if trend_strength > 0.4 else 'weak'
        }
    
    async def _detect_demand_anomalies(self, demands: List[float], 
                                     timestamps: List[datetime]) -> List[Dict[str, Any]]:
        """Detect anomalies in demand data."""
        anomalies = []
        
        if len(demands) < 10:
            return anomalies
        
        # Statistical anomaly detection using z-score
        mean_demand = np.mean(demands)
        std_demand = np.std(demands)
        threshold = 3.0  # 3 standard deviations
        
        for i, (demand, timestamp) in enumerate(zip(demands, timestamps)):
            z_score = abs(demand - mean_demand) / std_demand if std_demand > 0 else 0
            
            if z_score > threshold:
                anomalies.append({
                    'index': i,
                    'timestamp': timestamp.isoformat(),
                    'demand_value': demand,
                    'z_score': z_score,
                    'deviation_from_mean': demand - mean_demand,
                    'anomaly_type': 'high' if demand > mean_demand else 'low'
                })
        
        return anomalies
    
    async def _generate_demand_insights(self, basic_stats: Dict[str, Any],
                                      temporal_patterns: Dict[str, Any],
                                      trend_analysis: Dict[str, Any],
                                      anomalies: List[Dict[str, Any]]) -> List[str]:
        """Generate insights from demand analysis."""
        insights = []
        
        # Basic statistics insights
        cv = basic_stats['coefficient_of_variation']
        if cv > 0.3:
            insights.append("High demand variability detected - consider demand management strategies")
        elif cv < 0.1:
            insights.append("Very stable demand pattern - good for capacity planning")
        
        # Temporal pattern insights
        if 'weekday_vs_weekend' in temporal_patterns:
            weekend_factor = temporal_patterns['weekday_vs_weekend']['weekend_factor']
            if weekend_factor > 1.2:
                insights.append("Weekend demand significantly higher than weekdays")
            elif weekend_factor < 0.8:
                insights.append("Weekend demand significantly lower than weekdays")
        
        # Trend insights
        if trend_analysis['trend_direction'] == 'increasing':
            insights.append(f"Increasing demand trend detected - plan for capacity expansion")
        elif trend_analysis['trend_direction'] == 'decreasing':
            insights.append("Decreasing demand trend - potential for efficiency improvements")
        
        # Anomaly insights
        if len(anomalies) > len(basic_stats) * 0.05:  # More than 5% anomalies
            insights.append("High number of demand anomalies - investigate data quality or external factors")
        
        return insights


# Convenience functions
async def forecast_water_demand(horizon: str, category: str = 'residential', 
                              periods: int = 24) -> Dict[str, Any]:
    """Generate water demand forecast."""
    agent = DemandForecastingAgent()
    
    config = {
        'horizon': horizon,
        'category': category,
        'periods': periods
    }
    
    return await agent.generate_demand_forecast(config)


async def analyze_historical_demand(historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze historical demand patterns."""
    agent = DemandForecastingAgent()
    return await agent.analyze_demand_patterns(historical_data)
