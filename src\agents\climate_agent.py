"""
Climate Analysis Agent - Specialized AI agent for climate data analysis and predictions

This agent uses Google Gemini and other LLMs to analyze climate data,
identify patterns, and provide insights for water treatment optimization.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

import google.generativeai as genai
from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain.tools import Tool
from langchain.schema import SystemMessage, HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI

from src.utils.config import get_settings
from src.utils.logging_config import log_async_function_call, StructuredLogger

logger = StructuredLogger(__name__)


class ClimateAnalysisAgent:
    """
    Specialized AI agent for climate data analysis and environmental insights.
    
    This agent can:
    - Analyze historical climate trends
    - Predict future climate conditions
    - Identify extreme weather patterns
    - Assess climate impact on water treatment systems
    - Provide adaptation recommendations
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Initialize Gemini client
        if self.settings.GOOGLE_API_KEY:
            genai.configure(api_key=self.settings.GOOGLE_API_KEY)
            self.gemini_model = genai.GenerativeModel('gemini-pro')
            self.langchain_model = ChatGoogleGenerativeAI(
                model="gemini-pro",
                google_api_key=self.settings.GOOGLE_API_KEY,
                temperature=0.3
            )
        else:
            logger.warning("Google API key not configured - Climate agent will have limited functionality")
            self.gemini_model = None
            self.langchain_model = None
        
        # Agent tools
        self.tools = []
        self.agent_executor = None
        
        # Climate analysis capabilities
        self.analysis_capabilities = [
            "temperature_trend_analysis",
            "precipitation_pattern_recognition",
            "extreme_weather_detection",
            "seasonal_variation_modeling",
            "climate_change_impact_assessment",
            "water_availability_forecasting",
            "drought_risk_analysis",
            "flood_risk_assessment"
        ]
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the climate analysis agent."""
        try:
            logger.info("Initializing Climate Analysis Agent...")
            
            # Setup tools
            await self._setup_tools()
            
            # Initialize agent executor if LangChain model is available
            if self.langchain_model:
                await self._setup_agent_executor()
            
            self.is_initialized = True
            logger.info("Climate Analysis Agent initialized successfully", 
                       capabilities=len(self.analysis_capabilities))
            
        except Exception as e:
            logger.error("Failed to initialize Climate Analysis Agent", error=str(e))
            raise
    
    async def _setup_tools(self):
        """Setup tools for the climate analysis agent."""
        self.tools = [
            Tool(
                name="analyze_temperature_trends",
                description="Analyze temperature trends and patterns in climate data",
                func=self._analyze_temperature_trends
            ),
            Tool(
                name="detect_extreme_weather",
                description="Detect and analyze extreme weather events",
                func=self._detect_extreme_weather
            ),
            Tool(
                name="assess_climate_impact",
                description="Assess climate impact on water treatment systems",
                func=self._assess_climate_impact
            ),
            Tool(
                name="forecast_water_availability",
                description="Forecast water availability based on climate conditions",
                func=self._forecast_water_availability
            ),
            Tool(
                name="analyze_precipitation_patterns",
                description="Analyze precipitation patterns and seasonal variations",
                func=self._analyze_precipitation_patterns
            )
        ]
        
        logger.info("Climate analysis tools configured", tool_count=len(self.tools))
    
    async def _setup_agent_executor(self):
        """Setup the LangChain agent executor."""
        try:
            # System message for the climate agent
            system_message = SystemMessage(content="""
            You are a specialized Climate Analysis Agent for a water management decarbonisation system.
            
            Your role is to:
            1. Analyze climate data and identify patterns, trends, and anomalies
            2. Assess the impact of climate conditions on water treatment systems
            3. Provide recommendations for climate adaptation and resilience
            4. Predict future climate conditions and their implications
            5. Support decision-making for sustainable water management
            
            You have access to various climate analysis tools. Use them appropriately based on the query.
            Always provide data-driven insights and actionable recommendations.
            Consider both short-term operational impacts and long-term strategic planning.
            """)
            
            # Create agent (simplified version for now)
            # In a full implementation, you would use create_openai_functions_agent
            # self.agent_executor = AgentExecutor.from_agent_and_tools(
            #     agent=agent,
            #     tools=self.tools,
            #     verbose=True
            # )
            
            logger.info("Agent executor configured successfully")
            
        except Exception as e:
            logger.error("Failed to setup agent executor", error=str(e))
            raise
    
    @log_async_function_call
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute climate analysis based on input data.
        
        Args:
            input_data: Dictionary containing analysis request and data
            
        Returns:
            Dictionary containing analysis results and recommendations
        """
        try:
            analysis_type = input_data.get('analysis_type', 'general_climate_analysis')
            climate_data = input_data.get('climate_data', {})
            location = input_data.get('location', 'Unknown')
            time_range = input_data.get('time_range', '30_days')
            
            logger.info("Executing climate analysis", 
                       analysis_type=analysis_type, 
                       location=location,
                       time_range=time_range)
            
            # Route to appropriate analysis method
            if analysis_type == 'temperature_trend_analysis':
                result = await self._analyze_temperature_trends(climate_data)
            elif analysis_type == 'extreme_weather_detection':
                result = await self._detect_extreme_weather(climate_data)
            elif analysis_type == 'climate_impact_assessment':
                result = await self._assess_climate_impact(climate_data)
            elif analysis_type == 'water_availability_forecast':
                result = await self._forecast_water_availability(climate_data)
            elif analysis_type == 'precipitation_analysis':
                result = await self._analyze_precipitation_patterns(climate_data)
            else:
                result = await self._general_climate_analysis(input_data)
            
            # Enhance result with Gemini insights if available
            if self.gemini_model:
                enhanced_result = await self._enhance_with_gemini_insights(result, input_data)
                result.update(enhanced_result)
            
            logger.info("Climate analysis completed successfully", 
                       analysis_type=analysis_type)
            
            return {
                'success': True,
                'analysis_type': analysis_type,
                'location': location,
                'timestamp': datetime.now().isoformat(),
                'results': result,
                'agent': 'climate_analysis'
            }
            
        except Exception as e:
            logger.error("Climate analysis execution failed", 
                        analysis_type=analysis_type, 
                        error=str(e))
            
            return {
                'success': False,
                'error': str(e),
                'analysis_type': analysis_type,
                'timestamp': datetime.now().isoformat(),
                'agent': 'climate_analysis'
            }
    
    async def _analyze_temperature_trends(self, climate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze temperature trends in the climate data."""
        try:
            # Extract temperature data
            temperatures = climate_data.get('temperatures', [])
            timestamps = climate_data.get('timestamps', [])
            
            if not temperatures:
                return {'error': 'No temperature data provided'}
            
            # Convert to numpy arrays for analysis
            temp_array = np.array(temperatures)
            
            # Calculate statistics
            stats = {
                'mean_temperature': float(np.mean(temp_array)),
                'min_temperature': float(np.min(temp_array)),
                'max_temperature': float(np.max(temp_array)),
                'temperature_range': float(np.max(temp_array) - np.min(temp_array)),
                'std_deviation': float(np.std(temp_array)),
                'trend_slope': self._calculate_trend_slope(temp_array)
            }
            
            # Identify patterns
            patterns = {
                'increasing_trend': stats['trend_slope'] > 0.1,
                'decreasing_trend': stats['trend_slope'] < -0.1,
                'high_variability': stats['std_deviation'] > 5.0,
                'extreme_temperatures': len([t for t in temperatures if t > 35 or t < -10]) > 0
            }
            
            # Generate insights
            insights = []
            if patterns['increasing_trend']:
                insights.append("Temperature shows an increasing trend, which may increase cooling demands for water treatment systems.")
            if patterns['high_variability']:
                insights.append("High temperature variability detected, requiring adaptive system controls.")
            if patterns['extreme_temperatures']:
                insights.append("Extreme temperatures detected, system resilience measures recommended.")
            
            return {
                'statistics': stats,
                'patterns': patterns,
                'insights': insights,
                'data_points': len(temperatures)
            }
            
        except Exception as e:
            logger.error("Temperature trend analysis failed", error=str(e))
            return {'error': f'Temperature analysis failed: {str(e)}'}
    
    async def _detect_extreme_weather(self, climate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect extreme weather events in the climate data."""
        try:
            extreme_events = []
            
            # Temperature extremes
            temperatures = climate_data.get('temperatures', [])
            if temperatures:
                temp_array = np.array(temperatures)
                mean_temp = np.mean(temp_array)
                std_temp = np.std(temp_array)
                
                # Detect temperature extremes (beyond 2 standard deviations)
                for i, temp in enumerate(temperatures):
                    if abs(temp - mean_temp) > 2 * std_temp:
                        extreme_events.append({
                            'type': 'temperature_extreme',
                            'value': temp,
                            'index': i,
                            'severity': 'high' if abs(temp - mean_temp) > 3 * std_temp else 'moderate'
                        })
            
            # Precipitation extremes
            precipitation = climate_data.get('precipitation', [])
            if precipitation:
                precip_array = np.array(precipitation)
                # Detect heavy precipitation events (>95th percentile)
                threshold = np.percentile(precip_array, 95)
                
                for i, precip in enumerate(precipitation):
                    if precip > threshold and precip > 10:  # >10mm and above 95th percentile
                        extreme_events.append({
                            'type': 'heavy_precipitation',
                            'value': precip,
                            'index': i,
                            'severity': 'high' if precip > np.percentile(precip_array, 99) else 'moderate'
                        })
            
            # Wind speed extremes
            wind_speeds = climate_data.get('wind_speeds', [])
            if wind_speeds:
                wind_array = np.array(wind_speeds)
                # Detect high wind events (>90th percentile)
                threshold = np.percentile(wind_array, 90)
                
                for i, wind in enumerate(wind_speeds):
                    if wind > threshold and wind > 15:  # >15 m/s and above 90th percentile
                        extreme_events.append({
                            'type': 'high_wind',
                            'value': wind,
                            'index': i,
                            'severity': 'high' if wind > 25 else 'moderate'
                        })
            
            # Analyze impact on water treatment
            impact_assessment = self._assess_extreme_weather_impact(extreme_events)
            
            return {
                'extreme_events': extreme_events,
                'event_count': len(extreme_events),
                'impact_assessment': impact_assessment,
                'risk_level': self._calculate_risk_level(extreme_events)
            }
            
        except Exception as e:
            logger.error("Extreme weather detection failed", error=str(e))
            return {'error': f'Extreme weather detection failed: {str(e)}'}
    
    async def _assess_climate_impact(self, climate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess climate impact on water treatment systems."""
        try:
            impact_factors = {}
            
            # Temperature impact
            temperatures = climate_data.get('temperatures', [])
            if temperatures:
                avg_temp = np.mean(temperatures)
                impact_factors['temperature_impact'] = {
                    'cooling_demand': max(0, (avg_temp - 20) * 0.05),  # Increased cooling above 20°C
                    'chemical_reaction_rate': 1 + (avg_temp - 20) * 0.02,  # 2% increase per degree
                    'biological_activity': min(2.0, max(0.1, 1 + (avg_temp - 25) * 0.03))
                }
            
            # Precipitation impact
            precipitation = climate_data.get('precipitation', [])
            if precipitation:
                total_precip = sum(precipitation)
                impact_factors['precipitation_impact'] = {
                    'water_availability': min(2.0, max(0.1, total_precip / 100)),  # Normalized impact
                    'runoff_quality': max(0.5, 1 - (total_precip / 1000)),  # Quality decreases with heavy rain
                    'flood_risk': min(1.0, total_precip / 200)  # Risk increases with precipitation
                }
            
            # Overall system impact
            overall_impact = self._calculate_overall_impact(impact_factors)
            
            # Recommendations
            recommendations = self._generate_climate_recommendations(impact_factors)
            
            return {
                'impact_factors': impact_factors,
                'overall_impact': overall_impact,
                'recommendations': recommendations,
                'assessment_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("Climate impact assessment failed", error=str(e))
            return {'error': f'Climate impact assessment failed: {str(e)}'}
    
    async def _forecast_water_availability(self, climate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Forecast water availability based on climate conditions."""
        try:
            # Simple water availability model based on precipitation and temperature
            precipitation = climate_data.get('precipitation', [])
            temperatures = climate_data.get('temperatures', [])
            
            if not precipitation or not temperatures:
                return {'error': 'Insufficient data for water availability forecast'}
            
            # Calculate water balance components
            total_precip = sum(precipitation)
            avg_temp = np.mean(temperatures)
            
            # Estimate evapotranspiration (simplified Penman equation)
            evapotranspiration = self._estimate_evapotranspiration(avg_temp, len(precipitation))
            
            # Water availability forecast
            net_water_availability = total_precip - evapotranspiration
            
            forecast = {
                'total_precipitation': total_precip,
                'estimated_evapotranspiration': evapotranspiration,
                'net_water_availability': net_water_availability,
                'availability_status': self._classify_water_availability(net_water_availability),
                'confidence_level': 0.7  # Simplified confidence metric
            }
            
            return forecast
            
        except Exception as e:
            logger.error("Water availability forecast failed", error=str(e))
            return {'error': f'Water availability forecast failed: {str(e)}'}
    
    async def _analyze_precipitation_patterns(self, climate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze precipitation patterns and seasonal variations."""
        try:
            precipitation = climate_data.get('precipitation', [])
            timestamps = climate_data.get('timestamps', [])
            
            if not precipitation:
                return {'error': 'No precipitation data provided'}
            
            precip_array = np.array(precipitation)
            
            # Basic statistics
            stats = {
                'total_precipitation': float(np.sum(precip_array)),
                'mean_precipitation': float(np.mean(precip_array)),
                'max_precipitation': float(np.max(precip_array)),
                'dry_days': int(np.sum(precip_array == 0)),
                'wet_days': int(np.sum(precip_array > 0)),
                'heavy_rain_days': int(np.sum(precip_array > 10))  # >10mm considered heavy
            }
            
            # Pattern analysis
            patterns = {
                'drought_risk': stats['dry_days'] / len(precipitation) > 0.7,
                'flood_risk': stats['heavy_rain_days'] > len(precipitation) * 0.1,
                'irregular_pattern': np.std(precip_array) > np.mean(precip_array)
            }
            
            return {
                'statistics': stats,
                'patterns': patterns,
                'data_points': len(precipitation)
            }
            
        except Exception as e:
            logger.error("Precipitation pattern analysis failed", error=str(e))
            return {'error': f'Precipitation analysis failed: {str(e)}'}
    
    async def _general_climate_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform general climate analysis using available data."""
        try:
            climate_data = input_data.get('climate_data', {})
            
            # Combine all available analyses
            results = {}
            
            if climate_data.get('temperatures'):
                results['temperature_analysis'] = await self._analyze_temperature_trends(climate_data)
            
            if climate_data.get('precipitation'):
                results['precipitation_analysis'] = await self._analyze_precipitation_patterns(climate_data)
            
            # Always try to detect extreme weather if any data is available
            if any(climate_data.values()):
                results['extreme_weather'] = await self._detect_extreme_weather(climate_data)
            
            # Climate impact assessment
            if any(climate_data.values()):
                results['climate_impact'] = await self._assess_climate_impact(climate_data)
            
            return results
            
        except Exception as e:
            logger.error("General climate analysis failed", error=str(e))
            return {'error': f'General climate analysis failed: {str(e)}'}
    
    async def _enhance_with_gemini_insights(self, analysis_result: Dict[str, Any], input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance analysis results with Gemini AI insights."""
        try:
            if not self.gemini_model:
                return {}
            
            # Prepare prompt for Gemini
            prompt = f"""
            As a climate analysis expert, please provide additional insights based on this climate analysis:
            
            Analysis Results: {analysis_result}
            Location: {input_data.get('location', 'Unknown')}
            Time Range: {input_data.get('time_range', 'Unknown')}
            
            Please provide:
            1. Key insights and implications for water treatment systems
            2. Specific recommendations for system optimization
            3. Risk assessment and mitigation strategies
            4. Long-term adaptation suggestions
            
            Keep the response concise and actionable.
            """
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt
            )
            
            return {
                'gemini_insights': response.text,
                'enhanced_by': 'gemini-pro'
            }
            
        except Exception as e:
            logger.error("Gemini enhancement failed", error=str(e))
            return {'gemini_error': str(e)}
    
    # Helper methods
    def _calculate_trend_slope(self, data: np.ndarray) -> float:
        """Calculate the trend slope using linear regression."""
        if len(data) < 2:
            return 0.0
        
        x = np.arange(len(data))
        slope, _ = np.polyfit(x, data, 1)
        return float(slope)
    
    def _assess_extreme_weather_impact(self, extreme_events: List[Dict]) -> Dict[str, Any]:
        """Assess the impact of extreme weather events on water treatment."""
        high_severity_count = len([e for e in extreme_events if e.get('severity') == 'high'])
        total_events = len(extreme_events)
        
        return {
            'operational_disruption_risk': min(1.0, total_events / 10),
            'equipment_stress_level': min(1.0, high_severity_count / 5),
            'adaptation_urgency': 'high' if high_severity_count > 3 else 'moderate' if total_events > 5 else 'low'
        }
    
    def _calculate_risk_level(self, extreme_events: List[Dict]) -> str:
        """Calculate overall risk level based on extreme events."""
        if not extreme_events:
            return 'low'
        
        high_severity_count = len([e for e in extreme_events if e.get('severity') == 'high'])
        
        if high_severity_count > 5:
            return 'critical'
        elif high_severity_count > 2:
            return 'high'
        elif len(extreme_events) > 5:
            return 'moderate'
        else:
            return 'low'
    
    def _calculate_overall_impact(self, impact_factors: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall climate impact on the system."""
        # Simplified overall impact calculation
        impact_score = 0.0
        factor_count = 0
        
        for factor_type, factors in impact_factors.items():
            if isinstance(factors, dict):
                for key, value in factors.items():
                    if isinstance(value, (int, float)):
                        impact_score += abs(value - 1.0)  # Deviation from normal (1.0)
                        factor_count += 1
        
        avg_impact = impact_score / max(1, factor_count)
        
        return {
            'impact_score': avg_impact,
            'impact_level': 'high' if avg_impact > 0.5 else 'moderate' if avg_impact > 0.2 else 'low',
            'factors_analyzed': factor_count
        }
    
    def _generate_climate_recommendations(self, impact_factors: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on climate impact factors."""
        recommendations = []
        
        # Temperature-based recommendations
        temp_impact = impact_factors.get('temperature_impact', {})
        if temp_impact.get('cooling_demand', 0) > 0.2:
            recommendations.append("Consider upgrading cooling systems to handle increased thermal load")
        
        if temp_impact.get('biological_activity', 1) > 1.3:
            recommendations.append("Adjust biological treatment parameters for increased microbial activity")
        
        # Precipitation-based recommendations
        precip_impact = impact_factors.get('precipitation_impact', {})
        if precip_impact.get('flood_risk', 0) > 0.3:
            recommendations.append("Implement flood protection measures and emergency protocols")
        
        if precip_impact.get('water_availability', 1) < 0.5:
            recommendations.append("Develop water conservation strategies and alternative sources")
        
        return recommendations
    
    def _estimate_evapotranspiration(self, avg_temp: float, days: int) -> float:
        """Estimate evapotranspiration using simplified method."""
        # Simplified Penman equation approximation
        # ET = 0.0023 * (T_mean + 17.8) * sqrt(T_max - T_min) * Ra
        # Using simplified version: ET ≈ 0.1 * T_mean * days
        return max(0, 0.1 * avg_temp * days)
    
    def _classify_water_availability(self, net_availability: float) -> str:
        """Classify water availability status."""
        if net_availability > 100:
            return 'abundant'
        elif net_availability > 50:
            return 'adequate'
        elif net_availability > 0:
            return 'limited'
        else:
            return 'deficit'
    
    async def shutdown(self):
        """Shutdown the climate analysis agent."""
        try:
            logger.info("Shutting down Climate Analysis Agent...")
            self.is_initialized = False
            logger.info("Climate Analysis Agent shutdown completed")
            
        except Exception as e:
            logger.error("Error during climate agent shutdown", error=str(e))
