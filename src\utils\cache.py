"""
Caching utilities for the Water Management System.

This module provides caching functionality with Redis fallback to in-memory cache
for development environments where Redis is not available.
"""

import asyncio
import json
import logging
import time
from typing import Any, Optional, Dict, Union
from datetime import datetime, timedelta

from src.utils.config import get_settings

logger = logging.getLogger(__name__)


class InMemoryCache:
    """Simple in-memory cache implementation as Redis fallback."""
    
    def __init__(self, default_ttl: int = 3600):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        async with self._lock:
            if key in self.cache:
                entry = self.cache[key]
                if entry['expires_at'] > time.time():
                    return entry['value']
                else:
                    # Expired, remove from cache
                    del self.cache[key]
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with TTL."""
        async with self._lock:
            expires_at = time.time() + (ttl or self.default_ttl)
            self.cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        async with self._lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        value = await self.get(key)
        return value is not None
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        async with self._lock:
            self.cache.clear()
            return True
    
    async def keys(self, pattern: str = "*") -> list:
        """Get all keys matching pattern."""
        async with self._lock:
            if pattern == "*":
                return list(self.cache.keys())
            else:
                # Simple pattern matching (only supports * wildcard)
                import fnmatch
                return [key for key in self.cache.keys() if fnmatch.fnmatch(key, pattern)]
    
    async def cleanup_expired(self):
        """Remove expired entries from cache."""
        async with self._lock:
            current_time = time.time()
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry['expires_at'] <= current_time
            ]
            for key in expired_keys:
                del self.cache[key]
            return len(expired_keys)


class CacheManager:
    """Cache manager that handles both Redis and in-memory caching."""
    
    def __init__(self):
        self.settings = get_settings()
        self.redis_client = None
        self.fallback_cache = InMemoryCache(default_ttl=self.settings.CACHE_TTL)
        self.is_redis_available = False
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize cache manager."""
        try:
            logger.info("Initializing cache manager...")
            
            # Try to initialize Redis
            try:
                import redis.asyncio as redis
                
                self.redis_client = redis.from_url(
                    self.settings.get_redis_url(),
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                
                # Test Redis connection
                await self.redis_client.ping()
                self.is_redis_available = True
                logger.info("Redis cache initialized successfully")
                
            except Exception as e:
                logger.warning(f"Redis not available, using in-memory cache: {e}")
                self.is_redis_available = False
            
            # Start cleanup task for in-memory cache
            if not self.is_redis_available:
                asyncio.create_task(self._cleanup_task())
            
            self.is_initialized = True
            logger.info(f"Cache manager initialized (Redis: {self.is_redis_available})")
            
        except Exception as e:
            logger.error(f"Failed to initialize cache manager: {e}")
            raise
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            if self.is_redis_available and self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    try:
                        return json.loads(value)
                    except json.JSONDecodeError:
                        return value
                return None
            else:
                return await self.fallback_cache.get(key)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with TTL."""
        try:
            ttl = ttl or self.settings.CACHE_TTL
            
            if self.is_redis_available and self.redis_client:
                # Serialize value for Redis
                if isinstance(value, (dict, list)):
                    serialized_value = json.dumps(value)
                else:
                    serialized_value = str(value)
                
                await self.redis_client.setex(key, ttl, serialized_value)
                return True
            else:
                return await self.fallback_cache.set(key, value, ttl)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            if self.is_redis_available and self.redis_client:
                result = await self.redis_client.delete(key)
                return result > 0
            else:
                return await self.fallback_cache.delete(key)
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            if self.is_redis_available and self.redis_client:
                return await self.redis_client.exists(key) > 0
            else:
                return await self.fallback_cache.exists(key)
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def clear(self, pattern: str = "*") -> bool:
        """Clear cache entries matching pattern."""
        try:
            if self.is_redis_available and self.redis_client:
                if pattern == "*":
                    await self.redis_client.flushdb()
                else:
                    keys = await self.redis_client.keys(pattern)
                    if keys:
                        await self.redis_client.delete(*keys)
                return True
            else:
                if pattern == "*":
                    return await self.fallback_cache.clear()
                else:
                    keys = await self.fallback_cache.keys(pattern)
                    for key in keys:
                        await self.fallback_cache.delete(key)
                    return True
        except Exception as e:
            logger.error(f"Cache clear error for pattern {pattern}: {e}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            if self.is_redis_available and self.redis_client:
                info = await self.redis_client.info()
                return {
                    "type": "redis",
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory": info.get("used_memory", 0),
                    "used_memory_human": info.get("used_memory_human", "0B"),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0)
                }
            else:
                return {
                    "type": "in_memory",
                    "total_keys": len(self.fallback_cache.cache),
                    "memory_usage": "N/A"
                }
        except Exception as e:
            logger.error(f"Cache stats error: {e}")
            return {"type": "error", "error": str(e)}
    
    async def _cleanup_task(self):
        """Background task to cleanup expired entries in in-memory cache."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                cleaned = await self.fallback_cache.cleanup_expired()
                if cleaned > 0:
                    logger.debug(f"Cleaned up {cleaned} expired cache entries")
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    async def close(self):
        """Close cache connections."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            logger.info("Cache manager closed")
        except Exception as e:
            logger.error(f"Error closing cache manager: {e}")


# Global cache manager instance
_cache_manager = CacheManager()


async def init_cache():
    """Initialize the global cache manager."""
    await _cache_manager.initialize()


async def get_cache() -> CacheManager:
    """Get the global cache manager instance."""
    if not _cache_manager.is_initialized:
        await _cache_manager.initialize()
    return _cache_manager


async def close_cache():
    """Close the global cache manager."""
    await _cache_manager.close()


# Convenience functions
async def cache_get(key: str) -> Optional[Any]:
    """Get value from cache."""
    cache = await get_cache()
    return await cache.get(key)


async def cache_set(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """Set value in cache."""
    cache = await get_cache()
    return await cache.set(key, value, ttl)


async def cache_delete(key: str) -> bool:
    """Delete key from cache."""
    cache = await get_cache()
    return await cache.delete(key)


async def cache_exists(key: str) -> bool:
    """Check if key exists in cache."""
    cache = await get_cache()
    return await cache.exists(key)


def cache_key(*parts: str) -> str:
    """Generate a cache key from parts."""
    return ":".join(str(part) for part in parts)


def cache_decorator(ttl: int = 3600, key_prefix: str = ""):
    """Decorator to cache function results."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Generate cache key
            key_parts = [key_prefix or func.__name__]
            key_parts.extend(str(arg) for arg in args)
            key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
            cache_key_str = cache_key(*key_parts)
            
            # Try to get from cache
            cached_result = await cache_get(cache_key_str)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_set(cache_key_str, result, ttl)
            return result
        
        return wrapper
    return decorator
