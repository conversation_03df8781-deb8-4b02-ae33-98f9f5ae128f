# Water Management Decarbonisation System - Deployment Guide

## 🚀 **COMPLETE DEPLOYMENT GUIDE**

This guide provides comprehensive instructions for deploying the Water Management Decarbonisation System in production environments.

---

## 📋 **PREREQUISITES**

### **System Requirements**
- **Operating System**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows 10+
- **Memory**: Minimum 8GB RAM (16GB+ recommended for production)
- **Storage**: Minimum 50GB free space (100GB+ recommended)
- **CPU**: 4+ cores (8+ cores recommended for production)
- **Network**: Stable internet connection for API access

### **Software Dependencies**
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Python**: Version 3.9+ (if running without Docker)
- **PostgreSQL**: Version 15+ (managed by <PERSON><PERSON>)
- **Redis**: Version 7+ (managed by <PERSON><PERSON>)

---

## 🔑 **API KEYS SETUP**

### **Required API Keys**

1. **OpenAI API Key**
   - Visit: https://platform.openai.com/api-keys
   - Create account and generate API key
   - Set environment variable: `OPENAI_API_KEY`

2. **Google Gemini API Key**
   - Visit: https://makersuite.google.com/app/apikey
   - Generate API key
   - Set environment variable: `GEMINI_API_KEY`

3. **OpenWeatherMap API Key**
   - Visit: https://openweathermap.org/api
   - Sign up and get free API key
   - Set environment variable: `OPENWEATHERMAP_API_KEY`

4. **NASA API Key**
   - Visit: https://api.nasa.gov/
   - Generate API key (free)
   - Set environment variable: `NASA_API_KEY`

5. **NOAA API Key**
   - Visit: https://www.ncdc.noaa.gov/cdo-web/token
   - Request API token
   - Set environment variable: `NOAA_API_KEY`

6. **World Bank API Key** (Optional)
   - Visit: https://datahelpdesk.worldbank.org/knowledgebase/articles/889392
   - Set environment variable: `WORLD_BANK_API_KEY`

7. **ECMWF API Key** (Optional)
   - Visit: https://api.ecmwf.int/
   - Set environment variable: `ECMWF_API_KEY`

8. **Hugging Face Token** (Optional)
   - Visit: https://huggingface.co/settings/tokens
   - Set environment variable: `HUGGINGFACE_TOKEN`

### **Environment Variables Setup**

Create a `.env` file in the project root:

```bash
# Core API Keys (Required)
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
OPENWEATHERMAP_API_KEY=your_openweathermap_api_key_here

# Additional API Keys (Recommended)
NASA_API_KEY=your_nasa_api_key_here
NOAA_API_KEY=your_noaa_api_key_here
WORLD_BANK_API_KEY=your_worldbank_api_key_here
ECMWF_API_KEY=your_ecmwf_api_key_here
HUGGINGFACE_TOKEN=your_huggingface_token_here

# Database Configuration
DATABASE_URL=********************************************************/water_management
REDIS_URL=redis://:redis_water_2024@redis:6379/0

# Application Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
DEBUG=false

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET=your_jwt_secret_here

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
```

---

## 🐳 **DOCKER DEPLOYMENT**

### **Quick Start (Recommended)**

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd water_management_decarbonisation
   ```

2. **Setup Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your API keys
   nano .env
   ```

3. **Build and Start Services**
   ```bash
   docker-compose up -d
   ```

4. **Verify Deployment**
   ```bash
   docker-compose ps
   docker-compose logs water_app
   ```

5. **Access the Application**
   - **Main Dashboard**: http://localhost:8501
   - **Grafana Monitoring**: http://localhost:3000 (admin/water_grafana_2024)
   - **Prometheus Metrics**: http://localhost:9090

### **Production Deployment**

1. **Production Configuration**
   ```bash
   # Use production docker-compose file
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. **SSL/TLS Setup**
   ```bash
   # Generate SSL certificates
   mkdir -p nginx/ssl
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout nginx/ssl/nginx.key \
     -out nginx/ssl/nginx.crt
   ```

3. **Database Backup Setup**
   ```bash
   # Setup automated backups
   docker exec water_management_db pg_dump -U water_admin water_management > backup.sql
   ```

---

## 🔧 **MANUAL INSTALLATION**

### **Python Environment Setup**

1. **Create Virtual Environment**
   ```bash
   python3.9 -m venv water_management_env
   source water_management_env/bin/activate  # Linux/Mac
   # or
   water_management_env\Scripts\activate  # Windows
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Database Setup**
   ```bash
   # Install PostgreSQL
   sudo apt-get install postgresql postgresql-contrib  # Ubuntu
   
   # Create database
   sudo -u postgres createdb water_management
   sudo -u postgres createuser water_admin
   
   # Run initialization script
   psql -U water_admin -d water_management -f sql/init.sql
   ```

4. **Redis Setup**
   ```bash
   # Install Redis
   sudo apt-get install redis-server  # Ubuntu
   
   # Start Redis
   sudo systemctl start redis-server
   sudo systemctl enable redis-server
   ```

5. **Run Application**
   ```bash
   # Set environment variables
   export OPENAI_API_KEY=your_key_here
   export GEMINI_API_KEY=your_key_here
   # ... other environment variables
   
   # Start the application
   streamlit run src/app.py --server.port=8501
   ```

---

## 🌐 **CLOUD DEPLOYMENT**

### **AWS Deployment**

1. **EC2 Instance Setup**
   ```bash
   # Launch EC2 instance (t3.large or larger recommended)
   # Install Docker and Docker Compose
   sudo yum update -y
   sudo yum install -y docker
   sudo service docker start
   sudo usermod -a -G docker ec2-user
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **RDS Database Setup**
   ```bash
   # Create RDS PostgreSQL instance
   # Update DATABASE_URL in .env file
   DATABASE_URL=************************************************/water_management
   ```

3. **ElastiCache Redis Setup**
   ```bash
   # Create ElastiCache Redis cluster
   # Update REDIS_URL in .env file
   REDIS_URL=redis://elasticache-endpoint:6379/0
   ```

### **Google Cloud Platform Deployment**

1. **Cloud Run Deployment**
   ```bash
   # Build and push to Container Registry
   gcloud builds submit --tag gcr.io/PROJECT_ID/water-management
   
   # Deploy to Cloud Run
   gcloud run deploy water-management \
     --image gcr.io/PROJECT_ID/water-management \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

2. **Cloud SQL Setup**
   ```bash
   # Create Cloud SQL PostgreSQL instance
   gcloud sql instances create water-management-db \
     --database-version=POSTGRES_15 \
     --tier=db-f1-micro \
     --region=us-central1
   ```

### **Azure Deployment**

1. **Container Instances**
   ```bash
   # Create resource group
   az group create --name water-management-rg --location eastus
   
   # Deploy container
   az container create \
     --resource-group water-management-rg \
     --name water-management-app \
     --image your-registry/water-management:latest \
     --dns-name-label water-management \
     --ports 8501
   ```

---

## 📊 **MONITORING AND MAINTENANCE**

### **Health Checks**

1. **Application Health**
   ```bash
   # Check application status
   curl http://localhost:8501/_stcore/health
   
   # Check database connection
   docker exec water_management_db pg_isready -U water_admin
   
   # Check Redis connection
   docker exec water_management_cache redis-cli ping
   ```

2. **Performance Monitoring**
   - **Grafana Dashboard**: http://localhost:3000
   - **Prometheus Metrics**: http://localhost:9090
   - **Application Logs**: `docker-compose logs -f water_app`

### **Backup and Recovery**

1. **Database Backup**
   ```bash
   # Create backup
   docker exec water_management_db pg_dump -U water_admin water_management > backup_$(date +%Y%m%d_%H%M%S).sql
   
   # Restore backup
   docker exec -i water_management_db psql -U water_admin water_management < backup.sql
   ```

2. **Data Backup**
   ```bash
   # Backup data volumes
   docker run --rm -v water_management_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
   ```

### **Scaling**

1. **Horizontal Scaling**
   ```bash
   # Scale application containers
   docker-compose up -d --scale water_app=3
   ```

2. **Load Balancer Setup**
   ```nginx
   # nginx.conf
   upstream water_management {
       server water_app_1:8501;
       server water_app_2:8501;
       server water_app_3:8501;
   }
   ```

---

## 🔒 **SECURITY CONFIGURATION**

### **SSL/TLS Setup**

1. **Let's Encrypt (Recommended)**
   ```bash
   # Install Certbot
   sudo apt-get install certbot python3-certbot-nginx
   
   # Generate certificate
   sudo certbot --nginx -d your-domain.com
   ```

2. **Firewall Configuration**
   ```bash
   # UFW setup
   sudo ufw allow 22/tcp
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw enable
   ```

### **Database Security**

1. **PostgreSQL Security**
   ```sql
   -- Create read-only user
   CREATE USER water_readonly WITH PASSWORD 'readonly_password';
   GRANT CONNECT ON DATABASE water_management TO water_readonly;
   GRANT USAGE ON SCHEMA public TO water_readonly;
   GRANT SELECT ON ALL TABLES IN SCHEMA public TO water_readonly;
   ```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

1. **Application Won't Start**
   ```bash
   # Check logs
   docker-compose logs water_app
   
   # Check environment variables
   docker-compose exec water_app env | grep API_KEY
   ```

2. **Database Connection Issues**
   ```bash
   # Check database status
   docker-compose exec postgres pg_isready
   
   # Check connection string
   docker-compose exec water_app python -c "import os; print(os.getenv('DATABASE_URL'))"
   ```

3. **API Key Issues**
   ```bash
   # Verify API keys
   curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
   ```

### **Performance Issues**

1. **Memory Usage**
   ```bash
   # Monitor memory usage
   docker stats
   
   # Increase memory limits in docker-compose.yml
   deploy:
     resources:
       limits:
         memory: 4G
   ```

2. **Database Performance**
   ```sql
   -- Check slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC 
   LIMIT 10;
   ```

---

## 📞 **SUPPORT**

### **Getting Help**

1. **Documentation**: Check the `/docs` directory for detailed documentation
2. **Logs**: Always check application logs first: `docker-compose logs -f`
3. **Health Checks**: Use built-in health endpoints for diagnostics
4. **Monitoring**: Use Grafana dashboards for system insights

### **Maintenance Schedule**

1. **Daily**: Check application health and logs
2. **Weekly**: Review performance metrics and update dependencies
3. **Monthly**: Database maintenance and backup verification
4. **Quarterly**: Security updates and system optimization

---

## ✅ **DEPLOYMENT CHECKLIST**

- [ ] All API keys configured
- [ ] Environment variables set
- [ ] Docker and Docker Compose installed
- [ ] SSL certificates configured (production)
- [ ] Database initialized
- [ ] Backup strategy implemented
- [ ] Monitoring configured
- [ ] Health checks verified
- [ ] Security measures implemented
- [ ] Documentation reviewed

**🎉 Your Water Management Decarbonisation System is now ready for production!**
