"""Treatment Optimization Agent for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class OptimizationObjective(Enum):
    """Treatment optimization objectives."""
    ENERGY_EFFICIENCY = "energy_efficiency"
    CHEMICAL_REDUCTION = "chemical_reduction"
    WATER_QUALITY = "water_quality"
    COST_MINIMIZATION = "cost_minimization"
    THROUGHPUT_MAXIMIZATION = "throughput_maximization"
    COMPLIANCE = "compliance"


class TreatmentProcess(Enum):
    """Water treatment processes."""
    COAGULATION = "coagulation"
    FLOCCULATION = "flocculation"
    SEDIMENTATION = "sedimentation"
    FILTRATION = "filtration"
    DISINFECTION = "disinfection"
    PH_ADJUSTMENT = "ph_adjustment"
    SOFTENING = "softening"
    REVERSE_OSMOSIS = "reverse_osmosis"


@dataclass
class OptimizationRecommendation:
    """Treatment optimization recommendation."""
    process: TreatmentProcess
    parameter: str
    current_value: float
    recommended_value: float
    expected_improvement: float
    confidence: float
    reasoning: str
    implementation_priority: str
    estimated_savings: Dict[str, float] = field(default_factory=dict)


@dataclass
class TreatmentSystemState:
    """Current state of treatment system."""
    timestamp: datetime
    water_quality: Dict[str, float]
    operational_parameters: Dict[str, float]
    energy_consumption: float
    chemical_usage: Dict[str, float]
    flow_rate: float
    efficiency_metrics: Dict[str, float]


class TreatmentOptimizationAgent:
    """AI agent for optimizing water treatment processes."""
    
    def __init__(self):
        self.optimization_history: List[Dict[str, Any]] = []
        self.system_models: Dict[str, Any] = {}
        self.performance_baselines: Dict[str, float] = {}
        
        # Treatment process parameters and their optimal ranges
        self.process_parameters = {
            TreatmentProcess.COAGULATION: {
                'coagulant_dose': {'range': (5, 50), 'unit': 'mg/L', 'optimal': 25},
                'mixing_speed': {'range': (50, 200), 'unit': 'rpm', 'optimal': 120},
                'ph': {'range': (6.0, 8.0), 'unit': 'pH', 'optimal': 7.2}
            },
            TreatmentProcess.FLOCCULATION: {
                'mixing_time': {'range': (10, 60), 'unit': 'minutes', 'optimal': 30},
                'mixing_speed': {'range': (10, 50), 'unit': 'rpm', 'optimal': 25},
                'polymer_dose': {'range': (0.1, 2.0), 'unit': 'mg/L', 'optimal': 0.5}
            },
            TreatmentProcess.FILTRATION: {
                'filtration_rate': {'range': (2, 10), 'unit': 'm/h', 'optimal': 5},
                'backwash_frequency': {'range': (12, 72), 'unit': 'hours', 'optimal': 24},
                'pressure_drop': {'range': (0.1, 1.0), 'unit': 'bar', 'optimal': 0.3}
            },
            TreatmentProcess.DISINFECTION: {
                'chlorine_dose': {'range': (0.5, 4.0), 'unit': 'mg/L', 'optimal': 1.5},
                'contact_time': {'range': (10, 120), 'unit': 'minutes', 'optimal': 30},
                'ph': {'range': (6.5, 8.5), 'unit': 'pH', 'optimal': 7.5}
            }
        }
        
        # Initialize performance baselines
        self._initialize_baselines()
    
    def _initialize_baselines(self):
        """Initialize performance baselines for optimization."""
        self.performance_baselines = {
            'energy_efficiency': 85.0,  # %
            'chemical_efficiency': 90.0,  # %
            'water_quality_score': 95.0,  # %
            'operational_cost': 100.0,  # relative index
            'throughput': 1000.0,  # m³/h
            'compliance_score': 98.0  # %
        }
    
    async def optimize_treatment_process(self, water_data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize treatment process based on water quality data."""
        # Convert water_data to system_state format
        system_state = {
            'timestamp': datetime.now().isoformat(),
            'water_quality': water_data,
            'operational_parameters': {},
            'energy_consumption': water_data.get('flow_rate', 1000) * 0.4,  # Estimate
            'chemical_usage': {
                'coagulant': 25.0,
                'chlorine': 1.5,
                'polymer': 0.5
            },
            'flow_rate': water_data.get('flow_rate', 1000),
            'efficiency_metrics': {
                'overall_efficiency': 85.0,
                'filtration_efficiency': 92.0,
                'resource_utilization': 88.0
            }
        }

        return await self.analyze_treatment_system(system_state)

    @log_async_function_call
    async def analyze_treatment_system(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze current treatment system performance."""
        try:
            # Convert to TreatmentSystemState
            state = TreatmentSystemState(
                timestamp=datetime.fromisoformat(system_state.get('timestamp', datetime.now().isoformat())),
                water_quality=system_state.get('water_quality', {}),
                operational_parameters=system_state.get('operational_parameters', {}),
                energy_consumption=system_state.get('energy_consumption', 0),
                chemical_usage=system_state.get('chemical_usage', {}),
                flow_rate=system_state.get('flow_rate', 0),
                efficiency_metrics=system_state.get('efficiency_metrics', {})
            )
            
            # Perform comprehensive analysis
            performance_analysis = await self._analyze_performance(state)
            efficiency_analysis = await self._analyze_efficiency(state)
            quality_analysis = await self._analyze_water_quality(state)
            cost_analysis = await self._analyze_costs(state)
            
            # Generate overall assessment
            overall_score = self._calculate_overall_score(
                performance_analysis, efficiency_analysis, quality_analysis, cost_analysis
            )
            
            # Identify improvement opportunities
            improvement_opportunities = await self._identify_improvements(state)
            
            return {
                'status': 'success',
                'analysis_timestamp': datetime.now().isoformat(),
                'system_state': {
                    'timestamp': state.timestamp.isoformat(),
                    'flow_rate': state.flow_rate,
                    'energy_consumption': state.energy_consumption
                },
                'performance_analysis': performance_analysis,
                'efficiency_analysis': efficiency_analysis,
                'quality_analysis': quality_analysis,
                'cost_analysis': cost_analysis,
                'overall_score': overall_score,
                'improvement_opportunities': improvement_opportunities,
                'recommendations_count': len(improvement_opportunities)
            }
            
        except Exception as e:
            logger.error(f"Treatment system analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _analyze_performance(self, state: TreatmentSystemState) -> Dict[str, Any]:
        """Analyze system performance metrics."""
        performance_metrics = {}
        
        # Throughput efficiency
        target_flow = 1000.0  # m³/h
        throughput_efficiency = min(100, (state.flow_rate / target_flow) * 100)
        performance_metrics['throughput_efficiency'] = throughput_efficiency
        
        # Energy efficiency
        expected_energy = state.flow_rate * 0.4  # kWh per m³
        if expected_energy > 0:
            energy_efficiency = min(100, (expected_energy / state.energy_consumption) * 100)
        else:
            energy_efficiency = 0
        performance_metrics['energy_efficiency'] = energy_efficiency
        
        # Process efficiency
        process_efficiency = state.efficiency_metrics.get('overall_efficiency', 85.0)
        performance_metrics['process_efficiency'] = process_efficiency
        
        # Calculate performance score
        performance_score = (throughput_efficiency + energy_efficiency + process_efficiency) / 3
        
        return {
            'performance_score': performance_score,
            'metrics': performance_metrics,
            'status': 'excellent' if performance_score > 90 else 'good' if performance_score > 75 else 'needs_improvement',
            'baseline_comparison': {
                'current': performance_score,
                'baseline': self.performance_baselines.get('energy_efficiency', 85),
                'improvement': performance_score - self.performance_baselines.get('energy_efficiency', 85)
            }
        }
    
    async def _analyze_efficiency(self, state: TreatmentSystemState) -> Dict[str, Any]:
        """Analyze treatment efficiency."""
        efficiency_metrics = {}
        
        # Chemical efficiency
        total_chemical_usage = sum(state.chemical_usage.values())
        if state.flow_rate > 0:
            chemical_intensity = total_chemical_usage / state.flow_rate
            # Lower is better for chemical intensity
            chemical_efficiency = max(0, 100 - (chemical_intensity - 0.05) * 1000)
        else:
            chemical_efficiency = 0
        
        efficiency_metrics['chemical_efficiency'] = chemical_efficiency
        
        # Energy intensity
        if state.flow_rate > 0:
            energy_intensity = state.energy_consumption / state.flow_rate
            # Target: 0.4 kWh/m³
            energy_efficiency = max(0, 100 - abs(energy_intensity - 0.4) * 250)
        else:
            energy_efficiency = 0
        
        efficiency_metrics['energy_intensity_efficiency'] = energy_efficiency
        
        # Resource utilization
        resource_efficiency = state.efficiency_metrics.get('resource_utilization', 80.0)
        efficiency_metrics['resource_efficiency'] = resource_efficiency
        
        overall_efficiency = (chemical_efficiency + energy_efficiency + resource_efficiency) / 3
        
        return {
            'overall_efficiency': overall_efficiency,
            'metrics': efficiency_metrics,
            'efficiency_trends': {
                'chemical_usage_trend': 'stable',
                'energy_usage_trend': 'improving',
                'resource_utilization_trend': 'stable'
            },
            'efficiency_ranking': 'top_quartile' if overall_efficiency > 85 else 'above_average' if overall_efficiency > 70 else 'below_average'
        }
    
    async def _analyze_water_quality(self, state: TreatmentSystemState) -> Dict[str, Any]:
        """Analyze water quality metrics."""
        quality_scores = {}
        
        # pH compliance
        ph = state.water_quality.get('ph', 7.0)
        ph_score = 100 if 6.5 <= ph <= 8.5 else max(0, 100 - abs(ph - 7.0) * 20)
        quality_scores['ph_score'] = ph_score
        
        # Turbidity compliance
        turbidity = state.water_quality.get('turbidity', 1.0)
        turbidity_score = 100 if turbidity <= 1.0 else max(0, 100 - (turbidity - 1.0) * 25)
        quality_scores['turbidity_score'] = turbidity_score
        
        # Chlorine residual
        chlorine = state.water_quality.get('chlorine_residual', 0.5)
        chlorine_score = 100 if 0.2 <= chlorine <= 4.0 else max(0, 100 - abs(chlorine - 1.0) * 30)
        quality_scores['chlorine_score'] = chlorine_score
        
        # Bacteria count
        bacteria = state.water_quality.get('bacteria_count', 0)
        bacteria_score = 100 if bacteria == 0 else max(0, 100 - bacteria * 10)
        quality_scores['bacteria_score'] = bacteria_score
        
        # Overall quality score
        overall_quality = sum(quality_scores.values()) / len(quality_scores)
        
        # Compliance status
        compliance_issues = []
        if ph < 6.5 or ph > 8.5:
            compliance_issues.append(f"pH out of range: {ph}")
        if turbidity > 4.0:
            compliance_issues.append(f"High turbidity: {turbidity} NTU")
        if bacteria > 0:
            compliance_issues.append(f"Bacteria detected: {bacteria}")
        
        return {
            'overall_quality_score': overall_quality,
            'parameter_scores': quality_scores,
            'compliance_status': 'compliant' if not compliance_issues else 'non_compliant',
            'compliance_issues': compliance_issues,
            'quality_trend': 'improving',
            'regulatory_status': 'meets_standards' if overall_quality > 95 else 'monitoring_required'
        }
    
    async def _analyze_costs(self, state: TreatmentSystemState) -> Dict[str, Any]:
        """Analyze operational costs."""
        cost_breakdown = {}
        
        # Energy costs (assuming $0.10/kWh)
        energy_cost = state.energy_consumption * 0.10
        cost_breakdown['energy_cost'] = energy_cost
        
        # Chemical costs
        chemical_costs = {
            'coagulant': state.chemical_usage.get('coagulant', 0) * 0.50,  # $/kg
            'chlorine': state.chemical_usage.get('chlorine', 0) * 0.80,   # $/kg
            'polymer': state.chemical_usage.get('polymer', 0) * 2.00      # $/kg
        }
        total_chemical_cost = sum(chemical_costs.values())
        cost_breakdown['chemical_cost'] = total_chemical_cost
        
        # Maintenance costs (estimated)
        maintenance_cost = state.flow_rate * 0.02  # $0.02 per m³
        cost_breakdown['maintenance_cost'] = maintenance_cost
        
        # Total operational cost
        total_cost = energy_cost + total_chemical_cost + maintenance_cost
        
        # Cost per unit volume
        if state.flow_rate > 0:
            unit_cost = total_cost / state.flow_rate
        else:
            unit_cost = 0
        
        return {
            'total_operational_cost': total_cost,
            'cost_breakdown': cost_breakdown,
            'unit_cost_per_m3': unit_cost,
            'cost_efficiency': max(0, 100 - (unit_cost - 0.15) * 500),  # Target: $0.15/m³
            'cost_trend': 'stable',
            'cost_optimization_potential': max(0, (unit_cost - 0.12) / unit_cost * 100) if unit_cost > 0 else 0
        }
    
    def _calculate_overall_score(self, performance: Dict, efficiency: Dict, 
                               quality: Dict, cost: Dict) -> Dict[str, Any]:
        """Calculate overall system score."""
        # Weighted scoring
        weights = {
            'performance': 0.25,
            'efficiency': 0.25,
            'quality': 0.35,  # Quality is most important
            'cost': 0.15
        }
        
        scores = {
            'performance': performance['performance_score'],
            'efficiency': efficiency['overall_efficiency'],
            'quality': quality['overall_quality_score'],
            'cost': cost['cost_efficiency']
        }
        
        overall_score = sum(scores[key] * weights[key] for key in weights)
        
        # Determine grade
        if overall_score >= 90:
            grade = 'A'
            status = 'Excellent'
        elif overall_score >= 80:
            grade = 'B'
            status = 'Good'
        elif overall_score >= 70:
            grade = 'C'
            status = 'Satisfactory'
        else:
            grade = 'D'
            status = 'Needs Improvement'
        
        return {
            'overall_score': overall_score,
            'grade': grade,
            'status': status,
            'component_scores': scores,
            'weights_used': weights,
            'improvement_potential': max(0, 95 - overall_score)
        }
    
    async def _identify_improvements(self, state: TreatmentSystemState) -> List[Dict[str, Any]]:
        """Identify specific improvement opportunities."""
        improvements = []
        
        # Energy optimization opportunities
        if state.energy_consumption > 0 and state.flow_rate > 0:
            energy_intensity = state.energy_consumption / state.flow_rate
            if energy_intensity > 0.45:  # Above target
                improvements.append({
                    'category': 'energy_optimization',
                    'description': 'Reduce energy consumption through pump optimization',
                    'current_value': energy_intensity,
                    'target_value': 0.40,
                    'potential_savings': (energy_intensity - 0.40) * state.flow_rate * 0.10,
                    'implementation_effort': 'medium',
                    'payback_period': '6-12 months'
                })
        
        # Chemical optimization
        coagulant_usage = state.chemical_usage.get('coagulant', 0)
        if coagulant_usage > 30:  # mg/L
            improvements.append({
                'category': 'chemical_optimization',
                'description': 'Optimize coagulant dosing based on raw water quality',
                'current_value': coagulant_usage,
                'target_value': 25,
                'potential_savings': (coagulant_usage - 25) * state.flow_rate * 0.0005,
                'implementation_effort': 'low',
                'payback_period': '3-6 months'
            })
        
        # Water quality improvements
        ph = state.water_quality.get('ph', 7.0)
        if ph < 6.8 or ph > 8.2:
            improvements.append({
                'category': 'quality_improvement',
                'description': 'Improve pH control system for better stability',
                'current_value': ph,
                'target_value': 7.2,
                'potential_savings': 0,  # Quality improvement
                'implementation_effort': 'medium',
                'payback_period': 'immediate_compliance'
            })
        
        # Process efficiency improvements
        if state.efficiency_metrics.get('filtration_efficiency', 90) < 95:
            improvements.append({
                'category': 'process_optimization',
                'description': 'Optimize filtration process parameters',
                'current_value': state.efficiency_metrics.get('filtration_efficiency', 90),
                'target_value': 95,
                'potential_savings': state.flow_rate * 0.01,  # Reduced waste
                'implementation_effort': 'low',
                'payback_period': '1-3 months'
            })
        
        return improvements
    
    @log_async_function_call
    async def generate_optimization_recommendations(self, system_state: Dict[str, Any],
                                                  objectives: List[OptimizationObjective] = None) -> Dict[str, Any]:
        """Generate specific optimization recommendations."""
        try:
            if objectives is None:
                objectives = [OptimizationObjective.ENERGY_EFFICIENCY, OptimizationObjective.WATER_QUALITY]
            
            # Analyze current system
            analysis_result = await self.analyze_treatment_system(system_state)
            
            if analysis_result['status'] != 'success':
                return analysis_result
            
            recommendations = []
            
            # Generate recommendations for each objective
            for objective in objectives:
                obj_recommendations = await self._generate_objective_recommendations(
                    system_state, objective, analysis_result
                )
                recommendations.extend(obj_recommendations)
            
            # Prioritize recommendations
            prioritized_recommendations = self._prioritize_recommendations(recommendations)
            
            # Calculate potential impact
            total_impact = self._calculate_total_impact(prioritized_recommendations)
            
            return {
                'status': 'success',
                'optimization_objectives': [obj.value for obj in objectives],
                'total_recommendations': len(prioritized_recommendations),
                'recommendations': prioritized_recommendations,
                'potential_impact': total_impact,
                'implementation_timeline': self._generate_implementation_timeline(prioritized_recommendations),
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Optimization recommendations generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_objective_recommendations(self, system_state: Dict[str, Any],
                                                objective: OptimizationObjective,
                                                analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations for specific objective."""
        recommendations = []
        
        if objective == OptimizationObjective.ENERGY_EFFICIENCY:
            # Energy-focused recommendations
            energy_consumption = system_state.get('energy_consumption', 0)
            flow_rate = system_state.get('flow_rate', 1000)
            
            if energy_consumption > 0 and flow_rate > 0:
                energy_intensity = energy_consumption / flow_rate
                
                if energy_intensity > 0.45:
                    recommendations.append({
                        'objective': objective.value,
                        'recommendation_id': 'energy_001',
                        'title': 'Optimize Pump Operation',
                        'description': 'Implement variable frequency drives and optimize pump scheduling',
                        'current_performance': energy_intensity,
                        'target_performance': 0.40,
                        'expected_improvement': ((energy_intensity - 0.40) / energy_intensity) * 100,
                        'confidence': 0.85,
                        'implementation_cost': 15000,
                        'annual_savings': (energy_intensity - 0.40) * flow_rate * 8760 * 0.10,
                        'payback_period': 1.2,
                        'priority': 'high'
                    })
        
        elif objective == OptimizationObjective.CHEMICAL_REDUCTION:
            # Chemical optimization recommendations
            chemical_usage = system_state.get('chemical_usage', {})
            
            coagulant = chemical_usage.get('coagulant', 0)
            if coagulant > 30:
                recommendations.append({
                    'objective': objective.value,
                    'recommendation_id': 'chem_001',
                    'title': 'Optimize Coagulant Dosing',
                    'description': 'Implement real-time coagulant dosing based on raw water turbidity',
                    'current_performance': coagulant,
                    'target_performance': 25,
                    'expected_improvement': ((coagulant - 25) / coagulant) * 100,
                    'confidence': 0.90,
                    'implementation_cost': 8000,
                    'annual_savings': (coagulant - 25) * flow_rate * 365 * 0.0005,
                    'payback_period': 0.8,
                    'priority': 'medium'
                })
        
        elif objective == OptimizationObjective.WATER_QUALITY:
            # Quality improvement recommendations
            quality_analysis = analysis.get('quality_analysis', {})
            compliance_issues = quality_analysis.get('compliance_issues', [])
            
            if compliance_issues:
                recommendations.append({
                    'objective': objective.value,
                    'recommendation_id': 'qual_001',
                    'title': 'Address Water Quality Compliance Issues',
                    'description': f'Resolve {len(compliance_issues)} compliance issues',
                    'current_performance': quality_analysis.get('overall_quality_score', 0),
                    'target_performance': 98,
                    'expected_improvement': 98 - quality_analysis.get('overall_quality_score', 0),
                    'confidence': 0.95,
                    'implementation_cost': 5000,
                    'annual_savings': 0,  # Compliance value
                    'payback_period': 0,  # Immediate compliance requirement
                    'priority': 'critical'
                })
        
        return recommendations
    
    def _prioritize_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize recommendations based on impact and feasibility."""
        # Priority scoring
        priority_scores = {'critical': 100, 'high': 80, 'medium': 60, 'low': 40}
        
        for rec in recommendations:
            # Calculate priority score
            priority_score = priority_scores.get(rec.get('priority', 'medium'), 60)
            
            # Add impact score
            impact_score = rec.get('expected_improvement', 0) * rec.get('confidence', 0.5)
            
            # Add financial score
            if rec.get('payback_period', 10) > 0:
                financial_score = min(50, 50 / rec.get('payback_period', 10))
            else:
                financial_score = 50
            
            rec['total_score'] = priority_score + impact_score + financial_score
        
        # Sort by total score
        return sorted(recommendations, key=lambda x: x.get('total_score', 0), reverse=True)
    
    def _calculate_total_impact(self, recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate total potential impact of all recommendations."""
        total_savings = sum(rec.get('annual_savings', 0) for rec in recommendations)
        total_investment = sum(rec.get('implementation_cost', 0) for rec in recommendations)
        
        avg_payback = np.mean([rec.get('payback_period', 0) for rec in recommendations if rec.get('payback_period', 0) > 0])
        
        return {
            'total_annual_savings': total_savings,
            'total_investment_required': total_investment,
            'net_present_value': total_savings * 5 - total_investment,  # 5-year NPV
            'average_payback_period': avg_payback,
            'roi_percentage': (total_savings / total_investment * 100) if total_investment > 0 else 0
        }
    
    def _generate_implementation_timeline(self, recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate implementation timeline for recommendations."""
        timeline = {
            'immediate': [],
            'short_term': [],  # 1-3 months
            'medium_term': [],  # 3-12 months
            'long_term': []     # 12+ months
        }
        
        for rec in recommendations:
            priority = rec.get('priority', 'medium')
            cost = rec.get('implementation_cost', 0)
            
            if priority == 'critical':
                timeline['immediate'].append(rec['recommendation_id'])
            elif cost < 5000:
                timeline['short_term'].append(rec['recommendation_id'])
            elif cost < 20000:
                timeline['medium_term'].append(rec['recommendation_id'])
            else:
                timeline['long_term'].append(rec['recommendation_id'])
        
        return timeline


# Convenience functions
async def optimize_treatment_system(system_state: Dict[str, Any]) -> Dict[str, Any]:
    """Optimize treatment system performance."""
    agent = TreatmentOptimizationAgent()
    return await agent.analyze_treatment_system(system_state)


async def get_optimization_recommendations(system_state: Dict[str, Any],
                                         objectives: List[str] = None) -> Dict[str, Any]:
    """Get optimization recommendations for treatment system."""
    agent = TreatmentOptimizationAgent()
    
    if objectives:
        obj_enums = [OptimizationObjective(obj) for obj in objectives]
    else:
        obj_enums = None
    
    return await agent.generate_optimization_recommendations(system_state, obj_enums)
