"""
Climate Data Ingestion Manager.

This module orchestrates the complete climate data ingestion pipeline,
coordinating data collection, preprocessing, normalization, and storage.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import json
from dataclasses import dataclass, asdict

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call

# Data collectors
from src.data.collectors.openweather_collector import OpenWeatherMapCollector
from src.data.collectors.nasa_collector import NASAClimateCollector
from src.data.collectors.worldbank_collector import WorldBankClimateCollector
from src.data.collectors.noaa_collector import NOAAClimateCollector

# Data processors
from src.data.preprocessing.climate_preprocessor import ClimateDataPreprocessor, ProcessedClimateData
from src.data.preprocessing.data_normalizer import ClimateDataNormalizer, NormalizationConfig

logger = logging.getLogger(__name__)


@dataclass
class IngestionConfig:
    """Configuration for climate data ingestion."""
    sources: List[str] = None  # ['openweathermap', 'nasa', 'worldbank', 'noaa']
    locations: List[Dict[str, Any]] = None
    time_range: Dict[str, str] = None  # {'start': '2024-01-01', 'end': '2024-01-31'}
    preprocessing_enabled: bool = True
    normalization_enabled: bool = True
    normalization_method: str = 'standard'
    cache_results: bool = True
    cache_ttl: int = 3600
    batch_size: int = 10
    max_concurrent_requests: int = 5
    retry_attempts: int = 3
    retry_delay: float = 1.0
    
    def __post_init__(self):
        if self.sources is None:
            self.sources = ['openweathermap', 'nasa', 'worldbank', 'noaa']
        if self.locations is None:
            self.locations = [
                {"name": "New York", "lat": 40.7128, "lon": -74.0060},
                {"name": "London", "lat": 51.5074, "lon": -0.1278},
                {"name": "Tokyo", "lat": 35.6762, "lon": 139.6503}
            ]
        if self.time_range is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            self.time_range = {'start': start_date, 'end': end_date}


@dataclass
class IngestionResult:
    """Result of climate data ingestion operation."""
    success: bool
    total_records: int
    processed_records: int
    normalized_records: int
    errors: List[str]
    warnings: List[str]
    execution_time: float
    sources_used: List[str]
    locations_processed: List[str]
    data_quality_score: float
    metadata: Dict[str, Any]
    timestamp: datetime


class ClimateDataIngestionManager:
    """
    Comprehensive climate data ingestion manager.
    
    Orchestrates the complete pipeline:
    1. Data collection from multiple sources
    2. Data preprocessing and validation
    3. Data normalization and standardization
    4. Data storage and caching
    5. Quality assessment and reporting
    """
    
    def __init__(self, config: IngestionConfig = None):
        self.settings = get_settings()
        self.config = config or IngestionConfig()
        self.is_initialized = False
        
        # Data collectors
        self.collectors = {}
        
        # Data processors
        self.preprocessor = None
        self.normalizer = None
        
        # Semaphore for concurrent request limiting
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
        
        # Statistics tracking
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_records_processed': 0,
            'average_quality_score': 0.0
        }
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the ingestion manager and all components."""
        try:
            logger.info("Initializing Climate Data Ingestion Manager...")
            
            # Initialize collectors based on configuration
            if 'openweathermap' in self.config.sources:
                self.collectors['openweathermap'] = OpenWeatherMapCollector()
                await self.collectors['openweathermap'].initialize()
            
            if 'nasa' in self.config.sources:
                self.collectors['nasa'] = NASAClimateCollector()
                await self.collectors['nasa'].initialize()
            
            if 'worldbank' in self.config.sources:
                self.collectors['worldbank'] = WorldBankClimateCollector()
                await self.collectors['worldbank'].initialize()
            
            if 'noaa' in self.config.sources:
                self.collectors['noaa'] = NOAAClimateCollector()
                await self.collectors['noaa'].initialize()
            
            # Initialize preprocessor
            if self.config.preprocessing_enabled:
                self.preprocessor = ClimateDataPreprocessor()
                await self.preprocessor.initialize()
            
            # Initialize normalizer
            if self.config.normalization_enabled:
                norm_config = NormalizationConfig(method=self.config.normalization_method)
                self.normalizer = ClimateDataNormalizer(norm_config)
                await self.normalizer.initialize()
            
            self.is_initialized = True
            logger.info(f"Ingestion manager initialized with {len(self.collectors)} collectors")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ingestion manager: {e}")
            return False
    
    async def ingest_climate_data(self) -> IngestionResult:
        """Execute complete climate data ingestion pipeline."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if not self.is_initialized:
                await self.initialize()
            
            logger.info("Starting climate data ingestion pipeline...")
            
            # Initialize result tracking
            all_data = []
            errors = []
            warnings = []
            sources_used = []
            locations_processed = []
            
            # Collect data from all sources and locations
            for source in self.config.sources:
                if source not in self.collectors:
                    warnings.append(f"Collector for {source} not available")
                    continue
                
                try:
                    source_data = await self._collect_from_source(source)
                    if source_data:
                        all_data.extend(source_data)
                        sources_used.append(source)
                        logger.info(f"Collected {len(source_data)} records from {source}")
                    else:
                        warnings.append(f"No data collected from {source}")
                        
                except Exception as e:
                    error_msg = f"Failed to collect from {source}: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            # Track locations processed
            locations_processed = [loc["name"] for loc in self.config.locations]
            
            # Process collected data
            processed_data = []
            if all_data and self.config.preprocessing_enabled:
                try:
                    processed_data = await self._process_raw_data(all_data)
                    logger.info(f"Processed {len(processed_data)} records")
                except Exception as e:
                    error_msg = f"Data processing failed: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            else:
                processed_data = all_data
            
            # Normalize processed data
            normalized_data = []
            if processed_data and self.config.normalization_enabled:
                try:
                    normalized_data, norm_metadata = await self._normalize_data(processed_data)
                    logger.info(f"Normalized {len(normalized_data)} records")
                except Exception as e:
                    error_msg = f"Data normalization failed: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                    normalized_data = processed_data
            else:
                normalized_data = processed_data
            
            # Calculate quality metrics
            quality_score = self._calculate_overall_quality(normalized_data)
            
            # Cache results if enabled
            if self.config.cache_results and normalized_data:
                await self._cache_results(normalized_data)
            
            # Calculate execution time
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Create result
            result = IngestionResult(
                success=len(errors) == 0,
                total_records=len(all_data),
                processed_records=len(processed_data),
                normalized_records=len(normalized_data),
                errors=errors,
                warnings=warnings,
                execution_time=execution_time,
                sources_used=sources_used,
                locations_processed=locations_processed,
                data_quality_score=quality_score,
                metadata={
                    'config': asdict(self.config),
                    'stats': self.stats.copy()
                },
                timestamp=datetime.now()
            )
            
            # Update statistics
            self._update_stats(result)
            
            logger.info(f"Ingestion completed: {result.total_records} records, quality: {quality_score:.2f}")
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Ingestion pipeline failed: {e}")
            
            return IngestionResult(
                success=False,
                total_records=0,
                processed_records=0,
                normalized_records=0,
                errors=[str(e)],
                warnings=[],
                execution_time=execution_time,
                sources_used=[],
                locations_processed=[],
                data_quality_score=0.0,
                metadata={},
                timestamp=datetime.now()
            )
    
    async def _collect_from_source(self, source: str) -> List[Any]:
        """Collect data from a specific source."""
        collector = self.collectors.get(source)
        if not collector:
            return []
        
        collected_data = []
        
        try:
            if source == 'openweathermap':
                # Collect current weather for all locations
                for location in self.config.locations:
                    async with self.semaphore:
                        try:
                            data = await collector.get_current_weather(location)
                            if data:
                                collected_data.append(data)
                            await asyncio.sleep(0.1)  # Rate limiting
                        except Exception as e:
                            logger.warning(f"Failed to get OpenWeatherMap data for {location['name']}: {e}")
            
            elif source == 'nasa':
                # Collect NASA climate data for all locations
                for location in self.config.locations:
                    async with self.semaphore:
                        try:
                            summary = await collector.get_climate_summary(
                                location["lat"], location["lon"], location["name"]
                            )
                            if summary:
                                collected_data.append(summary)
                            await asyncio.sleep(0.2)  # Rate limiting
                        except Exception as e:
                            logger.warning(f"Failed to get NASA data for {location['name']}: {e}")
            
            elif source == 'worldbank':
                # Collect World Bank climate indicators
                try:
                    summary = await collector.get_climate_indicators_summary()
                    if summary:
                        collected_data.append(summary)
                except Exception as e:
                    logger.warning(f"Failed to get World Bank data: {e}")
            
            elif source == 'noaa':
                # Collect NOAA climate data for all locations
                for location in self.config.locations:
                    async with self.semaphore:
                        try:
                            summary = await collector.get_climate_summary_for_location(
                                location["lat"], location["lon"], location["name"]
                            )
                            if summary and not summary.get('error'):
                                collected_data.append(summary)
                            await asyncio.sleep(0.3)  # Rate limiting
                        except Exception as e:
                            logger.warning(f"Failed to get NOAA data for {location['name']}: {e}")
            
            self.stats['successful_requests'] += 1
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"Collection from {source} failed: {e}")
        
        finally:
            self.stats['total_requests'] += 1
        
        return collected_data
    
    async def _process_raw_data(self, raw_data: List[Any]) -> List[ProcessedClimateData]:
        """Process raw data through preprocessing pipeline."""
        processed_data = []
        
        for item in raw_data:
            try:
                # Determine source type and process accordingly
                if hasattr(item, 'source'):
                    source = item.source
                elif isinstance(item, dict):
                    source = item.get('source', 'unknown')
                else:
                    source = 'unknown'
                
                # Process based on source
                if source == 'openweathermap' or hasattr(item, 'weather_description'):
                    # Already processed OpenWeatherMap data
                    if isinstance(item, ProcessedClimateData):
                        processed_data.append(item)
                    else:
                        # Convert to ProcessedClimateData if needed
                        processed = await self._convert_to_processed_data(item, 'openweathermap')
                        if processed:
                            processed_data.append(processed)
                
                elif source in ['nasa', 'worldbank', 'noaa'] or isinstance(item, dict):
                    # Process other data types
                    processed = await self._convert_to_processed_data(item, source)
                    if processed:
                        processed_data.append(processed)
                
            except Exception as e:
                logger.warning(f"Failed to process data item: {e}")
        
        return processed_data
    
    async def _convert_to_processed_data(self, item: Any, source: str) -> Optional[ProcessedClimateData]:
        """Convert raw data item to ProcessedClimateData."""
        try:
            if isinstance(item, ProcessedClimateData):
                return item
            
            # Create basic ProcessedClimateData structure
            processed = ProcessedClimateData(
                timestamp=datetime.now(),
                location="Unknown",
                latitude=0.0,
                longitude=0.0,
                source=source
            )
            
            # Extract data based on source type
            if isinstance(item, dict):
                processed.location = item.get('location', 'Unknown')
                processed.latitude = item.get('latitude', 0.0)
                processed.longitude = item.get('longitude', 0.0)
                
                # Extract climate parameters if available
                if 'temperature' in item:
                    processed.temperature = item['temperature']
                if 'humidity' in item:
                    processed.humidity = item['humidity']
                if 'pressure' in item:
                    processed.pressure = item['pressure']
                if 'wind_speed' in item:
                    processed.wind_speed = item['wind_speed']
                if 'precipitation' in item:
                    processed.precipitation = item['precipitation']
            
            return processed
            
        except Exception as e:
            logger.warning(f"Failed to convert data to ProcessedClimateData: {e}")
            return None
    
    async def _normalize_data(self, data: List[ProcessedClimateData]) -> Tuple[List[ProcessedClimateData], Any]:
        """Normalize processed data."""
        if not self.normalizer:
            return data, None
        
        try:
            normalized_data, metadata = await self.normalizer.normalize_climate_data_list(data)
            return normalized_data, metadata
        except Exception as e:
            logger.error(f"Normalization failed: {e}")
            return data, None
    
    def _calculate_overall_quality(self, data: List[ProcessedClimateData]) -> float:
        """Calculate overall data quality score."""
        if not data:
            return 0.0
        
        total_score = sum(item.data_quality_score for item in data if hasattr(item, 'data_quality_score'))
        return total_score / len(data)
    
    async def _cache_results(self, data: List[ProcessedClimateData]):
        """Cache ingestion results."""
        try:
            cache_key = f"climate_ingestion:{datetime.now().strftime('%Y%m%d_%H')}"
            
            # Convert to serializable format
            serializable_data = []
            for item in data[:100]:  # Limit cache size
                item_dict = asdict(item)
                item_dict['timestamp'] = item.timestamp.isoformat()
                serializable_data.append(item_dict)
            
            await cache_set(cache_key, serializable_data, ttl=self.config.cache_ttl)
            logger.debug(f"Cached {len(serializable_data)} records")
            
        except Exception as e:
            logger.warning(f"Failed to cache results: {e}")
    
    def _update_stats(self, result: IngestionResult):
        """Update ingestion statistics."""
        self.stats['total_records_processed'] += result.total_records
        
        # Update average quality score
        if result.data_quality_score > 0:
            current_avg = self.stats['average_quality_score']
            total_ops = self.stats['successful_requests']
            
            if total_ops > 0:
                self.stats['average_quality_score'] = (
                    (current_avg * (total_ops - 1) + result.data_quality_score) / total_ops
                )
            else:
                self.stats['average_quality_score'] = result.data_quality_score
    
    async def get_ingestion_stats(self) -> Dict[str, Any]:
        """Get ingestion statistics."""
        return {
            'stats': self.stats.copy(),
            'config': asdict(self.config),
            'collectors_available': list(self.collectors.keys()),
            'is_initialized': self.is_initialized,
            'timestamp': datetime.now().isoformat()
        }
    
    async def shutdown(self):
        """Shutdown the ingestion manager and all collectors."""
        try:
            for collector in self.collectors.values():
                if hasattr(collector, 'shutdown'):
                    await collector.shutdown()
            
            logger.info("Climate Data Ingestion Manager shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during ingestion manager shutdown: {e}")


# Convenience functions
async def ingest_climate_data_for_locations(locations: List[Dict[str, Any]], 
                                          sources: List[str] = None) -> IngestionResult:
    """Ingest climate data for specific locations."""
    config = IngestionConfig(
        sources=sources or ['openweathermap', 'nasa'],
        locations=locations
    )
    
    manager = ClimateDataIngestionManager(config)
    result = await manager.ingest_climate_data()
    await manager.shutdown()
    
    return result


async def ingest_global_climate_data() -> IngestionResult:
    """Ingest climate data for default global locations."""
    manager = ClimateDataIngestionManager()
    result = await manager.ingest_climate_data()
    await manager.shutdown()

    return result


async def ingest_historical_climate_data(start_date: str, end_date: str,
                                       locations: List[Dict[str, Any]] = None) -> IngestionResult:
    """Ingest historical climate data for a date range."""
    config = IngestionConfig(
        sources=['nasa', 'worldbank', 'noaa'],  # Sources with historical data
        locations=locations,
        time_range={'start': start_date, 'end': end_date}
    )

    manager = ClimateDataIngestionManager(config)
    result = await manager.ingest_climate_data()
    await manager.shutdown()

    return result
