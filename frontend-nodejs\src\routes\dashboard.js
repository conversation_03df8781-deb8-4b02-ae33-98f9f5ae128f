/**
 * Dashboard Routes
 * Handles serving the main dashboard pages and components
 */

const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const router = express.Router();

// Dashboard page renderer
class DashboardRenderer {
  constructor() {
    this.templateCache = new Map();
  }

  async loadTemplate(templateName) {
    if (this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName);
    }

    try {
      const templatePath = path.join(__dirname, '../templates', `${templateName}.html`);
      const template = await fs.readFile(templatePath, 'utf8');
      this.templateCache.set(templateName, template);
      return template;
    } catch (error) {
      console.error(`Error loading template ${templateName}:`, error);
      return this.getDefaultTemplate(templateName);
    }
  }

  getDefaultTemplate(templateName) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Management Dashboard - ${templateName}</title>
    <link rel="stylesheet" href="/css/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div id="app">
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading ${templateName} dashboard...</p>
        </div>
    </div>
    <script src="/js/main.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            window.WaterManagementApp.init('${templateName}');
        });
    </script>
</body>
</html>`;
  }

  async renderPage(templateName, data = {}) {
    const template = await this.loadTemplate(templateName);
    
    // Simple template variable replacement
    let rendered = template;
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      rendered = rendered.replace(regex, value);
    }
    
    return rendered;
  }
}

const renderer = new DashboardRenderer();

// Main dashboard route
router.get('/', async (req, res) => {
  try {
    const html = await renderer.renderPage('main', {
      title: 'Water Management Dashboard',
      page: 'overview'
    });
    res.send(html);
  } catch (error) {
    console.error('Error rendering main dashboard:', error);
    res.status(500).send('Error loading dashboard');
  }
});

// Individual dashboard pages
const dashboardPages = [
  'water-quality',
  'treatment-systems', 
  'energy-grid',
  'ai-agents',
  'ml-optimization',
  'workflow-orchestration',
  'knowledge-graphs',
  'llm-integration',
  'climate-impact',
  'sensors',
  'analytics',
  'reports-dashboard',
  'system-management',
  'advanced-ai-dashboard',
  'digital-twin-dashboard',
  'blockchain-dashboard',
  'predictive-maintenance-dashboard'
];

dashboardPages.forEach(page => {
  router.get(`/${page}`, async (req, res) => {
    try {
      const html = await renderer.renderPage('main', {
        title: `Water Management - ${page.replace('-', ' ').toUpperCase()}`,
        page: page
      });
      res.send(html);
    } catch (error) {
      console.error(`Error rendering ${page} page:`, error);
      res.status(500).send(`Error loading ${page} page`);
    }
  });
});

// API endpoint for page content (AJAX loading)
router.get('/api/page/:pageName', async (req, res) => {
  const { pageName } = req.params;
  
  try {
    const contentPath = path.join(__dirname, '../components/pages', `${pageName}.html`);
    const content = await fs.readFile(contentPath, 'utf8');
    
    res.json({
      status: 'success',
      page: pageName,
      content: content,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`Error loading page content for ${pageName}:`, error);
    res.status(404).json({
      status: 'error',
      message: `Page content not found: ${pageName}`,
      error: error.message
    });
  }
});

// Health check for frontend
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'water-management-frontend',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: require('../../package.json').version
  });
});

module.exports = router;
