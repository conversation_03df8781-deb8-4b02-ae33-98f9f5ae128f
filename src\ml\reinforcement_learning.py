"""Reinforcement Learning Models for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class RLAlgorithm(Enum):
    """Reinforcement learning algorithms."""
    DQN = "deep_q_network"
    PPO = "proximal_policy_optimization"
    A3C = "asynchronous_advantage_actor_critic"
    DDPG = "deep_deterministic_policy_gradient"
    SAC = "soft_actor_critic"


class ActionSpace(Enum):
    """Action space types."""
    DISCRETE = "discrete"
    CONTINUOUS = "continuous"
    MIXED = "mixed"


@dataclass
class RLEnvironment:
    """Reinforcement learning environment configuration."""
    env_id: str
    state_dim: int
    action_dim: int
    action_space: ActionSpace
    reward_range: Tuple[float, float]
    max_episode_steps: int
    observation_space: Dict[str, Any]


@dataclass
class RLAgent:
    """Reinforcement learning agent."""
    agent_id: str
    algorithm: RLAlgorithm
    environment: RLEnvironment
    model: nn.Module
    optimizer: optim.Optimizer
    training_episodes: int = 0
    total_reward: float = 0.0
    best_reward: float = float('-inf')
    last_updated: datetime = field(default_factory=datetime.now)


class WaterTreatmentEnvironment:
    """Water treatment RL environment."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.state_dim = 20  # pH, turbidity, flow rate, etc.
        self.action_dim = 10  # Chemical dosing, pump speeds, etc.
        self.max_steps = 1000
        self.current_step = 0
        self.state = None
        self.target_quality = config.get('target_quality', 0.95)
        
        # Initialize state bounds
        self.state_bounds = {
            'ph': (6.0, 9.0),
            'turbidity': (0.0, 10.0),
            'flow_rate': (500.0, 2000.0),
            'energy_consumption': (100.0, 1000.0),
            'chemical_usage': (0.0, 100.0)
        }
        
        # Initialize action bounds
        self.action_bounds = {
            'coagulant_dose': (0.0, 50.0),
            'chlorine_dose': (0.0, 5.0),
            'pump_speed': (0.3, 1.0),
            'ph_adjustment': (-2.0, 2.0)
        }
    
    def reset(self) -> np.ndarray:
        """Reset environment to initial state."""
        self.current_step = 0
        
        # Initialize random state within bounds
        self.state = np.array([
            np.random.uniform(6.5, 8.5),  # pH
            np.random.uniform(1.0, 5.0),  # turbidity
            np.random.uniform(800.0, 1200.0),  # flow_rate
            np.random.uniform(200.0, 400.0),  # energy_consumption
            np.random.uniform(10.0, 30.0),  # chemical_usage
            # Add more state variables...
        ] + [np.random.uniform(-1, 1) for _ in range(15)])  # Additional features
        
        return self.state.copy()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """Execute action and return next state, reward, done, info."""
        self.current_step += 1
        
        # Apply action to environment
        self._apply_action(action)
        
        # Calculate reward
        reward = self._calculate_reward()
        
        # Check if episode is done
        done = (self.current_step >= self.max_steps or 
                self._is_terminal_state())
        
        # Additional info
        info = {
            'step': self.current_step,
            'water_quality': self._calculate_water_quality(),
            'energy_efficiency': self._calculate_energy_efficiency(),
            'cost': self._calculate_cost()
        }
        
        return self.state.copy(), reward, done, info
    
    def _apply_action(self, action: np.ndarray):
        """Apply action to update environment state."""
        # Normalize actions to valid ranges
        coagulant_dose = np.clip(action[0] * 25 + 25, 0, 50)
        chlorine_dose = np.clip(action[1] * 2.5 + 2.5, 0, 5)
        pump_speed = np.clip(action[2] * 0.35 + 0.65, 0.3, 1.0)
        ph_adjustment = np.clip(action[3] * 1.0, -2.0, 2.0)
        
        # Update state based on actions
        # pH dynamics
        self.state[0] += ph_adjustment * 0.1 + np.random.normal(0, 0.05)
        self.state[0] = np.clip(self.state[0], 6.0, 9.0)
        
        # Turbidity dynamics (affected by coagulant)
        turbidity_reduction = coagulant_dose * 0.1
        self.state[1] = max(0, self.state[1] - turbidity_reduction + np.random.normal(0, 0.1))
        
        # Flow rate dynamics (affected by pump speed)
        target_flow = pump_speed * 1500
        self.state[2] += (target_flow - self.state[2]) * 0.1 + np.random.normal(0, 10)
        self.state[2] = np.clip(self.state[2], 500, 2000)
        
        # Energy consumption
        self.state[3] = pump_speed * 300 + coagulant_dose * 2 + np.random.normal(0, 5)
        
        # Chemical usage
        self.state[4] = coagulant_dose + chlorine_dose + np.random.normal(0, 1)
    
    def _calculate_reward(self) -> float:
        """Calculate reward based on current state."""
        # Water quality reward
        quality_score = self._calculate_water_quality()
        quality_reward = quality_score * 10
        
        # Energy efficiency reward
        energy_efficiency = self._calculate_energy_efficiency()
        energy_reward = energy_efficiency * 5
        
        # Cost penalty
        cost = self._calculate_cost()
        cost_penalty = -cost * 0.1
        
        # Compliance bonus
        compliance_bonus = 5 if self._is_compliant() else -10
        
        # Stability reward
        stability_reward = 2 if self._is_stable() else -2
        
        total_reward = (quality_reward + energy_reward + cost_penalty + 
                       compliance_bonus + stability_reward)
        
        return total_reward
    
    def _calculate_water_quality(self) -> float:
        """Calculate water quality score (0-1)."""
        ph_score = 1.0 if 6.5 <= self.state[0] <= 8.5 else 0.5
        turbidity_score = max(0, 1.0 - self.state[1] / 10.0)
        
        return (ph_score + turbidity_score) / 2
    
    def _calculate_energy_efficiency(self) -> float:
        """Calculate energy efficiency score (0-1)."""
        energy_per_volume = self.state[3] / max(self.state[2], 1)
        target_efficiency = 0.3  # kWh/m³
        
        if energy_per_volume <= target_efficiency:
            return 1.0
        else:
            return max(0, 1.0 - (energy_per_volume - target_efficiency) / target_efficiency)
    
    def _calculate_cost(self) -> float:
        """Calculate operational cost."""
        energy_cost = self.state[3] * 0.1  # $0.1 per kWh
        chemical_cost = self.state[4] * 0.5  # $0.5 per kg
        
        return energy_cost + chemical_cost
    
    def _is_compliant(self) -> bool:
        """Check if current state meets compliance requirements."""
        return (6.5 <= self.state[0] <= 8.5 and  # pH
                self.state[1] <= 4.0)  # turbidity
    
    def _is_stable(self) -> bool:
        """Check if system is in stable operating condition."""
        return (abs(self.state[0] - 7.2) < 0.5 and  # pH near optimal
                self.state[1] < 2.0)  # Low turbidity
    
    def _is_terminal_state(self) -> bool:
        """Check if state is terminal (unsafe)."""
        return (self.state[0] < 6.0 or self.state[0] > 9.0 or  # pH out of safe range
                self.state[1] > 15.0)  # Extremely high turbidity


class DQNNetwork(nn.Module):
    """Deep Q-Network for water treatment optimization."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(DQNNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, action_dim)
        )
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """Forward pass through network."""
        return self.network(state)


class PPONetwork(nn.Module):
    """Proximal Policy Optimization network."""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super(PPONetwork, self).__init__()
        
        # Shared feature extractor
        self.shared_layers = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Policy head
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, action_dim),
            nn.Tanh()  # Actions in [-1, 1]
        )
        
        # Value head
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning policy and value."""
        features = self.shared_layers(state)
        policy = self.policy_head(features)
        value = self.value_head(features)
        
        return policy, value


class ReinforcementLearningSystem:
    """Comprehensive RL system for water treatment optimization."""
    
    def __init__(self):
        self.environments: Dict[str, WaterTreatmentEnvironment] = {}
        self.agents: Dict[str, RLAgent] = {}
        self.training_history: List[Dict[str, Any]] = []
        
        # Training configuration
        self.config = {
            'learning_rate': 0.001,
            'batch_size': 64,
            'memory_size': 10000,
            'epsilon_start': 1.0,
            'epsilon_end': 0.01,
            'epsilon_decay': 0.995,
            'target_update_freq': 100,
            'gamma': 0.99
        }
    
    @log_async_function_call
    async def create_environment(self, env_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create new RL environment."""
        try:
            env_id = env_config.get('env_id', f"env_{len(self.environments)}")
            
            environment = WaterTreatmentEnvironment(env_config)
            self.environments[env_id] = environment
            
            return {
                'status': 'success',
                'env_id': env_id,
                'state_dim': environment.state_dim,
                'action_dim': environment.action_dim,
                'max_steps': environment.max_steps
            }
            
        except Exception as e:
            logger.error(f"Environment creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def create_agent(self, agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create new RL agent."""
        try:
            agent_id = agent_config['agent_id']
            algorithm = RLAlgorithm(agent_config['algorithm'])
            env_id = agent_config['env_id']
            
            if env_id not in self.environments:
                return {'status': 'error', 'error': 'Environment not found'}
            
            environment = self.environments[env_id]
            
            # Create model based on algorithm
            if algorithm == RLAlgorithm.DQN:
                model = DQNNetwork(environment.state_dim, environment.action_dim)
            elif algorithm == RLAlgorithm.PPO:
                model = PPONetwork(environment.state_dim, environment.action_dim)
            else:
                model = DQNNetwork(environment.state_dim, environment.action_dim)  # Default
            
            optimizer = optim.Adam(model.parameters(), lr=self.config['learning_rate'])
            
            # Create environment wrapper
            env_wrapper = RLEnvironment(
                env_id=env_id,
                state_dim=environment.state_dim,
                action_dim=environment.action_dim,
                action_space=ActionSpace.CONTINUOUS,
                reward_range=(-100.0, 100.0),
                max_episode_steps=environment.max_steps,
                observation_space={'type': 'continuous', 'shape': (environment.state_dim,)}
            )
            
            agent = RLAgent(
                agent_id=agent_id,
                algorithm=algorithm,
                environment=env_wrapper,
                model=model,
                optimizer=optimizer
            )
            
            self.agents[agent_id] = agent
            
            return {
                'status': 'success',
                'agent_id': agent_id,
                'algorithm': algorithm.value,
                'model_parameters': sum(p.numel() for p in model.parameters()),
                'environment': env_id
            }
            
        except Exception as e:
            logger.error(f"Agent creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def train_agent(self, agent_id: str, episodes: int = 1000) -> Dict[str, Any]:
        """Train RL agent."""
        try:
            if agent_id not in self.agents:
                return {'status': 'error', 'error': 'Agent not found'}
            
            agent = self.agents[agent_id]
            environment = self.environments[agent.environment.env_id]
            
            training_rewards = []
            episode_lengths = []
            
            for episode in range(episodes):
                state = environment.reset()
                episode_reward = 0
                episode_length = 0
                done = False
                
                while not done:
                    # Select action
                    action = await self._select_action(agent, state)
                    
                    # Execute action
                    next_state, reward, done, info = environment.step(action)
                    
                    # Store experience (simplified)
                    episode_reward += reward
                    episode_length += 1
                    
                    state = next_state
                
                training_rewards.append(episode_reward)
                episode_lengths.append(episode_length)
                
                # Update agent
                if episode % 10 == 0:
                    await self._update_agent(agent, training_rewards[-10:])
                
                # Log progress
                if episode % 100 == 0:
                    avg_reward = np.mean(training_rewards[-100:])
                    logger.info(f"Episode {episode}, Average Reward: {avg_reward:.2f}")
            
            # Update agent statistics
            agent.training_episodes += episodes
            agent.total_reward = sum(training_rewards)
            agent.best_reward = max(training_rewards)
            agent.last_updated = datetime.now()
            
            # Store training history
            training_record = {
                'agent_id': agent_id,
                'episodes': episodes,
                'total_reward': sum(training_rewards),
                'average_reward': np.mean(training_rewards),
                'best_reward': max(training_rewards),
                'final_episode_length': episode_lengths[-1],
                'training_date': datetime.now().isoformat()
            }
            
            self.training_history.append(training_record)
            
            return {
                'status': 'success',
                'training_summary': {
                    'episodes_completed': episodes,
                    'total_reward': sum(training_rewards),
                    'average_reward': np.mean(training_rewards),
                    'best_reward': max(training_rewards),
                    'final_performance': training_rewards[-10:],
                    'convergence_episode': self._find_convergence_episode(training_rewards)
                },
                'agent_stats': {
                    'total_training_episodes': agent.training_episodes,
                    'lifetime_reward': agent.total_reward,
                    'best_lifetime_reward': agent.best_reward
                }
            }
            
        except Exception as e:
            logger.error(f"Agent training failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _select_action(self, agent: RLAgent, state: np.ndarray) -> np.ndarray:
        """Select action using agent's policy."""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        
        if agent.algorithm == RLAlgorithm.DQN:
            # Epsilon-greedy action selection
            epsilon = max(self.config['epsilon_end'], 
                         self.config['epsilon_start'] * (self.config['epsilon_decay'] ** agent.training_episodes))
            
            if np.random.random() < epsilon:
                # Random action
                return np.random.uniform(-1, 1, agent.environment.action_dim)
            else:
                # Greedy action
                with torch.no_grad():
                    q_values = agent.model(state_tensor)
                    action_idx = q_values.argmax().item()
                    # Convert discrete action to continuous
                    action = np.zeros(agent.environment.action_dim)
                    action[action_idx % agent.environment.action_dim] = 1.0
                    return action
        
        elif agent.algorithm == RLAlgorithm.PPO:
            with torch.no_grad():
                policy, _ = agent.model(state_tensor)
                # Add exploration noise
                noise = torch.normal(0, 0.1, policy.shape)
                action = torch.clamp(policy + noise, -1, 1)
                return action.squeeze().numpy()
        
        else:
            # Default random action
            return np.random.uniform(-1, 1, agent.environment.action_dim)
    
    async def _update_agent(self, agent: RLAgent, recent_rewards: List[float]):
        """Update agent's model (simplified)."""
        # This is a simplified update - in practice would use proper RL algorithms
        if len(recent_rewards) < 5:
            return
        
        # Simple gradient update based on reward trend
        reward_trend = np.mean(recent_rewards[-5:]) - np.mean(recent_rewards[:-5])
        
        if reward_trend > 0:
            # Positive trend - small update
            for param in agent.model.parameters():
                if param.grad is not None:
                    param.data += 0.001 * param.grad
    
    def _find_convergence_episode(self, rewards: List[float]) -> int:
        """Find episode where training converged."""
        if len(rewards) < 100:
            return len(rewards)
        
        # Look for stability in last 100 episodes
        window_size = 50
        for i in range(len(rewards) - window_size):
            window_std = np.std(rewards[i:i+window_size])
            if window_std < 5.0:  # Stable performance
                return i
        
        return len(rewards)


# Convenience functions
async def create_rl_environment(config: Dict[str, Any]) -> Dict[str, Any]:
    """Create RL environment for water treatment."""
    system = ReinforcementLearningSystem()
    return await system.create_environment(config)


async def train_water_treatment_agent(agent_config: Dict[str, Any], 
                                    episodes: int = 1000) -> Dict[str, Any]:
    """Train RL agent for water treatment optimization."""
    system = ReinforcementLearningSystem()
    
    # Create environment
    env_result = await system.create_environment(agent_config)
    if env_result['status'] != 'success':
        return env_result
    
    # Create agent
    agent_config['env_id'] = env_result['env_id']
    agent_result = await system.create_agent(agent_config)
    if agent_result['status'] != 'success':
        return agent_result
    
    # Train agent
    return await system.train_agent(agent_result['agent_id'], episodes)
