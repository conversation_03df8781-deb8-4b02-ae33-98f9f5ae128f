"""Real-Time Data Streaming System for Water Management."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
import json
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class StreamType(Enum):
    """Types of data streams."""
    SENSOR_DATA = "sensor_data"
    WEATHER_DATA = "weather_data"
    FLOW_DATA = "flow_data"
    QUALITY_DATA = "quality_data"
    ENERGY_DATA = "energy_data"
    ALERT_DATA = "alert_data"


class StreamStatus(Enum):
    """Stream status states."""
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


@dataclass
class StreamMessage:
    """Individual stream message."""
    message_id: str
    stream_id: str
    stream_type: StreamType
    data: Dict[str, Any]
    timestamp: datetime
    source: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataStream:
    """Data stream configuration."""
    stream_id: str
    stream_type: StreamType
    source: str
    description: str
    status: StreamStatus
    message_count: int = 0
    error_count: int = 0
    last_message_time: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    subscribers: List[str] = field(default_factory=list)


class RealTimeStreamingSystem:
    """Real-time data streaming system for water management."""
    
    def __init__(self):
        self.streams: Dict[str, DataStream] = {}
        self.message_handlers: Dict[str, List[Callable]] = {}
        self.message_buffer: Dict[str, List[StreamMessage]] = {}
        self.stream_processors: Dict[str, Callable] = {}
        
        # Streaming configuration
        self.config = {
            'buffer_size': 1000,
            'batch_size': 10,
            'processing_interval': 1.0,  # seconds
            'retention_hours': 24,
            'max_error_rate': 0.1
        }
        
        # Initialize system
        self._initialize_streams()
        self._start_processing_loop()
    
    def _initialize_streams(self):
        """Initialize default data streams."""
        default_streams = [
            {
                'stream_id': 'sensor_stream_001',
                'stream_type': StreamType.SENSOR_DATA,
                'source': 'treatment_plant_sensors',
                'description': 'Real-time sensor data from treatment plant'
            },
            {
                'stream_id': 'weather_stream_001',
                'stream_type': StreamType.WEATHER_DATA,
                'source': 'weather_api',
                'description': 'Real-time weather data'
            },
            {
                'stream_id': 'flow_stream_001',
                'stream_type': StreamType.FLOW_DATA,
                'source': 'flow_meters',
                'description': 'Real-time flow rate data'
            },
            {
                'stream_id': 'quality_stream_001',
                'stream_type': StreamType.QUALITY_DATA,
                'source': 'quality_sensors',
                'description': 'Real-time water quality data'
            },
            {
                'stream_id': 'energy_stream_001',
                'stream_type': StreamType.ENERGY_DATA,
                'source': 'energy_meters',
                'description': 'Real-time energy consumption data'
            }
        ]
        
        for stream_config in default_streams:
            stream = DataStream(
                stream_id=stream_config['stream_id'],
                stream_type=stream_config['stream_type'],
                source=stream_config['source'],
                description=stream_config['description'],
                status=StreamStatus.ACTIVE
            )
            
            self.streams[stream_config['stream_id']] = stream
            self.message_buffer[stream_config['stream_id']] = []
            self.message_handlers[stream_config['stream_id']] = []
    
    def _start_processing_loop(self):
        """Start the message processing loop."""
        asyncio.create_task(self._process_messages_loop())
    
    async def _process_messages_loop(self):
        """Continuous message processing loop."""
        while True:
            try:
                await self._process_buffered_messages()
                await asyncio.sleep(self.config['processing_interval'])
            except Exception as e:
                logger.error(f"Message processing loop error: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    @log_async_function_call
    async def create_stream(self, stream_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create new data stream."""
        try:
            stream_id = stream_config.get('stream_id') or f"stream_{uuid.uuid4().hex[:8]}"
            
            if stream_id in self.streams:
                return {'status': 'error', 'error': 'Stream already exists'}
            
            stream = DataStream(
                stream_id=stream_id,
                stream_type=StreamType(stream_config['stream_type']),
                source=stream_config['source'],
                description=stream_config.get('description', ''),
                status=StreamStatus.ACTIVE
            )
            
            self.streams[stream_id] = stream
            self.message_buffer[stream_id] = []
            self.message_handlers[stream_id] = []
            
            return {
                'status': 'success',
                'stream_id': stream_id,
                'stream_info': {
                    'stream_type': stream.stream_type.value,
                    'source': stream.source,
                    'description': stream.description,
                    'created_at': stream.created_at.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Stream creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def publish_message(self, stream_id: str, data: Dict[str, Any],
                            source: str = None, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Publish message to stream."""
        try:
            if stream_id not in self.streams:
                return {'status': 'error', 'error': 'Stream not found'}
            
            stream = self.streams[stream_id]
            
            if stream.status != StreamStatus.ACTIVE:
                return {'status': 'error', 'error': f'Stream is {stream.status.value}'}
            
            # Create message
            message_id = f"msg_{uuid.uuid4().hex[:12]}"
            message = StreamMessage(
                message_id=message_id,
                stream_id=stream_id,
                stream_type=stream.stream_type,
                data=data,
                timestamp=datetime.now(),
                source=source or stream.source,
                metadata=metadata or {}
            )
            
            # Add to buffer
            buffer = self.message_buffer[stream_id]
            buffer.append(message)
            
            # Maintain buffer size
            if len(buffer) > self.config['buffer_size']:
                buffer.pop(0)  # Remove oldest message
            
            # Update stream statistics
            stream.message_count += 1
            stream.last_message_time = message.timestamp
            
            # Process message immediately if handlers exist
            await self._process_message(message)
            
            return {
                'status': 'success',
                'message_id': message_id,
                'stream_id': stream_id,
                'timestamp': message.timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Message publishing failed: {e}")
            if stream_id in self.streams:
                self.streams[stream_id].error_count += 1
            return {'status': 'error', 'error': str(e)}
    
    async def _process_message(self, message: StreamMessage):
        """Process individual message."""
        try:
            # Call registered handlers
            handlers = self.message_handlers.get(message.stream_id, [])
            
            for handler in handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(message)
                    else:
                        handler(message)
                except Exception as e:
                    logger.error(f"Message handler error: {e}")
            
            # Call stream processor if exists
            processor = self.stream_processors.get(message.stream_id)
            if processor:
                try:
                    if asyncio.iscoroutinefunction(processor):
                        await processor(message)
                    else:
                        processor(message)
                except Exception as e:
                    logger.error(f"Stream processor error: {e}")
            
        except Exception as e:
            logger.error(f"Message processing failed: {e}")
    
    async def _process_buffered_messages(self):
        """Process messages in batches."""
        for stream_id, buffer in self.message_buffer.items():
            if not buffer:
                continue
            
            # Process in batches
            batch_size = self.config['batch_size']
            
            while len(buffer) >= batch_size:
                batch = buffer[:batch_size]
                
                try:
                    await self._process_message_batch(stream_id, batch)
                    # Remove processed messages
                    for _ in range(batch_size):
                        buffer.pop(0)
                        
                except Exception as e:
                    logger.error(f"Batch processing failed for {stream_id}: {e}")
                    break
    
    async def _process_message_batch(self, stream_id: str, messages: List[StreamMessage]):
        """Process batch of messages."""
        try:
            # Batch processing logic
            stream = self.streams[stream_id]
            
            # Example: Aggregate sensor data
            if stream.stream_type == StreamType.SENSOR_DATA:
                await self._process_sensor_batch(messages)
            elif stream.stream_type == StreamType.QUALITY_DATA:
                await self._process_quality_batch(messages)
            elif stream.stream_type == StreamType.FLOW_DATA:
                await self._process_flow_batch(messages)
            
        except Exception as e:
            logger.error(f"Message batch processing failed: {e}")
    
    async def _process_sensor_batch(self, messages: List[StreamMessage]):
        """Process batch of sensor messages."""
        # Aggregate sensor readings
        aggregated_data = {}
        
        for message in messages:
            data = message.data
            for sensor_id, reading in data.items():
                if sensor_id not in aggregated_data:
                    aggregated_data[sensor_id] = []
                aggregated_data[sensor_id].append(reading)
        
        # Calculate statistics
        sensor_stats = {}
        for sensor_id, readings in aggregated_data.items():
            if isinstance(readings[0], (int, float)):
                sensor_stats[sensor_id] = {
                    'count': len(readings),
                    'avg': sum(readings) / len(readings),
                    'min': min(readings),
                    'max': max(readings)
                }
        
        # Log aggregated data
        logger.info(f"Processed sensor batch: {len(messages)} messages, {len(sensor_stats)} sensors")
    
    async def _process_quality_batch(self, messages: List[StreamMessage]):
        """Process batch of water quality messages."""
        quality_params = ['ph', 'turbidity', 'chlorine', 'bacteria']
        param_values = {param: [] for param in quality_params}
        
        for message in messages:
            data = message.data
            for param in quality_params:
                if param in data:
                    param_values[param].append(data[param])
        
        # Check for quality alerts
        alerts = []
        for param, values in param_values.items():
            if values:
                avg_value = sum(values) / len(values)
                
                # Simple alert logic
                if param == 'ph' and (avg_value < 6.5 or avg_value > 8.5):
                    alerts.append(f"pH out of range: {avg_value:.2f}")
                elif param == 'turbidity' and avg_value > 4.0:
                    alerts.append(f"High turbidity: {avg_value:.2f} NTU")
                elif param == 'bacteria' and avg_value > 0:
                    alerts.append(f"Bacteria detected: {avg_value}")
        
        if alerts:
            await self._publish_alert(alerts, 'quality_monitoring')
    
    async def _process_flow_batch(self, messages: List[StreamMessage]):
        """Process batch of flow data messages."""
        flow_rates = []
        
        for message in messages:
            data = message.data
            if 'flow_rate' in data:
                flow_rates.append(data['flow_rate'])
        
        if flow_rates:
            avg_flow = sum(flow_rates) / len(flow_rates)
            
            # Check for flow anomalies
            if avg_flow < 500:  # Low flow alert
                await self._publish_alert([f"Low flow rate: {avg_flow:.2f} m³/h"], 'flow_monitoring')
            elif avg_flow > 1500:  # High flow alert
                await self._publish_alert([f"High flow rate: {avg_flow:.2f} m³/h"], 'flow_monitoring')
    
    async def _publish_alert(self, alerts: List[str], source: str):
        """Publish alert messages."""
        alert_data = {
            'alerts': alerts,
            'severity': 'medium',
            'timestamp': datetime.now().isoformat(),
            'source': source
        }
        
        # Find alert stream
        alert_stream_id = None
        for stream_id, stream in self.streams.items():
            if stream.stream_type == StreamType.ALERT_DATA:
                alert_stream_id = stream_id
                break
        
        if not alert_stream_id:
            # Create alert stream if it doesn't exist
            result = await self.create_stream({
                'stream_id': 'alert_stream_001',
                'stream_type': StreamType.ALERT_DATA.value,
                'source': 'system_monitoring',
                'description': 'System alerts and notifications'
            })
            if result['status'] == 'success':
                alert_stream_id = result['stream_id']
        
        if alert_stream_id:
            await self.publish_message(alert_stream_id, alert_data, source)
    
    @log_async_function_call
    async def subscribe_to_stream(self, stream_id: str, handler: Callable,
                                subscriber_id: str = None) -> Dict[str, Any]:
        """Subscribe to stream messages."""
        try:
            if stream_id not in self.streams:
                return {'status': 'error', 'error': 'Stream not found'}
            
            subscriber_id = subscriber_id or f"subscriber_{uuid.uuid4().hex[:8]}"
            
            # Add handler
            if stream_id not in self.message_handlers:
                self.message_handlers[stream_id] = []
            
            self.message_handlers[stream_id].append(handler)
            
            # Add to subscribers list
            stream = self.streams[stream_id]
            if subscriber_id not in stream.subscribers:
                stream.subscribers.append(subscriber_id)
            
            return {
                'status': 'success',
                'subscriber_id': subscriber_id,
                'stream_id': stream_id
            }
            
        except Exception as e:
            logger.error(f"Stream subscription failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def register_stream_processor(self, stream_id: str, processor: Callable):
        """Register stream processor for specific stream."""
        self.stream_processors[stream_id] = processor
    
    @log_async_function_call
    async def get_stream_messages(self, stream_id: str, limit: int = 100,
                                since: datetime = None) -> Dict[str, Any]:
        """Get recent messages from stream."""
        try:
            if stream_id not in self.streams:
                return {'status': 'error', 'error': 'Stream not found'}
            
            buffer = self.message_buffer[stream_id]
            
            # Filter by timestamp if specified
            if since:
                filtered_messages = [
                    msg for msg in buffer
                    if msg.timestamp >= since
                ]
            else:
                filtered_messages = buffer
            
            # Limit results
            recent_messages = filtered_messages[-limit:]
            
            # Convert to serializable format
            message_data = []
            for msg in recent_messages:
                message_data.append({
                    'message_id': msg.message_id,
                    'data': msg.data,
                    'timestamp': msg.timestamp.isoformat(),
                    'source': msg.source,
                    'metadata': msg.metadata
                })
            
            return {
                'status': 'success',
                'stream_id': stream_id,
                'messages': message_data,
                'total_messages': len(message_data)
            }
            
        except Exception as e:
            logger.error(f"Stream message retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_stream_status(self, stream_id: str = None) -> Dict[str, Any]:
        """Get stream status information."""
        try:
            if stream_id:
                # Get specific stream status
                if stream_id not in self.streams:
                    return {'status': 'error', 'error': 'Stream not found'}
                
                stream = self.streams[stream_id]
                buffer = self.message_buffer[stream_id]
                
                return {
                    'status': 'success',
                    'stream_status': {
                        'stream_id': stream_id,
                        'stream_type': stream.stream_type.value,
                        'source': stream.source,
                        'description': stream.description,
                        'status': stream.status.value,
                        'message_count': stream.message_count,
                        'error_count': stream.error_count,
                        'buffer_size': len(buffer),
                        'subscribers': len(stream.subscribers),
                        'last_message_time': stream.last_message_time.isoformat() if stream.last_message_time else None,
                        'created_at': stream.created_at.isoformat()
                    }
                }
            
            else:
                # Get all streams status
                streams_status = []
                
                for stream_id, stream in self.streams.items():
                    buffer = self.message_buffer[stream_id]
                    
                    streams_status.append({
                        'stream_id': stream_id,
                        'stream_type': stream.stream_type.value,
                        'status': stream.status.value,
                        'message_count': stream.message_count,
                        'error_count': stream.error_count,
                        'buffer_size': len(buffer),
                        'subscribers': len(stream.subscribers),
                        'last_message_time': stream.last_message_time.isoformat() if stream.last_message_time else None
                    })
                
                return {
                    'status': 'success',
                    'total_streams': len(self.streams),
                    'streams': streams_status,
                    'system_config': self.config
                }
            
        except Exception as e:
            logger.error(f"Stream status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def pause_stream(self, stream_id: str) -> Dict[str, Any]:
        """Pause stream processing."""
        if stream_id not in self.streams:
            return {'status': 'error', 'error': 'Stream not found'}
        
        self.streams[stream_id].status = StreamStatus.PAUSED
        
        return {
            'status': 'success',
            'stream_id': stream_id,
            'new_status': StreamStatus.PAUSED.value
        }
    
    async def resume_stream(self, stream_id: str) -> Dict[str, Any]:
        """Resume stream processing."""
        if stream_id not in self.streams:
            return {'status': 'error', 'error': 'Stream not found'}
        
        self.streams[stream_id].status = StreamStatus.ACTIVE
        
        return {
            'status': 'success',
            'stream_id': stream_id,
            'new_status': StreamStatus.ACTIVE.value
        }


# Convenience functions
async def create_data_stream(stream_type: str, source: str, description: str = "") -> Dict[str, Any]:
    """Create new data stream."""
    system = RealTimeStreamingSystem()
    return await system.create_stream({
        'stream_type': stream_type,
        'source': source,
        'description': description
    })


async def publish_stream_data(stream_id: str, data: Dict[str, Any], source: str = None) -> Dict[str, Any]:
    """Publish data to stream."""
    system = RealTimeStreamingSystem()
    return await system.publish_message(stream_id, data, source)


async def get_stream_data(stream_id: str, limit: int = 100) -> Dict[str, Any]:
    """Get recent stream data."""
    system = RealTimeStreamingSystem()
    return await system.get_stream_messages(stream_id, limit)
