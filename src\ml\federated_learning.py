"""Federated Learning System for Water Management."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import copy

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class FederatedStrategy(Enum):
    """Federated learning strategies."""
    FEDAVG = "federated_averaging"
    FEDPROX = "federated_proximal"
    SCAFFOLD = "scaffold"
    FEDNOVA = "fed_nova"


class ClientType(Enum):
    """Types of federated learning clients."""
    TREATMENT_PLANT = "treatment_plant"
    DISTRIBUTION_NETWORK = "distribution_network"
    MONITORING_STATION = "monitoring_station"
    RESEARCH_FACILITY = "research_facility"


@dataclass
class FederatedClient:
    """Federated learning client."""
    client_id: str
    client_type: ClientType
    location: str
    model: nn.Module
    optimizer: optim.Optimizer
    local_data_size: int
    last_update: datetime
    participation_rounds: int = 0
    average_loss: float = 0.0
    model_version: int = 0


@dataclass
class FederatedRound:
    """Federated learning round information."""
    round_id: int
    participants: List[str]
    global_model_version: int
    aggregation_strategy: FederatedStrategy
    round_start: datetime
    round_end: Optional[datetime] = None
    convergence_metrics: Dict[str, float] = field(default_factory=dict)


class WaterQualityModel(nn.Module):
    """Neural network model for water quality prediction."""
    
    def __init__(self, input_dim: int = 15, hidden_dim: int = 128, output_dim: int = 5):
        super(WaterQualityModel, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, output_dim)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network."""
        return self.network(x)


class TreatmentOptimizationModel(nn.Module):
    """Neural network model for treatment process optimization."""
    
    def __init__(self, input_dim: int = 20, hidden_dim: int = 256, output_dim: int = 10):
        super(TreatmentOptimizationModel, self).__init__()
        
        # Encoder
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU()
        )
        
        # Decoder
        self.decoder = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, output_dim),
            nn.Sigmoid()  # Output between 0 and 1
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through the network."""
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded


class FederatedLearningSystem:
    """Comprehensive federated learning system for water management."""
    
    def __init__(self):
        self.clients: Dict[str, FederatedClient] = {}
        self.global_model: Optional[nn.Module] = None
        self.federated_rounds: List[FederatedRound] = []
        self.current_round: int = 0
        self.convergence_threshold: float = 0.001
        
        # Federated learning configuration
        self.config = {
            'min_clients_per_round': 3,
            'max_clients_per_round': 10,
            'client_fraction': 0.7,
            'local_epochs': 5,
            'learning_rate': 0.001,
            'batch_size': 32,
            'convergence_patience': 5
        }
    
    @log_async_function_call
    async def initialize_global_model(self, model_type: str, model_config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize global federated model."""
        try:
            if model_type == 'water_quality':
                self.global_model = WaterQualityModel(
                    input_dim=model_config.get('input_dim', 15),
                    hidden_dim=model_config.get('hidden_dim', 128),
                    output_dim=model_config.get('output_dim', 5)
                )
            elif model_type == 'treatment_optimization':
                self.global_model = TreatmentOptimizationModel(
                    input_dim=model_config.get('input_dim', 20),
                    hidden_dim=model_config.get('hidden_dim', 256),
                    output_dim=model_config.get('output_dim', 10)
                )
            else:
                return {'status': 'error', 'error': f'Unknown model type: {model_type}'}
            
            # Initialize model weights
            for param in self.global_model.parameters():
                if len(param.shape) > 1:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.zeros_(param)
            
            return {
                'status': 'success',
                'model_type': model_type,
                'model_parameters': sum(p.numel() for p in self.global_model.parameters()),
                'model_size_mb': sum(p.numel() * 4 for p in self.global_model.parameters()) / (1024 * 1024)
            }
            
        except Exception as e:
            logger.error(f"Global model initialization failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def register_client(self, client_config: Dict[str, Any]) -> Dict[str, Any]:
        """Register new federated learning client."""
        try:
            client_id = client_config['client_id']
            client_type = ClientType(client_config['client_type'])
            location = client_config.get('location', 'unknown')
            local_data_size = client_config.get('local_data_size', 1000)
            
            if self.global_model is None:
                return {'status': 'error', 'error': 'Global model not initialized'}
            
            # Create local model copy
            local_model = copy.deepcopy(self.global_model)
            optimizer = optim.Adam(local_model.parameters(), lr=self.config['learning_rate'])
            
            client = FederatedClient(
                client_id=client_id,
                client_type=client_type,
                location=location,
                model=local_model,
                optimizer=optimizer,
                local_data_size=local_data_size,
                last_update=datetime.now()
            )
            
            self.clients[client_id] = client
            
            return {
                'status': 'success',
                'client_id': client_id,
                'client_type': client_type.value,
                'location': location,
                'model_synchronized': True,
                'registration_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Client registration failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def start_federated_round(self, strategy: FederatedStrategy = FederatedStrategy.FEDAVG) -> Dict[str, Any]:
        """Start new federated learning round."""
        try:
            if len(self.clients) < self.config['min_clients_per_round']:
                return {'status': 'error', 'error': 'Insufficient clients for federated round'}
            
            self.current_round += 1
            
            # Select participating clients
            participants = await self._select_clients()
            
            # Create round record
            fed_round = FederatedRound(
                round_id=self.current_round,
                participants=participants,
                global_model_version=self.current_round,
                aggregation_strategy=strategy,
                round_start=datetime.now()
            )
            
            # Distribute global model to participants
            await self._distribute_global_model(participants)
            
            return {
                'status': 'success',
                'round_id': self.current_round,
                'participants': participants,
                'strategy': strategy.value,
                'expected_completion': (datetime.now() + timedelta(hours=2)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Federated round start failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _select_clients(self) -> List[str]:
        """Select clients for federated round."""
        available_clients = list(self.clients.keys())
        
        # Calculate number of clients to select
        num_clients = min(
            self.config['max_clients_per_round'],
            max(
                self.config['min_clients_per_round'],
                int(len(available_clients) * self.config['client_fraction'])
            )
        )
        
        # Prioritize clients with more data and recent participation
        client_scores = {}
        for client_id in available_clients:
            client = self.clients[client_id]
            
            # Score based on data size and participation
            data_score = min(1.0, client.local_data_size / 5000)  # Normalize to max 5000 samples
            participation_score = 1.0 / (1.0 + client.participation_rounds * 0.1)  # Prefer less frequent participants
            recency_score = 1.0 / (1.0 + (datetime.now() - client.last_update).days * 0.1)
            
            client_scores[client_id] = data_score + participation_score + recency_score
        
        # Select top scoring clients
        selected_clients = sorted(client_scores.keys(), key=lambda x: client_scores[x], reverse=True)[:num_clients]
        
        return selected_clients
    
    async def _distribute_global_model(self, participants: List[str]):
        """Distribute global model to participating clients."""
        global_state_dict = self.global_model.state_dict()
        
        for client_id in participants:
            client = self.clients[client_id]
            client.model.load_state_dict(global_state_dict)
            client.model_version = self.current_round
    
    @log_async_function_call
    async def simulate_local_training(self, client_id: str, training_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate local training on client."""
        try:
            if client_id not in self.clients:
                return {'status': 'error', 'error': 'Client not found'}
            
            client = self.clients[client_id]
            
            # Simulate training data
            X = torch.randn(training_data.get('batch_size', 100), 
                           training_data.get('input_dim', 15))
            y = torch.randn(training_data.get('batch_size', 100), 
                           training_data.get('output_dim', 5))
            
            # Local training
            client.model.train()
            total_loss = 0.0
            
            for epoch in range(self.config['local_epochs']):
                client.optimizer.zero_grad()
                
                # Forward pass
                outputs = client.model(X)
                loss = nn.MSELoss()(outputs, y)
                
                # Backward pass
                loss.backward()
                client.optimizer.step()
                
                total_loss += loss.item()
            
            # Update client statistics
            client.participation_rounds += 1
            client.average_loss = total_loss / self.config['local_epochs']
            client.last_update = datetime.now()
            
            return {
                'status': 'success',
                'client_id': client_id,
                'local_epochs': self.config['local_epochs'],
                'average_loss': client.average_loss,
                'model_updated': True,
                'training_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Local training simulation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def aggregate_models(self, participants: List[str], 
                             strategy: FederatedStrategy = FederatedStrategy.FEDAVG) -> Dict[str, Any]:
        """Aggregate client models into global model."""
        try:
            if not participants:
                return {'status': 'error', 'error': 'No participants provided'}
            
            # Collect client models and weights
            client_models = []
            client_weights = []
            
            for client_id in participants:
                if client_id in self.clients:
                    client = self.clients[client_id]
                    client_models.append(client.model.state_dict())
                    client_weights.append(client.local_data_size)
            
            if not client_models:
                return {'status': 'error', 'error': 'No valid client models found'}
            
            # Normalize weights
            total_weight = sum(client_weights)
            normalized_weights = [w / total_weight for w in client_weights]
            
            # Aggregate based on strategy
            if strategy == FederatedStrategy.FEDAVG:
                aggregated_state = await self._federated_averaging(client_models, normalized_weights)
            elif strategy == FederatedStrategy.FEDPROX:
                aggregated_state = await self._federated_proximal(client_models, normalized_weights)
            else:
                aggregated_state = await self._federated_averaging(client_models, normalized_weights)
            
            # Update global model
            self.global_model.load_state_dict(aggregated_state)
            
            # Calculate convergence metrics
            convergence_metrics = await self._calculate_convergence_metrics(client_models)
            
            # Update round information
            if self.federated_rounds:
                current_round = self.federated_rounds[-1]
                current_round.round_end = datetime.now()
                current_round.convergence_metrics = convergence_metrics
            
            return {
                'status': 'success',
                'aggregation_strategy': strategy.value,
                'participants_aggregated': len(client_models),
                'convergence_metrics': convergence_metrics,
                'global_model_updated': True,
                'aggregation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Model aggregation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _federated_averaging(self, client_models: List[Dict], weights: List[float]) -> Dict:
        """Perform federated averaging aggregation."""
        aggregated_state = {}
        
        # Get parameter names from first model
        param_names = client_models[0].keys()
        
        for param_name in param_names:
            # Weighted average of parameters
            weighted_params = []
            for model_state, weight in zip(client_models, weights):
                weighted_params.append(model_state[param_name] * weight)
            
            aggregated_state[param_name] = torch.stack(weighted_params).sum(dim=0)
        
        return aggregated_state
    
    async def _federated_proximal(self, client_models: List[Dict], weights: List[float]) -> Dict:
        """Perform federated proximal aggregation."""
        # For simplicity, using federated averaging with regularization
        # In practice, would implement proper FedProx algorithm
        return await self._federated_averaging(client_models, weights)
    
    async def _calculate_convergence_metrics(self, client_models: List[Dict]) -> Dict[str, float]:
        """Calculate convergence metrics for federated round."""
        if len(client_models) < 2:
            return {'model_variance': 0.0, 'parameter_divergence': 0.0}
        
        # Calculate variance across client models
        param_variances = []
        param_names = client_models[0].keys()
        
        for param_name in param_names:
            param_values = [model[param_name].flatten() for model in client_models]
            param_stack = torch.stack(param_values)
            param_variance = torch.var(param_stack, dim=0).mean().item()
            param_variances.append(param_variance)
        
        model_variance = np.mean(param_variances)
        
        # Calculate parameter divergence from global model
        global_state = self.global_model.state_dict()
        divergences = []
        
        for model_state in client_models:
            model_divergence = 0.0
            for param_name in param_names:
                diff = model_state[param_name] - global_state[param_name]
                divergence = torch.norm(diff).item()
                model_divergence += divergence
            divergences.append(model_divergence)
        
        parameter_divergence = np.mean(divergences)
        
        return {
            'model_variance': model_variance,
            'parameter_divergence': parameter_divergence,
            'convergence_score': 1.0 / (1.0 + model_variance + parameter_divergence)
        }
    
    @log_async_function_call
    async def evaluate_global_model(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate global model performance."""
        try:
            if self.global_model is None:
                return {'status': 'error', 'error': 'Global model not initialized'}
            
            # Simulate test data
            X_test = torch.randn(test_data.get('test_size', 200), 
                               test_data.get('input_dim', 15))
            y_test = torch.randn(test_data.get('test_size', 200), 
                               test_data.get('output_dim', 5))
            
            # Evaluate model
            self.global_model.eval()
            with torch.no_grad():
                predictions = self.global_model(X_test)
                test_loss = nn.MSELoss()(predictions, y_test).item()
                
                # Calculate additional metrics
                mae = nn.L1Loss()(predictions, y_test).item()
                
                # R² score approximation
                ss_res = torch.sum((y_test - predictions) ** 2).item()
                ss_tot = torch.sum((y_test - torch.mean(y_test)) ** 2).item()
                r2_score = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            return {
                'status': 'success',
                'evaluation_metrics': {
                    'test_loss': test_loss,
                    'mean_absolute_error': mae,
                    'r2_score': r2_score,
                    'test_samples': test_data.get('test_size', 200)
                },
                'model_info': {
                    'current_round': self.current_round,
                    'total_clients': len(self.clients),
                    'model_parameters': sum(p.numel() for p in self.global_model.parameters())
                },
                'evaluation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Global model evaluation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def get_federated_status(self) -> Dict[str, Any]:
        """Get current federated learning system status."""
        try:
            client_stats = {}
            for client_id, client in self.clients.items():
                client_stats[client_id] = {
                    'client_type': client.client_type.value,
                    'location': client.location,
                    'participation_rounds': client.participation_rounds,
                    'last_update': client.last_update.isoformat(),
                    'average_loss': client.average_loss,
                    'data_size': client.local_data_size
                }
            
            # Calculate system metrics
            total_data_size = sum(client.local_data_size for client in self.clients.values())
            avg_participation = np.mean([client.participation_rounds for client in self.clients.values()]) if self.clients else 0
            
            return {
                'status': 'success',
                'system_overview': {
                    'current_round': self.current_round,
                    'total_clients': len(self.clients),
                    'total_data_size': total_data_size,
                    'average_participation': avg_participation,
                    'global_model_initialized': self.global_model is not None
                },
                'client_statistics': client_stats,
                'recent_rounds': [
                    {
                        'round_id': round_info.round_id,
                        'participants': len(round_info.participants),
                        'strategy': round_info.aggregation_strategy.value,
                        'convergence_score': round_info.convergence_metrics.get('convergence_score', 0)
                    }
                    for round_info in self.federated_rounds[-5:]  # Last 5 rounds
                ],
                'status_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience functions
async def setup_federated_learning(model_type: str, model_config: Dict[str, Any]) -> Dict[str, Any]:
    """Setup federated learning system."""
    system = FederatedLearningSystem()
    return await system.initialize_global_model(model_type, model_config)


async def run_federated_round(clients: List[Dict[str, Any]], 
                            strategy: str = 'federated_averaging') -> Dict[str, Any]:
    """Run complete federated learning round."""
    system = FederatedLearningSystem()
    
    # Initialize model
    model_result = await system.initialize_global_model('water_quality', {'input_dim': 15, 'output_dim': 5})
    if model_result['status'] != 'success':
        return model_result
    
    # Register clients
    for client_config in clients:
        await system.register_client(client_config)
    
    # Start round
    strategy_enum = FederatedStrategy(strategy)
    round_result = await system.start_federated_round(strategy_enum)
    if round_result['status'] != 'success':
        return round_result
    
    # Simulate local training
    participants = round_result['participants']
    for client_id in participants:
        await system.simulate_local_training(client_id, {'batch_size': 50})
    
    # Aggregate models
    return await system.aggregate_models(participants, strategy_enum)
