"""
Advanced Machine Learning Models for Water Management System.

Implementation of CNNs, RNNs, Transformers, Graph Neural Networks,
Reinforcement Learning, and other advanced ML architectures.
"""

import tensorflow as tf
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Configuration for ML models."""
    model_type: str
    input_shape: Tuple[int, ...]
    output_shape: Tuple[int, ...]
    hyperparameters: Dict[str, Any]
    training_config: Dict[str, Any]


class ConvolutionalNeuralNetwork:
    """CNN for spatial water quality data analysis."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        
    def build_model(self) -> tf.keras.Model:
        """Build CNN architecture for spatial data."""
        try:
            input_shape = self.config.input_shape
            
            model = tf.keras.Sequential([
                # Convolutional layers
                tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.MaxPooling2D((2, 2)),
                
                tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.MaxPooling2D((2, 2)),
                
                tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.MaxPooling2D((2, 2)),
                
                # Dense layers
                tf.keras.layers.Flatten(),
                tf.keras.layers.Dense(256, activation='relu'),
                tf.keras.layers.Dropout(0.5),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dropout(0.3),
                
                # Output layer
                tf.keras.layers.Dense(self.config.output_shape[0], activation='sigmoid')
            ])
            
            model.compile(
                optimizer='adam',
                loss='binary_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
            
            self.model = model
            return model
            
        except Exception as e:
            logger.error(f"Error building CNN model: {e}")
            return None
    
    async def train_model(self, X_train: np.ndarray, y_train: np.ndarray,
                         X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """Train the CNN model."""
        try:
            if self.model is None:
                self.build_model()
            
            # Training configuration
            epochs = self.config.training_config.get('epochs', 100)
            batch_size = self.config.training_config.get('batch_size', 32)
            
            # Callbacks
            callbacks = [
                tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
                tf.keras.callbacks.ReduceLROnPlateau(factor=0.5, patience=5),
                tf.keras.callbacks.ModelCheckpoint('best_cnn_model.h5', save_best_only=True)
            ]
            
            # Train model
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
            
            # Evaluate model
            train_loss, train_acc = self.model.evaluate(X_train, y_train, verbose=0)
            val_loss, val_acc = self.model.evaluate(X_val, y_val, verbose=0)
            
            return {
                'status': 'success',
                'training_history': history.history,
                'final_metrics': {
                    'train_loss': train_loss,
                    'train_accuracy': train_acc,
                    'val_loss': val_loss,
                    'val_accuracy': val_acc
                }
            }
            
        except Exception as e:
            logger.error(f"Error training CNN model: {e}")
            return {'status': 'error', 'error': str(e)}


class RecurrentNeuralNetwork:
    """RNN/LSTM for time series water data analysis."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
    
    def build_model(self) -> tf.keras.Model:
        """Build LSTM architecture for time series."""
        try:
            input_shape = self.config.input_shape
            
            model = tf.keras.Sequential([
                # LSTM layers
                tf.keras.layers.LSTM(128, return_sequences=True, input_shape=input_shape),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.BatchNormalization(),
                
                tf.keras.layers.LSTM(64, return_sequences=True),
                tf.keras.layers.Dropout(0.2),
                tf.keras.layers.BatchNormalization(),
                
                tf.keras.layers.LSTM(32, return_sequences=False),
                tf.keras.layers.Dropout(0.2),
                
                # Dense layers
                tf.keras.layers.Dense(64, activation='relu'),
                tf.keras.layers.Dropout(0.3),
                tf.keras.layers.Dense(32, activation='relu'),
                
                # Output layer
                tf.keras.layers.Dense(self.config.output_shape[0], activation='linear')
            ])
            
            model.compile(
                optimizer='adam',
                loss='mse',
                metrics=['mae', 'mape']
            )
            
            self.model = model
            return model
            
        except Exception as e:
            logger.error(f"Error building RNN model: {e}")
            return None
    
    async def predict_time_series(self, input_data: np.ndarray, 
                                 forecast_steps: int = 24) -> Dict[str, Any]:
        """Predict future time series values."""
        try:
            if self.model is None:
                return {'status': 'error', 'error': 'Model not trained'}
            
            # Make predictions
            predictions = []
            current_input = input_data[-1:].copy()
            
            for _ in range(forecast_steps):
                pred = self.model.predict(current_input, verbose=0)
                predictions.append(pred[0])
                
                # Update input for next prediction
                current_input = np.roll(current_input, -1, axis=1)
                current_input[0, -1] = pred[0]
            
            return {
                'status': 'success',
                'predictions': np.array(predictions),
                'forecast_steps': forecast_steps,
                'confidence_intervals': self._calculate_confidence_intervals(predictions)
            }
            
        except Exception as e:
            logger.error(f"Error making time series predictions: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _calculate_confidence_intervals(self, predictions: List[float]) -> Dict[str, List[float]]:
        """Calculate confidence intervals for predictions."""
        predictions = np.array(predictions)
        std = np.std(predictions)
        
        lower_bound = predictions - 1.96 * std
        upper_bound = predictions + 1.96 * std
        
        return {
            'lower_95': lower_bound.tolist(),
            'upper_95': upper_bound.tolist()
        }


class TransformerModel:
    """Transformer architecture for sequence processing."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
    
    def build_model(self) -> tf.keras.Model:
        """Build Transformer architecture."""
        try:
            seq_length = self.config.input_shape[0]
            d_model = self.config.hyperparameters.get('d_model', 128)
            num_heads = self.config.hyperparameters.get('num_heads', 8)
            num_layers = self.config.hyperparameters.get('num_layers', 4)
            
            # Input layers
            inputs = tf.keras.layers.Input(shape=self.config.input_shape)
            
            # Positional encoding
            x = self._add_positional_encoding(inputs, d_model)
            
            # Transformer blocks
            for _ in range(num_layers):
                x = self._transformer_block(x, d_model, num_heads)
            
            # Global average pooling
            x = tf.keras.layers.GlobalAveragePooling1D()(x)
            
            # Dense layers
            x = tf.keras.layers.Dense(128, activation='relu')(x)
            x = tf.keras.layers.Dropout(0.3)(x)
            x = tf.keras.layers.Dense(64, activation='relu')(x)
            
            # Output layer
            outputs = tf.keras.layers.Dense(self.config.output_shape[0], activation='linear')(x)
            
            model = tf.keras.Model(inputs=inputs, outputs=outputs)
            
            model.compile(
                optimizer='adam',
                loss='mse',
                metrics=['mae']
            )
            
            self.model = model
            return model
            
        except Exception as e:
            logger.error(f"Error building Transformer model: {e}")
            return None
    
    def _add_positional_encoding(self, inputs, d_model):
        """Add positional encoding to inputs."""
        seq_length = tf.shape(inputs)[1]
        
        # Create positional encoding
        position = tf.range(seq_length, dtype=tf.float32)[:, tf.newaxis]
        div_term = tf.exp(tf.range(0, d_model, 2, dtype=tf.float32) * -(np.log(10000.0) / d_model))
        
        pos_encoding = tf.zeros((seq_length, d_model))
        pos_encoding = tf.concat([
            tf.sin(position * div_term),
            tf.cos(position * div_term)
        ], axis=-1)
        
        return inputs + pos_encoding
    
    def _transformer_block(self, inputs, d_model, num_heads):
        """Create a transformer block."""
        # Multi-head attention
        attention = tf.keras.layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=d_model
        )(inputs, inputs)
        
        # Add & Norm
        attention = tf.keras.layers.Dropout(0.1)(attention)
        x1 = tf.keras.layers.LayerNormalization()(inputs + attention)
        
        # Feed forward
        ff = tf.keras.layers.Dense(d_model * 4, activation='relu')(x1)
        ff = tf.keras.layers.Dense(d_model)(ff)
        ff = tf.keras.layers.Dropout(0.1)(ff)
        
        # Add & Norm
        return tf.keras.layers.LayerNormalization()(x1 + ff)


class GraphNeuralNetwork(nn.Module):
    """Graph Neural Network for system relationships."""
    
    def __init__(self, config: ModelConfig):
        super(GraphNeuralNetwork, self).__init__()
        self.config = config
        
        input_dim = config.hyperparameters.get('input_dim', 64)
        hidden_dim = config.hyperparameters.get('hidden_dim', 128)
        output_dim = config.output_shape[0]
        
        self.conv1 = self._create_graph_conv_layer(input_dim, hidden_dim)
        self.conv2 = self._create_graph_conv_layer(hidden_dim, hidden_dim)
        self.conv3 = self._create_graph_conv_layer(hidden_dim, output_dim)
        
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
    
    def _create_graph_conv_layer(self, in_features, out_features):
        """Create a graph convolution layer."""
        return nn.Linear(in_features, out_features)
    
    def forward(self, x, edge_index):
        """Forward pass through GNN."""
        # First graph convolution
        x = self.conv1(x)
        x = self.relu(x)
        x = self.dropout(x)
        
        # Second graph convolution
        x = self.conv2(x)
        x = self.relu(x)
        x = self.dropout(x)
        
        # Output layer
        x = self.conv3(x)
        
        return x
    
    async def train_model(self, data_loader, epochs=100):
        """Train the GNN model."""
        try:
            optimizer = torch.optim.Adam(self.parameters(), lr=0.01)
            criterion = nn.MSELoss()
            
            self.train()
            
            for epoch in range(epochs):
                total_loss = 0
                
                for batch in data_loader:
                    optimizer.zero_grad()
                    
                    x, edge_index, y = batch.x, batch.edge_index, batch.y
                    
                    # Forward pass
                    out = self.forward(x, edge_index)
                    loss = criterion(out, y)
                    
                    # Backward pass
                    loss.backward()
                    optimizer.step()
                    
                    total_loss += loss.item()
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}, Loss: {total_loss:.4f}")
            
            return {'status': 'success', 'final_loss': total_loss}
            
        except Exception as e:
            logger.error(f"Error training GNN model: {e}")
            return {'status': 'error', 'error': str(e)}


class ReinforcementLearningAgent:
    """Reinforcement Learning for decision making."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.q_network = None
        self.target_network = None
        self.memory = []
        self.epsilon = 1.0
        self.epsilon_decay = 0.995
        self.epsilon_min = 0.01
    
    def build_q_network(self) -> tf.keras.Model:
        """Build Q-network for RL agent."""
        try:
            state_size = self.config.input_shape[0]
            action_size = self.config.output_shape[0]
            
            model = tf.keras.Sequential([
                tf.keras.layers.Dense(128, activation='relu', input_shape=(state_size,)),
                tf.keras.layers.Dense(128, activation='relu'),
                tf.keras.layers.Dense(64, activation='relu'),
                tf.keras.layers.Dense(action_size, activation='linear')
            ])
            
            model.compile(optimizer='adam', loss='mse')
            
            return model
            
        except Exception as e:
            logger.error(f"Error building Q-network: {e}")
            return None
    
    async def train_agent(self, environment, episodes=1000):
        """Train the RL agent."""
        try:
            if self.q_network is None:
                self.q_network = self.build_q_network()
                self.target_network = self.build_q_network()
            
            scores = []
            
            for episode in range(episodes):
                state = environment.reset()
                total_reward = 0
                done = False
                
                while not done:
                    # Choose action
                    action = self._choose_action(state)
                    
                    # Take action
                    next_state, reward, done = environment.step(action)
                    
                    # Store experience
                    self.memory.append((state, action, reward, next_state, done))
                    
                    # Train network
                    if len(self.memory) > 32:
                        await self._replay_experience()
                    
                    state = next_state
                    total_reward += reward
                
                scores.append(total_reward)
                
                # Update target network
                if episode % 100 == 0:
                    self.target_network.set_weights(self.q_network.get_weights())
                
                # Decay epsilon
                if self.epsilon > self.epsilon_min:
                    self.epsilon *= self.epsilon_decay
                
                if episode % 100 == 0:
                    logger.info(f"Episode {episode}, Average Score: {np.mean(scores[-100:]):.2f}")
            
            return {
                'status': 'success',
                'training_scores': scores,
                'final_epsilon': self.epsilon
            }
            
        except Exception as e:
            logger.error(f"Error training RL agent: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _choose_action(self, state):
        """Choose action using epsilon-greedy policy."""
        if np.random.random() <= self.epsilon:
            return np.random.randint(self.config.output_shape[0])
        
        q_values = self.q_network.predict(state.reshape(1, -1), verbose=0)
        return np.argmax(q_values[0])
    
    async def _replay_experience(self, batch_size=32):
        """Replay experience for training."""
        if len(self.memory) < batch_size:
            return
        
        batch = np.random.choice(len(self.memory), batch_size, replace=False)
        
        states = np.array([self.memory[i][0] for i in batch])
        actions = np.array([self.memory[i][1] for i in batch])
        rewards = np.array([self.memory[i][2] for i in batch])
        next_states = np.array([self.memory[i][3] for i in batch])
        dones = np.array([self.memory[i][4] for i in batch])
        
        # Calculate target Q-values
        target_q_values = self.target_network.predict(next_states, verbose=0)
        max_target_q_values = np.max(target_q_values, axis=1)
        
        targets = rewards + (1 - dones) * 0.99 * max_target_q_values
        
        # Get current Q-values
        current_q_values = self.q_network.predict(states, verbose=0)
        
        # Update Q-values for taken actions
        for i in range(batch_size):
            current_q_values[i][actions[i]] = targets[i]
        
        # Train the network
        self.q_network.fit(states, current_q_values, verbose=0)


class EnsembleModel:
    """Ensemble model for robust predictions."""
    
    def __init__(self, models: List[Any]):
        self.models = models
        self.weights = None
    
    async def train_ensemble(self, X_train, y_train, X_val, y_val):
        """Train ensemble of models."""
        try:
            model_predictions = []
            
            # Train each model
            for i, model in enumerate(self.models):
                logger.info(f"Training model {i+1}/{len(self.models)}")
                
                if hasattr(model, 'train_model'):
                    await model.train_model(X_train, y_train, X_val, y_val)
                
                # Get validation predictions
                if hasattr(model, 'predict'):
                    val_pred = model.predict(X_val)
                    model_predictions.append(val_pred)
            
            # Calculate ensemble weights based on validation performance
            self.weights = await self._calculate_ensemble_weights(model_predictions, y_val)
            
            return {
                'status': 'success',
                'ensemble_weights': self.weights,
                'num_models': len(self.models)
            }
            
        except Exception as e:
            logger.error(f"Error training ensemble: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_ensemble_weights(self, predictions, y_true):
        """Calculate optimal ensemble weights."""
        # Simple equal weighting for now
        return [1.0 / len(predictions)] * len(predictions)
    
    def predict(self, X):
        """Make ensemble predictions."""
        try:
            predictions = []
            
            for model in self.models:
                if hasattr(model, 'predict'):
                    pred = model.predict(X)
                    predictions.append(pred)
            
            # Weighted average
            ensemble_pred = np.zeros_like(predictions[0])
            for i, pred in enumerate(predictions):
                ensemble_pred += self.weights[i] * pred
            
            return ensemble_pred
            
        except Exception as e:
            logger.error(f"Error making ensemble predictions: {e}")
            return None


class AutoencoderModel:
    """Autoencoder for anomaly detection."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.threshold = None
    
    def build_model(self) -> tf.keras.Model:
        """Build autoencoder architecture."""
        try:
            input_dim = self.config.input_shape[0]
            encoding_dim = self.config.hyperparameters.get('encoding_dim', 32)
            
            # Encoder
            input_layer = tf.keras.layers.Input(shape=(input_dim,))
            encoded = tf.keras.layers.Dense(128, activation='relu')(input_layer)
            encoded = tf.keras.layers.Dense(64, activation='relu')(encoded)
            encoded = tf.keras.layers.Dense(encoding_dim, activation='relu')(encoded)
            
            # Decoder
            decoded = tf.keras.layers.Dense(64, activation='relu')(encoded)
            decoded = tf.keras.layers.Dense(128, activation='relu')(decoded)
            decoded = tf.keras.layers.Dense(input_dim, activation='sigmoid')(decoded)
            
            # Autoencoder model
            autoencoder = tf.keras.Model(input_layer, decoded)
            autoencoder.compile(optimizer='adam', loss='mse')
            
            self.model = autoencoder
            return autoencoder
            
        except Exception as e:
            logger.error(f"Error building autoencoder: {e}")
            return None
    
    async def detect_anomalies(self, X_test):
        """Detect anomalies using reconstruction error."""
        try:
            if self.model is None:
                return {'status': 'error', 'error': 'Model not trained'}
            
            # Get reconstructions
            reconstructions = self.model.predict(X_test)
            
            # Calculate reconstruction errors
            mse = np.mean(np.power(X_test - reconstructions, 2), axis=1)
            
            # Determine anomalies
            if self.threshold is None:
                self.threshold = np.percentile(mse, 95)  # Top 5% as anomalies
            
            anomalies = mse > self.threshold
            
            return {
                'status': 'success',
                'reconstruction_errors': mse,
                'anomalies': anomalies,
                'threshold': self.threshold,
                'anomaly_count': np.sum(anomalies)
            }
            
        except Exception as e:
            logger.error(f"Error detecting anomalies: {e}")
            return {'status': 'error', 'error': str(e)}


# Model factory
class ModelFactory:
    """Factory for creating ML models."""
    
    @staticmethod
    def create_model(model_type: str, config: ModelConfig):
        """Create model based on type."""
        model_classes = {
            'cnn': ConvolutionalNeuralNetwork,
            'rnn': RecurrentNeuralNetwork,
            'transformer': TransformerModel,
            'gnn': GraphNeuralNetwork,
            'rl': ReinforcementLearningAgent,
            'autoencoder': AutoencoderModel
        }
        
        if model_type in model_classes:
            return model_classes[model_type](config)
        else:
            raise ValueError(f"Unknown model type: {model_type}")


# Convenience functions
async def create_cnn_model(input_shape: Tuple[int, ...], output_shape: Tuple[int, ...]) -> ConvolutionalNeuralNetwork:
    """Create CNN model for spatial data."""
    config = ModelConfig(
        model_type='cnn',
        input_shape=input_shape,
        output_shape=output_shape,
        hyperparameters={},
        training_config={'epochs': 100, 'batch_size': 32}
    )
    return ConvolutionalNeuralNetwork(config)


async def create_lstm_model(input_shape: Tuple[int, ...], output_shape: Tuple[int, ...]) -> RecurrentNeuralNetwork:
    """Create LSTM model for time series."""
    config = ModelConfig(
        model_type='rnn',
        input_shape=input_shape,
        output_shape=output_shape,
        hyperparameters={},
        training_config={'epochs': 100, 'batch_size': 32}
    )
    return RecurrentNeuralNetwork(config)


async def create_transformer_model(input_shape: Tuple[int, ...], output_shape: Tuple[int, ...]) -> TransformerModel:
    """Create Transformer model for sequence processing."""
    config = ModelConfig(
        model_type='transformer',
        input_shape=input_shape,
        output_shape=output_shape,
        hyperparameters={'d_model': 128, 'num_heads': 8, 'num_layers': 4},
        training_config={'epochs': 100, 'batch_size': 32}
    )
    return TransformerModel(config)
