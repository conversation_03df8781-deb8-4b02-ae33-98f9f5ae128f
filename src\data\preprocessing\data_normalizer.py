"""
Data Normalization and Standardization Module.

This module provides comprehensive normalization and standardization capabilities
for climate data to ensure consistency across different sources and formats.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
    from sklearn.impute import SimpleImputer, KNNImputer
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn not available, using basic normalization")
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class NormalizationConfig:
    """Configuration for data normalization."""
    method: str = 'standard'  # 'standard', 'minmax', 'robust'
    feature_range: Tuple[float, float] = (0, 1)
    handle_outliers: bool = True
    outlier_method: str = 'iqr'  # 'iqr', 'zscore', 'isolation'
    missing_value_strategy: str = 'mean'  # 'mean', 'median', 'mode', 'knn'
    temporal_normalization: bool = True
    spatial_normalization: bool = True


@dataclass
class NormalizationMetadata:
    """Metadata for normalization operations."""
    method: str
    parameters: Dict[str, Any]
    feature_statistics: Dict[str, Dict[str, float]]
    outliers_detected: int
    missing_values_imputed: int
    timestamp: datetime
    data_shape: Tuple[int, int]


class ClimateDataNormalizer:
    """
    Comprehensive climate data normalization and standardization.
    
    Provides:
    - Multiple normalization methods (Standard, MinMax, Robust)
    - Outlier detection and handling
    - Missing value imputation
    - Temporal and spatial normalization
    - Unit standardization
    - Data quality scoring
    """
    
    def __init__(self, config: NormalizationConfig = None):
        self.settings = get_settings()
        self.config = config or NormalizationConfig()
        self.is_initialized = False
        
        # Scalers for different normalization methods
        self.scalers = {}
        self.imputers = {}
        self.normalization_metadata = {}
        
        # Standard units for climate variables
        self.standard_units = {
            'temperature': 'celsius',
            'temperature_min': 'celsius',
            'temperature_max': 'celsius',
            'feels_like': 'celsius',
            'humidity': 'percent',
            'pressure': 'hpa',
            'precipitation': 'mm',
            'wind_speed': 'ms',
            'wind_direction': 'degrees',
            'visibility': 'km',
            'uv_index': 'index',
            'cloud_cover': 'percent',
            'air_quality_index': 'index',
            'pm2_5': 'ugm3',
            'pm10': 'ugm3'
        }
        
        # Expected value ranges for validation
        self.value_ranges = {
            'temperature': (-60, 60),
            'temperature_min': (-60, 60),
            'temperature_max': (-60, 60),
            'feels_like': (-60, 60),
            'humidity': (0, 100),
            'pressure': (800, 1200),
            'precipitation': (0, 500),
            'wind_speed': (0, 150),
            'wind_direction': (0, 360),
            'visibility': (0, 50),
            'uv_index': (0, 15),
            'cloud_cover': (0, 100),
            'air_quality_index': (0, 500),
            'pm2_5': (0, 1000),
            'pm10': (0, 1000)
        }
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the normalizer."""
        try:
            logger.info("Initializing Climate Data Normalizer...")
            
            # Initialize scalers
            if self.config.method == 'standard':
                self.scalers['primary'] = StandardScaler()
            elif self.config.method == 'minmax':
                self.scalers['primary'] = MinMaxScaler(feature_range=self.config.feature_range)
            elif self.config.method == 'robust':
                self.scalers['primary'] = RobustScaler()
            else:
                raise ValueError(f"Unsupported normalization method: {self.config.method}")
            
            # Initialize imputers
            if self.config.missing_value_strategy in ['mean', 'median', 'most_frequent']:
                self.imputers['primary'] = SimpleImputer(strategy=self.config.missing_value_strategy)
            elif self.config.missing_value_strategy == 'knn':
                self.imputers['primary'] = KNNImputer(n_neighbors=5)
            
            self.is_initialized = True
            logger.info("Climate Data Normalizer initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize normalizer: {e}")
            return False
    
    async def normalize_dataframe(self, df: pd.DataFrame, fit: bool = True) -> Tuple[pd.DataFrame, NormalizationMetadata]:
        """Normalize a pandas DataFrame of climate data."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            logger.info(f"Normalizing DataFrame with shape {df.shape}")
            
            # Create a copy to avoid modifying original data
            df_normalized = df.copy()
            
            # Get numeric columns for normalization
            numeric_columns = df_normalized.select_dtypes(include=[np.number]).columns.tolist()
            climate_columns = [col for col in numeric_columns if col in self.standard_units.keys()]
            
            if not climate_columns:
                logger.warning("No climate columns found for normalization")
                return df_normalized, self._create_empty_metadata()
            
            # Handle missing values
            missing_count = 0
            if self.config.missing_value_strategy != 'none':
                missing_count = df_normalized[climate_columns].isnull().sum().sum()
                if missing_count > 0:
                    if fit:
                        df_normalized[climate_columns] = self.imputers['primary'].fit_transform(df_normalized[climate_columns])
                    else:
                        df_normalized[climate_columns] = self.imputers['primary'].transform(df_normalized[climate_columns])
            
            # Detect and handle outliers
            outliers_count = 0
            if self.config.handle_outliers:
                df_normalized, outliers_count = await self._handle_outliers(df_normalized, climate_columns)
            
            # Apply normalization
            if fit:
                df_normalized[climate_columns] = self.scalers['primary'].fit_transform(df_normalized[climate_columns])
            else:
                df_normalized[climate_columns] = self.scalers['primary'].transform(df_normalized[climate_columns])
            
            # Calculate feature statistics
            feature_stats = {}
            for col in climate_columns:
                feature_stats[col] = {
                    'mean': float(df[col].mean()) if not df[col].isnull().all() else 0.0,
                    'std': float(df[col].std()) if not df[col].isnull().all() else 0.0,
                    'min': float(df[col].min()) if not df[col].isnull().all() else 0.0,
                    'max': float(df[col].max()) if not df[col].isnull().all() else 0.0,
                    'missing_count': int(df[col].isnull().sum())
                }
            
            # Create metadata
            metadata = NormalizationMetadata(
                method=self.config.method,
                parameters=asdict(self.config),
                feature_statistics=feature_stats,
                outliers_detected=outliers_count,
                missing_values_imputed=missing_count,
                timestamp=datetime.now(),
                data_shape=df.shape
            )
            
            logger.info(f"Normalization completed: {len(climate_columns)} features, {outliers_count} outliers, {missing_count} missing values")
            
            return df_normalized, metadata
            
        except Exception as e:
            logger.error(f"Failed to normalize DataFrame: {e}")
            raise
    
    async def _handle_outliers(self, df: pd.DataFrame, columns: List[str]) -> Tuple[pd.DataFrame, int]:
        """Detect and handle outliers in the data."""
        try:
            df_clean = df.copy()
            total_outliers = 0
            
            for col in columns:
                if col not in df_clean.columns or df_clean[col].isnull().all():
                    continue
                
                if self.config.outlier_method == 'iqr':
                    # Interquartile Range method
                    Q1 = df_clean[col].quantile(0.25)
                    Q3 = df_clean[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    outliers = (df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)
                    
                elif self.config.outlier_method == 'zscore':
                    # Z-score method
                    z_scores = np.abs((df_clean[col] - df_clean[col].mean()) / df_clean[col].std())
                    outliers = z_scores > 3
                
                elif self.config.outlier_method == 'range':
                    # Use predefined ranges for climate variables
                    if col in self.value_ranges:
                        min_val, max_val = self.value_ranges[col]
                        outliers = (df_clean[col] < min_val) | (df_clean[col] > max_val)
                    else:
                        outliers = pd.Series([False] * len(df_clean), index=df_clean.index)
                
                else:
                    outliers = pd.Series([False] * len(df_clean), index=df_clean.index)
                
                # Count outliers
                outlier_count = outliers.sum()
                total_outliers += outlier_count
                
                if outlier_count > 0:
                    # Replace outliers with median
                    median_value = df_clean[col].median()
                    df_clean.loc[outliers, col] = median_value
                    logger.debug(f"Replaced {outlier_count} outliers in {col} with median value {median_value}")
            
            return df_clean, total_outliers
            
        except Exception as e:
            logger.error(f"Failed to handle outliers: {e}")
            return df, 0
    
    async def normalize_climate_data_list(self, data_list: List[ProcessedClimateData]) -> Tuple[List[ProcessedClimateData], NormalizationMetadata]:
        """Normalize a list of ProcessedClimateData objects."""
        try:
            if not data_list:
                return [], self._create_empty_metadata()
            
            # Convert to DataFrame
            records = []
            for data in data_list:
                record = asdict(data)
                record['timestamp'] = data.timestamp.isoformat()
                records.append(record)
            
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Normalize DataFrame
            df_normalized, metadata = await self.normalize_dataframe(df, fit=True)
            
            # Convert back to ProcessedClimateData objects
            normalized_data_list = []
            for _, row in df_normalized.iterrows():
                # Convert back to ProcessedClimateData
                data_dict = row.to_dict()
                data_dict['timestamp'] = pd.to_datetime(data_dict['timestamp']).to_pydatetime()
                
                # Handle lists that might have been converted to strings
                if isinstance(data_dict.get('missing_fields'), str):
                    try:
                        data_dict['missing_fields'] = json.loads(data_dict['missing_fields'])
                    except:
                        data_dict['missing_fields'] = []
                
                if isinstance(data_dict.get('anomaly_flags'), str):
                    try:
                        data_dict['anomaly_flags'] = json.loads(data_dict['anomaly_flags'])
                    except:
                        data_dict['anomaly_flags'] = []
                
                # Create ProcessedClimateData object
                normalized_data = ProcessedClimateData(**data_dict)
                normalized_data_list.append(normalized_data)
            
            return normalized_data_list, metadata
            
        except Exception as e:
            logger.error(f"Failed to normalize climate data list: {e}")
            return data_list, self._create_empty_metadata()
    
    async def standardize_units(self, data: ProcessedClimateData) -> ProcessedClimateData:
        """Standardize units for a single climate data point."""
        try:
            # This is a placeholder for unit conversion
            # In a real implementation, you would detect current units and convert
            
            standardized = data
            
            # Example conversions (would be more comprehensive in practice)
            # Temperature: ensure Celsius
            # Pressure: ensure hPa
            # Wind speed: ensure m/s
            # Precipitation: ensure mm
            
            logger.debug(f"Units standardized for {data.source} data")
            return standardized
            
        except Exception as e:
            logger.error(f"Failed to standardize units: {e}")
            return data
    
    async def apply_temporal_normalization(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply temporal normalization (e.g., seasonal adjustments)."""
        try:
            if 'timestamp' not in df.columns:
                return df
            
            df_temporal = df.copy()
            
            # Extract temporal features
            df_temporal['hour'] = pd.to_datetime(df_temporal['timestamp']).dt.hour
            df_temporal['day_of_year'] = pd.to_datetime(df_temporal['timestamp']).dt.dayofyear
            df_temporal['month'] = pd.to_datetime(df_temporal['timestamp']).dt.month
            
            # Apply seasonal normalization for temperature
            if 'temperature' in df_temporal.columns:
                # Simple seasonal adjustment (would be more sophisticated in practice)
                seasonal_factor = np.sin(2 * np.pi * df_temporal['day_of_year'] / 365.25)
                df_temporal['temperature_seasonal_adjusted'] = df_temporal['temperature'] - seasonal_factor * 5
            
            logger.debug("Temporal normalization applied")
            return df_temporal
            
        except Exception as e:
            logger.error(f"Failed to apply temporal normalization: {e}")
            return df
    
    async def apply_spatial_normalization(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply spatial normalization based on location."""
        try:
            if 'latitude' not in df.columns or 'longitude' not in df.columns:
                return df
            
            df_spatial = df.copy()
            
            # Normalize coordinates
            df_spatial['latitude_norm'] = df_spatial['latitude'] / 90.0  # Normalize to [-1, 1]
            df_spatial['longitude_norm'] = df_spatial['longitude'] / 180.0  # Normalize to [-1, 1]
            
            # Calculate distance from equator
            df_spatial['equator_distance'] = np.abs(df_spatial['latitude']) / 90.0
            
            logger.debug("Spatial normalization applied")
            return df_spatial
            
        except Exception as e:
            logger.error(f"Failed to apply spatial normalization: {e}")
            return df
    
    def _create_empty_metadata(self) -> NormalizationMetadata:
        """Create empty normalization metadata."""
        return NormalizationMetadata(
            method=self.config.method,
            parameters=asdict(self.config),
            feature_statistics={},
            outliers_detected=0,
            missing_values_imputed=0,
            timestamp=datetime.now(),
            data_shape=(0, 0)
        )
    
    async def denormalize_data(self, df_normalized: pd.DataFrame, metadata: NormalizationMetadata) -> pd.DataFrame:
        """Denormalize data back to original scale."""
        try:
            if not hasattr(self.scalers.get('primary'), 'inverse_transform'):
                logger.warning("Cannot denormalize: scaler not fitted or doesn't support inverse transform")
                return df_normalized
            
            df_denormalized = df_normalized.copy()
            
            # Get climate columns
            climate_columns = [col for col in df_denormalized.columns if col in self.standard_units.keys()]
            
            if climate_columns:
                df_denormalized[climate_columns] = self.scalers['primary'].inverse_transform(df_denormalized[climate_columns])
            
            logger.info("Data denormalized successfully")
            return df_denormalized
            
        except Exception as e:
            logger.error(f"Failed to denormalize data: {e}")
            return df_normalized
    
    async def get_normalization_summary(self, metadata: NormalizationMetadata) -> Dict[str, Any]:
        """Get a summary of normalization operations."""
        try:
            summary = {
                "normalization_method": metadata.method,
                "data_shape": metadata.data_shape,
                "features_normalized": len(metadata.feature_statistics),
                "outliers_detected": metadata.outliers_detected,
                "missing_values_imputed": metadata.missing_values_imputed,
                "timestamp": metadata.timestamp.isoformat(),
                "feature_summary": {}
            }
            
            # Add feature-level summary
            for feature, stats in metadata.feature_statistics.items():
                summary["feature_summary"][feature] = {
                    "mean": stats["mean"],
                    "std": stats["std"],
                    "range": [stats["min"], stats["max"]],
                    "missing_values": stats["missing_count"]
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to create normalization summary: {e}")
            return {}


# Convenience functions
async def normalize_climate_dataframe(df: pd.DataFrame, method: str = 'standard') -> Tuple[pd.DataFrame, NormalizationMetadata]:
    """Normalize a climate data DataFrame."""
    config = NormalizationConfig(method=method)
    normalizer = ClimateDataNormalizer(config)
    await normalizer.initialize()
    
    return await normalizer.normalize_dataframe(df, fit=True)


async def normalize_climate_data_list(data_list: List[ProcessedClimateData], method: str = 'standard') -> Tuple[List[ProcessedClimateData], NormalizationMetadata]:
    """Normalize a list of climate data objects."""
    config = NormalizationConfig(method=method)
    normalizer = ClimateDataNormalizer(config)
    await normalizer.initialize()
    
    return await normalizer.normalize_climate_data_list(data_list)
