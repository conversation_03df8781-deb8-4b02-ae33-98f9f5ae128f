#!/usr/bin/env python3
"""
Comprehensive Test Suite for Unified Environmental Platform
Tests both backend integration and frontend connectivity
"""

import asyncio
import sys
import logging
import time
from pathlib import Path
import requests
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from integrated_platform.unified_environmental_platform import UnifiedEnvironmentalPlatform

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntegratedSystemTester:
    def __init__(self):
        self.platform = UnifiedEnvironmentalPlatform()
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now()
        })
    
    async def test_platform_initialization(self):
        """Test unified platform initialization"""
        try:
            success = await self.platform.initialize()
            self.log_test_result(
                "Platform Initialization", 
                success, 
                "Platform initialized successfully" if success else "Failed to initialize"
            )
            return success
        except Exception as e:
            self.log_test_result("Platform Initialization", False, f"Exception: {e}")
            return False
    
    async def test_unified_operation(self):
        """Test unified operation execution"""
        try:
            # Test area (Taiwan Strait)
            area_bbox = (119.0, 23.0, 121.0, 25.0)
            
            result = await self.platform.run_unified_operation(area_bbox, "comprehensive_analysis")
            
            success = result.success and result.operation_id
            self.log_test_result(
                "Unified Operation", 
                success, 
                f"Operation {result.operation_id} completed in {result.duration_seconds:.2f}s"
            )
            return success
        except Exception as e:
            self.log_test_result("Unified Operation", False, f"Exception: {e}")
            return False
    
    async def test_dashboard_data_generation(self):
        """Test dashboard data generation"""
        try:
            area_bbox = (119.0, 23.0, 121.0, 25.0)
            dashboard_data = await self.platform.get_unified_dashboard_data(area_bbox)
            
            # Check required fields
            required_fields = ['timestamp', 'marine_conservation', 'water_management', 'integrated_analytics']
            success = all(field in dashboard_data for field in required_fields)
            
            self.log_test_result(
                "Dashboard Data Generation", 
                success, 
                f"Generated dashboard data with {len(dashboard_data)} fields"
            )
            return success
        except Exception as e:
            self.log_test_result("Dashboard Data Generation", False, f"Exception: {e}")
            return False
    
    async def test_system_status(self):
        """Test system status retrieval"""
        try:
            status = await self.platform.get_system_status()
            
            success = 'platform_status' in status and status['platform_status'] == 'operational'
            self.log_test_result(
                "System Status", 
                success, 
                f"Platform status: {status.get('platform_status', 'unknown')}"
            )
            return success
        except Exception as e:
            self.log_test_result("System Status", False, f"Exception: {e}")
            return False
    
    async def test_monitoring_area_management(self):
        """Test monitoring area management"""
        try:
            area_id = "test_area_001"
            area_bbox = (119.0, 23.0, 121.0, 25.0)
            name = "Test Monitoring Area"
            
            success = await self.platform.add_monitoring_area(area_id, area_bbox, name)
            self.log_test_result(
                "Monitoring Area Management", 
                success, 
                f"Added monitoring area: {name}"
            )
            return success
        except Exception as e:
            self.log_test_result("Monitoring Area Management", False, f"Exception: {e}")
            return False
    
    def test_api_endpoints(self):
        """Test API endpoints (requires running server)"""
        base_url = "http://localhost:8000"
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            success = response.status_code == 200
            self.log_test_result(
                "API Health Endpoint", 
                success, 
                f"Status: {response.status_code}"
            )
        except requests.exceptions.RequestException as e:
            self.log_test_result("API Health Endpoint", False, f"Connection error: {e}")
            return False
        
        # Test status endpoint
        try:
            response = requests.get(f"{base_url}/api/status", timeout=10)
            success = response.status_code == 200
            self.log_test_result(
                "API Status Endpoint", 
                success, 
                f"Status: {response.status_code}"
            )
        except requests.exceptions.RequestException as e:
            self.log_test_result("API Status Endpoint", False, f"Connection error: {e}")
            return False
        
        # Test dashboard endpoint
        try:
            response = requests.get(f"{base_url}/api/dashboard", timeout=15)
            success = response.status_code == 200
            if success:
                data = response.json()
                success = data.get('success', False)
            self.log_test_result(
                "API Dashboard Endpoint", 
                success, 
                f"Status: {response.status_code}"
            )
        except requests.exceptions.RequestException as e:
            self.log_test_result("API Dashboard Endpoint", False, f"Connection error: {e}")
            return False
        
        return True
    
    def test_frontend_accessibility(self):
        """Test frontend accessibility"""
        frontend_url = "http://localhost:3000"
        
        try:
            response = requests.get(frontend_url, timeout=10)
            success = response.status_code == 200
            self.log_test_result(
                "Frontend Accessibility", 
                success, 
                f"Status: {response.status_code}"
            )
            return success
        except requests.exceptions.RequestException as e:
            self.log_test_result("Frontend Accessibility", False, f"Connection error: {e}")
            return False
    
    async def run_backend_tests(self):
        """Run all backend tests"""
        logger.info("🧪 Running Backend Tests")
        logger.info("=" * 50)
        
        tests = [
            self.test_platform_initialization,
            self.test_unified_operation,
            self.test_dashboard_data_generation,
            self.test_system_status,
            self.test_monitoring_area_management,
        ]
        
        results = []
        for test in tests:
            try:
                result = await test()
                results.append(result)
            except Exception as e:
                logger.error(f"Test failed with exception: {e}")
                results.append(False)
        
        return results
    
    def run_integration_tests(self):
        """Run integration tests (requires running servers)"""
        logger.info("🔗 Running Integration Tests")
        logger.info("=" * 50)
        
        tests = [
            self.test_api_endpoints,
            self.test_frontend_accessibility,
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                logger.error(f"Test failed with exception: {e}")
                results.append(False)
        
        return results
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info("")
        logger.info("📊 TEST REPORT")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        logger.info("")
        
        if failed_tests > 0:
            logger.info("❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    logger.info(f"  - {result['test']}: {result['message']}")
        
        logger.info("")
        status = "🎉 ALL TESTS PASSED!" if failed_tests == 0 else f"⚠️ {failed_tests} TESTS FAILED"
        logger.info(status)
        
        return success_rate >= 80  # 80% success rate threshold

async def main():
    """Main test execution"""
    logger.info("🌊💧 Unified Environmental Platform - Comprehensive Test Suite")
    logger.info("=" * 70)
    
    tester = IntegratedSystemTester()
    
    # Run backend tests
    backend_results = await tester.run_backend_tests()
    
    # Wait a moment
    await asyncio.sleep(2)
    
    # Run integration tests (these require running servers)
    logger.info("\n⚠️ Integration tests require running servers:")
    logger.info("   Backend: python start_unified_platform.py")
    logger.info("   Or manually: uvicorn src.api.unified_api:app --reload")
    logger.info("")
    
    try:
        integration_results = tester.run_integration_tests()
    except Exception as e:
        logger.warning(f"Integration tests skipped: {e}")
        integration_results = []
    
    # Generate report
    success = tester.generate_test_report()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
