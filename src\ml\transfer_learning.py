"""Transfer Learning System for Water Management."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import copy

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class TransferStrategy(Enum):
    """Transfer learning strategies."""
    FEATURE_EXTRACTION = "feature_extraction"
    FINE_TUNING = "fine_tuning"
    DOMAIN_ADAPTATION = "domain_adaptation"
    MULTI_TASK = "multi_task"
    META_LEARNING = "meta_learning"


class DomainType(Enum):
    """Domain types for transfer learning."""
    WATER_TREATMENT = "water_treatment"
    WASTEWATER = "wastewater"
    DRINKING_WATER = "drinking_water"
    INDUSTRIAL_WATER = "industrial_water"
    AGRICULTURAL_WATER = "agricultural_water"
    ENVIRONMENTAL_MONITORING = "environmental_monitoring"


@dataclass
class SourceDomain:
    """Source domain for transfer learning."""
    domain_id: str
    domain_type: DomainType
    model_path: str
    data_characteristics: Dict[str, Any]
    performance_metrics: Dict[str, float]
    training_samples: int
    feature_dim: int
    output_dim: int
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class TransferTask:
    """Transfer learning task configuration."""
    task_id: str
    source_domain: SourceDomain
    target_domain: DomainType
    transfer_strategy: TransferStrategy
    target_data_size: int
    similarity_score: float
    expected_improvement: float
    status: str = "pending"


class WaterQualityEncoder(nn.Module):
    """Encoder network for water quality features."""
    
    def __init__(self, input_dim: int = 20, hidden_dim: int = 256, latent_dim: int = 64):
        super(WaterQualityEncoder, self).__init__()
        
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, latent_dim),
            nn.ReLU()
        )
        
        self.latent_dim = latent_dim
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through encoder."""
        return self.encoder(x)


class WaterQualityPredictor(nn.Module):
    """Predictor network for water quality."""
    
    def __init__(self, latent_dim: int = 64, hidden_dim: int = 128, output_dim: int = 5):
        super(WaterQualityPredictor, self).__init__()
        
        self.predictor = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, output_dim)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through predictor."""
        return self.predictor(x)


class TransferableWaterModel(nn.Module):
    """Complete transferable model for water management."""
    
    def __init__(self, input_dim: int = 20, hidden_dim: int = 256, 
                 latent_dim: int = 64, output_dim: int = 5):
        super(TransferableWaterModel, self).__init__()
        
        self.encoder = WaterQualityEncoder(input_dim, hidden_dim, latent_dim)
        self.predictor = WaterQualityPredictor(latent_dim, hidden_dim // 2, output_dim)
        
        # Domain classifier for domain adaptation
        self.domain_classifier = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 2),  # Binary domain classification
            nn.Softmax(dim=1)
        )
    
    def forward(self, x: torch.Tensor, return_features: bool = False) -> torch.Tensor:
        """Forward pass through complete model."""
        features = self.encoder(x)
        predictions = self.predictor(features)
        
        if return_features:
            return predictions, features
        return predictions
    
    def get_features(self, x: torch.Tensor) -> torch.Tensor:
        """Extract features using encoder."""
        return self.encoder(x)
    
    def classify_domain(self, features: torch.Tensor) -> torch.Tensor:
        """Classify domain of features."""
        return self.domain_classifier(features)


class TransferLearningSystem:
    """Comprehensive transfer learning system for water management."""
    
    def __init__(self):
        self.source_domains: Dict[str, SourceDomain] = {}
        self.transfer_tasks: Dict[str, TransferTask] = {}
        self.trained_models: Dict[str, TransferableWaterModel] = {}
        
        # Initialize some pre-trained source domains
        self._initialize_source_domains()
    
    def _initialize_source_domains(self):
        """Initialize pre-trained source domains."""
        # Drinking water treatment domain
        drinking_water_domain = SourceDomain(
            domain_id="drinking_water_v1",
            domain_type=DomainType.DRINKING_WATER,
            model_path="models/drinking_water_pretrained.pth",
            data_characteristics={
                'ph_range': (6.5, 8.5),
                'turbidity_range': (0.0, 4.0),
                'chlorine_range': (0.2, 4.0),
                'temperature_range': (5.0, 30.0),
                'flow_rate_range': (500.0, 2000.0)
            },
            performance_metrics={
                'accuracy': 0.92,
                'mse': 0.15,
                'r2_score': 0.88
            },
            training_samples=50000,
            feature_dim=20,
            output_dim=5
        )
        
        # Wastewater treatment domain
        wastewater_domain = SourceDomain(
            domain_id="wastewater_v1",
            domain_type=DomainType.WASTEWATER,
            model_path="models/wastewater_pretrained.pth",
            data_characteristics={
                'ph_range': (6.0, 9.0),
                'turbidity_range': (0.0, 50.0),
                'bod_range': (10.0, 300.0),
                'cod_range': (20.0, 500.0),
                'flow_rate_range': (100.0, 5000.0)
            },
            performance_metrics={
                'accuracy': 0.89,
                'mse': 0.22,
                'r2_score': 0.84
            },
            training_samples=30000,
            feature_dim=20,
            output_dim=5
        )
        
        # Industrial water domain
        industrial_domain = SourceDomain(
            domain_id="industrial_v1",
            domain_type=DomainType.INDUSTRIAL_WATER,
            model_path="models/industrial_pretrained.pth",
            data_characteristics={
                'ph_range': (5.0, 10.0),
                'turbidity_range': (0.0, 100.0),
                'conductivity_range': (100.0, 5000.0),
                'temperature_range': (10.0, 80.0),
                'pressure_range': (1.0, 10.0)
            },
            performance_metrics={
                'accuracy': 0.86,
                'mse': 0.28,
                'r2_score': 0.81
            },
            training_samples=25000,
            feature_dim=20,
            output_dim=5
        )
        
        self.source_domains = {
            "drinking_water_v1": drinking_water_domain,
            "wastewater_v1": wastewater_domain,
            "industrial_v1": industrial_domain
        }
    
    @log_async_function_call
    async def analyze_transfer_potential(self, target_config: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze transfer learning potential for target domain."""
        try:
            target_domain = DomainType(target_config['target_domain'])
            target_characteristics = target_config.get('data_characteristics', {})
            target_data_size = target_config.get('data_size', 1000)
            
            transfer_recommendations = []
            
            for source_id, source_domain in self.source_domains.items():
                # Calculate domain similarity
                similarity = await self._calculate_domain_similarity(
                    source_domain.data_characteristics, target_characteristics
                )
                
                # Estimate transfer benefit
                transfer_benefit = await self._estimate_transfer_benefit(
                    source_domain, target_domain, target_data_size, similarity
                )
                
                # Recommend transfer strategy
                strategy = await self._recommend_transfer_strategy(
                    source_domain, target_domain, similarity, target_data_size
                )
                
                recommendation = {
                    'source_domain_id': source_id,
                    'source_domain_type': source_domain.domain_type.value,
                    'similarity_score': similarity,
                    'expected_improvement': transfer_benefit,
                    'recommended_strategy': strategy.value,
                    'confidence': min(1.0, similarity * 0.8 + transfer_benefit * 0.2),
                    'source_performance': source_domain.performance_metrics
                }
                
                transfer_recommendations.append(recommendation)
            
            # Sort by expected improvement
            transfer_recommendations.sort(key=lambda x: x['expected_improvement'], reverse=True)
            
            return {
                'status': 'success',
                'target_domain': target_domain.value,
                'target_data_size': target_data_size,
                'transfer_recommendations': transfer_recommendations,
                'best_source': transfer_recommendations[0] if transfer_recommendations else None,
                'analysis_summary': {
                    'total_sources_analyzed': len(self.source_domains),
                    'viable_transfers': len([r for r in transfer_recommendations if r['expected_improvement'] > 0.1]),
                    'max_expected_improvement': max([r['expected_improvement'] for r in transfer_recommendations]) if transfer_recommendations else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Transfer potential analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _calculate_domain_similarity(self, source_chars: Dict[str, Any], 
                                         target_chars: Dict[str, Any]) -> float:
        """Calculate similarity between source and target domains."""
        if not target_chars:
            return 0.5  # Default similarity if no target characteristics
        
        similarities = []
        
        # Compare overlapping characteristics
        for key in source_chars:
            if key in target_chars:
                source_range = source_chars[key]
                target_range = target_chars[key]
                
                if isinstance(source_range, tuple) and isinstance(target_range, tuple):
                    # Calculate range overlap
                    overlap_start = max(source_range[0], target_range[0])
                    overlap_end = min(source_range[1], target_range[1])
                    
                    if overlap_end > overlap_start:
                        overlap_size = overlap_end - overlap_start
                        source_size = source_range[1] - source_range[0]
                        target_size = target_range[1] - target_range[0]
                        
                        # Jaccard similarity for ranges
                        union_size = source_size + target_size - overlap_size
                        similarity = overlap_size / union_size if union_size > 0 else 0
                        similarities.append(similarity)
                    else:
                        similarities.append(0.0)  # No overlap
        
        # Return average similarity
        return np.mean(similarities) if similarities else 0.3  # Default low similarity
    
    async def _estimate_transfer_benefit(self, source_domain: SourceDomain, 
                                       target_domain: DomainType, 
                                       target_data_size: int, 
                                       similarity: float) -> float:
        """Estimate potential benefit from transfer learning."""
        # Base benefit from source model performance
        base_benefit = source_domain.performance_metrics.get('r2_score', 0.5)
        
        # Similarity bonus
        similarity_bonus = similarity * 0.3
        
        # Data size penalty (less benefit with more target data)
        data_penalty = min(0.2, target_data_size / 10000)  # Penalty increases with more data
        
        # Domain compatibility bonus
        compatibility_bonus = 0.0
        if source_domain.domain_type == target_domain:
            compatibility_bonus = 0.2  # Same domain type
        elif (source_domain.domain_type in [DomainType.DRINKING_WATER, DomainType.WATER_TREATMENT] and
              target_domain in [DomainType.DRINKING_WATER, DomainType.WATER_TREATMENT]):
            compatibility_bonus = 0.15  # Related domains
        
        # Calculate final benefit
        transfer_benefit = (base_benefit * 0.4 + similarity_bonus + 
                          compatibility_bonus - data_penalty)
        
        return max(0.0, min(1.0, transfer_benefit))
    
    async def _recommend_transfer_strategy(self, source_domain: SourceDomain,
                                         target_domain: DomainType,
                                         similarity: float,
                                         target_data_size: int) -> TransferStrategy:
        """Recommend best transfer learning strategy."""
        if similarity > 0.8 and target_data_size < 1000:
            return TransferStrategy.FEATURE_EXTRACTION
        elif similarity > 0.6 and target_data_size < 5000:
            return TransferStrategy.FINE_TUNING
        elif similarity > 0.4:
            return TransferStrategy.DOMAIN_ADAPTATION
        elif target_data_size > 10000:
            return TransferStrategy.MULTI_TASK
        else:
            return TransferStrategy.FINE_TUNING  # Default
    
    @log_async_function_call
    async def create_transfer_task(self, task_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create new transfer learning task."""
        try:
            task_id = task_config.get('task_id', f"transfer_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            source_domain_id = task_config['source_domain_id']
            target_domain = DomainType(task_config['target_domain'])
            strategy = TransferStrategy(task_config.get('strategy', 'fine_tuning'))
            
            if source_domain_id not in self.source_domains:
                return {'status': 'error', 'error': 'Source domain not found'}
            
            source_domain = self.source_domains[source_domain_id]
            
            # Calculate similarity and expected improvement
            target_characteristics = task_config.get('target_characteristics', {})
            similarity = await self._calculate_domain_similarity(
                source_domain.data_characteristics, target_characteristics
            )
            
            expected_improvement = await self._estimate_transfer_benefit(
                source_domain, target_domain, 
                task_config.get('target_data_size', 1000), similarity
            )
            
            # Create transfer task
            transfer_task = TransferTask(
                task_id=task_id,
                source_domain=source_domain,
                target_domain=target_domain,
                transfer_strategy=strategy,
                target_data_size=task_config.get('target_data_size', 1000),
                similarity_score=similarity,
                expected_improvement=expected_improvement
            )
            
            self.transfer_tasks[task_id] = transfer_task
            
            return {
                'status': 'success',
                'task_id': task_id,
                'transfer_config': {
                    'source_domain': source_domain.domain_type.value,
                    'target_domain': target_domain.value,
                    'strategy': strategy.value,
                    'similarity_score': similarity,
                    'expected_improvement': expected_improvement
                },
                'task_created': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Transfer task creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def execute_transfer_learning(self, task_id: str, 
                                      target_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute transfer learning task."""
        try:
            if task_id not in self.transfer_tasks:
                return {'status': 'error', 'error': 'Transfer task not found'}
            
            task = self.transfer_tasks[task_id]
            task.status = "running"
            
            # Create target model based on source
            target_model = await self._create_target_model(task)
            
            # Generate synthetic target data for demonstration
            target_X, target_y = await self._generate_target_data(task, target_data)
            
            # Execute transfer strategy
            if task.transfer_strategy == TransferStrategy.FEATURE_EXTRACTION:
                result = await self._feature_extraction_transfer(target_model, target_X, target_y, task)
            elif task.transfer_strategy == TransferStrategy.FINE_TUNING:
                result = await self._fine_tuning_transfer(target_model, target_X, target_y, task)
            elif task.transfer_strategy == TransferStrategy.DOMAIN_ADAPTATION:
                result = await self._domain_adaptation_transfer(target_model, target_X, target_y, task)
            else:
                result = await self._fine_tuning_transfer(target_model, target_X, target_y, task)
            
            # Store trained model
            self.trained_models[task_id] = target_model
            task.status = "completed"
            
            return {
                'status': 'success',
                'task_id': task_id,
                'transfer_results': result,
                'model_performance': await self._evaluate_transferred_model(target_model, target_X, target_y),
                'completion_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Transfer learning execution failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _create_target_model(self, task: TransferTask) -> TransferableWaterModel:
        """Create target model based on source domain."""
        # Create model with same architecture as source
        model = TransferableWaterModel(
            input_dim=task.source_domain.feature_dim,
            output_dim=task.source_domain.output_dim
        )
        
        # Initialize with random weights (in practice, would load pre-trained weights)
        for param in model.parameters():
            if len(param.shape) > 1:
                nn.init.xavier_uniform_(param)
            else:
                nn.init.zeros_(param)
        
        return model
    
    async def _generate_target_data(self, task: TransferTask, 
                                  data_config: Dict[str, Any]) -> Tuple[torch.Tensor, torch.Tensor]:
        """Generate synthetic target domain data."""
        n_samples = task.target_data_size
        feature_dim = task.source_domain.feature_dim
        
        # Generate features with domain-specific characteristics
        X = torch.randn(n_samples, feature_dim)
        
        # Adjust features based on target domain
        if task.target_domain == DomainType.WASTEWATER:
            # Higher turbidity and different pH range for wastewater
            X[:, 0] = torch.normal(7.5, 0.8, (n_samples,))  # pH
            X[:, 1] = torch.exponential(torch.tensor(5.0)).expand(n_samples)  # Higher turbidity
        elif task.target_domain == DomainType.DRINKING_WATER:
            # Stricter ranges for drinking water
            X[:, 0] = torch.normal(7.2, 0.3, (n_samples,))  # pH
            X[:, 1] = torch.exponential(torch.tensor(1.0)).expand(n_samples)  # Low turbidity
        
        # Generate target labels
        y = torch.randn(n_samples, task.source_domain.output_dim)
        
        return X, y
    
    async def _feature_extraction_transfer(self, model: TransferableWaterModel,
                                         X: torch.Tensor, y: torch.Tensor,
                                         task: TransferTask) -> Dict[str, Any]:
        """Perform feature extraction transfer learning."""
        # Freeze encoder weights
        for param in model.encoder.parameters():
            param.requires_grad = False
        
        # Only train predictor
        optimizer = optim.Adam(model.predictor.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        model.train()
        total_loss = 0.0
        epochs = 50
        
        for epoch in range(epochs):
            optimizer.zero_grad()
            
            # Extract features with frozen encoder
            with torch.no_grad():
                features = model.encoder(X)
            
            # Train only predictor
            predictions = model.predictor(features)
            loss = criterion(predictions, y)
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        return {
            'strategy': 'feature_extraction',
            'epochs_trained': epochs,
            'final_loss': total_loss / epochs,
            'frozen_layers': 'encoder',
            'trainable_parameters': sum(p.numel() for p in model.predictor.parameters())
        }
    
    async def _fine_tuning_transfer(self, model: TransferableWaterModel,
                                  X: torch.Tensor, y: torch.Tensor,
                                  task: TransferTask) -> Dict[str, Any]:
        """Perform fine-tuning transfer learning."""
        # Train all layers with lower learning rate
        optimizer = optim.Adam(model.parameters(), lr=0.0001)  # Lower LR for fine-tuning
        criterion = nn.MSELoss()
        
        model.train()
        total_loss = 0.0
        epochs = 100
        
        for epoch in range(epochs):
            optimizer.zero_grad()
            
            predictions = model(X)
            loss = criterion(predictions, y)
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        return {
            'strategy': 'fine_tuning',
            'epochs_trained': epochs,
            'final_loss': total_loss / epochs,
            'learning_rate': 0.0001,
            'trainable_parameters': sum(p.numel() for p in model.parameters())
        }
    
    async def _domain_adaptation_transfer(self, model: TransferableWaterModel,
                                        X: torch.Tensor, y: torch.Tensor,
                                        task: TransferTask) -> Dict[str, Any]:
        """Perform domain adaptation transfer learning."""
        # Train with domain adversarial loss
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        domain_criterion = nn.CrossEntropyLoss()
        
        model.train()
        total_loss = 0.0
        epochs = 75
        
        # Create domain labels (0 for source, 1 for target)
        domain_labels = torch.ones(X.shape[0], dtype=torch.long)  # All target domain
        
        for epoch in range(epochs):
            optimizer.zero_grad()
            
            # Forward pass
            predictions, features = model(X, return_features=True)
            domain_pred = model.classify_domain(features)
            
            # Task loss
            task_loss = criterion(predictions, y)
            
            # Domain adversarial loss (reverse gradient)
            domain_loss = domain_criterion(domain_pred, domain_labels)
            
            # Combined loss
            total_loss_batch = task_loss - 0.1 * domain_loss  # Adversarial term
            
            total_loss_batch.backward()
            optimizer.step()
            
            total_loss += total_loss_batch.item()
        
        return {
            'strategy': 'domain_adaptation',
            'epochs_trained': epochs,
            'final_loss': total_loss / epochs,
            'domain_adversarial': True,
            'trainable_parameters': sum(p.numel() for p in model.parameters())
        }
    
    async def _evaluate_transferred_model(self, model: TransferableWaterModel,
                                        X: torch.Tensor, y: torch.Tensor) -> Dict[str, Any]:
        """Evaluate transferred model performance."""
        model.eval()
        
        with torch.no_grad():
            predictions = model(X)
            mse = nn.MSELoss()(predictions, y).item()
            mae = nn.L1Loss()(predictions, y).item()
            
            # Calculate R² score
            ss_res = torch.sum((y - predictions) ** 2).item()
            ss_tot = torch.sum((y - torch.mean(y)) ** 2).item()
            r2_score = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        
        return {
            'mse': mse,
            'mae': mae,
            'r2_score': r2_score,
            'rmse': np.sqrt(mse)
        }
    
    @log_async_function_call
    async def get_transfer_status(self, task_id: str) -> Dict[str, Any]:
        """Get status of transfer learning task."""
        try:
            if task_id not in self.transfer_tasks:
                return {'status': 'error', 'error': 'Transfer task not found'}
            
            task = self.transfer_tasks[task_id]
            
            return {
                'status': 'success',
                'task_info': {
                    'task_id': task_id,
                    'source_domain': task.source_domain.domain_type.value,
                    'target_domain': task.target_domain.value,
                    'transfer_strategy': task.transfer_strategy.value,
                    'similarity_score': task.similarity_score,
                    'expected_improvement': task.expected_improvement,
                    'current_status': task.status
                },
                'model_available': task_id in self.trained_models,
                'source_performance': task.source_domain.performance_metrics
            }
            
        except Exception as e:
            logger.error(f"Transfer status retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience functions
async def analyze_transfer_opportunities(target_domain: str, 
                                       target_characteristics: Dict[str, Any] = None) -> Dict[str, Any]:
    """Analyze transfer learning opportunities for target domain."""
    system = TransferLearningSystem()
    
    config = {
        'target_domain': target_domain,
        'data_characteristics': target_characteristics or {},
        'data_size': 1000
    }
    
    return await system.analyze_transfer_potential(config)


async def execute_water_transfer_learning(source_domain: str, target_domain: str,
                                        strategy: str = 'fine_tuning') -> Dict[str, Any]:
    """Execute transfer learning for water management."""
    system = TransferLearningSystem()
    
    # Create transfer task
    task_config = {
        'source_domain_id': source_domain,
        'target_domain': target_domain,
        'strategy': strategy,
        'target_data_size': 1000
    }
    
    task_result = await system.create_transfer_task(task_config)
    if task_result['status'] != 'success':
        return task_result
    
    # Execute transfer
    return await system.execute_transfer_learning(task_result['task_id'], {})
