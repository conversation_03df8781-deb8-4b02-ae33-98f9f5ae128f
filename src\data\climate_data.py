"""Climate Data Collection for Water Management System."""

import asyncio
import logging
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import os

logger = logging.getLogger(__name__)


class ClimateDataCollector:
    """Climate data collection from multiple sources."""
    
    def __init__(self):
        self.api_keys = {
            'openweathermap': os.getenv('OPENWEATHER_API_KEY', '********************************'),
            'nasa': os.getenv('NASA_API_KEY', 'DEMO_KEY'),
            'noaa': os.getenv('NOAA_API_KEY', 'demo_key'),
            'worldbank': None,  # No key required
            'european_climate': None  # No key required
        }
        
        self.base_urls = {
            'openweathermap': 'https://api.openweathermap.org/data/2.5',
            'nasa': 'https://api.nasa.gov/planetary/earth',
            'noaa': 'https://www.ncdc.noaa.gov/cdo-web/api/v2',
            'worldbank': 'https://climateknowledgeportal.worldbank.org/api',
            'european_climate': 'https://climate.copernicus.eu/api'
        }
        
        self.collected_data = []
    
    async def collect_weather_data(self, location: str) -> Dict[str, Any]:
        """Collect weather data for a specific location."""
        try:
            # Primary source: OpenWeatherMap
            weather_data = await self._get_openweather_data(location)
            
            if weather_data:
                # Enrich with additional sources
                enriched_data = await self._enrich_weather_data(weather_data, location)
                
                # Store collected data
                self.collected_data.append({
                    'location': location,
                    'data': enriched_data,
                    'timestamp': datetime.now().isoformat(),
                    'sources': ['openweathermap']
                })
                
                return enriched_data
            else:
                # Fallback to simulated data
                return await self._generate_simulated_data(location)
                
        except Exception as e:
            logger.error(f"Weather data collection failed for {location}: {e}")
            return await self._generate_simulated_data(location)
    
    async def _get_openweather_data(self, location: str) -> Optional[Dict[str, Any]]:
        """Get data from OpenWeatherMap API."""
        try:
            api_key = self.api_keys['openweathermap']
            url = f"{self.base_urls['openweathermap']}/weather"
            
            params = {
                'q': location,
                'appid': api_key,
                'units': 'metric'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                return {
                    'temperature': data['main']['temp'],
                    'humidity': data['main']['humidity'],
                    'pressure': data['main']['pressure'],
                    'wind_speed': data.get('wind', {}).get('speed', 0),
                    'wind_direction': data.get('wind', {}).get('deg', 0),
                    'visibility': data.get('visibility', 10000) / 1000,  # Convert to km
                    'weather_description': data['weather'][0]['description'],
                    'cloud_cover': data['clouds']['all'],
                    'coordinates': {
                        'lat': data['coord']['lat'],
                        'lon': data['coord']['lon']
                    },
                    'source': 'openweathermap',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                logger.warning(f"OpenWeatherMap API error: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"OpenWeatherMap API request failed: {e}")
            return None
    
    async def _enrich_weather_data(self, base_data: Dict[str, Any], location: str) -> Dict[str, Any]:
        """Enrich weather data with additional sources."""
        enriched = base_data.copy()
        
        # Add climate context
        enriched['climate_context'] = await self._get_climate_context(location)
        
        # Add air quality data (simulated)
        enriched['air_quality'] = {
            'aqi': 45,  # Simulated Air Quality Index
            'pm25': 12.5,  # Simulated PM2.5
            'pm10': 18.2,  # Simulated PM10
            'no2': 25.3,   # Simulated NO2
            'source': 'simulated'
        }
        
        # Add UV index (simulated)
        enriched['uv_index'] = {
            'value': 6.2,
            'risk_level': 'moderate',
            'source': 'simulated'
        }
        
        # Add precipitation forecast (simulated)
        enriched['precipitation_forecast'] = {
            'next_24h': 2.5,  # mm
            'next_48h': 8.1,  # mm
            'next_72h': 15.3, # mm
            'source': 'simulated'
        }
        
        return enriched
    
    async def _get_climate_context(self, location: str) -> Dict[str, Any]:
        """Get climate context for location."""
        # Simulated climate context based on location
        climate_zones = {
            'london': {
                'climate_zone': 'temperate_oceanic',
                'average_annual_temp': 11.0,
                'average_annual_precipitation': 601,
                'seasonal_variation': 'moderate'
            },
            'new york': {
                'climate_zone': 'humid_continental',
                'average_annual_temp': 12.9,
                'average_annual_precipitation': 1268,
                'seasonal_variation': 'high'
            },
            'tokyo': {
                'climate_zone': 'humid_subtropical',
                'average_annual_temp': 15.4,
                'average_annual_precipitation': 1528,
                'seasonal_variation': 'high'
            }
        }
        
        location_lower = location.lower()
        for city, context in climate_zones.items():
            if city in location_lower:
                return context
        
        # Default context
        return {
            'climate_zone': 'unknown',
            'average_annual_temp': 15.0,
            'average_annual_precipitation': 800,
            'seasonal_variation': 'moderate'
        }
    
    async def _generate_simulated_data(self, location: str) -> Dict[str, Any]:
        """Generate simulated weather data as fallback."""
        import random
        
        return {
            'temperature': round(random.uniform(10, 30), 1),
            'humidity': random.randint(30, 90),
            'pressure': round(random.uniform(980, 1030), 1),
            'wind_speed': round(random.uniform(0, 15), 1),
            'wind_direction': random.randint(0, 360),
            'visibility': round(random.uniform(5, 20), 1),
            'weather_description': random.choice(['clear sky', 'few clouds', 'scattered clouds', 'light rain']),
            'cloud_cover': random.randint(0, 100),
            'coordinates': {
                'lat': round(random.uniform(-90, 90), 4),
                'lon': round(random.uniform(-180, 180), 4)
            },
            'climate_context': await self._get_climate_context(location),
            'air_quality': {
                'aqi': random.randint(20, 80),
                'pm25': round(random.uniform(5, 25), 1),
                'pm10': round(random.uniform(10, 40), 1),
                'no2': round(random.uniform(15, 45), 1),
                'source': 'simulated'
            },
            'uv_index': {
                'value': round(random.uniform(1, 10), 1),
                'risk_level': random.choice(['low', 'moderate', 'high']),
                'source': 'simulated'
            },
            'precipitation_forecast': {
                'next_24h': round(random.uniform(0, 10), 1),
                'next_48h': round(random.uniform(0, 20), 1),
                'next_72h': round(random.uniform(0, 30), 1),
                'source': 'simulated'
            },
            'source': 'simulated',
            'timestamp': datetime.now().isoformat()
        }
    
    async def collect_multiple_locations(self, locations: List[str]) -> Dict[str, Any]:
        """Collect weather data for multiple locations."""
        results = {}
        
        for location in locations:
            try:
                data = await self.collect_weather_data(location)
                results[location] = data
                logger.info(f"Weather data collected for {location}")
            except Exception as e:
                logger.error(f"Failed to collect data for {location}: {e}")
                results[location] = {'error': str(e)}
        
        return results
    
    async def get_climate_trends(self, location: str, days: int = 30) -> Dict[str, Any]:
        """Get climate trends for location (simulated)."""
        import random
        
        # Simulate historical trend data
        trend_data = []
        base_temp = random.uniform(15, 25)
        
        for i in range(days):
            day_data = {
                'date': (datetime.now() - timedelta(days=days-i)).isoformat()[:10],
                'temperature': round(base_temp + random.uniform(-5, 5), 1),
                'humidity': random.randint(40, 80),
                'precipitation': round(random.uniform(0, 15), 1)
            }
            trend_data.append(day_data)
        
        # Calculate trends
        temps = [d['temperature'] for d in trend_data]
        humidity_vals = [d['humidity'] for d in trend_data]
        precip_vals = [d['precipitation'] for d in trend_data]
        
        return {
            'location': location,
            'period_days': days,
            'trends': {
                'temperature': {
                    'average': round(sum(temps) / len(temps), 1),
                    'min': min(temps),
                    'max': max(temps),
                    'trend': 'stable'  # Simplified
                },
                'humidity': {
                    'average': round(sum(humidity_vals) / len(humidity_vals), 1),
                    'min': min(humidity_vals),
                    'max': max(humidity_vals),
                    'trend': 'stable'
                },
                'precipitation': {
                    'total': round(sum(precip_vals), 1),
                    'average_daily': round(sum(precip_vals) / len(precip_vals), 1),
                    'max_daily': max(precip_vals),
                    'trend': 'stable'
                }
            },
            'data_points': trend_data,
            'generated_at': datetime.now().isoformat()
        }
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about collected data."""
        return {
            'total_collections': len(self.collected_data),
            'unique_locations': len(set(d['location'] for d in self.collected_data)),
            'data_sources': list(self.api_keys.keys()),
            'last_collection': self.collected_data[-1]['timestamp'] if self.collected_data else None,
            'api_status': {
                source: 'configured' if key else 'not_configured'
                for source, key in self.api_keys.items()
            }
        }
