"""AutoML Pipeline for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Types of ML models for AutoML."""
    LINEAR_REGRESSION = "linear_regression"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    NEURAL_NETWORK = "neural_network"
    SVM = "svm"
    LSTM = "lstm"
    TRANSFORMER = "transformer"


class OptimizationObjective(Enum):
    """Optimization objectives for AutoML."""
    ACCURACY = "accuracy"
    PRECISION = "precision"
    RECALL = "recall"
    F1_SCORE = "f1_score"
    AUC_ROC = "auc_roc"
    MAE = "mae"
    RMSE = "rmse"
    R2_SCORE = "r2_score"


@dataclass
class ModelCandidate:
    """ML model candidate for AutoML."""
    model_id: str
    model_type: ModelType
    hyperparameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    training_time: float
    model_size_mb: float
    complexity_score: float
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class AutoMLExperiment:
    """AutoML experiment configuration."""
    experiment_id: str
    dataset_info: Dict[str, Any]
    target_variable: str
    problem_type: str  # 'regression' or 'classification'
    optimization_objective: OptimizationObjective
    time_budget_minutes: int
    model_types: List[ModelType]
    cross_validation_folds: int
    created_at: datetime = field(default_factory=datetime.now)
    status: str = "pending"


class AutoMLPipeline:
    """Automated Machine Learning pipeline for water management."""
    
    def __init__(self):
        self.experiments: Dict[str, AutoMLExperiment] = {}
        self.model_candidates: Dict[str, List[ModelCandidate]] = {}
        self.best_models: Dict[str, ModelCandidate] = {}
        
        # AutoML configuration
        self.config = {
            'max_models_per_type': 5,
            'early_stopping_patience': 10,
            'hyperparameter_optimization': 'bayesian',
            'feature_selection': True,
            'ensemble_methods': True,
            'model_interpretability': True
        }
        
        # Model templates
        self.model_templates = self._initialize_model_templates()
        
        # Hyperparameter search spaces
        self.hyperparameter_spaces = self._initialize_hyperparameter_spaces()
    
    def _initialize_model_templates(self) -> Dict[ModelType, Dict[str, Any]]:
        """Initialize model templates with default configurations."""
        return {
            ModelType.LINEAR_REGRESSION: {
                'base_params': {'fit_intercept': True, 'normalize': False},
                'complexity': 1,
                'training_time_factor': 0.1
            },
            ModelType.RANDOM_FOREST: {
                'base_params': {'random_state': 42, 'n_jobs': -1},
                'complexity': 3,
                'training_time_factor': 0.5
            },
            ModelType.GRADIENT_BOOSTING: {
                'base_params': {'random_state': 42, 'validation_fraction': 0.1},
                'complexity': 4,
                'training_time_factor': 0.8
            },
            ModelType.NEURAL_NETWORK: {
                'base_params': {'random_state': 42, 'early_stopping': True},
                'complexity': 5,
                'training_time_factor': 1.5
            },
            ModelType.SVM: {
                'base_params': {'probability': True, 'random_state': 42},
                'complexity': 3,
                'training_time_factor': 1.0
            },
            ModelType.LSTM: {
                'base_params': {'epochs': 100, 'batch_size': 32},
                'complexity': 6,
                'training_time_factor': 2.0
            },
            ModelType.TRANSFORMER: {
                'base_params': {'num_heads': 8, 'num_layers': 4},
                'complexity': 7,
                'training_time_factor': 3.0
            }
        }
    
    def _initialize_hyperparameter_spaces(self) -> Dict[ModelType, Dict[str, Any]]:
        """Initialize hyperparameter search spaces."""
        return {
            ModelType.LINEAR_REGRESSION: {
                'alpha': [0.001, 0.01, 0.1, 1.0, 10.0],
                'l1_ratio': [0.0, 0.25, 0.5, 0.75, 1.0]
            },
            ModelType.RANDOM_FOREST: {
                'n_estimators': [50, 100, 200, 300],
                'max_depth': [5, 10, 15, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2', None]
            },
            ModelType.GRADIENT_BOOSTING: {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7, 9],
                'subsample': [0.8, 0.9, 1.0],
                'min_samples_split': [2, 5, 10]
            },
            ModelType.NEURAL_NETWORK: {
                'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50), (100, 100)],
                'activation': ['relu', 'tanh', 'logistic'],
                'alpha': [0.0001, 0.001, 0.01],
                'learning_rate': ['constant', 'adaptive'],
                'max_iter': [200, 500, 1000]
            },
            ModelType.SVM: {
                'C': [0.1, 1.0, 10.0, 100.0],
                'kernel': ['linear', 'rbf', 'poly'],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
                'degree': [2, 3, 4]
            },
            ModelType.LSTM: {
                'units': [32, 64, 128],
                'dropout': [0.0, 0.2, 0.3, 0.5],
                'recurrent_dropout': [0.0, 0.2, 0.3],
                'learning_rate': [0.001, 0.01, 0.1],
                'batch_size': [16, 32, 64]
            },
            ModelType.TRANSFORMER: {
                'num_heads': [4, 8, 12],
                'num_layers': [2, 4, 6],
                'hidden_dim': [128, 256, 512],
                'dropout': [0.1, 0.2, 0.3],
                'learning_rate': [0.0001, 0.001, 0.01]
            }
        }
    
    @log_async_function_call
    async def create_experiment(self, dataset_info: Dict[str, Any],
                              target_variable: str,
                              problem_type: str,
                              optimization_objective: OptimizationObjective,
                              time_budget_minutes: int = 60,
                              model_types: List[ModelType] = None) -> Dict[str, Any]:
        """Create AutoML experiment."""
        try:
            experiment_id = f"automl_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Default model types if not specified
            if model_types is None:
                if problem_type == 'regression':
                    model_types = [
                        ModelType.LINEAR_REGRESSION,
                        ModelType.RANDOM_FOREST,
                        ModelType.GRADIENT_BOOSTING,
                        ModelType.NEURAL_NETWORK
                    ]
                else:  # classification
                    model_types = [
                        ModelType.RANDOM_FOREST,
                        ModelType.GRADIENT_BOOSTING,
                        ModelType.NEURAL_NETWORK,
                        ModelType.SVM
                    ]
            
            experiment = AutoMLExperiment(
                experiment_id=experiment_id,
                dataset_info=dataset_info,
                target_variable=target_variable,
                problem_type=problem_type,
                optimization_objective=optimization_objective,
                time_budget_minutes=time_budget_minutes,
                model_types=model_types,
                cross_validation_folds=5
            )
            
            self.experiments[experiment_id] = experiment
            self.model_candidates[experiment_id] = []
            
            return {
                'status': 'success',
                'experiment_id': experiment_id,
                'experiment_config': {
                    'dataset_info': dataset_info,
                    'target_variable': target_variable,
                    'problem_type': problem_type,
                    'optimization_objective': optimization_objective.value,
                    'time_budget_minutes': time_budget_minutes,
                    'model_types': [mt.value for mt in model_types],
                    'cross_validation_folds': experiment.cross_validation_folds
                },
                'created_at': experiment.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"AutoML experiment creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def run_experiment(self, experiment_id: str) -> Dict[str, Any]:
        """Run AutoML experiment."""
        try:
            if experiment_id not in self.experiments:
                return {'status': 'error', 'error': 'Experiment not found'}
            
            experiment = self.experiments[experiment_id]
            experiment.status = "running"
            
            logger.info(f"Starting AutoML experiment {experiment_id}")
            
            # Calculate time budget per model type
            time_per_model_type = experiment.time_budget_minutes / len(experiment.model_types)
            
            all_candidates = []
            
            # Train models for each type
            for model_type in experiment.model_types:
                logger.info(f"Training {model_type.value} models")
                
                candidates = await self._train_model_type(
                    experiment, model_type, time_per_model_type
                )
                
                all_candidates.extend(candidates)
                self.model_candidates[experiment_id].extend(candidates)
            
            # Select best model
            best_model = self._select_best_model(
                all_candidates, experiment.optimization_objective
            )
            
            if best_model:
                self.best_models[experiment_id] = best_model
            
            # Create ensemble if enabled
            ensemble_model = None
            if self.config['ensemble_methods'] and len(all_candidates) > 1:
                ensemble_model = await self._create_ensemble(
                    all_candidates[:5], experiment  # Top 5 models
                )
            
            experiment.status = "completed"
            
            # Generate experiment report
            report = self._generate_experiment_report(
                experiment, all_candidates, best_model, ensemble_model
            )
            
            return {
                'status': 'success',
                'experiment_id': experiment_id,
                'experiment_report': report
            }
            
        except Exception as e:
            logger.error(f"AutoML experiment execution failed: {e}")
            if experiment_id in self.experiments:
                self.experiments[experiment_id].status = "failed"
            return {'status': 'error', 'error': str(e)}
    
    async def _train_model_type(self, experiment: AutoMLExperiment,
                              model_type: ModelType,
                              time_budget_minutes: float) -> List[ModelCandidate]:
        """Train multiple models of a specific type."""
        candidates = []
        
        # Get hyperparameter space
        param_space = self.hyperparameter_spaces.get(model_type, {})
        model_template = self.model_templates.get(model_type, {})
        
        # Generate hyperparameter combinations
        param_combinations = self._generate_hyperparameter_combinations(
            param_space, max_combinations=self.config['max_models_per_type']
        )
        
        for i, params in enumerate(param_combinations):
            # Simulate model training
            candidate = await self._train_single_model(
                experiment, model_type, params, model_template
            )
            
            if candidate:
                candidates.append(candidate)
            
            # Check time budget (simplified)
            if i >= self.config['max_models_per_type'] - 1:
                break
        
        # Sort by performance
        candidates.sort(
            key=lambda x: x.performance_metrics.get(
                experiment.optimization_objective.value, 0
            ),
            reverse=True
        )
        
        return candidates
    
    def _generate_hyperparameter_combinations(self, param_space: Dict[str, List],
                                            max_combinations: int) -> List[Dict[str, Any]]:
        """Generate hyperparameter combinations."""
        if not param_space:
            return [{}]
        
        combinations = []
        
        # Simple grid search (in practice, would use more sophisticated methods)
        import itertools
        
        param_names = list(param_space.keys())
        param_values = list(param_space.values())
        
        # Generate all combinations
        all_combinations = list(itertools.product(*param_values))
        
        # Limit to max_combinations
        selected_combinations = all_combinations[:max_combinations]
        
        for combination in selected_combinations:
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        return combinations
    
    async def _train_single_model(self, experiment: AutoMLExperiment,
                                model_type: ModelType,
                                hyperparameters: Dict[str, Any],
                                model_template: Dict[str, Any]) -> Optional[ModelCandidate]:
        """Train a single model with specific hyperparameters."""
        try:
            model_id = f"{model_type.value}_{len(self.model_candidates.get(experiment.experiment_id, []))}"
            
            # Simulate training time
            base_time = model_template.get('training_time_factor', 1.0)
            data_size_factor = experiment.dataset_info.get('num_samples', 1000) / 1000
            training_time = base_time * data_size_factor * np.random.uniform(0.8, 1.2)
            
            # Simulate performance metrics
            performance_metrics = self._simulate_model_performance(
                model_type, experiment.problem_type, experiment.optimization_objective
            )
            
            # Calculate model complexity
            complexity_score = model_template.get('complexity', 3)
            complexity_score += len(hyperparameters) * 0.1  # More hyperparameters = more complex
            
            # Simulate model size
            model_size_mb = complexity_score * np.random.uniform(5, 20)
            
            candidate = ModelCandidate(
                model_id=model_id,
                model_type=model_type,
                hyperparameters=hyperparameters,
                performance_metrics=performance_metrics,
                training_time=training_time,
                model_size_mb=model_size_mb,
                complexity_score=complexity_score
            )
            
            return candidate
            
        except Exception as e:
            logger.error(f"Single model training failed: {e}")
            return None
    
    def _simulate_model_performance(self, model_type: ModelType,
                                  problem_type: str,
                                  optimization_objective: OptimizationObjective) -> Dict[str, float]:
        """Simulate model performance metrics."""
        # Base performance by model type
        base_performance = {
            ModelType.LINEAR_REGRESSION: 0.75,
            ModelType.RANDOM_FOREST: 0.85,
            ModelType.GRADIENT_BOOSTING: 0.88,
            ModelType.NEURAL_NETWORK: 0.87,
            ModelType.SVM: 0.82,
            ModelType.LSTM: 0.86,
            ModelType.TRANSFORMER: 0.89
        }
        
        base_score = base_performance.get(model_type, 0.80)
        
        # Add random variation
        variation = np.random.normal(0, 0.05)
        base_score = max(0.5, min(0.98, base_score + variation))
        
        if problem_type == 'regression':
            return {
                'r2_score': base_score,
                'mae': (1 - base_score) * 10,
                'rmse': (1 - base_score) * 15,
                'mape': (1 - base_score) * 20
            }
        else:  # classification
            return {
                'accuracy': base_score,
                'precision': base_score * np.random.uniform(0.95, 1.05),
                'recall': base_score * np.random.uniform(0.95, 1.05),
                'f1_score': base_score * np.random.uniform(0.98, 1.02),
                'auc_roc': base_score * np.random.uniform(1.0, 1.05)
            }
    
    def _select_best_model(self, candidates: List[ModelCandidate],
                         optimization_objective: OptimizationObjective) -> Optional[ModelCandidate]:
        """Select best model based on optimization objective."""
        if not candidates:
            return None
        
        objective_metric = optimization_objective.value
        
        # Handle different optimization directions
        reverse_sort = objective_metric not in ['mae', 'rmse', 'mape']
        
        best_candidate = max(
            candidates,
            key=lambda x: x.performance_metrics.get(objective_metric, 0 if reverse_sort else float('inf'))
        )
        
        return best_candidate
    
    async def _create_ensemble(self, top_models: List[ModelCandidate],
                             experiment: AutoMLExperiment) -> ModelCandidate:
        """Create ensemble model from top performers."""
        try:
            # Simple ensemble (weighted average)
            ensemble_id = f"ensemble_{experiment.experiment_id}"
            
            # Calculate ensemble performance (typically better than individual models)
            objective_metric = experiment.optimization_objective.value
            
            individual_scores = [
                model.performance_metrics.get(objective_metric, 0)
                for model in top_models
            ]
            
            # Ensemble typically improves performance by 2-5%
            ensemble_score = max(individual_scores) * np.random.uniform(1.02, 1.05)
            ensemble_score = min(0.98, ensemble_score)  # Cap at 98%
            
            # Create ensemble performance metrics
            if experiment.problem_type == 'regression':
                ensemble_metrics = {
                    'r2_score': ensemble_score,
                    'mae': (1 - ensemble_score) * 10,
                    'rmse': (1 - ensemble_score) * 15,
                    'mape': (1 - ensemble_score) * 20
                }
            else:
                ensemble_metrics = {
                    'accuracy': ensemble_score,
                    'precision': ensemble_score * 0.98,
                    'recall': ensemble_score * 0.99,
                    'f1_score': ensemble_score * 0.985,
                    'auc_roc': ensemble_score * 1.02
                }
            
            # Ensemble hyperparameters
            ensemble_params = {
                'base_models': [model.model_id for model in top_models],
                'weights': [1.0 / len(top_models)] * len(top_models),
                'voting': 'soft' if experiment.problem_type == 'classification' else 'average'
            }
            
            ensemble_candidate = ModelCandidate(
                model_id=ensemble_id,
                model_type=ModelType.RANDOM_FOREST,  # Placeholder
                hyperparameters=ensemble_params,
                performance_metrics=ensemble_metrics,
                training_time=sum(model.training_time for model in top_models),
                model_size_mb=sum(model.model_size_mb for model in top_models),
                complexity_score=np.mean([model.complexity_score for model in top_models]) + 1
            )
            
            return ensemble_candidate
            
        except Exception as e:
            logger.error(f"Ensemble creation failed: {e}")
            return None
    
    def _generate_experiment_report(self, experiment: AutoMLExperiment,
                                  all_candidates: List[ModelCandidate],
                                  best_model: Optional[ModelCandidate],
                                  ensemble_model: Optional[ModelCandidate]) -> Dict[str, Any]:
        """Generate comprehensive experiment report."""
        # Model performance summary
        model_summary = []
        for candidate in all_candidates[:10]:  # Top 10 models
            model_summary.append({
                'model_id': candidate.model_id,
                'model_type': candidate.model_type.value,
                'performance_metrics': candidate.performance_metrics,
                'training_time': candidate.training_time,
                'model_size_mb': candidate.model_size_mb,
                'complexity_score': candidate.complexity_score
            })
        
        # Performance statistics
        objective_metric = experiment.optimization_objective.value
        objective_scores = [
            model.performance_metrics.get(objective_metric, 0)
            for model in all_candidates
        ]
        
        performance_stats = {
            'best_score': max(objective_scores) if objective_scores else 0,
            'worst_score': min(objective_scores) if objective_scores else 0,
            'mean_score': np.mean(objective_scores) if objective_scores else 0,
            'std_score': np.std(objective_scores) if objective_scores else 0
        }
        
        # Model type comparison
        type_performance = {}
        for model_type in experiment.model_types:
            type_models = [m for m in all_candidates if m.model_type == model_type]
            if type_models:
                type_scores = [m.performance_metrics.get(objective_metric, 0) for m in type_models]
                type_performance[model_type.value] = {
                    'best_score': max(type_scores),
                    'mean_score': np.mean(type_scores),
                    'model_count': len(type_models)
                }
        
        report = {
            'experiment_summary': {
                'experiment_id': experiment.experiment_id,
                'problem_type': experiment.problem_type,
                'optimization_objective': experiment.optimization_objective.value,
                'total_models_trained': len(all_candidates),
                'time_budget_minutes': experiment.time_budget_minutes,
                'status': experiment.status
            },
            'best_model': {
                'model_id': best_model.model_id if best_model else None,
                'model_type': best_model.model_type.value if best_model else None,
                'performance_metrics': best_model.performance_metrics if best_model else {},
                'hyperparameters': best_model.hyperparameters if best_model else {}
            } if best_model else None,
            'ensemble_model': {
                'model_id': ensemble_model.model_id if ensemble_model else None,
                'performance_metrics': ensemble_model.performance_metrics if ensemble_model else {},
                'base_models': ensemble_model.hyperparameters.get('base_models', []) if ensemble_model else []
            } if ensemble_model else None,
            'performance_statistics': performance_stats,
            'model_type_comparison': type_performance,
            'top_models': model_summary,
            'recommendations': self._generate_recommendations(experiment, best_model, all_candidates),
            'generated_at': datetime.now().isoformat()
        }
        
        return report
    
    def _generate_recommendations(self, experiment: AutoMLExperiment,
                                best_model: Optional[ModelCandidate],
                                all_candidates: List[ModelCandidate]) -> List[str]:
        """Generate recommendations based on experiment results."""
        recommendations = []
        
        if not best_model:
            recommendations.append("No successful models were trained. Consider adjusting the dataset or increasing time budget.")
            return recommendations
        
        # Performance recommendations
        objective_metric = experiment.optimization_objective.value
        best_score = best_model.performance_metrics.get(objective_metric, 0)
        
        if best_score < 0.7:
            recommendations.append("Model performance is below 70%. Consider feature engineering or collecting more data.")
        elif best_score < 0.8:
            recommendations.append("Model performance is moderate. Consider hyperparameter tuning or ensemble methods.")
        else:
            recommendations.append("Model performance is good. Consider deploying to production.")
        
        # Model complexity recommendations
        if best_model.complexity_score > 6:
            recommendations.append("Best model is complex. Consider simpler models for faster inference.")
        
        # Training time recommendations
        if best_model.training_time > 60:
            recommendations.append("Training time is high. Consider model optimization or distributed training.")
        
        # Model type recommendations
        model_type_scores = {}
        for candidate in all_candidates:
            model_type = candidate.model_type
            score = candidate.performance_metrics.get(objective_metric, 0)
            
            if model_type not in model_type_scores:
                model_type_scores[model_type] = []
            model_type_scores[model_type].append(score)
        
        # Find consistently good model types
        good_types = []
        for model_type, scores in model_type_scores.items():
            if np.mean(scores) > 0.8:
                good_types.append(model_type.value)
        
        if good_types:
            recommendations.append(f"Consider focusing on {', '.join(good_types)} for future experiments.")
        
        return recommendations
    
    def get_experiment_status(self, experiment_id: str) -> Dict[str, Any]:
        """Get experiment status and progress."""
        if experiment_id not in self.experiments:
            return {'status': 'error', 'error': 'Experiment not found'}
        
        experiment = self.experiments[experiment_id]
        candidates = self.model_candidates.get(experiment_id, [])
        
        return {
            'status': 'success',
            'experiment_status': {
                'experiment_id': experiment_id,
                'status': experiment.status,
                'created_at': experiment.created_at.isoformat(),
                'models_trained': len(candidates),
                'target_models': len(experiment.model_types) * self.config['max_models_per_type'],
                'best_model_id': self.best_models.get(experiment_id, {}).model_id if experiment_id in self.best_models else None
            }
        }
    
    def list_experiments(self) -> Dict[str, Any]:
        """List all AutoML experiments."""
        experiment_list = []
        
        for experiment_id, experiment in self.experiments.items():
            candidates = self.model_candidates.get(experiment_id, [])
            best_model = self.best_models.get(experiment_id)
            
            experiment_list.append({
                'experiment_id': experiment_id,
                'problem_type': experiment.problem_type,
                'optimization_objective': experiment.optimization_objective.value,
                'status': experiment.status,
                'models_trained': len(candidates),
                'best_score': best_model.performance_metrics.get(
                    experiment.optimization_objective.value, 0
                ) if best_model else 0,
                'created_at': experiment.created_at.isoformat()
            })
        
        # Sort by creation date (newest first)
        experiment_list.sort(key=lambda x: x['created_at'], reverse=True)
        
        return {
            'status': 'success',
            'experiments': experiment_list,
            'total_experiments': len(experiment_list)
        }


# Convenience functions
async def create_automl_experiment(dataset_info: Dict[str, Any], target_variable: str,
                                 problem_type: str, optimization_objective: str,
                                 time_budget_minutes: int = 60) -> Dict[str, Any]:
    """Create AutoML experiment."""
    pipeline = AutoMLPipeline()
    return await pipeline.create_experiment(
        dataset_info, target_variable, problem_type,
        OptimizationObjective(optimization_objective), time_budget_minutes
    )


async def run_automl_experiment(experiment_id: str) -> Dict[str, Any]:
    """Run AutoML experiment."""
    pipeline = AutoMLPipeline()
    return await pipeline.run_experiment(experiment_id)
