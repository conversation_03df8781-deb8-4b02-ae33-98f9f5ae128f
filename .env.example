# =============================================================================
# WATER MANAGEMENT DECARBONISATION PROJECT - ENVIRONMENT CONFIGURATION
# =============================================================================

# =============================================================================
# LLM API KEYS
# =============================================================================
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here
OPENAI_MODEL=gpt-4-turbo-preview

# Google Gemini API Configuration
GOOGLE_API_KEY=AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk
GOOGLE_PROJECT_ID=watermanagement-project

# Anthropic Claude API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Cohere API Configuration
COHERE_API_KEY=your_cohere_api_key_here

# Hugging Face API Configuration
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# =============================================================================
# CLIMATE AND ENVIRONMENTAL DATA APIs
# =============================================================================
# OpenWeatherMap API
OPENWEATHER_API_KEY=your_openweathermap_api_key_here
OPENWEATHER_BASE_URL=https://api.openweathermap.org/data/2.5

# NASA APIs
NASA_API_KEY=your_nasa_api_key_here
NASA_BASE_URL=https://api.nasa.gov

# NOAA Climate Data
NOAA_API_TOKEN=your_noaa_api_token_here
NOAA_BASE_URL=https://www.ncdc.noaa.gov/cdo-web/api/v2

# World Bank Climate API
WORLDBANK_API_KEY=your_worldbank_api_key_here
WORLDBANK_BASE_URL=https://climateknowledgeportal.worldbank.org/api

# European Space Agency Climate Data
ESA_API_KEY=your_esa_api_key_here

# =============================================================================
# WATER AND ENVIRONMENTAL MONITORING APIs
# =============================================================================
# USGS Water Services
USGS_API_KEY=your_usgs_api_key_here
USGS_BASE_URL=https://waterservices.usgs.gov

# EPA Water Quality Portal
EPA_API_KEY=your_epa_api_key_here
EPA_BASE_URL=https://www.waterqualitydata.us

# =============================================================================
# ENERGY AND SUSTAINABILITY APIs
# =============================================================================
# IEA Energy Statistics
IEA_API_KEY=your_iea_api_key_here

# Carbon Intensity API
CARBON_INTENSITY_API_KEY=your_carbon_intensity_api_key_here
CARBON_INTENSITY_BASE_URL=https://api.carbonintensity.org.uk

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database
DATABASE_URL=postgresql://username:password@localhost:5432/watermanagement
DB_HOST=localhost
DB_PORT=5432
DB_NAME=watermanagement
DB_USER=postgres
DB_PASSWORD=your_db_password_here

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password_here

# MongoDB Configuration (Optional)
MONGODB_URL=mongodb://localhost:27017/watermanagement
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=watermanagement

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Application Settings
APP_NAME=Water Management Decarbonisation System
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Security Settings
SECRET_KEY=your_secret_key_here_change_in_production
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=true

# Streamlit Configuration
STREAMLIT_HOST=0.0.0.0
STREAMLIT_PORT=8501

# =============================================================================
# MACHINE LEARNING AND AI CONFIGURATION
# =============================================================================
# Model Configuration
DEFAULT_LLM_MODEL=gpt-4-turbo-preview
DEFAULT_EMBEDDING_MODEL=text-embedding-ada-002
MAX_TOKENS=4096
TEMPERATURE=0.7

# Training Configuration
BATCH_SIZE=32
LEARNING_RATE=0.001
EPOCHS=100
VALIDATION_SPLIT=0.2

# Optimization Configuration
OPTIMIZATION_ALGORITHM=genetic
POPULATION_SIZE=100
GENERATIONS=50
MUTATION_RATE=0.1
CROSSOVER_RATE=0.8

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# Weights & Biases
WANDB_API_KEY=your_wandb_api_key_here
WANDB_PROJECT=water-management-decarbonisation
WANDB_ENTITY=your_wandb_entity_here

# MLflow Configuration
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_EXPERIMENT_NAME=water-treatment-optimization

# Prometheus Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password_here
SMTP_USE_TLS=true

# Slack Integration (for alerts)
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
SLACK_CHANNEL=#water-management-alerts

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Docker Configuration
DOCKER_REGISTRY=your_docker_registry_here
DOCKER_IMAGE_TAG=latest

# Kubernetes Configuration
K8S_NAMESPACE=water-management
K8S_CLUSTER_NAME=production-cluster

# Cloud Provider Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-west-2

AZURE_CLIENT_ID=your_azure_client_id_here
AZURE_CLIENT_SECRET=your_azure_client_secret_here
AZURE_TENANT_ID=your_azure_tenant_id_here

GCP_PROJECT_ID=your_gcp_project_id_here
GCP_SERVICE_ACCOUNT_KEY=path/to/your/service-account-key.json

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/Disable Features
ENABLE_REAL_TIME_OPTIMIZATION=true
ENABLE_CLIMATE_INTEGRATION=true
ENABLE_MULTI_AGENT_SYSTEM=true
ENABLE_FEDERATED_LEARNING=true
ENABLE_QUANTUM_OPTIMIZATION=false
ENABLE_BLOCKCHAIN_INTEGRATION=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=1000

# Parallel Processing
MAX_WORKERS=8
ASYNC_POOL_SIZE=10

# =============================================================================
# RESEARCH AND DEVELOPMENT
# =============================================================================
# Experimental Features
ENABLE_EXPERIMENTAL_FEATURES=false
RESEARCH_MODE=false
COLLECT_RESEARCH_DATA=false

# Data Collection
DATA_COLLECTION_INTERVAL=300  # seconds
DATA_RETENTION_DAYS=365
ENABLE_DATA_ANONYMIZATION=true

# =============================================================================
# COMPLIANCE AND GOVERNANCE
# =============================================================================
# Regulatory Compliance
GDPR_COMPLIANCE=true
CCPA_COMPLIANCE=true
ENABLE_AUDIT_LOGGING=true
DATA_ENCRYPTION_ENABLED=true

# Governance
ENABLE_MODEL_GOVERNANCE=true
ENABLE_DATA_LINEAGE=true
ENABLE_BIAS_DETECTION=true
ENABLE_EXPLAINABILITY=true
