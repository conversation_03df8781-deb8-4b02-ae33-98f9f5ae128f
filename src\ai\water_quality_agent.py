"""Water Quality Agent for Water Management System."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class WaterQualityParameter(Enum):
    """Water quality parameters."""
    PH = "ph"
    TURBIDITY = "turbidity"
    CHLORINE_RESIDUAL = "chlorine_residual"
    DISSOLVED_OXYGEN = "dissolved_oxygen"
    TEMPERATURE = "temperature"
    CONDUCTIVITY = "conductivity"
    BACTERIA_COUNT = "bacteria_count"
    NITRATES = "nitrates"
    PHOSPHATES = "phosphates"
    HEAVY_METALS = "heavy_metals"


class QualityStandard(Enum):
    """Water quality standards."""
    WHO_DRINKING_WATER = "who_drinking_water"
    EPA_SAFE_DRINKING = "epa_safe_drinking"
    EU_DRINKING_WATER = "eu_drinking_water"
    LOCAL_REGULATIONS = "local_regulations"


@dataclass
class QualityMeasurement:
    """Water quality measurement."""
    parameter: WaterQualityParameter
    value: float
    unit: str
    timestamp: datetime
    location: str
    method: str
    confidence: float = 0.95


@dataclass
class QualityAlert:
    """Water quality alert."""
    alert_id: str
    parameter: WaterQualityParameter
    current_value: float
    threshold_value: float
    severity: str
    description: str
    recommended_actions: List[str]
    timestamp: datetime = field(default_factory=datetime.now)


class WaterQualityAgent:
    """AI agent for water quality monitoring and optimization."""
    
    def __init__(self):
        self.quality_history: List[QualityMeasurement] = []
        self.active_alerts: List[QualityAlert] = []
        self.quality_standards: Dict[QualityStandard, Dict[str, Dict[str, float]]] = {}
        
        # Initialize quality standards and models
        self._initialize_quality_standards()
        self._initialize_quality_models()
    
    def _initialize_quality_standards(self):
        """Initialize water quality standards."""
        self.quality_standards = {
            QualityStandard.WHO_DRINKING_WATER: {
                'ph': {'min': 6.5, 'max': 8.5, 'optimal': 7.2},
                'turbidity': {'min': 0.0, 'max': 4.0, 'optimal': 1.0},
                'chlorine_residual': {'min': 0.2, 'max': 5.0, 'optimal': 1.0},
                'dissolved_oxygen': {'min': 5.0, 'max': 15.0, 'optimal': 8.0},
                'temperature': {'min': 5.0, 'max': 25.0, 'optimal': 15.0},
                'conductivity': {'min': 0.0, 'max': 2500.0, 'optimal': 500.0},
                'bacteria_count': {'min': 0.0, 'max': 0.0, 'optimal': 0.0},
                'nitrates': {'min': 0.0, 'max': 50.0, 'optimal': 10.0},
                'phosphates': {'min': 0.0, 'max': 5.0, 'optimal': 1.0}
            },
            QualityStandard.EPA_SAFE_DRINKING: {
                'ph': {'min': 6.5, 'max': 8.5, 'optimal': 7.0},
                'turbidity': {'min': 0.0, 'max': 1.0, 'optimal': 0.3},
                'chlorine_residual': {'min': 0.2, 'max': 4.0, 'optimal': 0.8},
                'bacteria_count': {'min': 0.0, 'max': 0.0, 'optimal': 0.0},
                'nitrates': {'min': 0.0, 'max': 10.0, 'optimal': 5.0}
            }
        }
    
    def _initialize_quality_models(self):
        """Initialize water quality prediction models."""
        self.quality_models = {
            'ph_stability': {
                'factors': ['temperature', 'alkalinity', 'chemical_dosing'],
                'stability_range': (6.8, 7.8),
                'correction_methods': ['acid_dosing', 'base_dosing', 'buffer_adjustment']
            },
            'chlorine_decay': {
                'factors': ['temperature', 'ph', 'organic_matter', 'contact_time'],
                'decay_rate': 0.1,  # per hour
                'minimum_residual': 0.2
            },
            'turbidity_removal': {
                'factors': ['coagulation', 'flocculation', 'sedimentation', 'filtration'],
                'target_removal': 0.95,
                'process_efficiency': 0.90
            }
        }
    
    async def assess_water_quality(self, quality_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess water quality - alias for analyze_water_quality."""
        return await self.analyze_water_quality(quality_data)

    @log_async_function_call
    async def analyze_water_quality(self, quality_data: Dict[str, Any],
                                  standard: QualityStandard = QualityStandard.WHO_DRINKING_WATER) -> Dict[str, Any]:
        """Analyze current water quality against standards."""
        try:
            # Parse quality measurements
            measurements = []
            for param, value in quality_data.items():
                if param in [p.value for p in WaterQualityParameter]:
                    measurement = QualityMeasurement(
                        parameter=WaterQualityParameter(param),
                        value=value,
                        unit=self._get_parameter_unit(param),
                        timestamp=datetime.now(),
                        location=quality_data.get('location', 'main_outlet'),
                        method=quality_data.get('method', 'sensor')
                    )
                    measurements.append(measurement)
            
            # Analyze compliance
            compliance_analysis = await self._analyze_compliance(measurements, standard)
            
            # Calculate quality index
            quality_index = await self._calculate_quality_index(measurements, standard)
            
            # Identify trends
            trend_analysis = await self._analyze_trends(measurements)
            
            # Generate alerts
            alerts = await self._generate_quality_alerts(measurements, standard)
            
            # Predict quality changes
            predictions = await self._predict_quality_changes(measurements)
            
            # Generate recommendations
            recommendations = await self._generate_quality_recommendations(
                measurements, compliance_analysis, alerts
            )
            
            return {
                'status': 'success',
                'analysis_timestamp': datetime.now().isoformat(),
                'quality_summary': {
                    'overall_quality_index': quality_index,
                    'compliance_status': compliance_analysis['overall_compliance'],
                    'parameters_tested': len(measurements),
                    'alerts_generated': len(alerts)
                },
                'measurements': [
                    {
                        'parameter': m.parameter.value,
                        'value': m.value,
                        'unit': m.unit,
                        'timestamp': m.timestamp.isoformat()
                    }
                    for m in measurements
                ],
                'compliance_analysis': compliance_analysis,
                'quality_index': quality_index,
                'trend_analysis': trend_analysis,
                'alerts': [
                    {
                        'alert_id': a.alert_id,
                        'parameter': a.parameter.value,
                        'current_value': a.current_value,
                        'threshold_value': a.threshold_value,
                        'severity': a.severity,
                        'description': a.description,
                        'recommended_actions': a.recommended_actions
                    }
                    for a in alerts
                ],
                'predictions': predictions,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"Water quality analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _get_parameter_unit(self, parameter: str) -> str:
        """Get unit for water quality parameter."""
        units = {
            'ph': 'pH units',
            'turbidity': 'NTU',
            'chlorine_residual': 'mg/L',
            'dissolved_oxygen': 'mg/L',
            'temperature': '°C',
            'conductivity': 'μS/cm',
            'bacteria_count': 'CFU/100mL',
            'nitrates': 'mg/L',
            'phosphates': 'mg/L',
            'heavy_metals': 'μg/L'
        }
        return units.get(parameter, 'units')
    
    async def _analyze_compliance(self, measurements: List[QualityMeasurement],
                                standard: QualityStandard) -> Dict[str, Any]:
        """Analyze compliance with water quality standards."""
        standards = self.quality_standards.get(standard, {})
        compliance_results = {}
        
        compliant_parameters = 0
        total_parameters = 0
        
        for measurement in measurements:
            param_name = measurement.parameter.value
            if param_name in standards:
                param_standards = standards[param_name]
                min_val = param_standards['min']
                max_val = param_standards['max']
                optimal_val = param_standards['optimal']
                
                # Check compliance
                is_compliant = min_val <= measurement.value <= max_val
                
                # Calculate deviation from optimal
                deviation_from_optimal = abs(measurement.value - optimal_val)
                relative_deviation = deviation_from_optimal / optimal_val if optimal_val > 0 else 0
                
                # Determine compliance level
                if is_compliant:
                    if relative_deviation <= 0.1:
                        compliance_level = 'excellent'
                    elif relative_deviation <= 0.2:
                        compliance_level = 'good'
                    else:
                        compliance_level = 'acceptable'
                    compliant_parameters += 1
                else:
                    compliance_level = 'non_compliant'
                
                compliance_results[param_name] = {
                    'value': measurement.value,
                    'min_standard': min_val,
                    'max_standard': max_val,
                    'optimal_standard': optimal_val,
                    'is_compliant': is_compliant,
                    'compliance_level': compliance_level,
                    'deviation_from_optimal': deviation_from_optimal,
                    'relative_deviation_percent': relative_deviation * 100
                }
                
                total_parameters += 1
        
        # Calculate overall compliance
        compliance_rate = compliant_parameters / total_parameters if total_parameters > 0 else 0
        
        if compliance_rate == 1.0:
            overall_compliance = 'fully_compliant'
        elif compliance_rate >= 0.9:
            overall_compliance = 'mostly_compliant'
        elif compliance_rate >= 0.7:
            overall_compliance = 'partially_compliant'
        else:
            overall_compliance = 'non_compliant'
        
        return {
            'overall_compliance': overall_compliance,
            'compliance_rate': compliance_rate,
            'compliant_parameters': compliant_parameters,
            'total_parameters': total_parameters,
            'parameter_compliance': compliance_results,
            'standard_used': standard.value
        }
    
    async def _calculate_quality_index(self, measurements: List[QualityMeasurement],
                                     standard: QualityStandard) -> Dict[str, Any]:
        """Calculate overall water quality index."""
        standards = self.quality_standards.get(standard, {})
        
        # Parameter weights (importance factors)
        parameter_weights = {
            'ph': 0.20,
            'turbidity': 0.15,
            'chlorine_residual': 0.15,
            'bacteria_count': 0.25,  # Most important for safety
            'dissolved_oxygen': 0.10,
            'temperature': 0.05,
            'conductivity': 0.05,
            'nitrates': 0.03,
            'phosphates': 0.02
        }
        
        weighted_scores = []
        total_weight = 0
        
        for measurement in measurements:
            param_name = measurement.parameter.value
            if param_name in standards and param_name in parameter_weights:
                param_standards = standards[param_name]
                weight = parameter_weights[param_name]
                
                # Calculate parameter score (0-100)
                min_val = param_standards['min']
                max_val = param_standards['max']
                optimal_val = param_standards['optimal']
                
                if min_val <= measurement.value <= max_val:
                    # Within acceptable range
                    deviation_from_optimal = abs(measurement.value - optimal_val)
                    max_acceptable_deviation = max(optimal_val - min_val, max_val - optimal_val)
                    
                    if max_acceptable_deviation > 0:
                        score = 100 * (1 - deviation_from_optimal / max_acceptable_deviation)
                    else:
                        score = 100
                else:
                    # Outside acceptable range
                    if measurement.value < min_val:
                        excess_deviation = min_val - measurement.value
                        score = max(0, 50 - (excess_deviation / min_val) * 50)
                    else:
                        excess_deviation = measurement.value - max_val
                        score = max(0, 50 - (excess_deviation / max_val) * 50)
                
                weighted_scores.append(score * weight)
                total_weight += weight
        
        # Calculate overall quality index
        if total_weight > 0:
            quality_index = sum(weighted_scores) / total_weight
        else:
            quality_index = 50  # Default neutral score
        
        # Determine quality grade
        if quality_index >= 90:
            grade = 'A'
            description = 'Excellent'
        elif quality_index >= 80:
            grade = 'B'
            description = 'Good'
        elif quality_index >= 70:
            grade = 'C'
            description = 'Fair'
        elif quality_index >= 60:
            grade = 'D'
            description = 'Poor'
        else:
            grade = 'F'
            description = 'Unacceptable'
        
        return {
            'overall_index': quality_index,
            'grade': grade,
            'description': description,
            'parameters_included': len(weighted_scores),
            'calculation_method': 'weighted_average'
        }
    
    async def _analyze_trends(self, measurements: List[QualityMeasurement]) -> Dict[str, Any]:
        """Analyze trends in water quality parameters."""
        # Simulate trend analysis (in practice, would use historical data)
        trends = {}
        
        for measurement in measurements:
            param_name = measurement.parameter.value
            
            # Simulate trend calculation
            trend_direction = np.random.choice(['improving', 'stable', 'declining'], p=[0.3, 0.5, 0.2])
            trend_magnitude = np.random.uniform(0.1, 2.0)
            
            trends[param_name] = {
                'current_value': measurement.value,
                'trend_direction': trend_direction,
                'trend_magnitude': trend_magnitude,
                'confidence': 0.75,
                'data_points': 30,  # Simulated
                'time_period': '30_days'
            }
        
        return {
            'trend_summary': trends,
            'overall_trend': 'stable',  # Simplified
            'analysis_period': '30_days',
            'confidence_level': 0.75
        }
    
    async def _generate_quality_alerts(self, measurements: List[QualityMeasurement],
                                     standard: QualityStandard) -> List[QualityAlert]:
        """Generate quality alerts for out-of-range parameters."""
        alerts = []
        standards = self.quality_standards.get(standard, {})
        
        for measurement in measurements:
            param_name = measurement.parameter.value
            if param_name in standards:
                param_standards = standards[param_name]
                min_val = param_standards['min']
                max_val = param_standards['max']
                
                # Check for violations
                if measurement.value < min_val or measurement.value > max_val:
                    # Determine severity
                    if param_name == 'bacteria_count' and measurement.value > 0:
                        severity = 'critical'
                    elif param_name == 'ph' and (measurement.value < 6.0 or measurement.value > 9.0):
                        severity = 'high'
                    elif measurement.value < min_val * 0.8 or measurement.value > max_val * 1.2:
                        severity = 'high'
                    else:
                        severity = 'medium'
                    
                    # Generate alert
                    alert = QualityAlert(
                        alert_id=f"alert_{param_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        parameter=measurement.parameter,
                        current_value=measurement.value,
                        threshold_value=min_val if measurement.value < min_val else max_val,
                        severity=severity,
                        description=f"{param_name} is out of acceptable range",
                        recommended_actions=self._get_corrective_actions(param_name, measurement.value, min_val, max_val)
                    )
                    
                    alerts.append(alert)
        
        return alerts
    
    def _get_corrective_actions(self, parameter: str, current_value: float,
                              min_val: float, max_val: float) -> List[str]:
        """Get recommended corrective actions for parameter violations."""
        actions = []
        
        if parameter == 'ph':
            if current_value < min_val:
                actions.extend([
                    'Increase alkalinity dosing',
                    'Reduce acid dosing if applicable',
                    'Check chemical feed systems',
                    'Verify pH sensor calibration'
                ])
            else:
                actions.extend([
                    'Increase acid dosing',
                    'Reduce alkalinity dosing',
                    'Check chemical feed systems',
                    'Verify pH sensor calibration'
                ])
        
        elif parameter == 'turbidity':
            if current_value > max_val:
                actions.extend([
                    'Increase coagulant dosing',
                    'Optimize flocculation process',
                    'Check filter performance',
                    'Increase backwash frequency',
                    'Inspect raw water quality'
                ])
        
        elif parameter == 'chlorine_residual':
            if current_value < min_val:
                actions.extend([
                    'Increase chlorine dosing',
                    'Check chlorine feed system',
                    'Verify contact time',
                    'Check for chlorine demand increases'
                ])
            else:
                actions.extend([
                    'Reduce chlorine dosing',
                    'Check dosing system calibration',
                    'Verify chlorine analyzer accuracy'
                ])
        
        elif parameter == 'bacteria_count':
            if current_value > max_val:
                actions.extend([
                    'Increase disinfection',
                    'Check disinfection system',
                    'Verify contact time',
                    'Inspect distribution system',
                    'Consider emergency disinfection protocol'
                ])
        
        else:
            actions.append(f'Investigate {parameter} levels and adjust treatment accordingly')
        
        return actions
    
    async def _predict_quality_changes(self, measurements: List[QualityMeasurement]) -> Dict[str, Any]:
        """Predict future water quality changes."""
        predictions = {}
        
        for measurement in measurements:
            param_name = measurement.parameter.value
            
            # Simple prediction model (in practice, would use ML models)
            current_value = measurement.value
            
            # Simulate prediction
            predicted_change = np.random.normal(0, 0.1)  # Small random change
            predicted_value = current_value + predicted_change
            
            # Confidence decreases with time
            confidence_24h = 0.85
            confidence_7d = 0.65
            confidence_30d = 0.45
            
            predictions[param_name] = {
                'current_value': current_value,
                'predicted_24h': {
                    'value': predicted_value,
                    'confidence': confidence_24h,
                    'trend': 'stable'
                },
                'predicted_7d': {
                    'value': predicted_value + np.random.normal(0, 0.2),
                    'confidence': confidence_7d,
                    'trend': 'stable'
                },
                'predicted_30d': {
                    'value': predicted_value + np.random.normal(0, 0.5),
                    'confidence': confidence_30d,
                    'trend': 'stable'
                }
            }
        
        return {
            'parameter_predictions': predictions,
            'model_version': '1.0',
            'last_updated': datetime.now().isoformat()
        }
    
    async def _generate_quality_recommendations(self, measurements: List[QualityMeasurement],
                                              compliance_analysis: Dict[str, Any],
                                              alerts: List[QualityAlert]) -> List[Dict[str, Any]]:
        """Generate water quality improvement recommendations."""
        recommendations = []
        
        # Address non-compliant parameters
        for param, compliance in compliance_analysis['parameter_compliance'].items():
            if not compliance['is_compliant']:
                recommendations.append({
                    'type': 'compliance_correction',
                    'parameter': param,
                    'description': f'Correct {param} to meet compliance standards',
                    'current_value': compliance['value'],
                    'target_range': f"{compliance['min_standard']} - {compliance['max_standard']}",
                    'priority': 'high',
                    'estimated_time': '2-4 hours'
                })
        
        # Optimize parameters that are compliant but not optimal
        for param, compliance in compliance_analysis['parameter_compliance'].items():
            if compliance['is_compliant'] and compliance['relative_deviation_percent'] > 15:
                recommendations.append({
                    'type': 'optimization',
                    'parameter': param,
                    'description': f'Optimize {param} closer to ideal value',
                    'current_value': compliance['value'],
                    'optimal_value': compliance['optimal_standard'],
                    'priority': 'medium',
                    'estimated_time': '1-2 hours'
                })
        
        # General system recommendations
        if compliance_analysis['compliance_rate'] < 0.9:
            recommendations.append({
                'type': 'system_review',
                'description': 'Comprehensive system review recommended due to multiple compliance issues',
                'priority': 'high',
                'estimated_time': '4-8 hours'
            })
        
        return recommendations


# Convenience functions
async def analyze_water_quality_data(quality_data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze water quality data."""
    agent = WaterQualityAgent()
    return await agent.analyze_water_quality(quality_data)


async def check_water_quality_compliance(quality_data: Dict[str, Any], 
                                       standard: str = 'who_drinking_water') -> Dict[str, Any]:
    """Check water quality compliance against standards."""
    agent = WaterQualityAgent()
    quality_standard = QualityStandard(standard)
    result = await agent.analyze_water_quality(quality_data, quality_standard)
    
    return {
        'status': result['status'],
        'compliance_status': result.get('compliance_analysis', {}).get('overall_compliance'),
        'quality_index': result.get('quality_index', {}),
        'alerts': result.get('alerts', [])
    }
