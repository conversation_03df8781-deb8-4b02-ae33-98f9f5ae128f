"""WebSocket Manager for Real-time Communication."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class WebSocketManager:
    """WebSocket connection manager for real-time updates."""
    
    def __init__(self):
        self.connections: Set[str] = set()
        self.subscriptions: Dict[str, Set[str]] = {}
        self.message_queue: List[Dict[str, Any]] = []
        self.is_running = False
    
    async def start_server(self, host: str = 'localhost', port: int = 8765):
        """Start WebSocket server."""
        try:
            self.is_running = True
            logger.info(f"WebSocket server started on {host}:{port}")
            
            # Start message broadcasting task
            asyncio.create_task(self._broadcast_messages())
            
            return True
        except Exception as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            return False
    
    async def stop_server(self):
        """Stop WebSocket server."""
        self.is_running = False
        self.connections.clear()
        self.subscriptions.clear()
        logger.info("WebSocket server stopped")
    
    async def add_connection(self, connection_id: str):
        """Add new WebSocket connection."""
        self.connections.add(connection_id)
        logger.info(f"WebSocket connection added: {connection_id}")
        
        # Send welcome message
        await self.send_to_connection(connection_id, {
            'type': 'welcome',
            'message': 'Connected to Water Management System',
            'timestamp': datetime.now().isoformat()
        })
    
    async def remove_connection(self, connection_id: str):
        """Remove WebSocket connection."""
        self.connections.discard(connection_id)
        
        # Remove from all subscriptions
        for topic in self.subscriptions:
            self.subscriptions[topic].discard(connection_id)
        
        logger.info(f"WebSocket connection removed: {connection_id}")
    
    async def subscribe_to_topic(self, connection_id: str, topic: str):
        """Subscribe connection to a topic."""
        if topic not in self.subscriptions:
            self.subscriptions[topic] = set()
        
        self.subscriptions[topic].add(connection_id)
        logger.info(f"Connection {connection_id} subscribed to {topic}")
        
        # Send subscription confirmation
        await self.send_to_connection(connection_id, {
            'type': 'subscription_confirmed',
            'topic': topic,
            'timestamp': datetime.now().isoformat()
        })
    
    async def unsubscribe_from_topic(self, connection_id: str, topic: str):
        """Unsubscribe connection from a topic."""
        if topic in self.subscriptions:
            self.subscriptions[topic].discard(connection_id)
            logger.info(f"Connection {connection_id} unsubscribed from {topic}")
    
    async def broadcast_to_topic(self, topic: str, message: Dict[str, Any]):
        """Broadcast message to all subscribers of a topic."""
        if topic not in self.subscriptions:
            return
        
        message_with_metadata = {
            **message,
            'topic': topic,
            'timestamp': datetime.now().isoformat(),
            'type': 'broadcast'
        }
        
        subscribers = self.subscriptions[topic].copy()
        for connection_id in subscribers:
            await self.send_to_connection(connection_id, message_with_metadata)
        
        logger.info(f"Broadcasted message to {len(subscribers)} subscribers of {topic}")
    
    async def send_to_connection(self, connection_id: str, message: Dict[str, Any]):
        """Send message to specific connection."""
        if connection_id not in self.connections:
            logger.warning(f"Connection {connection_id} not found")
            return False
        
        # Simulate sending message
        logger.debug(f"Sent message to {connection_id}: {message.get('type', 'unknown')}")
        return True
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all connections."""
        message_with_metadata = {
            **message,
            'timestamp': datetime.now().isoformat(),
            'type': 'global_broadcast'
        }
        
        connections = self.connections.copy()
        for connection_id in connections:
            await self.send_to_connection(connection_id, message_with_metadata)
        
        logger.info(f"Broadcasted message to {len(connections)} connections")
    
    async def _broadcast_messages(self):
        """Background task to broadcast queued messages."""
        while self.is_running:
            try:
                if self.message_queue:
                    message = self.message_queue.pop(0)
                    topic = message.get('topic')
                    
                    if topic:
                        await self.broadcast_to_topic(topic, message)
                    else:
                        await self.broadcast_to_all(message)
                
                await asyncio.sleep(0.1)  # Small delay
            except Exception as e:
                logger.error(f"Error in message broadcasting: {e}")
                await asyncio.sleep(1)
    
    async def queue_message(self, message: Dict[str, Any]):
        """Queue message for broadcasting."""
        self.message_queue.append(message)
    
    async def send_water_quality_update(self, data: Dict[str, Any]):
        """Send water quality update."""
        message = {
            'type': 'water_quality_update',
            'data': data,
            'topic': 'water_quality'
        }
        await self.queue_message(message)
    
    async def send_alert(self, alert_data: Dict[str, Any]):
        """Send system alert."""
        message = {
            'type': 'alert',
            'data': alert_data,
            'topic': 'alerts',
            'priority': alert_data.get('priority', 'medium')
        }
        await self.queue_message(message)
    
    async def send_system_status_update(self, status_data: Dict[str, Any]):
        """Send system status update."""
        message = {
            'type': 'system_status',
            'data': status_data,
            'topic': 'system_status'
        }
        await self.queue_message(message)
    
    async def get_connection_stats(self):
        """Get WebSocket connection statistics."""
        return {
            'total_connections': len(self.connections),
            'active_subscriptions': sum(len(subs) for subs in self.subscriptions.values()),
            'topics': list(self.subscriptions.keys()),
            'queued_messages': len(self.message_queue),
            'server_running': self.is_running,
            'uptime': '99.9%'  # Simulated
        }
