"""Conversation Memory System for Water Management LLM Interactions."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import hashlib

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of conversation messages."""
    USER_QUERY = "user_query"
    SYSTEM_RESPONSE = "system_response"
    CONTEXT_UPDATE = "context_update"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    ERROR = "error"


class ConversationContext(Enum):
    """Conversation context types."""
    WATER_QUALITY = "water_quality"
    TREATMENT_OPTIMIZATION = "treatment_optimization"
    MAINTENANCE = "maintenance"
    ENERGY_EFFICIENCY = "energy_efficiency"
    CLIMATE_ANALYSIS = "climate_analysis"
    GENERAL = "general"


@dataclass
class ConversationMessage:
    """Individual conversation message."""
    message_id: str
    message_type: MessageType
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    context: Optional[ConversationContext] = None
    importance_score: float = 0.5
    references: List[str] = field(default_factory=list)


@dataclass
class ConversationSession:
    """Conversation session containing multiple messages."""
    session_id: str
    user_id: str
    title: str
    messages: List[ConversationMessage] = field(default_factory=list)
    context_summary: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    is_active: bool = True


class ConversationMemorySystem:
    """Advanced conversation memory system for LLM interactions."""
    
    def __init__(self, max_sessions: int = 1000, max_messages_per_session: int = 500):
        self.sessions: Dict[str, ConversationSession] = {}
        self.user_sessions: Dict[str, List[str]] = {}  # user_id -> session_ids
        self.context_index: Dict[ConversationContext, List[str]] = {}
        self.max_sessions = max_sessions
        self.max_messages_per_session = max_messages_per_session
        
        # Memory configuration
        self.config = {
            'retention_days': 90,
            'importance_threshold': 0.7,
            'context_window_size': 20,
            'summarization_threshold': 100,
            'auto_cleanup': True
        }
        
        # Initialize context index
        for context in ConversationContext:
            self.context_index[context] = []
    
    @log_async_function_call
    async def create_session(self, user_id: str, title: str = None) -> Dict[str, Any]:
        """Create new conversation session."""
        try:
            session_id = self._generate_session_id(user_id)
            
            session = ConversationSession(
                session_id=session_id,
                user_id=user_id,
                title=title or f"Water Management Session {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )
            
            self.sessions[session_id] = session
            
            # Update user sessions
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = []
            self.user_sessions[user_id].append(session_id)
            
            # Cleanup if needed
            await self._cleanup_old_sessions()
            
            return {
                'status': 'success',
                'session_id': session_id,
                'title': session.title,
                'created_at': session.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Session creation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _generate_session_id(self, user_id: str) -> str:
        """Generate unique session ID."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        user_hash = hashlib.md5(user_id.encode()).hexdigest()[:8]
        return f"session_{user_hash}_{timestamp}"
    
    @log_async_function_call
    async def add_message(self, session_id: str, message_type: MessageType, 
                         content: str, metadata: Dict[str, Any] = None,
                         context: ConversationContext = None) -> Dict[str, Any]:
        """Add message to conversation session."""
        try:
            if session_id not in self.sessions:
                return {'status': 'error', 'error': 'Session not found'}
            
            session = self.sessions[session_id]
            
            # Generate message ID
            message_id = f"msg_{len(session.messages) + 1}_{datetime.now().strftime('%H%M%S')}"
            
            # Calculate importance score
            importance_score = self._calculate_importance_score(content, message_type, metadata)
            
            # Create message
            message = ConversationMessage(
                message_id=message_id,
                message_type=message_type,
                content=content,
                metadata=metadata or {},
                context=context,
                importance_score=importance_score
            )
            
            # Add to session
            session.messages.append(message)
            session.last_activity = datetime.now()
            
            # Update context index
            if context:
                if session_id not in self.context_index[context]:
                    self.context_index[context].append(session_id)
            
            # Check if summarization is needed
            if len(session.messages) > self.config['summarization_threshold']:
                await self._summarize_session(session_id)
            
            # Cleanup old messages if needed
            if len(session.messages) > self.max_messages_per_session:
                await self._cleanup_session_messages(session_id)
            
            return {
                'status': 'success',
                'message_id': message_id,
                'importance_score': importance_score,
                'session_message_count': len(session.messages)
            }
            
        except Exception as e:
            logger.error(f"Message addition failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _calculate_importance_score(self, content: str, message_type: MessageType, 
                                  metadata: Dict[str, Any] = None) -> float:
        """Calculate importance score for message."""
        base_score = 0.5
        
        # Message type weights
        type_weights = {
            MessageType.USER_QUERY: 0.7,
            MessageType.SYSTEM_RESPONSE: 0.6,
            MessageType.CONTEXT_UPDATE: 0.8,
            MessageType.TOOL_CALL: 0.5,
            MessageType.TOOL_RESULT: 0.6,
            MessageType.ERROR: 0.9
        }
        
        base_score = type_weights.get(message_type, 0.5)
        
        # Content-based scoring
        important_keywords = [
            'emergency', 'critical', 'failure', 'contamination', 'alert',
            'optimization', 'efficiency', 'maintenance', 'prediction',
            'climate', 'sustainability', 'compliance', 'safety'
        ]
        
        content_lower = content.lower()
        keyword_score = sum(0.1 for keyword in important_keywords if keyword in content_lower)
        
        # Length bonus for detailed content
        length_score = min(0.2, len(content) / 1000)
        
        # Metadata-based scoring
        metadata_score = 0.0
        if metadata:
            if metadata.get('priority') == 'high':
                metadata_score += 0.2
            if metadata.get('confidence', 0) > 0.8:
                metadata_score += 0.1
            if metadata.get('data_quality') == 'high':
                metadata_score += 0.1
        
        total_score = base_score + keyword_score + length_score + metadata_score
        return min(1.0, max(0.0, total_score))
    
    @log_async_function_call
    async def get_conversation_context(self, session_id: str, 
                                     context_window: int = None) -> Dict[str, Any]:
        """Get conversation context for LLM."""
        try:
            if session_id not in self.sessions:
                return {'status': 'error', 'error': 'Session not found'}
            
            session = self.sessions[session_id]
            window_size = context_window or self.config['context_window_size']
            
            # Get recent messages
            recent_messages = session.messages[-window_size:]
            
            # Get high-importance messages from earlier in conversation
            important_messages = [
                msg for msg in session.messages[:-window_size]
                if msg.importance_score >= self.config['importance_threshold']
            ]
            
            # Combine and format for LLM
            context_messages = []
            
            # Add important historical messages
            for msg in important_messages[-5:]:  # Limit to 5 important messages
                context_messages.append({
                    'role': 'user' if msg.message_type == MessageType.USER_QUERY else 'assistant',
                    'content': msg.content,
                    'timestamp': msg.timestamp.isoformat(),
                    'importance': msg.importance_score,
                    'context': msg.context.value if msg.context else None
                })
            
            # Add recent messages
            for msg in recent_messages:
                context_messages.append({
                    'role': 'user' if msg.message_type == MessageType.USER_QUERY else 'assistant',
                    'content': msg.content,
                    'timestamp': msg.timestamp.isoformat(),
                    'importance': msg.importance_score,
                    'context': msg.context.value if msg.context else None
                })
            
            # Generate context summary
            context_summary = await self._generate_context_summary(session)
            
            return {
                'status': 'success',
                'session_id': session_id,
                'context_messages': context_messages,
                'context_summary': context_summary,
                'total_messages': len(session.messages),
                'session_duration': (session.last_activity - session.created_at).total_seconds()
            }
            
        except Exception as e:
            logger.error(f"Context retrieval failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_context_summary(self, session: ConversationSession) -> Dict[str, Any]:
        """Generate summary of conversation context."""
        summary = {
            'main_topics': [],
            'key_decisions': [],
            'data_discussed': [],
            'recommendations_made': [],
            'issues_identified': []
        }
        
        # Analyze messages for key themes
        for message in session.messages:
            content_lower = message.content.lower()
            
            # Identify topics
            if any(word in content_lower for word in ['water quality', 'ph', 'turbidity']):
                if 'water_quality' not in summary['main_topics']:
                    summary['main_topics'].append('water_quality')
            
            if any(word in content_lower for word in ['optimization', 'efficiency', 'performance']):
                if 'optimization' not in summary['main_topics']:
                    summary['main_topics'].append('optimization')
            
            if any(word in content_lower for word in ['maintenance', 'repair', 'equipment']):
                if 'maintenance' not in summary['main_topics']:
                    summary['main_topics'].append('maintenance')
            
            if any(word in content_lower for word in ['climate', 'weather', 'temperature']):
                if 'climate' not in summary['main_topics']:
                    summary['main_topics'].append('climate')
            
            # Identify decisions and recommendations
            if message.message_type == MessageType.SYSTEM_RESPONSE:
                if any(word in content_lower for word in ['recommend', 'suggest', 'should']):
                    summary['recommendations_made'].append(message.content[:100] + '...')
                
                if any(word in content_lower for word in ['decision', 'conclude', 'determine']):
                    summary['key_decisions'].append(message.content[:100] + '...')
            
            # Identify issues
            if any(word in content_lower for word in ['problem', 'issue', 'error', 'failure']):
                summary['issues_identified'].append(message.content[:100] + '...')
        
        # Limit lists to prevent overflow
        for key in summary:
            if isinstance(summary[key], list):
                summary[key] = summary[key][:5]
        
        return summary
    
    @log_async_function_call
    async def search_conversations(self, user_id: str, query: str, 
                                 context: ConversationContext = None,
                                 limit: int = 10) -> Dict[str, Any]:
        """Search conversations by content."""
        try:
            results = []
            query_lower = query.lower()
            
            # Get user sessions
            user_session_ids = self.user_sessions.get(user_id, [])
            
            # Search in relevant sessions
            search_sessions = user_session_ids
            if context:
                context_sessions = self.context_index.get(context, [])
                search_sessions = list(set(user_session_ids) & set(context_sessions))
            
            for session_id in search_sessions:
                if session_id not in self.sessions:
                    continue
                
                session = self.sessions[session_id]
                
                # Search messages
                for message in session.messages:
                    if query_lower in message.content.lower():
                        results.append({
                            'session_id': session_id,
                            'session_title': session.title,
                            'message_id': message.message_id,
                            'message_type': message.message_type.value,
                            'content': message.content,
                            'timestamp': message.timestamp.isoformat(),
                            'importance_score': message.importance_score,
                            'context': message.context.value if message.context else None
                        })
            
            # Sort by importance and recency
            results.sort(key=lambda x: (x['importance_score'], x['timestamp']), reverse=True)
            
            return {
                'status': 'success',
                'query': query,
                'results': results[:limit],
                'total_matches': len(results)
            }
            
        except Exception as e:
            logger.error(f"Conversation search failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _summarize_session(self, session_id: str):
        """Summarize session when it gets too long."""
        try:
            session = self.sessions[session_id]
            
            # Keep recent messages and high-importance messages
            recent_threshold = 50
            recent_messages = session.messages[-recent_threshold:]
            
            important_messages = [
                msg for msg in session.messages[:-recent_threshold]
                if msg.importance_score >= self.config['importance_threshold']
            ]
            
            # Create summary message
            summary_content = f"Session summary: {len(session.messages)} messages processed. "
            summary_content += f"Key topics: {', '.join(session.context_summary.get('main_topics', []))}"
            
            summary_message = ConversationMessage(
                message_id=f"summary_{datetime.now().strftime('%H%M%S')}",
                message_type=MessageType.CONTEXT_UPDATE,
                content=summary_content,
                importance_score=0.9
            )
            
            # Replace old messages with summary + important + recent
            session.messages = [summary_message] + important_messages + recent_messages
            
            logger.info(f"Session {session_id} summarized")
            
        except Exception as e:
            logger.error(f"Session summarization failed: {e}")
    
    async def _cleanup_session_messages(self, session_id: str):
        """Clean up old messages from session."""
        try:
            session = self.sessions[session_id]
            
            if len(session.messages) <= self.max_messages_per_session:
                return
            
            # Keep recent messages and high-importance messages
            keep_recent = self.max_messages_per_session // 2
            recent_messages = session.messages[-keep_recent:]
            
            important_messages = [
                msg for msg in session.messages[:-keep_recent]
                if msg.importance_score >= self.config['importance_threshold']
            ]
            
            # Limit important messages
            max_important = self.max_messages_per_session // 4
            important_messages = important_messages[-max_important:]
            
            session.messages = important_messages + recent_messages
            
            logger.info(f"Cleaned up session {session_id}")
            
        except Exception as e:
            logger.error(f"Session cleanup failed: {e}")
    
    async def _cleanup_old_sessions(self):
        """Clean up old inactive sessions."""
        if not self.config['auto_cleanup']:
            return
        
        try:
            cutoff_date = datetime.now() - timedelta(days=self.config['retention_days'])
            sessions_to_remove = []
            
            for session_id, session in self.sessions.items():
                if session.last_activity < cutoff_date and not session.is_active:
                    sessions_to_remove.append(session_id)
            
            # Remove old sessions
            for session_id in sessions_to_remove:
                session = self.sessions[session_id]
                
                # Remove from user sessions
                if session.user_id in self.user_sessions:
                    if session_id in self.user_sessions[session.user_id]:
                        self.user_sessions[session.user_id].remove(session_id)
                
                # Remove from context index
                for context_sessions in self.context_index.values():
                    if session_id in context_sessions:
                        context_sessions.remove(session_id)
                
                # Remove session
                del self.sessions[session_id]
            
            if sessions_to_remove:
                logger.info(f"Cleaned up {len(sessions_to_remove)} old sessions")
            
            # Limit total sessions
            if len(self.sessions) > self.max_sessions:
                # Remove oldest sessions
                sessions_by_age = sorted(
                    self.sessions.items(),
                    key=lambda x: x[1].last_activity
                )
                
                excess_count = len(self.sessions) - self.max_sessions
                for session_id, _ in sessions_by_age[:excess_count]:
                    del self.sessions[session_id]
                
                logger.info(f"Removed {excess_count} excess sessions")
            
        except Exception as e:
            logger.error(f"Session cleanup failed: {e}")
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """Get session information."""
        if session_id not in self.sessions:
            return {'status': 'error', 'error': 'Session not found'}
        
        session = self.sessions[session_id]
        
        return {
            'status': 'success',
            'session_info': {
                'session_id': session_id,
                'user_id': session.user_id,
                'title': session.title,
                'message_count': len(session.messages),
                'created_at': session.created_at.isoformat(),
                'last_activity': session.last_activity.isoformat(),
                'is_active': session.is_active,
                'context_summary': session.context_summary
            }
        }
    
    def list_user_sessions(self, user_id: str) -> Dict[str, Any]:
        """List all sessions for a user."""
        user_session_ids = self.user_sessions.get(user_id, [])
        
        sessions_info = []
        for session_id in user_session_ids:
            if session_id in self.sessions:
                session = self.sessions[session_id]
                sessions_info.append({
                    'session_id': session_id,
                    'title': session.title,
                    'message_count': len(session.messages),
                    'last_activity': session.last_activity.isoformat(),
                    'is_active': session.is_active
                })
        
        # Sort by last activity
        sessions_info.sort(key=lambda x: x['last_activity'], reverse=True)
        
        return {
            'status': 'success',
            'user_id': user_id,
            'sessions': sessions_info,
            'total_sessions': len(sessions_info)
        }


# Convenience functions
async def create_conversation_session(user_id: str, title: str = None) -> Dict[str, Any]:
    """Create new conversation session."""
    memory_system = ConversationMemorySystem()
    return await memory_system.create_session(user_id, title)


async def add_conversation_message(session_id: str, message_type: str, 
                                 content: str, context: str = None) -> Dict[str, Any]:
    """Add message to conversation."""
    memory_system = ConversationMemorySystem()
    
    msg_type = MessageType(message_type)
    ctx = ConversationContext(context) if context else None
    
    return await memory_system.add_message(session_id, msg_type, content, context=ctx)


async def get_conversation_context(session_id: str) -> Dict[str, Any]:
    """Get conversation context for LLM."""
    memory_system = ConversationMemorySystem()
    return await memory_system.get_conversation_context(session_id)
