"""Model Interpretability and Explainability for Water Management ML Models."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

# ML interpretability libraries
try:
    import shap
    from sklearn.inspection import permutation_importance
    from sklearn.tree import export_text
    INTERPRETABILITY_AVAILABLE = True
except ImportError:
    INTERPRETABILITY_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("SHAP or sklearn not available. Some interpretability features will be limited.")

from src.utils.logging_config import log_async_function_call

logger = logging.getLogger(__name__)


class ExplanationMethod(Enum):
    """Model explanation methods."""
    SHAP = "shap"
    LIME = "lime"
    PERMUTATION_IMPORTANCE = "permutation_importance"
    FEATURE_IMPORTANCE = "feature_importance"
    PARTIAL_DEPENDENCE = "partial_dependence"
    COUNTERFACTUAL = "counterfactual"
    ANCHOR = "anchor"
    INTEGRATED_GRADIENTS = "integrated_gradients"


class ExplanationScope(Enum):
    """Scope of explanation."""
    GLOBAL = "global"
    LOCAL = "local"
    COHORT = "cohort"


@dataclass
class FeatureExplanation:
    """Explanation for individual feature."""
    feature_name: str
    importance_score: float
    contribution: float
    confidence: float
    explanation_text: str
    supporting_evidence: List[str] = field(default_factory=list)


@dataclass
class ModelExplanation:
    """Complete model explanation."""
    explanation_id: str
    model_id: str
    explanation_method: ExplanationMethod
    explanation_scope: ExplanationScope
    feature_explanations: List[FeatureExplanation]
    overall_explanation: str
    confidence_score: float
    created_at: datetime = field(default_factory=datetime.now)


class ModelInterpretabilitySystem:
    """Comprehensive model interpretability and explainability system."""
    
    def __init__(self):
        self.explanations: Dict[str, ModelExplanation] = {}
        self.explainers: Dict[str, Any] = {}
        
        # Feature importance baselines
        self.feature_baselines = {
            'water_quality': {
                'ph': 0.25,
                'turbidity': 0.20,
                'chlorine_residual': 0.15,
                'temperature': 0.10,
                'flow_rate': 0.12,
                'pressure': 0.08,
                'conductivity': 0.10
            },
            'treatment_optimization': {
                'coagulant_dose': 0.30,
                'ph_adjustment': 0.25,
                'filtration_rate': 0.20,
                'chlorination_level': 0.15,
                'settling_time': 0.10
            },
            'energy_efficiency': {
                'pump_speed': 0.35,
                'motor_efficiency': 0.25,
                'system_load': 0.20,
                'operating_hours': 0.12,
                'maintenance_status': 0.08
            }
        }
    
    @log_async_function_call
    async def explain_model_prediction(self, explanation_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate explanation for model prediction."""
        try:
            model_id = explanation_config['model_id']
            input_data = explanation_config['input_data']
            method = ExplanationMethod(explanation_config.get('method', 'feature_importance'))
            scope = ExplanationScope(explanation_config.get('scope', 'local'))
            
            explanation_id = f"explanation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Generate explanation based on method
            if method == ExplanationMethod.SHAP and INTERPRETABILITY_AVAILABLE:
                explanation_result = await self._generate_shap_explanation(
                    model_id, input_data, scope
                )
            elif method == ExplanationMethod.PERMUTATION_IMPORTANCE:
                explanation_result = await self._generate_permutation_explanation(
                    model_id, input_data, scope
                )
            elif method == ExplanationMethod.FEATURE_IMPORTANCE:
                explanation_result = await self._generate_feature_importance_explanation(
                    model_id, input_data, scope
                )
            elif method == ExplanationMethod.PARTIAL_DEPENDENCE:
                explanation_result = await self._generate_partial_dependence_explanation(
                    model_id, input_data, scope
                )
            elif method == ExplanationMethod.COUNTERFACTUAL:
                explanation_result = await self._generate_counterfactual_explanation(
                    model_id, input_data, scope
                )
            else:
                # Default to feature importance
                explanation_result = await self._generate_feature_importance_explanation(
                    model_id, input_data, scope
                )
            
            # Create model explanation
            model_explanation = ModelExplanation(
                explanation_id=explanation_id,
                model_id=model_id,
                explanation_method=method,
                explanation_scope=scope,
                feature_explanations=explanation_result['feature_explanations'],
                overall_explanation=explanation_result['overall_explanation'],
                confidence_score=explanation_result['confidence_score']
            )
            
            self.explanations[explanation_id] = model_explanation
            
            return {
                'status': 'success',
                'explanation_id': explanation_id,
                'explanation_summary': {
                    'method': method.value,
                    'scope': scope.value,
                    'confidence_score': explanation_result['confidence_score'],
                    'top_features': explanation_result['feature_explanations'][:5]
                },
                'detailed_explanation': {
                    'overall_explanation': explanation_result['overall_explanation'],
                    'feature_explanations': [
                        {
                            'feature_name': fe.feature_name,
                            'importance_score': fe.importance_score,
                            'contribution': fe.contribution,
                            'explanation_text': fe.explanation_text
                        }
                        for fe in explanation_result['feature_explanations']
                    ]
                },
                'visualization_data': explanation_result.get('visualization_data', {}),
                'created_at': model_explanation.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Model explanation generation failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_shap_explanation(self, model_id: str, input_data: Dict[str, Any],
                                       scope: ExplanationScope) -> Dict[str, Any]:
        """Generate SHAP-based explanation."""
        try:
            # Simulate SHAP values (in practice, would use actual model and SHAP)
            feature_names = list(input_data.keys())
            n_features = len(feature_names)
            
            # Generate synthetic SHAP values
            shap_values = np.random.normal(0, 0.1, n_features)
            base_value = 0.5
            
            # Create feature explanations
            feature_explanations = []
            for i, (feature_name, shap_value) in enumerate(zip(feature_names, shap_values)):
                importance_score = abs(shap_value)
                contribution = shap_value
                
                explanation_text = self._generate_shap_explanation_text(
                    feature_name, shap_value, input_data[feature_name]
                )
                
                feature_explanations.append(FeatureExplanation(
                    feature_name=feature_name,
                    importance_score=importance_score,
                    contribution=contribution,
                    confidence=0.85,
                    explanation_text=explanation_text,
                    supporting_evidence=[f"SHAP value: {shap_value:.4f}"]
                ))
            
            # Sort by importance
            feature_explanations.sort(key=lambda x: x.importance_score, reverse=True)
            
            # Generate overall explanation
            overall_explanation = await self._generate_shap_overall_explanation(
                feature_explanations, base_value
            )
            
            return {
                'feature_explanations': feature_explanations,
                'overall_explanation': overall_explanation,
                'confidence_score': 0.85,
                'visualization_data': {
                    'shap_values': shap_values.tolist(),
                    'feature_names': feature_names,
                    'base_value': base_value
                }
            }
            
        except Exception as e:
            logger.error(f"SHAP explanation generation failed: {e}")
            return await self._generate_feature_importance_explanation(model_id, input_data, scope)
    
    async def _generate_permutation_explanation(self, model_id: str, input_data: Dict[str, Any],
                                              scope: ExplanationScope) -> Dict[str, Any]:
        """Generate permutation importance explanation."""
        feature_names = list(input_data.keys())
        
        # Simulate permutation importance scores
        importance_scores = np.random.exponential(0.1, len(feature_names))
        importance_scores = importance_scores / np.sum(importance_scores)  # Normalize
        
        feature_explanations = []
        for feature_name, importance in zip(feature_names, importance_scores):
            explanation_text = self._generate_permutation_explanation_text(
                feature_name, importance, input_data[feature_name]
            )
            
            feature_explanations.append(FeatureExplanation(
                feature_name=feature_name,
                importance_score=importance,
                contribution=importance * np.random.choice([-1, 1]),  # Random direction
                confidence=0.75,
                explanation_text=explanation_text,
                supporting_evidence=[f"Permutation importance: {importance:.4f}"]
            ))
        
        # Sort by importance
        feature_explanations.sort(key=lambda x: x.importance_score, reverse=True)
        
        overall_explanation = await self._generate_permutation_overall_explanation(feature_explanations)
        
        return {
            'feature_explanations': feature_explanations,
            'overall_explanation': overall_explanation,
            'confidence_score': 0.75,
            'visualization_data': {
                'importance_scores': importance_scores.tolist(),
                'feature_names': feature_names
            }
        }
    
    async def _generate_feature_importance_explanation(self, model_id: str, input_data: Dict[str, Any],
                                                     scope: ExplanationScope) -> Dict[str, Any]:
        """Generate feature importance explanation."""
        feature_names = list(input_data.keys())
        
        # Use baseline importance or generate synthetic
        domain = self._infer_domain_from_features(feature_names)
        baseline_importance = self.feature_baselines.get(domain, {})
        
        feature_explanations = []
        for feature_name in feature_names:
            # Get baseline importance or assign random
            importance = baseline_importance.get(feature_name, np.random.exponential(0.1))
            
            # Add some noise
            importance *= np.random.normal(1.0, 0.2)
            importance = max(0, importance)
            
            explanation_text = self._generate_feature_importance_explanation_text(
                feature_name, importance, input_data[feature_name], domain
            )
            
            feature_explanations.append(FeatureExplanation(
                feature_name=feature_name,
                importance_score=importance,
                contribution=importance * np.random.choice([-1, 1]),
                confidence=0.80,
                explanation_text=explanation_text,
                supporting_evidence=[f"Feature importance: {importance:.4f}"]
            ))
        
        # Sort by importance
        feature_explanations.sort(key=lambda x: x.importance_score, reverse=True)
        
        overall_explanation = await self._generate_feature_importance_overall_explanation(
            feature_explanations, domain
        )
        
        return {
            'feature_explanations': feature_explanations,
            'overall_explanation': overall_explanation,
            'confidence_score': 0.80,
            'visualization_data': {
                'importance_scores': [fe.importance_score for fe in feature_explanations],
                'feature_names': [fe.feature_name for fe in feature_explanations]
            }
        }
    
    async def _generate_partial_dependence_explanation(self, model_id: str, input_data: Dict[str, Any],
                                                     scope: ExplanationScope) -> Dict[str, Any]:
        """Generate partial dependence explanation."""
        feature_names = list(input_data.keys())
        
        feature_explanations = []
        for feature_name in feature_names:
            # Simulate partial dependence analysis
            feature_value = input_data[feature_name]
            
            # Generate synthetic partial dependence curve
            x_values = np.linspace(feature_value * 0.5, feature_value * 1.5, 20)
            y_values = np.sin(x_values / feature_value) * 0.1 + 0.5  # Synthetic curve
            
            # Calculate importance based on curve steepness
            gradient = np.gradient(y_values)
            importance = np.mean(np.abs(gradient))
            
            explanation_text = self._generate_partial_dependence_explanation_text(
                feature_name, feature_value, importance
            )
            
            feature_explanations.append(FeatureExplanation(
                feature_name=feature_name,
                importance_score=importance,
                contribution=gradient[len(gradient)//2],  # Gradient at current value
                confidence=0.70,
                explanation_text=explanation_text,
                supporting_evidence=[f"Partial dependence gradient: {importance:.4f}"]
            ))
        
        # Sort by importance
        feature_explanations.sort(key=lambda x: x.importance_score, reverse=True)
        
        overall_explanation = await self._generate_partial_dependence_overall_explanation(feature_explanations)
        
        return {
            'feature_explanations': feature_explanations,
            'overall_explanation': overall_explanation,
            'confidence_score': 0.70
        }
    
    async def _generate_counterfactual_explanation(self, model_id: str, input_data: Dict[str, Any],
                                                 scope: ExplanationScope) -> Dict[str, Any]:
        """Generate counterfactual explanation."""
        feature_names = list(input_data.keys())
        
        # Generate counterfactual scenarios
        counterfactuals = []
        feature_explanations = []
        
        for feature_name in feature_names:
            original_value = input_data[feature_name]
            
            # Generate counterfactual value (±20% change)
            change_factor = np.random.choice([0.8, 1.2])
            counterfactual_value = original_value * change_factor
            
            # Estimate impact of change
            impact = abs(change_factor - 1.0) * np.random.exponential(1.0)
            
            explanation_text = self._generate_counterfactual_explanation_text(
                feature_name, original_value, counterfactual_value, impact
            )
            
            feature_explanations.append(FeatureExplanation(
                feature_name=feature_name,
                importance_score=impact,
                contribution=impact * (1 if change_factor > 1 else -1),
                confidence=0.65,
                explanation_text=explanation_text,
                supporting_evidence=[f"Counterfactual impact: {impact:.4f}"]
            ))
            
            counterfactuals.append({
                'feature': feature_name,
                'original_value': original_value,
                'counterfactual_value': counterfactual_value,
                'predicted_impact': impact
            })
        
        # Sort by importance
        feature_explanations.sort(key=lambda x: x.importance_score, reverse=True)
        
        overall_explanation = await self._generate_counterfactual_overall_explanation(
            feature_explanations, counterfactuals
        )
        
        return {
            'feature_explanations': feature_explanations,
            'overall_explanation': overall_explanation,
            'confidence_score': 0.65,
            'visualization_data': {
                'counterfactuals': counterfactuals
            }
        }
    
    def _infer_domain_from_features(self, feature_names: List[str]) -> str:
        """Infer domain from feature names."""
        water_quality_features = {'ph', 'turbidity', 'chlorine', 'conductivity'}
        treatment_features = {'coagulant', 'filtration', 'chlorination', 'settling'}
        energy_features = {'pump', 'motor', 'efficiency', 'power'}
        
        feature_set = set(name.lower() for name in feature_names)
        
        if any(f in ' '.join(feature_names).lower() for f in water_quality_features):
            return 'water_quality'
        elif any(f in ' '.join(feature_names).lower() for f in treatment_features):
            return 'treatment_optimization'
        elif any(f in ' '.join(feature_names).lower() for f in energy_features):
            return 'energy_efficiency'
        else:
            return 'water_quality'  # Default
    
    def _generate_shap_explanation_text(self, feature_name: str, shap_value: float, 
                                      feature_value: Any) -> str:
        """Generate explanation text for SHAP value."""
        direction = "increases" if shap_value > 0 else "decreases"
        magnitude = "significantly" if abs(shap_value) > 0.1 else "moderately" if abs(shap_value) > 0.05 else "slightly"
        
        return f"Feature '{feature_name}' with value {feature_value} {magnitude} {direction} the prediction by {abs(shap_value):.4f} units."
    
    def _generate_permutation_explanation_text(self, feature_name: str, importance: float,
                                             feature_value: Any) -> str:
        """Generate explanation text for permutation importance."""
        importance_level = "high" if importance > 0.2 else "moderate" if importance > 0.1 else "low"
        
        return f"Feature '{feature_name}' has {importance_level} importance ({importance:.4f}) for the model's predictions."
    
    def _generate_feature_importance_explanation_text(self, feature_name: str, importance: float,
                                                    feature_value: Any, domain: str) -> str:
        """Generate explanation text for feature importance."""
        importance_level = "critical" if importance > 0.3 else "important" if importance > 0.15 else "moderate" if importance > 0.05 else "minor"
        
        domain_context = {
            'water_quality': "water quality assessment",
            'treatment_optimization': "treatment process optimization",
            'energy_efficiency': "energy efficiency prediction"
        }.get(domain, "model prediction")
        
        return f"Feature '{feature_name}' is {importance_level} for {domain_context}, with current value {feature_value}."
    
    def _generate_partial_dependence_explanation_text(self, feature_name: str, feature_value: Any,
                                                    importance: float) -> str:
        """Generate explanation text for partial dependence."""
        sensitivity = "highly sensitive" if importance > 0.1 else "moderately sensitive" if importance > 0.05 else "less sensitive"
        
        return f"The model is {sensitivity} to changes in '{feature_name}' around the current value of {feature_value}."
    
    def _generate_counterfactual_explanation_text(self, feature_name: str, original_value: Any,
                                                counterfactual_value: Any, impact: float) -> str:
        """Generate explanation text for counterfactual."""
        change_direction = "increasing" if counterfactual_value > original_value else "decreasing"
        impact_level = "significant" if impact > 0.5 else "moderate" if impact > 0.2 else "small"
        
        return f"If '{feature_name}' changed from {original_value} to {counterfactual_value}, it would have a {impact_level} impact on the prediction."
    
    async def _generate_shap_overall_explanation(self, feature_explanations: List[FeatureExplanation],
                                               base_value: float) -> str:
        """Generate overall explanation for SHAP analysis."""
        top_features = feature_explanations[:3]
        positive_contributors = [fe for fe in top_features if fe.contribution > 0]
        negative_contributors = [fe for fe in top_features if fe.contribution < 0]
        
        explanation = f"The model's prediction is based on a baseline value of {base_value:.3f}. "
        
        if positive_contributors:
            pos_names = [fe.feature_name for fe in positive_contributors]
            explanation += f"Features {', '.join(pos_names)} increase the prediction. "
        
        if negative_contributors:
            neg_names = [fe.feature_name for fe in negative_contributors]
            explanation += f"Features {', '.join(neg_names)} decrease the prediction. "
        
        explanation += f"The most influential feature is '{top_features[0].feature_name}' with a contribution of {top_features[0].contribution:.4f}."
        
        return explanation
    
    async def _generate_permutation_overall_explanation(self, feature_explanations: List[FeatureExplanation]) -> str:
        """Generate overall explanation for permutation importance."""
        top_features = feature_explanations[:3]
        total_importance = sum(fe.importance_score for fe in feature_explanations)
        
        explanation = f"Permutation importance analysis reveals that the top 3 features account for "
        explanation += f"{sum(fe.importance_score for fe in top_features) / total_importance * 100:.1f}% of the model's decision-making. "
        explanation += f"The most important feature is '{top_features[0].feature_name}' "
        explanation += f"({top_features[0].importance_score / total_importance * 100:.1f}% of total importance)."
        
        return explanation
    
    async def _generate_feature_importance_overall_explanation(self, feature_explanations: List[FeatureExplanation],
                                                             domain: str) -> str:
        """Generate overall explanation for feature importance."""
        top_features = feature_explanations[:3]
        
        explanation = f"For {domain} modeling, the analysis shows that "
        explanation += f"'{top_features[0].feature_name}' is the most critical factor, "
        explanation += f"followed by '{top_features[1].feature_name}' and '{top_features[2].feature_name}'. "
        explanation += f"These three features together drive the majority of the model's predictions."
        
        return explanation
    
    async def _generate_partial_dependence_overall_explanation(self, feature_explanations: List[FeatureExplanation]) -> str:
        """Generate overall explanation for partial dependence."""
        most_sensitive = feature_explanations[0]
        
        explanation = f"Partial dependence analysis shows that the model is most sensitive to changes in "
        explanation += f"'{most_sensitive.feature_name}'. Small changes in this feature can significantly "
        explanation += f"impact the model's output. Other features show varying degrees of sensitivity, "
        explanation += f"with some having more linear relationships and others showing non-linear dependencies."
        
        return explanation
    
    async def _generate_counterfactual_overall_explanation(self, feature_explanations: List[FeatureExplanation],
                                                         counterfactuals: List[Dict[str, Any]]) -> str:
        """Generate overall explanation for counterfactual analysis."""
        most_impactful = feature_explanations[0]
        
        explanation = f"Counterfactual analysis reveals that changing '{most_impactful.feature_name}' "
        explanation += f"would have the largest impact on the prediction. "
        explanation += f"Among {len(counterfactuals)} scenarios tested, the model shows varying sensitivity "
        explanation += f"to different feature modifications, providing insights into which changes would "
        explanation += f"be most effective for achieving desired outcomes."
        
        return explanation


# Convenience functions
async def explain_water_quality_prediction(model_id: str, input_data: Dict[str, Any],
                                         method: str = 'feature_importance') -> Dict[str, Any]:
    """Explain water quality model prediction."""
    system = ModelInterpretabilitySystem()
    
    config = {
        'model_id': model_id,
        'input_data': input_data,
        'method': method,
        'scope': 'local'
    }
    
    return await system.explain_model_prediction(config)


async def generate_global_model_explanation(model_id: str, feature_names: List[str],
                                          method: str = 'feature_importance') -> Dict[str, Any]:
    """Generate global explanation for model behavior."""
    system = ModelInterpretabilitySystem()
    
    # Create dummy input data for global explanation
    input_data = {name: 1.0 for name in feature_names}
    
    config = {
        'model_id': model_id,
        'input_data': input_data,
        'method': method,
        'scope': 'global'
    }
    
    return await system.explain_model_prediction(config)
