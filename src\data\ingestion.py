"""Data Ingestion System for Water Management Platform."""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

from src.utils.logging_config import log_async_function_call
from src.data.climate_data_collector import ClimateDataCollector

logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """Types of data sources."""
    CLIMATE_API = "climate_api"
    SENSOR_DATA = "sensor_data"
    MANUAL_INPUT = "manual_input"
    FILE_UPLOAD = "file_upload"
    DATABASE = "database"
    STREAM = "stream"


class DataFormat(Enum):
    """Supported data formats."""
    JSON = "json"
    CSV = "csv"
    XML = "xml"
    BINARY = "binary"
    TIME_SERIES = "time_series"


@dataclass
class DataIngestionJob:
    """Data ingestion job configuration."""
    job_id: str
    source_type: DataSourceType
    source_config: Dict[str, Any]
    data_format: DataFormat
    destination: str
    schedule: Optional[str] = None
    status: str = "pending"
    created_at: datetime = field(default_factory=datetime.now)
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    error_count: int = 0
    success_count: int = 0


@dataclass
class IngestionResult:
    """Result of data ingestion operation."""
    job_id: str
    status: str
    records_processed: int
    records_successful: int
    records_failed: int
    processing_time: float
    errors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


class DataIngestionSystem:
    """Comprehensive data ingestion system."""
    
    def __init__(self):
        self.ingestion_jobs: Dict[str, DataIngestionJob] = {}
        self.ingestion_history: List[IngestionResult] = []
        self.active_jobs: Dict[str, asyncio.Task] = {}
        
        # Initialize components
        self.climate_collector = ClimateDataCollector()
        
        # Configuration
        self.config = {
            'max_concurrent_jobs': 10,
            'retry_attempts': 3,
            'retry_delay': 60,  # seconds
            'batch_size': 1000,
            'timeout': 300,  # seconds
            'data_validation': True
        }
        
        # Start scheduler
        asyncio.create_task(self._job_scheduler())
    
    @log_async_function_call
    async def create_ingestion_job(self, job_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create new data ingestion job."""
        try:
            job_id = job_config.get('job_id') or f"job_{uuid.uuid4().hex[:8]}"
            
            if job_id in self.ingestion_jobs:
                return {'status': 'error', 'error': 'Job ID already exists'}
            
            job = DataIngestionJob(
                job_id=job_id,
                source_type=DataSourceType(job_config['source_type']),
                source_config=job_config['source_config'],
                data_format=DataFormat(job_config['data_format']),
                destination=job_config['destination'],
                schedule=job_config.get('schedule')
            )
            
            self.ingestion_jobs[job_id] = job
            
            # Schedule next run if schedule provided
            if job.schedule:
                job.next_run = self._calculate_next_run(job.schedule)
            
            return {
                'status': 'success',
                'job_id': job_id,
                'job_config': {
                    'source_type': job.source_type.value,
                    'data_format': job.data_format.value,
                    'destination': job.destination,
                    'schedule': job.schedule,
                    'created_at': job.created_at.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to create ingestion job: {e}")
            return {'status': 'error', 'error': str(e)}
    
    @log_async_function_call
    async def run_ingestion_job(self, job_id: str, immediate: bool = False) -> Dict[str, Any]:
        """Run data ingestion job."""
        try:
            if job_id not in self.ingestion_jobs:
                return {'status': 'error', 'error': 'Job not found'}
            
            job = self.ingestion_jobs[job_id]
            
            if job_id in self.active_jobs and not self.active_jobs[job_id].done():
                return {'status': 'error', 'error': 'Job already running'}
            
            # Start ingestion task
            task = asyncio.create_task(self._execute_ingestion_job(job))
            self.active_jobs[job_id] = task
            
            if immediate:
                # Wait for completion
                result = await task
                return result
            else:
                # Return immediately
                return {
                    'status': 'success',
                    'message': 'Job started',
                    'job_id': job_id
                }
            
        except Exception as e:
            logger.error(f"Failed to run ingestion job {job_id}: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _execute_ingestion_job(self, job: DataIngestionJob) -> Dict[str, Any]:
        """Execute individual ingestion job."""
        start_time = datetime.now()
        
        try:
            job.status = "running"
            job.last_run = start_time
            
            logger.info(f"Starting ingestion job {job.job_id}")
            
            # Route to appropriate ingestion method
            if job.source_type == DataSourceType.CLIMATE_API:
                result = await self._ingest_climate_data(job)
            elif job.source_type == DataSourceType.SENSOR_DATA:
                result = await self._ingest_sensor_data(job)
            elif job.source_type == DataSourceType.FILE_UPLOAD:
                result = await self._ingest_file_data(job)
            elif job.source_type == DataSourceType.DATABASE:
                result = await self._ingest_database_data(job)
            elif job.source_type == DataSourceType.STREAM:
                result = await self._ingest_stream_data(job)
            else:
                result = IngestionResult(
                    job_id=job.job_id,
                    status="error",
                    records_processed=0,
                    records_successful=0,
                    records_failed=0,
                    processing_time=0,
                    errors=[f"Unsupported source type: {job.source_type.value}"]
                )
            
            # Update job status
            if result.status == "success":
                job.status = "completed"
                job.success_count += 1
            else:
                job.status = "failed"
                job.error_count += 1
            
            # Calculate next run
            if job.schedule:
                job.next_run = self._calculate_next_run(job.schedule)
            
            # Store result
            self.ingestion_history.append(result)
            
            # Cleanup old history (keep last 1000 entries)
            if len(self.ingestion_history) > 1000:
                self.ingestion_history = self.ingestion_history[-1000:]
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Completed ingestion job {job.job_id} in {processing_time:.2f}s")
            
            return {
                'status': 'success',
                'job_id': job.job_id,
                'result': {
                    'status': result.status,
                    'records_processed': result.records_processed,
                    'records_successful': result.records_successful,
                    'records_failed': result.records_failed,
                    'processing_time': result.processing_time,
                    'errors': result.errors
                }
            }
            
        except Exception as e:
            job.status = "error"
            job.error_count += 1
            
            error_result = IngestionResult(
                job_id=job.job_id,
                status="error",
                records_processed=0,
                records_successful=0,
                records_failed=0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                errors=[str(e)]
            )
            
            self.ingestion_history.append(error_result)
            
            logger.error(f"Ingestion job {job.job_id} failed: {e}")
            
            return {'status': 'error', 'error': str(e)}
    
    async def _ingest_climate_data(self, job: DataIngestionJob) -> IngestionResult:
        """Ingest climate data from APIs."""
        try:
            config = job.source_config
            location = config.get('location', {'lat': 40.7128, 'lon': -74.0060})  # Default NYC
            
            # Collect climate data
            climate_data = await self.climate_collector.collect_all_climate_data(location)
            
            if climate_data['status'] == 'success':
                # Process and store data
                records_processed = len(climate_data.get('raw_data', {}))
                
                # Simulate data storage
                await self._store_data(job.destination, climate_data, job.data_format)
                
                return IngestionResult(
                    job_id=job.job_id,
                    status="success",
                    records_processed=records_processed,
                    records_successful=records_processed,
                    records_failed=0,
                    processing_time=1.5,
                    metadata={'data_sources': list(climate_data.get('raw_data', {}).keys())}
                )
            else:
                return IngestionResult(
                    job_id=job.job_id,
                    status="error",
                    records_processed=0,
                    records_successful=0,
                    records_failed=1,
                    processing_time=0.5,
                    errors=[climate_data.get('error', 'Unknown error')]
                )
                
        except Exception as e:
            return IngestionResult(
                job_id=job.job_id,
                status="error",
                records_processed=0,
                records_successful=0,
                records_failed=1,
                processing_time=0.1,
                errors=[str(e)]
            )
    
    async def _ingest_sensor_data(self, job: DataIngestionJob) -> IngestionResult:
        """Ingest sensor data."""
        try:
            config = job.source_config
            sensor_endpoint = config.get('endpoint')
            
            # Simulate sensor data collection
            sensor_data = {
                'sensors': [
                    {'id': 'temp_001', 'value': 22.5, 'unit': 'C'},
                    {'id': 'ph_001', 'value': 7.2, 'unit': 'pH'},
                    {'id': 'flow_001', 'value': 1250, 'unit': 'm3/h'}
                ],
                'timestamp': datetime.now().isoformat(),
                'location': config.get('location', 'treatment_plant_1')
            }
            
            # Store data
            await self._store_data(job.destination, sensor_data, job.data_format)
            
            return IngestionResult(
                job_id=job.job_id,
                status="success",
                records_processed=len(sensor_data['sensors']),
                records_successful=len(sensor_data['sensors']),
                records_failed=0,
                processing_time=0.8,
                metadata={'sensor_count': len(sensor_data['sensors'])}
            )
            
        except Exception as e:
            return IngestionResult(
                job_id=job.job_id,
                status="error",
                records_processed=0,
                records_successful=0,
                records_failed=1,
                processing_time=0.1,
                errors=[str(e)]
            )
    
    async def _ingest_file_data(self, job: DataIngestionJob) -> IngestionResult:
        """Ingest data from file upload."""
        try:
            config = job.source_config
            file_path = config.get('file_path')
            
            # Simulate file processing
            if job.data_format == DataFormat.CSV:
                # Simulate CSV processing
                records_processed = 150  # Simulated row count
            elif job.data_format == DataFormat.JSON:
                # Simulate JSON processing
                records_processed = 75   # Simulated record count
            else:
                records_processed = 1    # Single file
            
            # Simulate data validation
            validation_errors = []
            if self.config['data_validation']:
                # Simulate validation
                failed_records = max(0, int(records_processed * 0.02))  # 2% failure rate
            else:
                failed_records = 0
            
            successful_records = records_processed - failed_records
            
            return IngestionResult(
                job_id=job.job_id,
                status="success" if failed_records == 0 else "partial",
                records_processed=records_processed,
                records_successful=successful_records,
                records_failed=failed_records,
                processing_time=2.3,
                errors=validation_errors,
                metadata={'file_path': file_path, 'format': job.data_format.value}
            )
            
        except Exception as e:
            return IngestionResult(
                job_id=job.job_id,
                status="error",
                records_processed=0,
                records_successful=0,
                records_failed=1,
                processing_time=0.1,
                errors=[str(e)]
            )
    
    async def _ingest_database_data(self, job: DataIngestionJob) -> IngestionResult:
        """Ingest data from database source."""
        try:
            config = job.source_config
            query = config.get('query', 'SELECT * FROM water_quality')
            
            # Simulate database query
            records_processed = 500  # Simulated result count
            
            return IngestionResult(
                job_id=job.job_id,
                status="success",
                records_processed=records_processed,
                records_successful=records_processed,
                records_failed=0,
                processing_time=1.8,
                metadata={'query': query}
            )
            
        except Exception as e:
            return IngestionResult(
                job_id=job.job_id,
                status="error",
                records_processed=0,
                records_successful=0,
                records_failed=1,
                processing_time=0.1,
                errors=[str(e)]
            )
    
    async def _ingest_stream_data(self, job: DataIngestionJob) -> IngestionResult:
        """Ingest data from stream source."""
        try:
            config = job.source_config
            stream_url = config.get('stream_url')
            
            # Simulate stream data processing
            records_processed = 1000  # Simulated stream records
            
            return IngestionResult(
                job_id=job.job_id,
                status="success",
                records_processed=records_processed,
                records_successful=records_processed,
                records_failed=0,
                processing_time=5.2,
                metadata={'stream_url': stream_url}
            )
            
        except Exception as e:
            return IngestionResult(
                job_id=job.job_id,
                status="error",
                records_processed=0,
                records_successful=0,
                records_failed=1,
                processing_time=0.1,
                errors=[str(e)]
            )
    
    async def _store_data(self, destination: str, data: Any, format: DataFormat):
        """Store ingested data."""
        # Simulate data storage
        logger.info(f"Storing data to {destination} in {format.value} format")
        
        # In a real implementation, this would:
        # - Connect to the destination (database, file system, etc.)
        # - Transform data to the required format
        # - Store the data
        # - Handle errors and retries
        
        await asyncio.sleep(0.1)  # Simulate storage time
    
    def _calculate_next_run(self, schedule: str) -> datetime:
        """Calculate next run time based on schedule."""
        # Simple schedule parsing (in practice, would use cron-like syntax)
        if schedule == 'hourly':
            return datetime.now() + timedelta(hours=1)
        elif schedule == 'daily':
            return datetime.now() + timedelta(days=1)
        elif schedule == 'weekly':
            return datetime.now() + timedelta(weeks=1)
        else:
            # Default to hourly
            return datetime.now() + timedelta(hours=1)
    
    async def _job_scheduler(self):
        """Background job scheduler."""
        while True:
            try:
                current_time = datetime.now()
                
                # Check for scheduled jobs
                for job_id, job in self.ingestion_jobs.items():
                    if (job.schedule and job.next_run and 
                        current_time >= job.next_run and
                        job_id not in self.active_jobs):
                        
                        logger.info(f"Starting scheduled job {job_id}")
                        await self.run_ingestion_job(job_id)
                
                # Sleep for 60 seconds before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Job scheduler error: {e}")
                await asyncio.sleep(60)
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of ingestion job."""
        if job_id not in self.ingestion_jobs:
            return {'status': 'error', 'error': 'Job not found'}
        
        job = self.ingestion_jobs[job_id]
        
        # Get recent results
        recent_results = [r for r in self.ingestion_history if r.job_id == job_id][-5:]
        
        return {
            'status': 'success',
            'job_info': {
                'job_id': job_id,
                'source_type': job.source_type.value,
                'data_format': job.data_format.value,
                'destination': job.destination,
                'status': job.status,
                'created_at': job.created_at.isoformat(),
                'last_run': job.last_run.isoformat() if job.last_run else None,
                'next_run': job.next_run.isoformat() if job.next_run else None,
                'success_count': job.success_count,
                'error_count': job.error_count,
                'schedule': job.schedule
            },
            'recent_results': [
                {
                    'status': r.status,
                    'records_processed': r.records_processed,
                    'records_successful': r.records_successful,
                    'processing_time': r.processing_time,
                    'timestamp': r.timestamp.isoformat()
                }
                for r in recent_results
            ]
        }
    
    def list_jobs(self) -> Dict[str, Any]:
        """List all ingestion jobs."""
        jobs_info = []
        
        for job_id, job in self.ingestion_jobs.items():
            jobs_info.append({
                'job_id': job_id,
                'source_type': job.source_type.value,
                'status': job.status,
                'last_run': job.last_run.isoformat() if job.last_run else None,
                'success_count': job.success_count,
                'error_count': job.error_count
            })
        
        return {
            'status': 'success',
            'total_jobs': len(jobs_info),
            'jobs': jobs_info
        }


# Convenience functions
async def create_climate_ingestion_job(location: Dict[str, float], 
                                     schedule: str = 'hourly') -> Dict[str, Any]:
    """Create climate data ingestion job."""
    system = DataIngestionSystem()
    
    job_config = {
        'source_type': DataSourceType.CLIMATE_API.value,
        'source_config': {'location': location},
        'data_format': DataFormat.JSON.value,
        'destination': 'climate_database',
        'schedule': schedule
    }
    
    return await system.create_ingestion_job(job_config)


async def ingest_sensor_data(sensor_endpoint: str, location: str) -> Dict[str, Any]:
    """Ingest sensor data immediately."""
    system = DataIngestionSystem()
    
    job_config = {
        'source_type': DataSourceType.SENSOR_DATA.value,
        'source_config': {'endpoint': sensor_endpoint, 'location': location},
        'data_format': DataFormat.JSON.value,
        'destination': 'sensor_database'
    }
    
    result = await system.create_ingestion_job(job_config)
    if result['status'] == 'success':
        return await system.run_ingestion_job(result['job_id'], immediate=True)
    
    return result
