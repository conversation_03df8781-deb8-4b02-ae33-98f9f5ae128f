# Core Python Dependencies
python>=3.9

# Deep Learning Frameworks
tensorflow>=2.13.0
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
datasets>=2.12.0

# LLM and Agent Frameworks
langchain>=0.0.350
langgraph>=0.0.40
openai>=1.0.0
google-generativeai>=0.8.0
anthropic>=0.7.0
cohere>=4.0.0

# Data Science and Analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
dash>=2.10.0

# Web Framework and APIs
fastapi>=0.100.0
uvicorn[standard]>=0.22.0
streamlit>=1.25.0
requests>=2.31.0
httpx>=0.24.0
pydantic>=2.0.0
websockets>=12.0
python-socketio>=5.10.0
aiofiles>=23.2.0

# Database and Caching
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
redis>=4.5.0
pymongo>=4.4.0

# Climate and Environmental APIs
pyowm>=3.3.0  # OpenWeatherMap
nasapy>=0.0.3  # NASA APIs
wbgapi>=1.0.12  # World Bank API
noaa-sdk>=0.1.18  # NOAA Climate Data

# Geospatial and Mapping
folium>=0.14.0
geopandas>=0.13.0
shapely>=2.0.0
rasterio>=1.3.0
cartopy>=0.21.0

# Optimization Libraries
optuna>=3.2.0
hyperopt>=0.2.7
deap>=1.3.3  # Genetic Algorithms
pymoo>=0.6.0  # Multi-objective Optimization
cvxpy>=1.3.0  # Convex Optimization

# Time Series and Forecasting
prophet>=1.1.4
statsmodels>=0.14.0
pmdarima>=2.0.3
sktime>=0.21.0

# Distributed Computing
dask>=2023.6.0
ray>=2.5.0
celery>=5.3.0

# Monitoring and Logging
wandb>=0.15.0
mlflow>=2.4.0
loguru>=0.7.0
prometheus-client>=0.17.0

# Testing and Quality
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0

# Utilities
python-dotenv>=1.0.0
click>=8.1.0
tqdm>=4.65.0
joblib>=1.3.0
pyyaml>=6.0
jsonschema>=4.18.0
packaging>=23.2,<25

# Jupyter and Development
jupyter>=1.0.0
ipykernel>=6.24.0
ipywidgets>=8.0.0
notebook>=6.5.0

# Docker and Deployment
docker>=6.1.0
kubernetes>=27.2.0

# Security
cryptography>=41.0.0
python-jose>=3.3.0
passlib>=1.7.4

# Documentation
sphinx>=7.1.0
mkdocs>=1.5.0
mkdocs-material>=9.1.0
