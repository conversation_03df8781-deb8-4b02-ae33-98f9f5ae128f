#!/usr/bin/env node
/**
 * Installation and Setup Script for Water Management Frontend
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🌊 Water Management Frontend - Installation Script');
console.log('================================================\n');

// Check Node.js version
function checkNodeVersion() {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    console.log(`📋 Checking Node.js version: ${nodeVersion}`);
    
    if (majorVersion < 16) {
        console.error('❌ Node.js version 16 or higher is required');
        console.error('   Please upgrade Node.js and try again');
        process.exit(1);
    }
    
    console.log('✅ Node.js version is compatible\n');
}

// Check npm version
function checkNpmVersion() {
    try {
        const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
        console.log(`📋 npm version: ${npmVersion}`);
        console.log('✅ npm is available\n');
    } catch (error) {
        console.error('❌ npm is not available');
        console.error('   Please install npm and try again');
        process.exit(1);
    }
}

// Install dependencies
function installDependencies() {
    console.log('📦 Installing dependencies...');
    console.log('   This may take a few minutes...\n');
    
    try {
        execSync('npm install', { 
            stdio: 'inherit',
            cwd: __dirname 
        });
        console.log('\n✅ Dependencies installed successfully\n');
    } catch (error) {
        console.error('❌ Failed to install dependencies');
        console.error('   Error:', error.message);
        process.exit(1);
    }
}

// Setup environment file
function setupEnvironment() {
    const envPath = path.join(__dirname, '.env');
    const envExamplePath = path.join(__dirname, '.env.example');
    
    console.log('⚙️ Setting up environment configuration...');
    
    if (!fs.existsSync(envPath)) {
        if (fs.existsSync(envExamplePath)) {
            fs.copyFileSync(envExamplePath, envPath);
            console.log('✅ Created .env file from .env.example');
        } else {
            // Create basic .env file
            const basicEnv = `# Water Management Frontend Configuration
PORT=3000
NODE_ENV=development
BACKEND_API_URL=http://localhost:8001
WEBSOCKET_ENABLED=true
`;
            fs.writeFileSync(envPath, basicEnv);
            console.log('✅ Created basic .env file');
        }
    } else {
        console.log('✅ .env file already exists');
    }
    
    console.log('');
}

// Create necessary directories
function createDirectories() {
    console.log('📁 Creating necessary directories...');
    
    const directories = [
        'logs',
        'public',
        'src/components/pages',
        'src/styles',
        'src/assets'
    ];
    
    directories.forEach(dir => {
        const dirPath = path.join(__dirname, dir);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`   Created: ${dir}`);
        }
    });
    
    console.log('✅ Directories created\n');
}

// Create basic CSS file
function createBasicStyles() {
    const cssPath = path.join(__dirname, 'src/styles/main.css');
    
    if (!fs.existsSync(cssPath)) {
        console.log('🎨 Creating basic stylesheet...');
        
        const basicCSS = `/* Water Management Frontend - Main Styles */

:root {
    --primary-color: #1f77b4;
    --secondary-color: #4ade80;
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-color: #ffffff;
    --text-secondary: #94a3b8;
    --border-color: #334155;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.app-container {
    min-height: 100vh;
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
}

.header {
    grid-area: header;
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 1rem;
}

.sidebar {
    grid-area: sidebar;
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
}

.main-content {
    grid-area: main;
    padding: 1rem;
    overflow-y: auto;
}

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-container {
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background: #1e6ba8;
}

@media (max-width: 768px) {
    .app-container {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }
    
    .sidebar {
        display: none;
    }
}
`;
        
        fs.writeFileSync(cssPath, basicCSS);
        console.log('✅ Created basic stylesheet\n');
    }
}

// Check backend connectivity
async function checkBackendConnectivity() {
    console.log('🔗 Checking backend connectivity...');
    
    try {
        const { default: fetch } = await import('node-fetch');
        const response = await fetch('http://localhost:8001/api/health', {
            timeout: 5000
        });
        
        if (response.ok) {
            console.log('✅ Backend is accessible');
        } else {
            console.log('⚠️ Backend responded with status:', response.status);
        }
    } catch (error) {
        console.log('⚠️ Backend is not accessible (this is normal if not running yet)');
        console.log('   Make sure to start the backend server before using the frontend');
    }
    
    console.log('');
}

// Display completion message
function displayCompletionMessage() {
    console.log('🎉 Installation completed successfully!\n');
    console.log('📋 Next steps:');
    console.log('   1. Review and update the .env file if needed');
    console.log('   2. Make sure the backend server is running on port 8001');
    console.log('   3. Start the frontend server:\n');
    console.log('      npm run dev     # Development mode');
    console.log('      npm start       # Production mode\n');
    console.log('   4. Open your browser to: http://localhost:3000\n');
    console.log('🌊 Water Management Frontend is ready to use!');
}

// Main installation process
async function main() {
    try {
        checkNodeVersion();
        checkNpmVersion();
        createDirectories();
        setupEnvironment();
        installDependencies();
        createBasicStyles();
        await checkBackendConnectivity();
        displayCompletionMessage();
    } catch (error) {
        console.error('\n❌ Installation failed:', error.message);
        process.exit(1);
    }
}

// Run installation
if (require.main === module) {
    main();
}

module.exports = {
    checkNodeVersion,
    checkNpmVersion,
    installDependencies,
    setupEnvironment,
    createDirectories
};
