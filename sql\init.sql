-- Water Management Decarbonisation System Database Initialization

-- Create database if it doesn't exist (for development)
-- Note: This will be executed by docker-entrypoint-initdb.d

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create climate_data table
CREATE TABLE IF NOT EXISTS climate_data (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude FLOAT NOT NULL,
    longitude FLOAT NOT NULL,
    temperature FLOAT,
    humidity FLOAT,
    precipitation FLOAT,
    wind_speed FLOAT,
    pressure FLOAT,
    source VARCHAR(100) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create water_treatment_systems table
CREATE TABLE IF NOT EXISTS water_treatment_systems (
    id SERIAL PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    capacity_liters_per_day INTEGER,
    energy_consumption_kwh FLOAT,
    efficiency_percentage FLOAT,
    configuration JSONB,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create optimization_results table
CREATE TABLE IF NOT EXISTS optimization_results (
    id SERIAL PRIMARY KEY,
    system_id INTEGER REFERENCES water_treatment_systems(id),
    optimization_type VARCHAR(100) NOT NULL,
    input_parameters JSONB,
    results JSONB,
    performance_metrics JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create agent_logs table
CREATE TABLE IF NOT EXISTS agent_logs (
    id SERIAL PRIMARY KEY,
    agent_type VARCHAR(100) NOT NULL,
    task_id VARCHAR(255),
    input_data JSONB,
    output_data JSONB,
    execution_time_seconds FLOAT,
    success BOOLEAN,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_climate_data_timestamp ON climate_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_climate_data_location ON climate_data(location);
CREATE INDEX IF NOT EXISTS idx_climate_data_source ON climate_data(source);
CREATE INDEX IF NOT EXISTS idx_optimization_results_system_id ON optimization_results(system_id);
CREATE INDEX IF NOT EXISTS idx_optimization_results_timestamp ON optimization_results(timestamp);
CREATE INDEX IF NOT EXISTS idx_agent_logs_agent_type ON agent_logs(agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_logs_timestamp ON agent_logs(timestamp);

-- Insert sample data for development
INSERT INTO water_treatment_systems (name, location, capacity_liters_per_day, energy_consumption_kwh, efficiency_percentage, configuration) 
VALUES 
    ('Primary Treatment Plant', 'New York', 100000, 250.5, 92.3, '{"filters": 3, "uv_treatment": true, "chemical_dosing": true}'),
    ('Secondary Treatment Facility', 'London', 75000, 180.2, 89.7, '{"filters": 2, "uv_treatment": false, "chemical_dosing": true}'),
    ('Pilot Decarbonisation Plant', 'Tokyo', 50000, 120.8, 95.1, '{"filters": 4, "uv_treatment": true, "chemical_dosing": false, "renewable_energy": true}')
ON CONFLICT DO NOTHING;

-- Insert sample climate data
INSERT INTO climate_data (timestamp, location, latitude, longitude, temperature, humidity, precipitation, wind_speed, pressure, source, metadata)
VALUES 
    (NOW() - INTERVAL '1 hour', 'New York', 40.7128, -74.0060, 22.5, 65, 0, 12.3, 1013.2, 'sample', '{"quality": "good"}'),
    (NOW() - INTERVAL '2 hours', 'New York', 40.7128, -74.0060, 21.8, 68, 2.3, 11.8, 1012.8, 'sample', '{"quality": "good"}'),
    (NOW() - INTERVAL '3 hours', 'New York', 40.7128, -74.0060, 20.9, 72, 0, 10.5, 1014.1, 'sample', '{"quality": "good"}'),
    (NOW() - INTERVAL '1 hour', 'London', 51.5074, -0.1278, 18.2, 78, 5.7, 15.2, 1008.5, 'sample', '{"quality": "good"}'),
    (NOW() - INTERVAL '2 hours', 'London', 51.5074, -0.1278, 17.5, 82, 8.1, 16.8, 1007.2, 'sample', '{"quality": "good"}'),
    (NOW() - INTERVAL '1 hour', 'Tokyo', 35.6762, 139.6503, 26.8, 58, 0, 8.7, 1016.3, 'sample', '{"quality": "good"}')
ON CONFLICT DO NOTHING;

-- Create a view for recent climate data
CREATE OR REPLACE VIEW recent_climate_data AS
SELECT 
    location,
    AVG(temperature) as avg_temperature,
    AVG(humidity) as avg_humidity,
    SUM(precipitation) as total_precipitation,
    AVG(wind_speed) as avg_wind_speed,
    COUNT(*) as data_points,
    MAX(timestamp) as latest_reading
FROM climate_data 
WHERE timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY location;

-- Create a view for system performance
CREATE OR REPLACE VIEW system_performance AS
SELECT 
    wts.name,
    wts.location,
    wts.capacity_liters_per_day,
    wts.energy_consumption_kwh,
    wts.efficiency_percentage,
    COUNT(or_table.id) as optimization_runs,
    MAX(or_table.timestamp) as last_optimization
FROM water_treatment_systems wts
LEFT JOIN optimization_results or_table ON wts.id = or_table.system_id
GROUP BY wts.id, wts.name, wts.location, wts.capacity_liters_per_day, wts.energy_consumption_kwh, wts.efficiency_percentage;

-- Grant permissions (for development)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
