"""
Configuration management for the Water Management Decarbonisation System.

This module handles all configuration settings, environment variables,
and application settings using Pydantic for validation.
"""

import os
from typing import Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application Settings
    APP_NAME: str = Field(default="Water Management Decarbonisation System")
    APP_VERSION: str = Field(default="1.0.0")
    APP_ENVIRONMENT: str = Field(default="development")
    DEBUG: bool = Field(default=True)
    LOG_LEVEL: str = Field(default="INFO")
    
    # API Configuration
    API_HOST: str = Field(default="0.0.0.0")
    API_PORT: int = Field(default=8000)
    API_WORKERS: int = Field(default=4)
    API_RELOAD: bool = Field(default=True)
    
    # Streamlit Configuration
    STREAMLIT_HOST: str = Field(default="0.0.0.0")
    STREAMLIT_PORT: int = Field(default=8501)
    
    # Security Settings
    SECRET_KEY: str = Field(default="your_secret_key_here_change_in_production")
    JWT_SECRET_KEY: str = Field(default="your_jwt_secret_key_here")
    JWT_ALGORITHM: str = Field(default="HS256")
    JWT_EXPIRATION_HOURS: int = Field(default=24)
    
    # Database Configuration
    DATABASE_URL: str = Field(default="postgresql://postgres:password@localhost:5432/watermanagement")
    DB_HOST: str = Field(default="localhost")
    DB_PORT: int = Field(default=5432)
    DB_NAME: str = Field(default="watermanagement")
    DB_USER: str = Field(default="postgres")
    DB_PASSWORD: str = Field(default="password")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0")
    REDIS_HOST: str = Field(default="localhost")
    REDIS_PORT: int = Field(default=6379)
    REDIS_DB: int = Field(default=0)
    REDIS_PASSWORD: Optional[str] = Field(default=None)
    
    # LLM API Keys
    OPENAI_API_KEY: Optional[str] = Field(default=None)
    OPENAI_ORG_ID: Optional[str] = Field(default=None)
    OPENAI_MODEL: str = Field(default="gpt-4-turbo-preview")
    
    GOOGLE_API_KEY: Optional[str] = Field(default=None)
    GOOGLE_PROJECT_ID: Optional[str] = Field(default=None)
    
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None)
    COHERE_API_KEY: Optional[str] = Field(default=None)
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None)
    
    # Climate and Environmental Data APIs
    OPENWEATHER_API_KEY: Optional[str] = Field(default=None)
    OPENWEATHER_BASE_URL: str = Field(default="https://api.openweathermap.org/data/2.5")
    
    NASA_API_KEY: Optional[str] = Field(default=None)
    NASA_BASE_URL: str = Field(default="https://api.nasa.gov")
    
    NOAA_API_TOKEN: Optional[str] = Field(default=None)
    NOAA_BASE_URL: str = Field(default="https://www.ncdc.noaa.gov/cdo-web/api/v2")
    
    WORLDBANK_API_KEY: Optional[str] = Field(default=None)
    WORLDBANK_BASE_URL: str = Field(default="https://climateknowledgeportal.worldbank.org/api")
    
    # Water and Environmental Monitoring APIs
    USGS_API_KEY: Optional[str] = Field(default=None)
    USGS_BASE_URL: str = Field(default="https://waterservices.usgs.gov")
    
    EPA_API_KEY: Optional[str] = Field(default=None)
    EPA_BASE_URL: str = Field(default="https://www.waterqualitydata.us")
    
    # Energy and Sustainability APIs
    IEA_API_KEY: Optional[str] = Field(default=None)
    CARBON_INTENSITY_API_KEY: Optional[str] = Field(default=None)
    CARBON_INTENSITY_BASE_URL: str = Field(default="https://api.carbonintensity.org.uk")
    
    # Model Configuration
    DEFAULT_LLM_MODEL: str = Field(default="gpt-4-turbo-preview")
    DEFAULT_EMBEDDING_MODEL: str = Field(default="text-embedding-ada-002")
    MAX_TOKENS: int = Field(default=4096)
    TEMPERATURE: float = Field(default=0.7)
    
    # Training Configuration
    BATCH_SIZE: int = Field(default=32)
    LEARNING_RATE: float = Field(default=0.001)
    EPOCHS: int = Field(default=100)
    VALIDATION_SPLIT: float = Field(default=0.2)
    
    # Optimization Configuration
    OPTIMIZATION_ALGORITHM: str = Field(default="genetic")
    POPULATION_SIZE: int = Field(default=100)
    GENERATIONS: int = Field(default=50)
    MUTATION_RATE: float = Field(default=0.1)
    CROSSOVER_RATE: float = Field(default=0.8)
    
    # Monitoring and Logging
    WANDB_API_KEY: Optional[str] = Field(default=None)
    WANDB_PROJECT: str = Field(default="water-management-decarbonisation")
    WANDB_ENTITY: Optional[str] = Field(default=None)
    
    MLFLOW_TRACKING_URI: str = Field(default="http://localhost:5000")
    MLFLOW_EXPERIMENT_NAME: str = Field(default="water-treatment-optimization")
    
    # Feature Flags
    ENABLE_REAL_TIME_OPTIMIZATION: bool = Field(default=True)
    ENABLE_CLIMATE_INTEGRATION: bool = Field(default=True)
    ENABLE_MULTI_AGENT_SYSTEM: bool = Field(default=True)
    ENABLE_FEDERATED_LEARNING: bool = Field(default=True)
    ENABLE_QUANTUM_OPTIMIZATION: bool = Field(default=False)
    ENABLE_BLOCKCHAIN_INTEGRATION: bool = Field(default=False)
    
    # Performance Tuning
    CACHE_TTL: int = Field(default=3600)
    CACHE_MAX_SIZE: int = Field(default=1000)
    RATE_LIMIT_PER_MINUTE: int = Field(default=100)
    RATE_LIMIT_PER_HOUR: int = Field(default=1000)
    MAX_WORKERS: int = Field(default=8)
    ASYNC_POOL_SIZE: int = Field(default=10)
    
    # Data Collection
    DATA_COLLECTION_INTERVAL: int = Field(default=300)  # seconds
    DATA_RETENTION_DAYS: int = Field(default=365)
    ENABLE_DATA_ANONYMIZATION: bool = Field(default=True)
    
    # Compliance and Governance
    GDPR_COMPLIANCE: bool = Field(default=True)
    CCPA_COMPLIANCE: bool = Field(default=True)
    ENABLE_AUDIT_LOGGING: bool = Field(default=True)
    DATA_ENCRYPTION_ENABLED: bool = Field(default=True)
    ENABLE_MODEL_GOVERNANCE: bool = Field(default=True)
    ENABLE_DATA_LINEAGE: bool = Field(default=True)
    ENABLE_BIAS_DETECTION: bool = Field(default=True)
    ENABLE_EXPLAINABILITY: bool = Field(default=True)
    
    @field_validator('LOG_LEVEL')
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'LOG_LEVEL must be one of {valid_levels}')
        return v.upper()

    @field_validator('APP_ENVIRONMENT')
    @classmethod
    def validate_environment(cls, v):
        """Validate application environment."""
        valid_envs = ['development', 'staging', 'production']
        if v.lower() not in valid_envs:
            raise ValueError(f'APP_ENVIRONMENT must be one of {valid_envs}')
        return v.lower()

    @field_validator('TEMPERATURE')
    @classmethod
    def validate_temperature(cls, v):
        """Validate LLM temperature parameter."""
        if not 0.0 <= v <= 2.0:
            raise ValueError('TEMPERATURE must be between 0.0 and 2.0')
        return v

    @field_validator('VALIDATION_SPLIT')
    @classmethod
    def validate_validation_split(cls, v):
        """Validate validation split ratio."""
        if not 0.0 < v < 1.0:
            raise ValueError('VALIDATION_SPLIT must be between 0.0 and 1.0')
        return v
    
    def get_database_url(self) -> str:
        """Get the complete database URL."""
        if self.DATABASE_URL and self.DATABASE_URL != "postgresql://postgres:password@localhost:5432/watermanagement":
            return self.DATABASE_URL
        
        return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    def get_redis_url(self) -> str:
        """Get the complete Redis URL."""
        if self.REDIS_URL and self.REDIS_URL != "redis://localhost:6379/0":
            return self.REDIS_URL
        
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        else:
            return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.APP_ENVIRONMENT == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.APP_ENVIRONMENT == "development"
    
    def get_available_llm_providers(self) -> List[str]:
        """Get list of available LLM providers based on configured API keys."""
        providers = []
        
        if self.OPENAI_API_KEY:
            providers.append("openai")
        if self.GOOGLE_API_KEY:
            providers.append("google")
        if self.ANTHROPIC_API_KEY:
            providers.append("anthropic")
        if self.COHERE_API_KEY:
            providers.append("cohere")
        if self.HUGGINGFACE_API_KEY:
            providers.append("huggingface")
        
        return providers
    
    def get_available_climate_apis(self) -> List[str]:
        """Get list of available climate data APIs based on configured keys."""
        apis = []
        
        if self.OPENWEATHER_API_KEY:
            apis.append("openweather")
        if self.NASA_API_KEY:
            apis.append("nasa")
        if self.NOAA_API_TOKEN:
            apis.append("noaa")
        if self.WORLDBANK_API_KEY:
            apis.append("worldbank")
        
        return apis
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"  # Ignore extra fields from .env file
    }


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


def get_environment_info() -> dict:
    """Get comprehensive environment information."""
    settings = get_settings()
    
    return {
        "app_name": settings.APP_NAME,
        "app_version": settings.APP_VERSION,
        "environment": settings.APP_ENVIRONMENT,
        "debug_mode": settings.DEBUG,
        "available_llm_providers": settings.get_available_llm_providers(),
        "available_climate_apis": settings.get_available_climate_apis(),
        "database_configured": bool(settings.DATABASE_URL),
        "redis_configured": bool(settings.REDIS_URL),
        "feature_flags": {
            "real_time_optimization": settings.ENABLE_REAL_TIME_OPTIMIZATION,
            "climate_integration": settings.ENABLE_CLIMATE_INTEGRATION,
            "multi_agent_system": settings.ENABLE_MULTI_AGENT_SYSTEM,
            "federated_learning": settings.ENABLE_FEDERATED_LEARNING,
            "quantum_optimization": settings.ENABLE_QUANTUM_OPTIMIZATION,
            "blockchain_integration": settings.ENABLE_BLOCKCHAIN_INTEGRATION,
        }
    }
