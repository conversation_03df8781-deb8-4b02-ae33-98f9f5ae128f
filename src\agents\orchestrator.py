"""
Agent Orchestrator - Coordinates multiple AI agents for water treatment optimization

This module implements the central orchestrator that manages and coordinates
various specialized AI agents for different aspects of water treatment optimization.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from langchain.agents import AgentExecutor
from langchain.memory import ConversationBufferMemory
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor

from src.agents.climate_agent import ClimateAnalysisAgent
from src.agents.optimization_agent import WaterTreatmentOptimizationAgent
from src.agents.energy_agent import EnergyEfficiencyAgent
from src.agents.sustainability_agent import SustainabilityAssessmentAgent
from src.agents.risk_agent import RiskAnalysisAgent
from src.utils.config import get_settings

logger = logging.getLogger(__name__)


class AgentType(Enum):
    """Enumeration of available agent types."""
    CLIMATE_ANALYSIS = "climate_analysis"
    WATER_TREATMENT_OPTIMIZATION = "water_treatment_optimization"
    ENERGY_EFFICIENCY = "energy_efficiency"
    SUSTAINABILITY_ASSESSMENT = "sustainability_assessment"
    RISK_ANALYSIS = "risk_analysis"
    COST_OPTIMIZATION = "cost_optimization"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    PERFORMANCE_MONITORING = "performance_monitoring"
    PREDICTIVE_MAINTENANCE = "predictive_maintenance"
    DECISION_SUPPORT = "decision_support"


@dataclass
class AgentTask:
    """Represents a task to be executed by an agent."""
    agent_type: AgentType
    task_id: str
    description: str
    input_data: Dict[str, Any]
    priority: int = 1
    dependencies: List[str] = None
    timeout: int = 300  # seconds


@dataclass
class AgentResult:
    """Represents the result of an agent task execution."""
    task_id: str
    agent_type: AgentType
    success: bool
    result: Any
    error_message: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = None


class AgentOrchestrator:
    """
    Central orchestrator for managing and coordinating AI agents.
    
    This class implements a multi-agent system that can:
    - Coordinate multiple specialized agents
    - Handle task dependencies and scheduling
    - Manage agent communication and data flow
    - Provide fault tolerance and error handling
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.agents: Dict[AgentType, Any] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.results_store: Dict[str, AgentResult] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.is_initialized = False
        self.is_running = False
        
        # Agent communication graph
        self.workflow_graph = None
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
    
    async def initialize(self):
        """Initialize all agents and the orchestration system."""
        try:
            logger.info("Initializing Agent Orchestrator...")
            
            # Initialize individual agents
            await self._initialize_agents()
            
            # Build workflow graph
            self._build_workflow_graph()
            
            # Start task processing
            asyncio.create_task(self._process_task_queue())
            
            self.is_initialized = True
            self.is_running = True
            
            logger.info("Agent Orchestrator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Agent Orchestrator: {e}")
            raise
    
    async def _initialize_agents(self):
        """Initialize all specialized agents."""
        try:
            # Climate Analysis Agent
            self.agents[AgentType.CLIMATE_ANALYSIS] = ClimateAnalysisAgent()
            await self.agents[AgentType.CLIMATE_ANALYSIS].initialize()
            
            # Water Treatment Optimization Agent
            self.agents[AgentType.WATER_TREATMENT_OPTIMIZATION] = WaterTreatmentOptimizationAgent()
            await self.agents[AgentType.WATER_TREATMENT_OPTIMIZATION].initialize()
            
            # Energy Efficiency Agent
            self.agents[AgentType.ENERGY_EFFICIENCY] = EnergyEfficiencyAgent()
            await self.agents[AgentType.ENERGY_EFFICIENCY].initialize()
            
            # Sustainability Assessment Agent
            self.agents[AgentType.SUSTAINABILITY_ASSESSMENT] = SustainabilityAssessmentAgent()
            await self.agents[AgentType.SUSTAINABILITY_ASSESSMENT].initialize()
            
            # Risk Analysis Agent
            self.agents[AgentType.RISK_ANALYSIS] = RiskAnalysisAgent()
            await self.agents[AgentType.RISK_ANALYSIS].initialize()
            
            logger.info(f"Initialized {len(self.agents)} agents")
            
        except Exception as e:
            logger.error(f"Failed to initialize agents: {e}")
            raise
    
    def _build_workflow_graph(self):
        """Build the LangGraph workflow for agent coordination."""
        try:
            # Define the workflow state
            workflow = StateGraph(dict)
            
            # Add agent nodes
            for agent_type in self.agents.keys():
                workflow.add_node(
                    agent_type.value,
                    self._create_agent_node(agent_type)
                )
            
            # Define workflow edges (agent dependencies)
            workflow.add_edge("climate_analysis", "water_treatment_optimization")
            workflow.add_edge("climate_analysis", "energy_efficiency")
            workflow.add_edge("water_treatment_optimization", "sustainability_assessment")
            workflow.add_edge("energy_efficiency", "sustainability_assessment")
            workflow.add_edge("sustainability_assessment", "risk_analysis")
            workflow.add_edge("risk_analysis", END)
            
            # Set entry point
            workflow.set_entry_point("climate_analysis")
            
            # Compile the workflow
            self.workflow_graph = workflow.compile()
            
            logger.info("Workflow graph built successfully")
            
        except Exception as e:
            logger.error(f"Failed to build workflow graph: {e}")
            raise
    
    def _create_agent_node(self, agent_type: AgentType):
        """Create a workflow node for an agent."""
        async def agent_node(state: Dict[str, Any]) -> Dict[str, Any]:
            try:
                agent = self.agents[agent_type]
                result = await agent.execute(state)
                
                # Update state with agent result
                state[f"{agent_type.value}_result"] = result
                
                return state
                
            except Exception as e:
                logger.error(f"Error in agent node {agent_type.value}: {e}")
                state[f"{agent_type.value}_error"] = str(e)
                return state
        
        return agent_node
    
    async def submit_task(self, task: AgentTask) -> str:
        """Submit a task for execution by the appropriate agent."""
        try:
            await self.task_queue.put(task)
            logger.info(f"Task {task.task_id} submitted for agent {task.agent_type.value}")
            return task.task_id
            
        except Exception as e:
            logger.error(f"Failed to submit task {task.task_id}: {e}")
            raise
    
    async def get_result(self, task_id: str, timeout: int = 300) -> Optional[AgentResult]:
        """Get the result of a completed task."""
        start_time = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            if task_id in self.results_store:
                return self.results_store[task_id]
            
            await asyncio.sleep(0.1)
        
        return None
    
    async def execute_workflow(self, initial_state: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the complete agent workflow."""
        try:
            if not self.workflow_graph:
                raise ValueError("Workflow graph not initialized")
            
            logger.info("Executing agent workflow...")
            
            # Execute the workflow
            result = await self.workflow_graph.ainvoke(initial_state)
            
            logger.info("Agent workflow completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute workflow: {e}")
            raise
    
    async def _process_task_queue(self):
        """Process tasks from the task queue."""
        while self.is_running:
            try:
                # Get task from queue with timeout
                task = await asyncio.wait_for(
                    self.task_queue.get(),
                    timeout=1.0
                )
                
                # Execute task
                asyncio.create_task(self._execute_task(task))
                
            except asyncio.TimeoutError:
                # No tasks in queue, continue
                continue
            except Exception as e:
                logger.error(f"Error processing task queue: {e}")
    
    async def _execute_task(self, task: AgentTask):
        """Execute a single task."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Check if agent exists
            if task.agent_type not in self.agents:
                raise ValueError(f"Agent {task.agent_type.value} not found")
            
            # Execute task with timeout
            agent = self.agents[task.agent_type]
            result = await asyncio.wait_for(
                agent.execute(task.input_data),
                timeout=task.timeout
            )
            
            # Store successful result
            execution_time = asyncio.get_event_loop().time() - start_time
            self.results_store[task.task_id] = AgentResult(
                task_id=task.task_id,
                agent_type=task.agent_type,
                success=True,
                result=result,
                execution_time=execution_time
            )
            
            logger.info(f"Task {task.task_id} completed successfully in {execution_time:.2f}s")
            
        except Exception as e:
            # Store error result
            execution_time = asyncio.get_event_loop().time() - start_time
            self.results_store[task.task_id] = AgentResult(
                task_id=task.task_id,
                agent_type=task.agent_type,
                success=False,
                result=None,
                error_message=str(e),
                execution_time=execution_time
            )
            
            logger.error(f"Task {task.task_id} failed after {execution_time:.2f}s: {e}")
    
    async def shutdown(self):
        """Shutdown the orchestrator and all agents."""
        try:
            logger.info("Shutting down Agent Orchestrator...")
            
            self.is_running = False
            
            # Cancel running tasks
            for task_id, task in self.running_tasks.items():
                task.cancel()
            
            # Shutdown individual agents
            for agent in self.agents.values():
                if hasattr(agent, 'shutdown'):
                    await agent.shutdown()
            
            logger.info("Agent Orchestrator shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during orchestrator shutdown: {e}")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get the status of all agents."""
        return {
            "initialized": self.is_initialized,
            "running": self.is_running,
            "agents": {
                agent_type.value: {
                    "status": "active" if agent_type in self.agents else "inactive",
                    "type": agent_type.value
                }
                for agent_type in AgentType
            },
            "task_queue_size": self.task_queue.qsize(),
            "completed_tasks": len(self.results_store),
            "running_tasks": len(self.running_tasks)
        }
