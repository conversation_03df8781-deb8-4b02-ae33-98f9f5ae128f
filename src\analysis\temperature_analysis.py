"""Temperature Analysis Module for Climate Data Processing."""

import numpy as np
import pandas as pd
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)


class TemperatureAnalyzer:
    """Advanced temperature data analysis."""
    
    def __init__(self):
        self.analysis_results = {}
    
    async def analyze_temperature_trends(self, temperature_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze temperature trends and patterns."""
        try:
            # Extract temperature values
            temps = temperature_data.get('temperature_values', [])
            timestamps = temperature_data.get('timestamps', [])
            
            if not temps:
                return {'status': 'error', 'error': 'No temperature data provided'}
            
            # Calculate statistics
            mean_temp = np.mean(temps)
            max_temp = np.max(temps)
            min_temp = np.min(temps)
            std_temp = np.std(temps)
            
            # Trend analysis
            if len(temps) > 1:
                trend_slope = np.polyfit(range(len(temps)), temps, 1)[0]
                trend_direction = 'increasing' if trend_slope > 0.01 else 'decreasing' if trend_slope < -0.01 else 'stable'
            else:
                trend_slope = 0
                trend_direction = 'insufficient_data'
            
            # Heat stress analysis
            heat_stress_days = sum(1 for temp in temps if temp > 35)  # Days above 35°C
            
            return {
                'status': 'success',
                'statistics': {
                    'mean_temperature': mean_temp,
                    'max_temperature': max_temp,
                    'min_temperature': min_temp,
                    'temperature_range': max_temp - min_temp,
                    'standard_deviation': std_temp
                },
                'trends': {
                    'trend_slope': trend_slope,
                    'trend_direction': trend_direction,
                    'heat_stress_days': heat_stress_days
                },
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Temperature analysis failed: {e}")
            return {'status': 'error', 'error': str(e)}


# Convenience function
async def analyze_temperature_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze temperature data."""
    analyzer = TemperatureAnalyzer()
    return await analyzer.analyze_temperature_trends(data)
