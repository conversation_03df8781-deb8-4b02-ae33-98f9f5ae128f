#!/usr/bin/env python3
"""
Ultimate Simple Startup for Unified Environmental Platform
One command to start both backend and frontend (HTML version)
"""

import subprocess
import sys
import os
import time
import logging
import webbrowser
import threading

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_backend():
    """Check if backend is running"""
    try:
        import requests
        response = requests.get('http://localhost:8000/health', timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start backend server"""
    logger.info("🚀 Starting backend server...")
    
    if check_backend():
        logger.info("✅ Backend already running on http://localhost:8000")
        return True
    
    try:
        # Start backend in a new window
        if sys.platform == 'win32':
            subprocess.Popen([
                'cmd', '/c', 'start', 'cmd', '/k',
                f'{sys.executable} -m uvicorn src.api.unified_api:app --host 0.0.0.0 --port 8000 --reload'
            ], shell=True)
        else:
            subprocess.Popen([
                sys.executable, '-m', 'uvicorn',
                'src.api.unified_api:app',
                '--host', '0.0.0.0',
                '--port', '8000',
                '--reload'
            ])
        
        logger.info("✅ Backend server starting...")
        
        # Wait for backend to be ready
        for i in range(15):  # Wait up to 45 seconds
            time.sleep(3)
            if check_backend():
                logger.info("✅ Backend is ready!")
                return True
            logger.info(f"⏳ Waiting for backend... ({i+1}/15)")
        
        # Try one more time
        if check_backend():
            logger.info("✅ Backend is ready!")
            return True
        
        logger.error("❌ Backend failed to start")
        return False
        
    except Exception as e:
        logger.error(f"❌ Failed to start backend: {e}")
        return False

def start_simple_frontend():
    """Start simple HTML frontend"""
    logger.info("🎨 Starting simple HTML frontend...")
    
    try:
        # Start simple frontend in a new window
        if sys.platform == 'win32':
            subprocess.Popen([
                'cmd', '/c', 'start', 'cmd', '/k',
                f'{sys.executable} start_simple_frontend.py'
            ], shell=True)
        else:
            subprocess.Popen([sys.executable, 'start_simple_frontend.py'])
        
        logger.info("✅ Simple frontend starting...")
        time.sleep(3)  # Give it time to start
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to start frontend: {e}")
        return False

def open_dashboard():
    """Open dashboard in browser"""
    def open_browser():
        time.sleep(5)  # Wait for servers to be ready
        try:
            webbrowser.open('http://localhost:3000')
            logger.info("🌐 Dashboard opened in browser")
        except Exception as e:
            logger.warning(f"⚠️ Could not open browser: {e}")
    
    threading.Thread(target=open_browser, daemon=True).start()

def main():
    """Main startup function"""
    logger.info("🌊💧 Ultimate Simple Startup - Unified Environmental Platform")
    logger.info("=" * 70)
    
    # Start backend
    if not start_backend():
        logger.error("❌ Failed to start backend")
        return 1
    
    # Start simple frontend
    if not start_simple_frontend():
        logger.error("❌ Failed to start frontend")
        return 1
    
    # Open dashboard
    open_dashboard()
    
    logger.info("")
    logger.info("🎉 Unified Environmental Platform is running!")
    logger.info("📊 Frontend Dashboard: http://localhost:3000")
    logger.info("🔌 Backend API: http://localhost:8000")
    logger.info("📚 API Documentation: http://localhost:8000/docs")
    logger.info("")
    logger.info("💡 Simple HTML frontend - works immediately!")
    logger.info("💡 Both servers are running in separate windows")
    logger.info("💡 Close those windows to stop the servers")
    logger.info("")
    logger.info("🌐 Dashboard should open automatically in your browser")
    logger.info("🌐 If not, go to: http://localhost:3000")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
