/**
 * WebSocket Client
 * Handles real-time communication with the backend
 */

class WebSocketClient {
    constructor(url = window.location.origin) {
        this.url = url;
        this.socket = null;
        this.isConnected = false;
        this.isPaused = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.eventHandlers = new Map();
        this.subscriptions = new Set();
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.handleConnect = this.handleConnect.bind(this);
        this.handleDisconnect = this.handleDisconnect.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    // Connect to WebSocket server
    async connect() {
        return new Promise((resolve, reject) => {
            try {
                console.log(`🔌 Connecting to WebSocket: ${this.url}`);
                
                this.socket = io(this.url, {
                    transports: ['websocket', 'polling'],
                    timeout: 10000,
                    reconnection: true,
                    reconnectionAttempts: this.maxReconnectAttempts,
                    reconnectionDelay: this.reconnectDelay
                });

                // Setup event handlers
                this.socket.on('connect', () => {
                    this.handleConnect();
                    resolve();
                });

                this.socket.on('disconnect', this.handleDisconnect);
                this.socket.on('error', (error) => {
                    this.handleError(error);
                    reject(error);
                });

                this.socket.on('connect_error', (error) => {
                    console.error('WebSocket connection error:', error);
                    reject(error);
                });

                // Setup data event handlers
                this.setupDataHandlers();

            } catch (error) {
                console.error('WebSocket connection failed:', error);
                reject(error);
            }
        });
    }

    // Setup data event handlers
    setupDataHandlers() {
        // Initial data
        this.socket.on('initial-data', (data) => {
            console.log('📊 Received initial data:', data);
            this.emit('initial-data', data);
        });

        // Data updates
        this.socket.on('data-update', (data) => {
            if (!this.isPaused) {
                console.log('📊 Received data update:', data);
                this.emit('data-update', data);
            }
        });

        // Specific data type updates
        this.socket.on('climate-update', (data) => {
            if (!this.isPaused) {
                this.emit('climate-update', data);
            }
        });

        this.socket.on('water-quality-update', (data) => {
            if (!this.isPaused) {
                this.emit('water-quality-update', data);
            }
        });

        this.socket.on('energy-update', (data) => {
            if (!this.isPaused) {
                this.emit('energy-update', data);
            }
        });

        this.socket.on('ai-agents-update', (data) => {
            if (!this.isPaused) {
                this.emit('ai-agents-update', data);
            }
        });

        this.socket.on('sensors-update', (data) => {
            if (!this.isPaused) {
                this.emit('sensors-update', data);
            }
        });

        // AI chat responses
        this.socket.on('ai-chat-response', (data) => {
            this.emit('ai-chat-response', data);
        });

        this.socket.on('ai-chat-error', (data) => {
            this.emit('ai-chat-error', data);
        });

        // System commands
        this.socket.on('system-command-result', (data) => {
            this.emit('system-command-result', data);
        });

        this.socket.on('system-command-error', (data) => {
            this.emit('system-command-error', data);
        });

        // Error handling
        this.socket.on('error', (error) => {
            console.error('WebSocket error:', error);
            this.emit('error', error);
        });
    }

    // Handle connection
    handleConnect() {
        console.log('✅ WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connect');
        
        // Resubscribe to previous subscriptions
        this.resubscribe();
    }

    // Handle disconnection
    handleDisconnect(reason) {
        console.log(`🔌 WebSocket disconnected: ${reason}`);
        this.isConnected = false;
        this.emit('disconnect', reason);
        
        // Attempt reconnection if not intentional
        if (reason !== 'io client disconnect') {
            this.attemptReconnect();
        }
    }

    // Handle errors
    handleError(error) {
        console.error('WebSocket error:', error);
        this.emit('error', error);
    }

    // Attempt reconnection
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
            
            setTimeout(() => {
                if (!this.isConnected) {
                    this.connect().catch(error => {
                        console.error('Reconnection failed:', error);
                    });
                }
            }, delay);
        } else {
            console.error('❌ Max reconnection attempts reached');
            this.emit('reconnect-failed');
        }
    }

    // Disconnect from WebSocket
    disconnect() {
        if (this.socket) {
            console.log('🔌 Disconnecting WebSocket');
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
        }
    }

    // Subscribe to data type
    subscribe(dataType) {
        if (this.isConnected && this.socket) {
            console.log(`📡 Subscribing to ${dataType}`);
            this.socket.emit('subscribe', dataType);
            this.subscriptions.add(dataType);
        }
    }

    // Unsubscribe from data type
    unsubscribe(dataType) {
        if (this.isConnected && this.socket) {
            console.log(`📡 Unsubscribing from ${dataType}`);
            this.socket.emit('unsubscribe', dataType);
            this.subscriptions.delete(dataType);
        }
    }

    // Resubscribe to all previous subscriptions
    resubscribe() {
        this.subscriptions.forEach(dataType => {
            this.subscribe(dataType);
        });
    }

    // Send AI chat message
    sendChatMessage(message, id = null) {
        if (this.isConnected && this.socket) {
            const chatData = {
                id: id || this.generateId(),
                message: message,
                timestamp: new Date().toISOString()
            };
            
            console.log('💬 Sending chat message:', chatData);
            this.socket.emit('ai-chat', chatData);
            
            return chatData.id;
        }
        
        throw new Error('WebSocket not connected');
    }

    // Send system command
    sendSystemCommand(command) {
        if (this.isConnected && this.socket) {
            console.log('⚙️ Sending system command:', command);
            this.socket.emit('system-command', command);
        } else {
            throw new Error('WebSocket not connected');
        }
    }

    // Notify page change
    notifyPageChange(pageName) {
        if (this.isConnected && this.socket) {
            this.socket.emit('page-change', pageName);
        }
    }

    // Event handling
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    emit(event, data = null) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }

    // Pause updates (useful when page is hidden)
    pause() {
        console.log('⏸️ Pausing WebSocket updates');
        this.isPaused = true;
    }

    // Resume updates
    resume() {
        console.log('▶️ Resuming WebSocket updates');
        this.isPaused = false;
    }

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Get connection status
    getStatus() {
        return {
            connected: this.isConnected,
            paused: this.isPaused,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions)
        };
    }
}

// Export to global scope
window.WebSocketClient = WebSocketClient;
