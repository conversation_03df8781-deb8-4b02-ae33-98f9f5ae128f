"""
Seasonal Variation Modeling Module.

This module provides comprehensive seasonal variation modeling capabilities
for climate data, including seasonal decomposition, trend analysis, forecasting,
and water treatment optimization based on seasonal patterns.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
from scipy import stats
from scipy.stats import linregress
from scipy.signal import find_peaks
import json

from src.utils.config import get_settings
from src.utils.cache import cache_decorator, cache_set, cache_get
from src.utils.logging_config import log_async_function_call
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData

logger = logging.getLogger(__name__)


@dataclass
class SeasonalComponent:
    """Seasonal component information."""
    season: str
    component_type: str  # 'temperature', 'precipitation', 'humidity', etc.
    mean_value: float
    amplitude: float
    phase_shift: float  # Days from start of year
    trend_slope: float  # Change per year
    variability: float  # Standard deviation
    confidence: float


@dataclass
class SeasonalForecast:
    """Seasonal forecast information."""
    forecast_date: datetime
    forecast_horizon_days: int
    predicted_values: List[float]
    confidence_intervals: List[Tuple[float, float]]
    seasonal_component: float
    trend_component: float
    forecast_confidence: float


@dataclass
class SeasonalModelResult:
    """Result of seasonal variation modeling."""
    location: str
    analysis_period: Dict[str, str]
    seasonal_components: Dict[str, SeasonalComponent]
    seasonal_decomposition: Dict[str, Any]
    seasonal_forecasts: Dict[str, List[SeasonalForecast]]
    seasonal_trends: Dict[str, Any]
    climate_cycles: Dict[str, Any]
    water_treatment_seasonal_plan: Dict[str, Any]
    model_performance: Dict[str, float]
    timestamp: datetime


class SeasonalVariationModeler:
    """
    Comprehensive seasonal variation modeling system.
    
    Provides:
    - Advanced seasonal decomposition (trend, seasonal, residual)
    - Multi-parameter seasonal analysis
    - Seasonal forecasting with confidence intervals
    - Climate cycle detection (annual, multi-annual)
    - Water treatment seasonal optimization
    - Seasonal anomaly detection
    - Climate change impact on seasonal patterns
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.is_initialized = False
        
        # Modeling parameters
        self.seasonal_periods = {
            'annual': 365.25,
            'semi_annual': 182.625,
            'quarterly': 91.3125,
            'monthly': 30.44
        }
        
        # Water treatment seasonal parameters
        self.treatment_seasonal_factors = {
            'temperature_efficiency': {
                'optimal_range': (15, 25),  # °C
                'efficiency_curve': 'bell',  # bell, linear, exponential
                'seasonal_adjustment': 0.15  # 15% seasonal variation
            },
            'precipitation_capacity': {
                'base_capacity': 100,  # mm/day
                'seasonal_multiplier': 1.5,  # Peak season capacity
                'overflow_threshold': 150  # mm/day
            },
            'biological_activity': {
                'temperature_dependent': True,
                'optimal_temp_range': (18, 28),  # °C
                'activity_curve': 'exponential'
            }
        }
        
        # Forecasting parameters
        self.forecast_horizons = [30, 90, 180, 365]  # Days
        self.min_data_years = 2  # Minimum years for reliable seasonal modeling
        self.confidence_levels = [0.68, 0.95]  # 1σ and 2σ confidence intervals
    
    @log_async_function_call
    async def initialize(self):
        """Initialize the seasonal variation modeler."""
        try:
            logger.info("Initializing Seasonal Variation Modeler...")
            self.is_initialized = True
            logger.info("Seasonal Variation Modeler initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize seasonal modeler: {e}")
            return False
    
    async def model_seasonal_variations(self, data: List[ProcessedClimateData], 
                                      location: str = None) -> SeasonalModelResult:
        """Perform comprehensive seasonal variation modeling."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not data:
                raise ValueError("No data provided for modeling")
            
            logger.info(f"Modeling seasonal variations for {len(data)} data points")
            
            # Convert to DataFrame for analysis
            df = await self._prepare_seasonal_dataframe(data)
            
            if df.empty or len(df) < 365:  # Need at least 1 year
                raise ValueError("Insufficient data for seasonal modeling (need at least 1 year)")
            
            # Determine location
            analysis_location = location or self._extract_location(data)
            
            # Perform seasonal decomposition for each parameter
            seasonal_components = await self._decompose_seasonal_components(df)
            
            # Advanced seasonal decomposition
            seasonal_decomposition = await self._advanced_seasonal_decomposition(df)
            
            # Generate seasonal forecasts
            seasonal_forecasts = await self._generate_seasonal_forecasts(df, seasonal_components)
            
            # Analyze seasonal trends
            seasonal_trends = await self._analyze_seasonal_trends(df, seasonal_components)
            
            # Detect climate cycles
            climate_cycles = await self._detect_climate_cycles(df)
            
            # Generate water treatment seasonal plan
            treatment_plan = await self._generate_seasonal_treatment_plan(df, seasonal_components)
            
            # Evaluate model performance
            model_performance = await self._evaluate_model_performance(df, seasonal_components)
            
            # Create result
            result = SeasonalModelResult(
                location=analysis_location,
                analysis_period={
                    'start': df.index.min().isoformat(),
                    'end': df.index.max().isoformat(),
                    'duration_days': (df.index.max() - df.index.min()).days,
                    'years_of_data': (df.index.max() - df.index.min()).days / 365.25
                },
                seasonal_components=seasonal_components,
                seasonal_decomposition=seasonal_decomposition,
                seasonal_forecasts=seasonal_forecasts,
                seasonal_trends=seasonal_trends,
                climate_cycles=climate_cycles,
                water_treatment_seasonal_plan=treatment_plan,
                model_performance=model_performance,
                timestamp=datetime.now()
            )
            
            logger.info(f"Seasonal variation modeling completed for {analysis_location}")
            return result
            
        except Exception as e:
            logger.error(f"Seasonal variation modeling failed: {e}")
            raise
    
    async def _prepare_seasonal_dataframe(self, data: List[ProcessedClimateData]) -> pd.DataFrame:
        """Prepare DataFrame from climate data for seasonal modeling."""
        try:
            records = []
            for item in data:
                record = {
                    'timestamp': item.timestamp,
                    'location': item.location,
                    'source': item.source,
                    'quality_score': getattr(item, 'data_quality_score', 1.0)
                }
                
                # Add available climate parameters
                if item.temperature is not None:
                    record['temperature'] = item.temperature
                if item.temperature_max is not None:
                    record['temperature_max'] = item.temperature_max
                if item.temperature_min is not None:
                    record['temperature_min'] = item.temperature_min
                if item.precipitation is not None:
                    record['precipitation'] = item.precipitation
                if hasattr(item, 'humidity') and item.humidity is not None:
                    record['humidity'] = item.humidity
                if hasattr(item, 'pressure') and item.pressure is not None:
                    record['pressure'] = item.pressure
                if hasattr(item, 'wind_speed') and item.wind_speed is not None:
                    record['wind_speed'] = item.wind_speed
                
                records.append(record)
            
            if not records:
                return pd.DataFrame()
            
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Handle duplicates
            df = df.groupby(df.index).mean(numeric_only=True)
            
            # Add time-based features for seasonal analysis
            df['day_of_year'] = df.index.dayofyear
            df['month'] = df.index.month
            df['season'] = df['month'].map(self._get_season)
            df['year'] = df.index.year
            
            # Fill missing values with appropriate methods
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if col == 'precipitation':
                    df[col] = df[col].fillna(0.0)  # No rain
                elif col not in ['day_of_year', 'month', 'year']:
                    df[col] = df[col].interpolate(method='linear')
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to prepare seasonal DataFrame: {e}")
            return pd.DataFrame()
    
    def _extract_location(self, data: List[ProcessedClimateData]) -> str:
        """Extract location from climate data."""
        locations = [item.location for item in data if item.location]
        if locations:
            return max(set(locations), key=locations.count)
        return "Unknown Location"
    
    def _get_season(self, month: int) -> str:
        """Map month to season."""
        if month in [12, 1, 2]:
            return 'Winter'
        elif month in [3, 4, 5]:
            return 'Spring'
        elif month in [6, 7, 8]:
            return 'Summer'
        else:
            return 'Autumn'
    
    async def _decompose_seasonal_components(self, df: pd.DataFrame) -> Dict[str, SeasonalComponent]:
        """Decompose seasonal components for each climate parameter."""
        try:
            components = {}
            
            # Parameters to analyze
            climate_params = ['temperature', 'precipitation', 'humidity', 'pressure', 'wind_speed']
            
            for param in climate_params:
                if param not in df.columns:
                    continue
                
                data_series = df[param].dropna()
                if len(data_series) < 365:  # Need at least 1 year
                    continue
                
                # Perform seasonal decomposition
                component = await self._analyze_parameter_seasonality(data_series, param)
                if component:
                    components[param] = component
            
            return components
            
        except Exception as e:
            logger.error(f"Seasonal component decomposition failed: {e}")
            return {}
    
    async def _analyze_parameter_seasonality(self, data_series: pd.Series, param_name: str) -> Optional[SeasonalComponent]:
        """Analyze seasonality for a specific parameter."""
        try:
            # Calculate seasonal statistics
            df_temp = pd.DataFrame({'value': data_series})
            df_temp['day_of_year'] = data_series.index.dayofyear
            df_temp['month'] = data_series.index.month
            df_temp['season'] = df_temp['month'].map(self._get_season)
            df_temp['year'] = data_series.index.year
            
            # Calculate seasonal means
            seasonal_means = df_temp.groupby('season')['value'].mean()
            
            # Calculate overall statistics
            mean_value = float(data_series.mean())
            annual_amplitude = float(seasonal_means.max() - seasonal_means.min())
            
            # Find phase shift (day of year with peak value)
            daily_means = df_temp.groupby('day_of_year')['value'].mean()
            if param_name == 'precipitation':
                # For precipitation, find wettest day
                phase_shift = float(daily_means.idxmax())
            else:
                # For temperature and others, find peak day
                phase_shift = float(daily_means.idxmax())
            
            # Calculate trend slope
            time_numeric = (data_series.index - data_series.index.min()).days
            slope, _, r_value, p_value, _ = linregress(time_numeric, data_series.values)
            trend_slope = float(slope * 365.25)  # Per year
            
            # Calculate variability
            variability = float(data_series.std())
            
            # Calculate confidence based on R² and data length
            confidence = float(min(1.0, r_value**2 + (len(data_series) / 1000)))
            
            return SeasonalComponent(
                season=param_name,
                component_type=param_name,
                mean_value=mean_value,
                amplitude=annual_amplitude,
                phase_shift=phase_shift,
                trend_slope=trend_slope,
                variability=variability,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Parameter seasonality analysis failed for {param_name}: {e}")
            return None
    
    async def _advanced_seasonal_decomposition(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform advanced seasonal decomposition."""
        try:
            decomposition = {}
            
            # Parameters to decompose
            climate_params = ['temperature', 'precipitation']
            
            for param in climate_params:
                if param not in df.columns:
                    continue
                
                data_series = df[param].dropna()
                if len(data_series) < 730:  # Need at least 2 years for reliable decomposition
                    continue
                
                # Simple seasonal decomposition (moving averages)
                param_decomp = await self._decompose_time_series(data_series, param)
                if param_decomp:
                    decomposition[param] = param_decomp
            
            return decomposition
            
        except Exception as e:
            logger.error(f"Advanced seasonal decomposition failed: {e}")
            return {}
    
    async def _decompose_time_series(self, data_series: pd.Series, param_name: str) -> Optional[Dict[str, Any]]:
        """Decompose time series into trend, seasonal, and residual components."""
        try:
            # Calculate trend using 365-day moving average
            trend = data_series.rolling(window=365, center=True, min_periods=180).mean()
            
            # Calculate seasonal component
            detrended = data_series - trend
            
            # Group by day of year and calculate seasonal pattern
            df_temp = pd.DataFrame({'detrended': detrended})
            df_temp['day_of_year'] = detrended.index.dayofyear
            seasonal_pattern = df_temp.groupby('day_of_year')['detrended'].mean()
            
            # Map seasonal pattern back to full time series
            seasonal = pd.Series(index=data_series.index, dtype=float)
            for date in data_series.index:
                day_of_year = date.dayofyear
                seasonal[date] = seasonal_pattern.get(day_of_year, 0.0)
            
            # Calculate residual
            residual = data_series - trend - seasonal
            
            # Calculate component statistics
            trend_strength = 1 - (residual.var() / (trend + residual).var()) if (trend + residual).var() > 0 else 0
            seasonal_strength = 1 - (residual.var() / (seasonal + residual).var()) if (seasonal + residual).var() > 0 else 0
            
            return {
                'original': data_series.to_dict(),
                'trend': trend.dropna().to_dict(),
                'seasonal': seasonal.to_dict(),
                'residual': residual.dropna().to_dict(),
                'trend_strength': float(trend_strength),
                'seasonal_strength': float(seasonal_strength),
                'decomposition_quality': float((trend_strength + seasonal_strength) / 2)
            }
            
        except Exception as e:
            logger.error(f"Time series decomposition failed for {param_name}: {e}")
            return None

    async def _generate_seasonal_forecasts(self, df: pd.DataFrame,
                                         seasonal_components: Dict[str, SeasonalComponent]) -> Dict[str, List[SeasonalForecast]]:
        """Generate seasonal forecasts for climate parameters."""
        try:
            forecasts = {}

            for param_name, component in seasonal_components.items():
                if param_name not in df.columns:
                    continue

                param_forecasts = []
                data_series = df[param_name].dropna()

                if len(data_series) < 365:
                    continue

                # Generate forecasts for different horizons
                for horizon_days in self.forecast_horizons:
                    forecast = await self._generate_parameter_forecast(
                        data_series, component, horizon_days
                    )
                    if forecast:
                        param_forecasts.append(forecast)

                if param_forecasts:
                    forecasts[param_name] = param_forecasts

            return forecasts

        except Exception as e:
            logger.error(f"Seasonal forecast generation failed: {e}")
            return {}

    async def _generate_parameter_forecast(self, data_series: pd.Series,
                                         component: SeasonalComponent,
                                         horizon_days: int) -> Optional[SeasonalForecast]:
        """Generate forecast for a specific parameter and horizon."""
        try:
            # Start forecast from last data point
            last_date = data_series.index.max()
            forecast_dates = pd.date_range(
                start=last_date + timedelta(days=1),
                periods=horizon_days,
                freq='D'
            )

            predicted_values = []
            confidence_intervals = []

            for forecast_date in forecast_dates:
                # Calculate seasonal component
                day_of_year = forecast_date.dayofyear
                seasonal_value = component.amplitude * np.sin(
                    2 * np.pi * (day_of_year - component.phase_shift) / 365.25
                )

                # Calculate trend component
                days_from_start = (forecast_date - data_series.index.min()).days
                trend_value = component.trend_slope * (days_from_start / 365.25)

                # Combine components
                predicted_value = component.mean_value + seasonal_value + trend_value
                predicted_values.append(float(predicted_value))

                # Calculate confidence intervals
                uncertainty = component.variability * np.sqrt(1 + (days_from_start / len(data_series)))
                ci_lower = predicted_value - 1.96 * uncertainty  # 95% CI
                ci_upper = predicted_value + 1.96 * uncertainty
                confidence_intervals.append((float(ci_lower), float(ci_upper)))

            # Calculate forecast confidence
            forecast_confidence = component.confidence * np.exp(-horizon_days / 365.25)

            return SeasonalForecast(
                forecast_date=last_date,
                forecast_horizon_days=horizon_days,
                predicted_values=predicted_values,
                confidence_intervals=confidence_intervals,
                seasonal_component=float(component.amplitude),
                trend_component=float(component.trend_slope),
                forecast_confidence=float(forecast_confidence)
            )

        except Exception as e:
            logger.error(f"Parameter forecast generation failed: {e}")
            return None

    async def _analyze_seasonal_trends(self, df: pd.DataFrame,
                                     seasonal_components: Dict[str, SeasonalComponent]) -> Dict[str, Any]:
        """Analyze trends in seasonal patterns."""
        try:
            trends = {}

            for param_name, component in seasonal_components.items():
                if param_name not in df.columns:
                    continue

                data_series = df[param_name].dropna()

                # Analyze seasonal trend changes
                param_trends = await self._analyze_parameter_seasonal_trends(data_series, component)
                if param_trends:
                    trends[param_name] = param_trends

            # Overall seasonal trend summary
            trends['summary'] = {
                'parameters_analyzed': len(trends),
                'strongest_seasonal_signal': self._find_strongest_seasonal_signal(seasonal_components),
                'climate_change_indicators': self._assess_climate_change_indicators(trends)
            }

            return trends

        except Exception as e:
            logger.error(f"Seasonal trends analysis failed: {e}")
            return {}

    async def _analyze_parameter_seasonal_trends(self, data_series: pd.Series,
                                               component: SeasonalComponent) -> Optional[Dict[str, Any]]:
        """Analyze seasonal trends for a specific parameter."""
        try:
            # Group data by season and year
            df_temp = pd.DataFrame({'value': data_series})
            df_temp['month'] = data_series.index.month
            df_temp['season'] = df_temp['month'].map(self._get_season)
            df_temp['year'] = data_series.index.year

            seasonal_trends = {}

            # Analyze trend for each season
            for season in ['Spring', 'Summer', 'Autumn', 'Winter']:
                season_data = df_temp[df_temp['season'] == season]

                if len(season_data) < 3:  # Need at least 3 years
                    continue

                # Calculate annual means for this season
                annual_means = season_data.groupby('year')['value'].mean()

                if len(annual_means) >= 3:
                    # Calculate trend
                    years = annual_means.index.values
                    values = annual_means.values
                    slope, _, r_value, p_value, _ = linregress(years, values)

                    seasonal_trends[season] = {
                        'trend_slope': float(slope),  # Change per year
                        'trend_confidence': float(r_value**2),
                        'trend_significance': p_value < 0.05,
                        'mean_value': float(annual_means.mean()),
                        'years_analyzed': len(annual_means)
                    }

            return {
                'seasonal_trends': seasonal_trends,
                'overall_trend': component.trend_slope,
                'seasonal_amplitude_trend': 0.0,  # Simplified for now
                'phase_shift_trend': 0.0  # Simplified for now
            }

        except Exception as e:
            logger.error(f"Parameter seasonal trends analysis failed: {e}")
            return None

    def _find_strongest_seasonal_signal(self, seasonal_components: Dict[str, SeasonalComponent]) -> str:
        """Find parameter with strongest seasonal signal."""
        try:
            if not seasonal_components:
                return 'none'

            strongest_param = max(
                seasonal_components.keys(),
                key=lambda p: seasonal_components[p].amplitude / seasonal_components[p].mean_value
                if seasonal_components[p].mean_value > 0 else 0
            )

            return strongest_param

        except Exception as e:
            logger.warning(f"Failed to find strongest seasonal signal: {e}")
            return 'unknown'

    def _assess_climate_change_indicators(self, trends: Dict[str, Any]) -> List[str]:
        """Assess climate change indicators from seasonal trends."""
        try:
            indicators = []

            # Check for temperature trends
            if 'temperature' in trends:
                temp_trends = trends['temperature'].get('seasonal_trends', {})
                overall_trend = trends['temperature'].get('overall_trend', 0)

                if overall_trend > 0.5:  # >0.5°C per year warming
                    indicators.append('significant_warming_trend')

            # Check for precipitation trends
            if 'precipitation' in trends:
                precip_trends = trends['precipitation'].get('seasonal_trends', {})

                # Check for changing precipitation patterns
                significant_changes = sum(
                    1 for season_data in precip_trends.values()
                    if season_data.get('trend_significance', False)
                )

                if significant_changes >= 2:
                    indicators.append('changing_precipitation_patterns')

            return indicators

        except Exception as e:
            logger.warning(f"Climate change indicators assessment failed: {e}")
            return []

    async def _detect_climate_cycles(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Detect climate cycles beyond annual seasonality."""
        try:
            cycles = {}

            # Parameters to analyze for cycles
            climate_params = ['temperature', 'precipitation']

            for param in climate_params:
                if param not in df.columns:
                    continue

                data_series = df[param].dropna()
                if len(data_series) < 1095:  # Need at least 3 years
                    continue

                # Detect multi-annual cycles
                param_cycles = await self._detect_parameter_cycles(data_series, param)
                if param_cycles:
                    cycles[param] = param_cycles

            return cycles

        except Exception as e:
            logger.error(f"Climate cycle detection failed: {e}")
            return {}

    async def _detect_parameter_cycles(self, data_series: pd.Series, param_name: str) -> Optional[Dict[str, Any]]:
        """Detect cycles in a specific parameter."""
        try:
            # Simple cycle detection using annual means
            df_temp = pd.DataFrame({'value': data_series})
            df_temp['year'] = data_series.index.year

            annual_means = df_temp.groupby('year')['value'].mean()

            if len(annual_means) < 5:  # Need at least 5 years
                return None

            # Calculate variability in annual means
            annual_variability = annual_means.std()
            overall_mean = annual_means.mean()

            # Detect potential cycles (simplified)
            cycle_strength = annual_variability / overall_mean if overall_mean > 0 else 0

            return {
                'annual_variability': float(annual_variability),
                'cycle_strength': float(cycle_strength),
                'years_analyzed': len(annual_means),
                'potential_cycles': ['annual'] if cycle_strength > 0.1 else []
            }

        except Exception as e:
            logger.error(f"Parameter cycle detection failed for {param_name}: {e}")
            return None

    async def _generate_seasonal_treatment_plan(self, df: pd.DataFrame,
                                              seasonal_components: Dict[str, SeasonalComponent]) -> Dict[str, Any]:
        """Generate seasonal water treatment optimization plan."""
        try:
            treatment_plan = {}

            # Seasonal capacity planning
            if 'precipitation' in seasonal_components:
                precip_component = seasonal_components['precipitation']
                treatment_plan['capacity_planning'] = await self._plan_seasonal_capacity(precip_component)

            # Temperature-based efficiency planning
            if 'temperature' in seasonal_components:
                temp_component = seasonal_components['temperature']
                treatment_plan['efficiency_optimization'] = await self._plan_temperature_efficiency(temp_component)

            # Seasonal maintenance scheduling
            treatment_plan['maintenance_schedule'] = await self._plan_seasonal_maintenance(seasonal_components)

            # Seasonal monitoring recommendations
            treatment_plan['monitoring_recommendations'] = await self._plan_seasonal_monitoring(seasonal_components)

            return treatment_plan

        except Exception as e:
            logger.error(f"Seasonal treatment plan generation failed: {e}")
            return {}

    async def _plan_seasonal_capacity(self, precip_component: SeasonalComponent) -> Dict[str, Any]:
        """Plan seasonal treatment capacity based on precipitation patterns."""
        try:
            base_capacity = self.treatment_seasonal_factors['precipitation_capacity']['base_capacity']
            seasonal_multiplier = self.treatment_seasonal_factors['precipitation_capacity']['seasonal_multiplier']

            # Calculate seasonal capacity requirements
            peak_season_capacity = base_capacity * seasonal_multiplier
            low_season_capacity = base_capacity * 0.7

            return {
                'base_capacity': base_capacity,
                'peak_season_capacity': peak_season_capacity,
                'low_season_capacity': low_season_capacity,
                'peak_precipitation_period': f"Day {precip_component.phase_shift:.0f} of year",
                'capacity_adjustment_factor': seasonal_multiplier,
                'recommendations': [
                    'Increase capacity during peak precipitation season',
                    'Schedule maintenance during low precipitation periods',
                    'Monitor overflow systems during peak season'
                ]
            }

        except Exception as e:
            logger.error(f"Seasonal capacity planning failed: {e}")
            return {}

    async def _plan_temperature_efficiency(self, temp_component: SeasonalComponent) -> Dict[str, Any]:
        """Plan temperature-based efficiency optimization."""
        try:
            optimal_range = self.treatment_seasonal_factors['temperature_efficiency']['optimal_range']

            # Calculate seasonal efficiency variations
            peak_temp_day = temp_component.phase_shift
            min_temp_day = (peak_temp_day + 182.625) % 365.25  # Opposite season

            return {
                'optimal_temperature_range': optimal_range,
                'peak_efficiency_period': f"Day {min_temp_day:.0f} of year (cooler temperatures)",
                'low_efficiency_period': f"Day {peak_temp_day:.0f} of year (peak temperatures)",
                'seasonal_efficiency_variation': f"{temp_component.amplitude:.1f}°C amplitude",
                'recommendations': [
                    'Implement cooling systems during peak temperature periods',
                    'Adjust chemical dosing based on seasonal temperature',
                    'Monitor biological activity during temperature extremes',
                    'Optimize energy usage during moderate temperature periods'
                ]
            }

        except Exception as e:
            logger.error(f"Temperature efficiency planning failed: {e}")
            return {}

    async def _plan_seasonal_maintenance(self, seasonal_components: Dict[str, SeasonalComponent]) -> Dict[str, Any]:
        """Plan seasonal maintenance schedule."""
        try:
            maintenance_schedule = {
                'Spring': ['Equipment inspection after winter', 'Calibrate sensors', 'Check heating systems'],
                'Summer': ['Cooling system maintenance', 'High-capacity equipment check', 'Monitor for overheating'],
                'Autumn': ['Prepare for winter operations', 'Insulation check', 'Backup system testing'],
                'Winter': ['Monitor freeze protection', 'Heating system operation', 'Emergency equipment check']
            }

            # Customize based on local climate patterns
            if 'precipitation' in seasonal_components:
                precip_component = seasonal_components['precipitation']
                peak_precip_season = self._get_season_from_day(precip_component.phase_shift)
                maintenance_schedule[peak_precip_season].append('Extra drainage system maintenance')

            if 'temperature' in seasonal_components:
                temp_component = seasonal_components['temperature']
                peak_temp_season = self._get_season_from_day(temp_component.phase_shift)
                maintenance_schedule[peak_temp_season].append('Cooling system priority maintenance')

            return {
                'seasonal_schedule': maintenance_schedule,
                'critical_periods': self._identify_critical_maintenance_periods(seasonal_components),
                'recommendations': [
                    'Schedule major maintenance during mild weather periods',
                    'Prepare equipment before extreme seasons',
                    'Maintain backup systems year-round'
                ]
            }

        except Exception as e:
            logger.error(f"Seasonal maintenance planning failed: {e}")
            return {}

    async def _plan_seasonal_monitoring(self, seasonal_components: Dict[str, SeasonalComponent]) -> Dict[str, Any]:
        """Plan seasonal monitoring recommendations."""
        try:
            monitoring_plan = {
                'high_frequency_periods': [],
                'standard_monitoring_periods': [],
                'parameters_to_monitor': {},
                'alert_thresholds': {}
            }

            # Determine high-frequency monitoring periods
            for param_name, component in seasonal_components.items():
                if component.amplitude > component.mean_value * 0.3:  # High seasonal variation
                    peak_season = self._get_season_from_day(component.phase_shift)
                    if peak_season not in monitoring_plan['high_frequency_periods']:
                        monitoring_plan['high_frequency_periods'].append(peak_season)

                    monitoring_plan['parameters_to_monitor'][param_name] = {
                        'peak_season': peak_season,
                        'monitoring_frequency': 'daily' if component.amplitude > component.mean_value * 0.5 else 'weekly',
                        'alert_threshold': component.mean_value + 2 * component.variability
                    }

            return monitoring_plan

        except Exception as e:
            logger.error(f"Seasonal monitoring planning failed: {e}")
            return {}

    def _get_season_from_day(self, day_of_year: float) -> str:
        """Get season from day of year."""
        day = int(day_of_year) % 365
        if day < 80 or day >= 355:  # Dec 21 - Mar 20
            return 'Winter'
        elif day < 172:  # Mar 21 - Jun 20
            return 'Spring'
        elif day < 266:  # Jun 21 - Sep 22
            return 'Summer'
        else:  # Sep 23 - Dec 20
            return 'Autumn'

    def _identify_critical_maintenance_periods(self, seasonal_components: Dict[str, SeasonalComponent]) -> List[str]:
        """Identify critical maintenance periods."""
        try:
            critical_periods = []

            # Identify periods with extreme conditions
            for param_name, component in seasonal_components.items():
                if component.amplitude > component.mean_value * 0.4:  # High variation
                    peak_season = self._get_season_from_day(component.phase_shift)
                    if peak_season not in critical_periods:
                        critical_periods.append(peak_season)

            return critical_periods

        except Exception as e:
            logger.warning(f"Failed to identify critical maintenance periods: {e}")
            return []

    async def _evaluate_model_performance(self, df: pd.DataFrame,
                                        seasonal_components: Dict[str, SeasonalComponent]) -> Dict[str, float]:
        """Evaluate seasonal model performance."""
        try:
            performance = {}

            for param_name, component in seasonal_components.items():
                if param_name not in df.columns:
                    continue

                data_series = df[param_name].dropna()

                # Calculate model performance metrics
                param_performance = await self._evaluate_parameter_model(data_series, component)
                if param_performance:
                    performance[param_name] = param_performance

            # Overall model performance
            if performance:
                performance['overall'] = {
                    'mean_confidence': float(np.mean([comp.confidence for comp in seasonal_components.values()])),
                    'parameters_modeled': len(performance),
                    'model_quality': float(np.mean([perf.get('r_squared', 0) for perf in performance.values() if isinstance(perf, dict)]))
                }

            return performance

        except Exception as e:
            logger.error(f"Model performance evaluation failed: {e}")
            return {}

    async def _evaluate_parameter_model(self, data_series: pd.Series, component: SeasonalComponent) -> Optional[Dict[str, float]]:
        """Evaluate model performance for a specific parameter."""
        try:
            # Generate model predictions
            predicted_values = []
            actual_values = []

            for date, actual_value in data_series.items():
                # Calculate model prediction
                day_of_year = date.dayofyear
                seasonal_value = component.amplitude * np.sin(
                    2 * np.pi * (day_of_year - component.phase_shift) / 365.25
                )

                days_from_start = (date - data_series.index.min()).days
                trend_value = component.trend_slope * (days_from_start / 365.25)

                predicted_value = component.mean_value + seasonal_value + trend_value

                predicted_values.append(predicted_value)
                actual_values.append(actual_value)

            # Calculate performance metrics
            predicted_values = np.array(predicted_values)
            actual_values = np.array(actual_values)

            # R-squared
            ss_res = np.sum((actual_values - predicted_values) ** 2)
            ss_tot = np.sum((actual_values - np.mean(actual_values)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0

            # RMSE
            rmse = np.sqrt(np.mean((actual_values - predicted_values) ** 2))

            # MAE
            mae = np.mean(np.abs(actual_values - predicted_values))

            return {
                'r_squared': float(r_squared),
                'rmse': float(rmse),
                'mae': float(mae),
                'model_confidence': component.confidence
            }

        except Exception as e:
            logger.error(f"Parameter model evaluation failed: {e}")
            return None


# Convenience functions
async def model_seasonal_variations_for_location(data: List[ProcessedClimateData],
                                               location: str = None) -> SeasonalModelResult:
    """Model seasonal variations for a specific location."""
    modeler = SeasonalVariationModeler()
    await modeler.initialize()

    return await modeler.model_seasonal_variations(data, location)


async def generate_seasonal_forecast(data: List[ProcessedClimateData],
                                   parameter: str = 'temperature',
                                   horizon_days: int = 90,
                                   location: str = None) -> Optional[SeasonalForecast]:
    """Generate seasonal forecast for a specific parameter."""
    modeler = SeasonalVariationModeler()
    await modeler.initialize()

    result = await modeler.model_seasonal_variations(data, location)

    if result and parameter in result.seasonal_forecasts:
        forecasts = result.seasonal_forecasts[parameter]
        # Find forecast with matching horizon
        for forecast in forecasts:
            if forecast.forecast_horizon_days == horizon_days:
                return forecast

    return None


async def get_seasonal_treatment_plan(data: List[ProcessedClimateData],
                                    location: str = None) -> Dict[str, Any]:
    """Get seasonal water treatment optimization plan."""
    modeler = SeasonalVariationModeler()
    await modeler.initialize()

    result = await modeler.model_seasonal_variations(data, location)
    return result.water_treatment_seasonal_plan if result else {}
